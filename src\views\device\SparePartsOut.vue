<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-08-16 10:54:07
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-12-21 10:57:44
 * @FilePath: \equipment_manage_backendg:\gitee\equipment_manage_front\src\views\device\SparePartsOut.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-row :gutter="10" class="goods_box">
      <!-- 左侧 -->
      <div class="bg-white left-content">
        <el-row class="menu-title p-2 m-2">
          <el-tabs v-model="editableTabsValue" stretch @tab-click="tabclick">
            <el-tab-pane
              v-for="(item, index) in partsTypeList"
              :key="index"
              :label="item.second_class"
              :name="item.second_class"
            >
              <div class="goods_list">
                <el-row class="good_list__item">
                  <el-col
                    :md="8"
                    :lg="8"
                    :xl="4"
                    v-for="(items, index1) in goodsList"
                    :key="index1"
                  >
                    <el-card class="box-card">
                      <div class="text item" @click="addCart(items)">
                        <div class="block">
                          <el-image
                            :src="items.part_img"
                            lazy
                            fit="fill"
                            style="width: 100%; height: 100%"
                          >
                            <div slot="placeholder" class="image-slot">
                              加载中<span class="dot">...</span>
                            </div>
                          </el-image>
                        </div>
                        <div class="bottom">
                          <div class="text-content">
                            <h5 class="name">{{ items.part_name }}</h5>
                            <h5 class="number">{{ items.part_number }}</h5>
                            <h5 class="price">
                              <i class="el-icon-coin"></i>
                              库位：{{ items.storage_number }}
                            </h5>
                            <h5>
                              <i class="el-icon-coin"></i>
                              <span class="price">
                                库存：{{ items.stock_quantity }}</span
                              >
                            </h5>
                          </div>
                          <div class="bottom-end">
                            <div class="ring">
                              <i class="el-icon-shopping-cart-1"></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
              </div>
            </el-tab-pane>
          </el-tabs>
          <!-- 分页 -->
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[12, 16, 20, 24]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </el-row>
      </div>
      <!-- 右侧收银 -->
      <div class="bg-white right-content">
        <div class="search-content">
          <el-input
            placeholder="请输入备件名称或扫描备件编号"
            @input="handleInput"
            v-model="keywords"
            ref="inputRef"
            id="inputId"
            clearable
            autofocus
            autosize
            suffix-icon="el-icon-search"
          ></el-input>
        </div>
        <div class="table-title">
          <span>配件名称</span>
          <span>配件编号</span>
          <span>领用数量</span>
          <span>小计</span>
          <span>使用用途</span>
          <span>操作</span>
        </div>
        <div class="menu-list-content">
          <template>
            <div v-if="consumeList.length > 0">
              <div
                class="goods_item-list"
                v-for="(item, index) in consumeList"
                :key="index"
              >
                <span>{{ item.part_name }}</span>
                <span>{{ item.part_number }}</span>
                <span>
                  <el-input-number
                    style="width: 7.2rem"
                    size="small"
                    v-model="item.quantity"
                    @change="handleChange($event, item.part_number)"
                    :min="1"
                    :max="10000"
                  ></el-input-number>
                </span>
                <span>{{ item.subtotal }}</span>
                <span>
                  <el-select v-model="item.destiniation" placeholder="选择用途">
                    <el-option label="更换" value="更换"></el-option>
                    <el-option label="领用" value="领用"></el-option>
                  </el-select>
                </span>
                <i class="el-icon-error" @click="delItems(item)"></i>
              </div>
            </div>
            <div v-else>
              <el-empty description="请点击左侧的备件或者扫描条形码"></el-empty>
            </div>
          </template>
        </div>
        <div class="userinfo-content">
          <div>
            <span class="el-icon-user-solid"></span> <span>领用人信息</span>
          </div>
          <div class="tuichu" v-show="userInfo.length > 0">
            <h5 @click="clearInfo">
              <i class="el-icon-s-release"></i> 注销信息
            </h5>
          </div>
        </div>
        <div class="bottom-info" v-if="userInfo.length > 0">
          <div class="bottom-info__left">
            <div class="head_img">
              <img :src="userInfo[0].avatar" />
            </div>
            <div>
              <h5>领用人：{{ userInfo[0].username }}</h5>
              <h5>工号: {{ userInfo[0].uuid }}</h5>
              <h5>班次: {{ userInfo[0].shift }}</h5>
              <h5>领用部门：{{ userInfo[0].department }}</h5>
              <h5>领用时间：{{ userInfo[0].time }}</h5>
            </div>
          </div>
          <div class="bottom_right">
            <div style="display: flex; align-items: center">
              <span class="heji">合计:</span
              ><span class="total">{{ calculatorTotal }}</span
              ><span class="unit">件</span>
            </div>
          </div>
        </div>
        <div v-else class="brush-card">
          <el-image :src="scanUrl" fit="fill"></el-image>
          <h5>请将员工卡放置于读卡器上方...</h5>
          <!-- 识别卡片按钮 -->
          <el-button
            type="primary"
            size="medium"
            :loading="loading"
            icon="el-icon-c-scale-to-original"
            @click="analyseCard"
            >{{ analyseText }}</el-button
          >
          <!-- 隐藏的input框 -->
          <input
            type="text"
            v-model="hiddenInputValue"
            ref="hiddenInput"
            style="border: none; opacity: 0; outline: none"
          />
        </div>
        <div class="mail-box" v-show="consumeList.length > 0">
          <el-checkbox-group v-model="sent">
            <el-checkbox label="发送邮件通知" name="sent"></el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="bottom-info-btn" v-show="consumeList.length > 0">
          <el-col :span="12">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="medium"
              @click="clearOut"
              >清空购物篮</el-button
            >
          </el-col>
          <el-col :span="12">
            <el-button
              type="primary"
              size="medium"
              icon="el-icon-thumb"
              @click="outStorage"
              >确认出库</el-button
            >
          </el-col>
        </div>
      </div>
    </el-row>
  </div>
</template>
<script>
// 防抖节流函数
const debounce = (fn, delay) => {
  let timer = null;
  return () => {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(fn, delay);
  };
};

export default {
  data() {
    return {
      loading: false, // 识别卡片按钮的loading
      analyseText: "请刷员工卡...",
      userInfo: [], // 用户信息
      scanUrl: "../src/assets/image/scan.png",
      keywords: "", // 搜索关键字
      sent: true, // 是否发送邮件
      hiddenInputValue: "", // 隐藏input框的值
      editableTabs: [], // 分类列表
      goodsList: [], // 备件列表
      tabIndex: 1,
      editableTabsValue: "", // 分类的值
      currentPage: 1, // 当前页
      pageSize: 12, // 每页条数
      total: 0, // 总条数
      consumeList: [], // 购物车列表
      successVoice: "../src/assets/voice/success.mp3", // 成功语音
      failVoice: "../src/assets/voice/fail.mp3", // 失败语音
      cardScanned: false, // 标记是否已经刷卡
      partsTypeList: [], // 备件类型列表
    };
  },
  // 计算属性，计算购物车中的备件总数
  computed: {
    calculatorTotal() {
      let total = 0;
      this.consumeList.forEach((item) => {
        total += item.subtotal;
      });
      return total;
    },
  },
  mounted() {
    setTimeout(() => {
      // 聚焦
      this.$refs.inputRef.focus();
      // 获取当前用户信息
      const cardInfo = localStorage.getItem("cardInfo");
      this.userInfo = cardInfo ? JSON.parse(cardInfo) : [];
    }, 100);
    // 加载
    this.showloading();
    this.getPartsTypeList();
  },
  methods: {
    // 封装播放语音的方法
    playAudio(audioFile) {
      const audio = new Audio(audioFile);
      audio.play();
    },
    // 清除刷卡的信息
    clearInfo() {
      this.$confirm("注销后如需领用配备件需再次刷卡, 是否继续?", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.consumeList = [];
          this.userInfo = [];
          // 清楚本地缓存
          localStorage.removeItem("cardInfo");
          this.$message({
            type: "success",
            message: "注销成功!",
          });
          this.analyseText = "请刷员工卡...";
          this.cardScanned = false; // 重置刷卡标记
          // 清空后自动聚焦
          // document.getElementById('inputId').focus();
          // 清空后自动聚焦
          this.$refs.inputRef.focus();
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消注销",
          });
        });
    },
    // 全局加载方法
    showloading() {
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text: "Flyknit", //显示在加载图标下方的加载文案
        spinner: "el-icon-loading", //自定义加载图标类名
        background: "rgba(255, 255, 255, 0.85)", //遮罩层颜色
        target: document.querySelector(".left-content"), //loadin覆盖的dom元素节点
      });
      //成功回调函数停止加载
      setTimeout(() => {
        loading.close();
      }, 1000);
    },
    // 出库api封装
    async outStorage() {
      // 检测购物篮中是否存在备件
      if (this.consumeList.length === 0) {
        return this.$message.warning("购物篮中没有备件!");
      }
      // 检测购物篮中是否存在备件
      if (this.userInfo.length === 0) {
        return this.$message.warning("请先刷卡!");
      }
      const loading = this.$loading({
        lock: true, //lock的修改符--默认是false
        text: "Flyknit", //显示在加载图标下方的加载文案
        spinner: "el-icon-loading", //自定义加载图标类名
        background: "rgba(255, 255, 255, 0.85)", //遮罩层颜色
        target: document.querySelector(".right-content"), //loadin覆盖的dom元素节点
      });
      try {
        const res = await this.$http.sparePartsOut({
          data: this.consumeList,
          userinfo: this.userInfo,
        });
        if (res.status !== 200) {
          loading.close();
          return this.$message.error(res.message);
        } else {
          // 清空后自动聚焦
          setTimeout(() => {
            loading.close();
            this.$notify({
              type: "success",
              message: "出库成功!",
            });
            this.consumeList = [];
            this.userInfo = [];
            // 清楚本地缓存
            localStorage.removeItem("cardInfo");
            this.analyseText = "请刷员工卡...";
            this.cardScanned = false; // 重置刷卡标记
            this.getPartsListByType();
            this.$refs.inputRef.focus();
            this.keywords = "";
          }, 1000);
        }
      } catch (error) {
        loading.close();
        return this.$message.error(error.message);
      }
    },
    // 识别卡片
    analyseCard() {
      if (this.cardScanned) {
        return this.$notify({
          type: "warning",
          message: "请不要重复刷卡!",
        });
      }
      this.$refs.hiddenInput.focus();
      const checkInputValue = setInterval(() => {
        if (this.hiddenInputValue) {
          clearInterval(checkInputValue);
          this.analyseText = "员工卡识别中...";
          this.getAnalyseCard(this.hiddenInputValue);
        }
      }, 100);
    },
    // 识别卡片接口
    async getAnalyseCard(code) {
      this.loading = true;
      try {
        const res = await this.$http.employeeCard({ cardNumber: code });
        if (res.status !== 200) {
          this.playAudio(this.failVoice);
          return this.$message.error(res.message);
        } else {
          this.playAudio(this.successVoice);
          this.analyseText = "识别成功";
          this.$notify({
            type: "success",
            message: "识别成功!",
          });
          this.loading = false;
          this.userInfo.push(res.data);
          // 获取当前年月日时分秒
          const now = new Date().toLocaleString();
          this.userInfo[0].time = now;
          // 将用户信息存入到本地
          localStorage.setItem("cardInfo", JSON.stringify(this.userInfo));
          this.hiddenInputValue = "";
          this.cardScanned = true; // 设置刷卡标记
          // 清空后取消聚焦
          this.$refs.inputRef.blur();
        }
      } catch (error) {
        this.loading = false;
        this.playAudio(this.failVoice);
        return this.$message.error(error.message);
      }
    },
    // 计数器改变, 修改购物车中的数量
    handleChange(val, depart_nuber) {
      const index = this.consumeList.findIndex(
        (item) => item.depart_nuber === depart_nuber
      );
      if (index > -1) {
        this.consumeList[index].quantity = val;
        this.consumeList[index].subtotal = val;
      }
    },
    // 扫描枪扫码
    handleInput() {
      // 监听回车
      document.onkeydown = (e) => {
        const key = window.event.keyCode;
        if (key === 13) {
          this.queryProduct(this.keywords);
          this.keywords = "";
        }
      };
    },
    // 根据keywords到this.goodsList中查询商品信息
    queryProduct(keywords) {
      const index = this.goodsList.findIndex(
        (item) => item.part_number === keywords || item.part_name === keywords
      );
      if (index > -1) {
        this.addCart(this.goodsList[index]);
      } else {
        return this.$message.warning("未找到该商品!");
      }
    },
    // 加入购物篮
    addCart(data) {
      // 判断如果没有刷卡信息, 则提示刷卡
      if (this.userInfo.length === 0) {
        return this.$message.warning("请先刷卡!");
      }
      const obj = {
        id: data.id,
        part_name: data.part_name,
        part_number: data.part_number,
        quantity: 1,
        subtotal: 1,
      };
      // 如果购物篮中已经存在该商品, 则数量加1
      const index = this.consumeList.findIndex(
        (item) => item.part_number === data.part_number
      );
      if (index > -1) {
        this.consumeList[index].quantity += 1;
        this.consumeList[index].subtotal = this.consumeList[index].quantity;
        return;
      }
      this.consumeList.push(obj);
    },
    // 删除购物篮中的商品
    delItems(data) {
      const index = this.consumeList.findIndex(
        (item) => item.part_number === data.part_number
      );
      if (index > -1) {
        this.consumeList.splice(index, 1);
      }
    },
    tabclick(tab) {
      const { index, name } = tab;
      this.tabIndex = index;
      this.editableTabsValue = name;
      this.getPartsListByType();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getPartsListByType();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getPartsListByType();
    },
    // 清空购物篮
    clearOut() {
      this.$confirm("此操作将清空购物篮中所有备件, 是否继续?", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.consumeList = [];
          this.$message({
            type: "success",
            message: "清空成功!",
          });
          // 清空后自动聚焦
          document.getElementById("inputId").focus();
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消清空",
          });
        });
    },
    // 获取备件类型列表
    async getPartsTypeList() {
      try {
        const res = await this.$http.getPartsTypeList();
        if (res.status !== 200) {
          return this.$message.error(res.message);
        } else {
          this.partsTypeList = res.data;
          this.editableTabsValue = res.data[0].second_class;
          this.getPartsListByType();
        }
      } catch (error) {
        return this.$message.error(error);
      }
    },
    // 根据分类获取备件列表
    async getPartsListByType() {
      try {
        const res = await this.$http.getPartsListByType({
          type: this.editableTabsValue,
          page: this.currentPage,
          pageSize: this.pageSize,
        });
        if (res.status !== 200) {
          return this.$message.error(res.message);
        } else {
          this.goodsList = res.data.list;
          this.total = res.data.total;
        }
      } catch (error) {
        return this.$message.error(error);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.bg-white {
  background-color: #feffff;
}
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 50px;
}

.menu-title {
  padding: 20px;
}
.goods_box {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  overflow: hidden;
}

.left-content {
  flex: 3;
  box-sizing: border-box;
  overflow: hidden;
  flex-shrink: 0;
}

.right-content {
  flex: 2;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  margin-left: 15px;
  padding: 20px;

  .search-content {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    overflow: hidden;
    background-color: #eee;

    .el-input {
      width: 100%;
      border-radius: 20px;
    }

    .el-input__inner {
      height: 50px;
      line-height: 50px;
      border: 2px solid #feffff;
    }
  }

  .table-title {
    width: 100%;
    display: flex;
    padding: 10px;
    background-color: #f8f9fa;
    margin-top: 10px;

    span {
      display: block;
      flex: 1;
      text-align: center;
      color: #000;
      font-weight: bold;
      font-size: 15px;
      letter-spacing: 3px;
    }
  }

  .menu-list-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    max-height: 400px;
    overflow-y: scroll;

    .goods_item-list {
      display: flex;
      justify-content: space-around;
      align-items: center;
      border-bottom: 1px solid #eee;
      padding: 10px 20px;
      position: relative;
      align-items: center;

      span {
        display: flex;
        flex: 1;
        font-size: 14px;
        margin-left: 5px;
      }

      i {
        color: #f56c6c;
        font-size: 24px;
        padding: 0 20px;
        cursor: pointer;
      }
    }
    .el-icon-error {
      position: relative;
      left: 10px;
    }

    .goods_item-list span:nth-child(4) {
      color: #517df7;
      font-weight: bold;
      font-size: 16px;
      position: relative;
      left: 30px;
    }

    .goods_item-list span:nth-child(1) {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .userinfo-content {
    width: 100%;
    padding: 5px 0 0 0;
    background-color: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .el-icon-user-solid {
      color: #517df7;
    }

    span {
      font-size: 14px;
      color: #333;
      margin-bottom: 5px;
      font-weight: bold;
    }

    .tuichu {
      padding: 0 10px;
      h5 {
        border: 1px solid #f56c6c;
        padding: 3px;
        font-size: 12px;
        color: #f56c6c;
        font-weight: bold;
        cursor: pointer;
      }
    }
  }

  .bottom-info {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 10px;
    border: 1px solid #eee;
    margin-top: 10px;

    .bottom-info__left {
      display: flex;

      .head_img {
        margin-right: 20px;
        box-sizing: border-box;
        overflow: hidden;
        width: 70px;
        height: 70px;
        border-radius: 50%;

        img {
          width: 100%;
          height: 100%;
        }
      }

      h5 {
        font-size: 14px;
        color: #333;
        margin-bottom: 10px;
      }
    }

    .bottom_right {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 20px 0;

      .heji {
        font-size: 16px;
        font-weight: bold;
        letter-spacing: 3px;
      }

      .total {
        margin-left: 10px;
        font-size: 22px;
        font-weight: bold;
        color: #517df7;
      }

      .unit {
        margin: 0 5px;
      }
    }
  }

  .brush-card {
    padding-top: 70px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border: 1px solid #eee;
    padding-bottom: 30px;
    h5 {
      font-size: 14px;
      color: #999;
      margin-top: 10px;
    }
    .el-image {
      width: 120px;
      height: 120px;
    }
    .el-button {
      margin-top: 40px;
    }
  }

  .mail-box {
    width: 100%;
    padding: 10px 0;
  }

  .bottom-info-btn {
    width: 100%;

    .el-button {
      width: 100%;
      letter-spacing: 3px;
      font-weight: bold;
      font-size: 16px;
      color: #feffff;
    }
  }
}

.goods_list {
  max-height: 700px;
  overflow-y: scroll;
}

.box-card {
  margin: 10px;
  cursor: pointer;
  box-sizing: border-box;
  overflow: hidden;

  .bottom {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    .text-content {
      flex: 1;
      line-height: 24px;
      .el-icon-coin {
        color: #666;
        font-size: 16px;
      }
    }

    .bottom-end {
      flex: 1;
      display: flex;
      flex-direction: column;
      flex: 1;
      margin-right: 2rem;
      position: absolute;
      right: -45px;
      top: 5px;
      .el-icon-coin {
        color: #409eff;
        font-size: 20px;
      }

      .ring {
        width: 30px;
        height: 30px;
        padding: 3px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #409eff;
        margin-left: 10px;
        margin-top: 10px;
        border: 1px solid #409eff;

        i {
          font-size: 20px;
          color: #feffff;
          font-weight: bold;
        }
      }

      .price {
        color: #50c878;
        font-weight: bold;
        font-size: 17px;
      }
    }

    .number {
      font-size: 14px;
      color: #999;
    }

    .name {
      font-size: 15px;
      color: #f72323;
      font-weight: bolder;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 130px;
      font-family: "Times New Roman", Times, serif;
    }

    .price {
      font-size: 14px;
      color: #999;
    }
  }

  .block {
    border: 1px solid #eee;
    border-radius: 7px;

    .el-image {
      flex-shrink: 0;
    }
  }
}

.el-image {
  border-radius: 5px;
}

.el-image:hover {
  scale: 1.1;
}

.box-card:hover {
  box-shadow: 0 0 10px rgba(81, 125, 247, 0.7);
  scale: 1.1;

  .name {
    color: #517df7;
    font-size: 15px;
  }
}

.el-pagination {
  margin-top: 10px;
}
</style>
