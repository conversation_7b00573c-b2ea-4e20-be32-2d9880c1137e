import{Q as y,a_ as w,aS as $,a7 as C,bC as h,i as S,X as q,c as v,a$ as O,bI as Q,_ as E,C as k,R as B,o as I,g as G,m as g,e as N,cG as z,n as e,I as P,H as b,S as _,d as D,t as F,y as T,G as U,ad as X,k as J,a9 as Y,j as Z,bW as x,a8 as ee,U as ae,ag as A}from"./index-444b28c3.js";import{U as V,C as oe,d as le}from"./event-fe80fd0c.js";import{a as se,u as ne,b as te}from"./index-4d7f16ce.js";const K=Symbol("radioGroupKey"),M=y({size:w,disabled:Boolean,label:{type:[String,Number,Boolean],default:""}}),re=y({...M,modelValue:{type:[String,Number,Boolean],default:""},name:{type:String,default:""},border:Boolean}),L={[V]:s=>$(s)||C(s)||h(s),[oe]:s=>$(s)||C(s)||h(s)},j=(s,m)=>{const n=S(),o=q(K,void 0),d=v(()=>!!o),c=v({get(){return d.value?o.modelValue:s.modelValue},set(i){d.value?o.changeEvent(i):m&&m(V,i),n.value.checked=s.modelValue===s.label}}),r=O(v(()=>o==null?void 0:o.size)),u=Q(v(()=>o==null?void 0:o.disabled)),l=S(!1),p=v(()=>u.value||d.value&&c.value!==s.label?-1:0);return{radioRef:n,isGroup:d,radioGroup:o,focus:l,size:r,disabled:u,tabIndex:p,modelValue:c}},ie=["value","name","disabled"],de={name:"ElRadio"},ue=k({...de,props:re,emits:L,setup(s,{emit:m}){const n=s,o=B("radio"),{radioRef:d,radioGroup:c,focus:r,size:u,disabled:l,modelValue:p}=j(n,m);function i(){U(()=>m("change",p.value))}return(a,t)=>{var f;return I(),G("label",{class:b([e(o).b(),e(o).is("disabled",e(l)),e(o).is("focus",e(r)),e(o).is("bordered",a.border),e(o).is("checked",e(p)===a.label),e(o).m(e(u))])},[g("span",{class:b([e(o).e("input"),e(o).is("disabled",e(l)),e(o).is("checked",e(p)===a.label)])},[N(g("input",{ref_key:"radioRef",ref:d,"onUpdate:modelValue":t[0]||(t[0]=R=>P(p)?p.value=R:null),class:b(e(o).e("original")),value:a.label,name:a.name||((f=e(c))==null?void 0:f.name),disabled:e(l),type:"radio",onFocus:t[1]||(t[1]=R=>r.value=!0),onBlur:t[2]||(t[2]=R=>r.value=!1),onChange:i},null,42,ie),[[z,e(p)]]),g("span",{class:b(e(o).e("inner"))},null,2)],2),g("span",{class:b(e(o).e("label")),onKeydown:t[3]||(t[3]=T(()=>{},["stop"]))},[_(a.$slots,"default",{},()=>[D(F(a.label),1)])],34)],2)}}});var pe=E(ue,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/radio/src/radio.vue"]]);const me=y({...M,name:{type:String,default:""}}),ce=["value","name","disabled"],be={name:"ElRadioButton"},fe=k({...be,props:me,setup(s){const m=s,n=B("radio"),{radioRef:o,focus:d,size:c,disabled:r,modelValue:u,radioGroup:l}=j(m),p=v(()=>({backgroundColor:(l==null?void 0:l.fill)||"",borderColor:(l==null?void 0:l.fill)||"",boxShadow:l!=null&&l.fill?`-1px 0 0 0 ${l.fill}`:"",color:(l==null?void 0:l.textColor)||""}));return(i,a)=>{var t;return I(),G("label",{class:b([e(n).b("button"),e(n).is("active",e(u)===i.label),e(n).is("disabled",e(r)),e(n).is("focus",e(d)),e(n).bm("button",e(c))])},[N(g("input",{ref_key:"radioRef",ref:o,"onUpdate:modelValue":a[0]||(a[0]=f=>P(u)?u.value=f:null),class:b(e(n).be("button","original-radio")),value:i.label,type:"radio",name:i.name||((t=e(l))==null?void 0:t.name),disabled:e(r),onFocus:a[1]||(a[1]=f=>d.value=!0),onBlur:a[2]||(a[2]=f=>d.value=!1)},null,42,ce),[[z,e(u)]]),g("span",{class:b(e(n).be("button","inner")),style:X(e(u)===i.label?e(p):{}),onKeydown:a[3]||(a[3]=T(()=>{},["stop"]))},[_(i.$slots,"default",{},()=>[D(F(i.label),1)])],38)],2)}}});var H=E(fe,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/radio/src/radio-button.vue"]]);const ve=y({id:{type:String,default:void 0},size:w,disabled:Boolean,modelValue:{type:[String,Number,Boolean],default:""},fill:{type:String,default:""},label:{type:String,default:void 0},textColor:{type:String,default:""},name:{type:String,default:void 0},validateEvent:{type:Boolean,default:!0}}),ge=L,ye=["id","aria-label","aria-labelledby"],Re={name:"ElRadioGroup"},Se=k({...Re,props:ve,emits:ge,setup(s,{emit:m}){const n=s,o=B("radio"),d=se(),c=S(),{formItem:r}=ne(),{inputId:u,isLabeledByFormItem:l}=te(n,{formItemContext:r}),p=a=>{m(V,a),U(()=>m("change",a))};J(()=>{const a=c.value.querySelectorAll("[type=radio]"),t=a[0];!Array.from(a).some(f=>f.checked)&&t&&(t.tabIndex=0)});const i=v(()=>n.name||d.value);return Y(K,Z({...x(n),changeEvent:p,name:i})),ee(()=>n.modelValue,()=>{n.validateEvent&&(r==null||r.validate("change").catch(a=>le()))}),(a,t)=>(I(),G("div",{id:e(u),ref_key:"radioGroupRef",ref:c,class:b(e(o).b("group")),role:"radiogroup","aria-label":e(l)?void 0:a.label||"radio-group","aria-labelledby":e(l)?e(r).labelId:void 0},[_(a.$slots,"default")],10,ye))}});var W=E(Se,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/radio/src/radio-group.vue"]]);const Ie=ae(pe,{RadioButton:H,RadioGroup:W}),Ge=A(W);A(H);export{Ie as E,Ge as a};
