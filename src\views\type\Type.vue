<!--
 * @Author: flyknit <EMAIL>
 * @Date: 2024-05-07 12:47:39
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-05-20 08:33:32
 * @FilePath: \electronic-filed:\gitee\backend-demo\src\views\type\Type.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by ${<EMAIL>}, All Rights Reserved. 
-->
<template>
  <div>
    <el-card>
      <el-row :gutter="10" style="display: flex; align-items: center">
        <el-col :span="24">
          <el-form inline :form="form" v-if="User.type == 2" size="medium">
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-plus"
                size="medium"
                @click="switchHandler"
                >添加标准名</el-button
              >
            </el-form-item>
          </el-form>
          <el-table
            :data="styleList"
            resizable
            stripe
            v-loading="loading"
            element-loading-text="Flyknit"
            style="font-size: 0.88rem"
          >
            <el-table-column label="#" type="index">
            </el-table-column>
            <el-table-column
              label="名字"
              header-align="center"
              sortable
              prop="name"
              align="center"
            ></el-table-column>
            <el-table-column
              label="类型"
              sortable
              prop="type"
              header-align="center"
              align="center"
            >
              <template slot-scope="scope">
                <el-tag v-if="scope.row.type === 0" type="success" size="mini"
                  >毛坯标准</el-tag
                >
                <el-tag
                  v-else-if="scope.row.type === 1"
                  type="warning"
                  size="mini"
                  >光坯标准</el-tag
                >
              </template>
            </el-table-column>
            <!-- 操作 -->
            <el-table-column
              label="操作"
              header-align="center"
              align="center"
              v-if="User.type == 2"
            >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="mini"
                  @click="editHandler(scope.row)"
                  >编辑</el-button
                >
                <el-button
                  type="text"
                  style="color: #eb4c42"
                  size="mini"
                  @click="deleteHandler(scope.row.id)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <div class="page" v-show="total > 0">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="currentchange"
              :current-page="queryInfo.currentnum"
              :page-sizes="pageSizes"
              :page-size="queryInfo.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            >
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <!-- 添加点位弹窗 -->
    <el-dialog :title="title" :visible.sync="dialogFormVisible" width="35%">
      <el-form :model="form" :rules="rules" ref="form" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入标准名称"></el-input>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio label="0">毛坯标准</el-radio>
            <el-radio label="1">光坯标准</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm('form')">{{
            btnText
          }}</el-button>
          <el-button @click="dialogFormVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from "vuex";
export default {
  name: "Point",
  data() {
    return {
      styleList: [], // 标准名列表
      loading: true,
      checked: false,
      queryInfo: {
        currentnum: 1,
        pageNum: 1,
        pageSize: 12,
      },
      pmList: [
        {
          id: 1,
          name: "UPPER-L",
        },
        {
          id: 2,
          name: "UPPER-R",
        },
        {
          id: 3,
          name: "HEEL",
        },
        {
          id: 4,
          name: "HEEL-L",
        },
        {
          id: 5,
          name: "HEEL-R",
        },
        {
          id: 6,
          name: "TONGUE",
        },
        {
          id: 7,
          name: "PAIR",
        },
        {
          id: 8,
          name: "UPPER",
        },
        {
          id: 9,
          name: "MED-L",
        },
        {
          id: 10,
          name: "MED-R",
        },
        {
          id: 11,
          name: "RAND-L",
        },
        {
          id: 12,
          name: "RAND-R",
        },
        {
          id: 13,
          name: "STRAP-R",
        },
        {
          id: 14,
          name: "STRAP-L",
        },
        {
          id: 15,
          name: "CUFF-L",
        },
        {
          id: 16,
          name: "CUFF-R",
        },
        {
          id: 17,
          name: "VAMP-R",
        },
        {
          id: 18,
          name: "VAMP-R",
        },
        {
          id: 19,
          name: "LAT-R",
        },
        {
          id: 20,
          name: "LAT-L",
        },
      ], // 品名列表
      total: 0,
      pageSizes: [12, 16, 18, 20],
      dialogFormVisible: false,
      form: {
        name: "",
        type: "0",
      },
      rules: {
        name: [
          { required: true, message: "请输入标准名", trigger: "blur" },
        ],
        type: [{ required: true, message: "请选择类型", trigger: "blur" }],
      },
      btnText: "添加",
      title: "添加标准名",
    };
  },
  computed: {
    ...mapState({
      User: (state) => state.users,
    }),
  },
  created() {
    this.getStyleList();
  },
  methods: {
    // 删除点位
    async deleteHandler(id) {
      this.$confirm("此操作将永久删除该点位, 是否继续?", "系统提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.deleteStyleApi(id);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 全局删除点位接口
    async deleteStyleApi(id) {
      try {
        const res = await this.$http.deleteStandardName({ id });
        if (res.code !== 200) {
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        this.$notify({
          title: "成功",
          message: res.message,
          type: "success",
        });
        this.getStyleList();
      } catch (error) {
        return this.$notify({
          title: "系统提示",
          message: "删除点位失败",
          type: "error",
        });
      }
    },
    // 编辑点位
    editHandler(row) {
      this.title = "编辑点位";
      this.btnText = "编辑";
      this.form.name = row.name;
      this.form.type = String(row.type);
      this.form.id = row.id;
      this.dialogFormVisible = true;
    },
    // 添加点位弹窗开关
    switchHandler() {
      this.dialogFormVisible = true;
      this.form = {
        name: "",
        type: "0",
      };
      this.btnText = "添加";
      this.title = "添加标准名";
    },
    // 添加标准名
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.submitFormApi();
        } else {
          return false;
        }
      });
    },
    // 封装提交表单接口
    async submitFormApi() {
      console.log("form", this.form);
      try {
        const res = await this.$http.addStandardName(this.form);
        if (res.code !== 200) {
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        this.$notify({
          title: "成功",
          message: res.message,
          type: "success",
        });
        this.dialogFormVisible = false;
        this.form = {
          name: "",
          type: "0",
        };
        this.getStyleList();
      } catch (error) {
        return this.$notify({
          title: "系统提示",
          message: error.message,
          type: "error",
        });
      }
    },
    // 获取标准名列表
    async getStyleList() {
      try {
        const res = await this.$http.getStandardNameList(this.queryInfo);
        if (res.code !== 200) {
          this.loading = false;
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        const { result, total } = res.data.list;
        this.styleList = result;
        this.total = total;
        setTimeout(() => {
          this.loading = false;
        }, 500);
      } catch (error) {
        this.loading = false;
        return this.$notify({
          title: "系统提示",
          message: error.message,
          type: "error",
        });
      }
    },
    handleSizeChange(val) {
      this.loading = true;
      this.queryInfo.pageSize = val;
      this.getStyleList();
    },
    currentchange(val) {
      this.loading = true;
      this.queryInfo.pageNum = val;
      this.getStyleList();
    },
  },
};
</script>
<style scoped>
.page {
  display: flex;
  justify-content: flex-start;
  margin-top: 20px;
}
</style>