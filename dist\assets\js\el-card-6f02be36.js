import{Q as l,aw as n,_ as p,C as c,R as i,o,g as t,H as r,n as s,S as d,d as u,t as m,f as y,m as f,ad as h,U as v}from"./index-444b28c3.js";const S=l({header:{type:String,default:""},bodyStyle:{type:n([String,Object,Array]),default:""},shadow:{type:String,values:["always","hover","never"],default:"always"}}),w={name:"ElCard"},C=c({...w,props:S,setup(b){const a=i("card");return(e,g)=>(o(),t("div",{class:r([s(a).b(),s(a).is(`${e.shadow}-shadow`)])},[e.$slots.header||e.header?(o(),t("div",{key:0,class:r(s(a).e("header"))},[d(e.$slots,"header",{},()=>[u(m(e.header),1)])],2)):y("v-if",!0),f("div",{class:r(s(a).e("body")),style:h(e.bodyStyle)},[d(e.$slots,"default")],6)],2))}});var _=p(C,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/card/src/card.vue"]]);const E=v(_);export{E};
