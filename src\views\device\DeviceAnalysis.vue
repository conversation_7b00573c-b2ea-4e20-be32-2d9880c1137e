<template>
    <div>
        <!-- 设备分析，配件更换次数，数量，维修次数，刷油次数，扣分次数，原因，停机天数，运行天数 -->
        <el-card>
            <!-- 表单，设备编号，区域，部门，搜索按钮，重置按钮，刷新按钮  -->
            <el-form :inline="true" :model="form" class="demo-form-inline">
                <el-form-item>
                    <el-input v-model="form.deviceId" placeholder="请输入设备编号或设备名称"></el-input>
                </el-form-item>
                <el-form-item label="区域">
                    <el-select v-model="form.region" placeholder="请选择区域">
                        <el-option label="区域一" value="shanghai"></el-option>
                        <el-option label="区域二" value="beijing"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="部门">
                    <el-select placeholder="请选择部门" v-model="form.department" clearable>
                        <el-option label="IT部门" value="IT部门"></el-option>
                        <el-option label="织造车间" value="织造车间"></el-option>
                        <el-option label="整理车间" value="整理车间"></el-option>
                        <el-option label="检验车间" value="检验车间"></el-option>
                        <el-option label="打样车间" value="打样车间"></el-option>
                        <el-option label="综合维修" value="综合维修"></el-option>
                        <el-option label="化验室" value="化验室"></el-option>
                        <el-option label="行政办公室" value="行政办公室"></el-option>
                        <el-option label="食堂" value="食堂"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSubmit" icon="el-icon-search">搜索</el-button>
                    <el-button @click="onReset" icon="el-icon-refresh">重置</el-button>
                    <el-button @click="onRefresh" type="primary" plain icon="el-icon-refresh-right"></el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-row :gutter="20" class="analyse-content">
            <el-col :span="6" v-for="item in 12" :key="item">
                <div class="block-layout">
                    <div class="left-block">
                        <div class="status-box">
                            <span class="status-label">运行状态</span>
                            <i class="el-icon-loading"></i>
                        </div>
                        <div class="left-block-bottom">
                            <div class="image-container">
                                <img :src="url" alt="Image" class="rounded-image">
                            </div>
                            <div class="device-number">
                                设备编号：F0001
                            </div>
                        </div>
                    </div>
                    <div class="right-block">
                        <div class="stats-item">
                            <div class="stats-label">维修次数：</div>
                            <div class="stats-value">2</div>
                        </div>
                        <div class="stats-item">
                            <div class="stats-label">维修金额：</div>
                            <div class="stats-value">1520.00</div>
                        </div>
                        <div class="stats-item">
                            <div class="stats-label">配件更换次数：</div>
                            <div class="stats-value">5</div>
                        </div>
                        <div class="stats-item">
                            <div class="stats-label">刷油次数：</div>
                            <div class="stats-value">3</div>
                        </div>
                        <div class="stats-item">
                            <div class="stats-label">扣分次数：</div>
                            <div class="stats-value">2 <i class="el-icon-question"></i> </div>
                        </div>
                        <div class="stats-item">
                            <div class="stats-label">停机天数：</div>
                            <div class="stats-value">8</div>
                        </div>
                        <div class="stats-item">
                            <div class="stats-label">运行天数：</div>
                            <div class="stats-value">100</div>
                        </div>
                    </div>
                </div>
            </el-col>
        </el-row>
        <!-- 分页 -->
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
            :page-sizes="[12, 14, 16, 18]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
            :total="total">
        </el-pagination>
    </div>
</template>
<script>
export default {
    data() {
        return {
            form: {
                deviceId: '',
                region: '',
                department: ''
            },
            currentPage: 1,
            pageSize: 12,
            total: 400,
            url: '../src/assets/image/stoll.jpg',
        }
    },
    methods: {
        onSubmit() {
            console.log('submit!');
        },
        onReset() {
            console.log('reset!');
        },
        onRefresh() {
            console.log('refresh!');
        },
        handleSizeChange(val) {
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
        }
    }
}
</script>
<style lang="scss">
.el-card__body {
    padding: 20px 20px 0 20px;
}

.analyse-content {
    padding: 20px 0;

    .el-col {
        margin-bottom: 20px;
    }
}

.block-layout {
    display: flex;
    justify-content: space-between;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background-color: #fff;
    padding: 20px 10px;
    border-radius: 4px;
    transition: box-shadow 0.3s;
    cursor: pointer;
}

.block-layout:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.left-block {
    display: flex;
    flex-direction: column;
    padding-left: 10px;
    .status-box {
        display: flex;
        align-items: left;
        margin-bottom: 10px;

        .el-icon-loading {
            font-size: 20px;
            color: #34C823;
            margin-left: 10px;
        }

        .status-label {
            display: block;
            width: 50px;
            height: 20px;
            padding: 3px 5px;
            background-color: #34C823;
            color: #feffff;
            text-align: center;
            border-radius: 3px;
            font-size: 12px;
        }
    }

    .left-block-bottom {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
}

.image-container {
    width: 140px;
    height: 120px;
    overflow: hidden;
}

.rounded-image {
    width: 100%;
    height: auto;
}

.device-number {
    margin-top: 10px;
    font-weight: 800;
    color: #333;
    font-size: 16px;
}

.right-block {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    overflow: hidden;
    flex-shrink: 0;
    padding: 0 20px;
}

.stats-item {
    display: flex;
    align-items: center;
    margin-bottom: 18px;
}

.stats-label {
    font-weight: bold;
    font-size: 14px;
    color: #999;
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.stats-value {
    margin-left: 5px;
    font-size: 13px;
    color: #409EFF;
    font-weight: bold;

    .el-icon-question {
        margin-left: 5px;
        font-size: 14px;
        color: #999;
    }
}
.demo-form-inline {
    display: flex;
    flex-wrap: wrap;
}
</style>