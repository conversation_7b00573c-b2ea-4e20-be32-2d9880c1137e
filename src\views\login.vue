<!--
 * @Author: flyknit <EMAIL>
 * @Date: 2024-04-09 09:02:42
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-05-18 17:00:46
 * @FilePath: \electronic-filed:\gitee\backend-demo\src\views\login.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by ${<EMAIL>}, All Rights Reserved. 
-->
<template>
  <el-row class="login-container">
    <el-col class="login-forms">
      <el-col
        :lg="24"
        style="display: flex; align-items: center; padding: 20px 0"
      >
        <el-col :lg="14" class="left-carousel">
          <el-carousel indicator-position="outside">
            <el-carousel-item v-for="(item, index) in imgList" :key="index">
              <el-image :src="item.url" alt="加载失败" fit="fill">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
              <!-- <div class="carousel-text">
                <h4>{{ item.text }}</h4>
              </div> -->
            </el-carousel-item>
          </el-carousel>
        </el-col>
        <el-col :lg="10" class="right">
          <el-col class="login-form">
            <el-col
              :span="20"
              style="
                display: flex;
                align-items: center;
                justify-content: center;
                text-align: center;
              "
            >
              <!-- <el-col :span="4">
                <img
                  style="
                    width: 40px;
                    height: 40px;
                    border-radius: 100%;
                    margin: 0.5rem 0.5rem 0 0.5rem;
                  "
                  :src="circleUrl"
                  fit="fill"
                />
              </el-col> -->
              <el-col :span="24">
                <h2 style="margin: 0px 0;color: black;font-size: 1.5rem;">打样巡检管理系统</h2>
              </el-col>
            </el-col>
            <el-form
              ref="form"
              :model="form"
              class="w-[260px]"
              style="margin-top: 20px"
              :rules="rules"
            >
              <el-form-item prop="username">
                <el-input
                  clearable
                  v-model="form.username"
                  placeholder="请输入用户名"
                >
                  <template #prefix>
                    <i class="el-icon-user-solid"></i>
                  </template>
                </el-input>
                <span v-show="form.username&&form.username.length >=5" class="el-icon-success"></span>
              </el-form-item>
              <el-form-item prop="password">
                <el-input
                  type="password"
                  v-model="form.password"
                  placeholder="请输入密码"
                  show-password
                  clearable
                  @keyup="submitInfo"
                >
                  <template #prefix>
                    <i class="el-icon-lock"></i>
                  </template>
                </el-input>
                <span v-show="form.password&&form.password.length>=8" class="el-icon-success"></span>
              </el-form-item>
              <div style="display: flex; justify-content: space-between;position: relative;top: -6px;padding: 0 0.75rem;">
                <el-link type="info">注册账号</el-link>
                <el-link type="info">忘记密码</el-link>
              </div>
              <el-form-item>
                <el-button
                  round
                  type="primary"
                  :loading="loading"
                  size="medium"
                  v-on:keyup.13.native="submitInfo('form')"
                  @click="submitInfo('form')"
                  >登   录</el-button
                >
              </el-form-item>
            </el-form>
          </el-col>
        </el-col>
      </el-col>
    </el-col>
    <el-col
      :span="24"
      style="
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        bottom: 10px;
      "
    >
      <h3 style="color: #909399; font-size: 0.9rem">
        2024 © Flyknit Center Vietnam All Rights Reserved.
      </h3>
    </el-col>
  </el-row>
</template>

<script>
export default {
  data() {
    return {
      form: {
        phone: "",
        password: "",
      },
      lg: 12,
      loading: false, // 按钮加载
      circleUrl: "/src/assets/image/logo.png",
      fits: "fill",
      imgList: [
        {
          url: "/src/assets/image/login.png",
          text: "高效管理",
        },
      ],
      rules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
          {
            min: 2,
            max: 6,
            message: "用户名长度应为2-6个字符",
            trigger: "blur",
          },
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          {
            min: 6,
            max: 20,
            message: "长度在 6 到 20 个字符",
            trigger: "blur",
          },
        ],
      },
    };
  },
  mounted() {
    // 监听键盘的点击事件
    window.addEventListener("keydown", this.keyDown);
  },
  destroyed() {
    window.removeEventListener("keydown", this.keyDown, false);
  },
  methods: {
    //绑定监听事件
    keyDown(e) {
      //如果是按回车则执行登录方法
      if (e.keyCode == 13) {
        this.submitInfo("form");
      }
    },
    // 登录
    submitInfo(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.loginHanlder();
        } else {
          return false;
        }
      });
    },
    // 登录逻辑
    async loginHanlder() {
      this.loading = true;
      const { getGreeting } = this;
      await this.$http
        .userLogin(this.form)
        .then((res) => {
          if (res.data) {
            this.$notify({
              title: "登录成功",
              message: `${getGreeting()}，欢迎回来，${this.form.username}`,
              type: "success",
            });
            const { list } = res.data;
            console.log('list', list);
            // 存储用户信息
            this.$store.commit("setUserInfo", list);
            this.$router.push("/");
          }
        })
        .catch((err) => {
          this.loading = false;
        })
        .finally(() => {
          this.loading = false;
          // 清空输入框的值
          setTimeout(() => {
            this.form.password = "";
          }, 1000);
        });
    },
    // 根据当前时间判断是早上还是中午还是晚上,返回不同的问候语
    getGreeting() {
      const hour = new Date().getHours();
      if (hour < 6) {
        return "凌晨好";
      } else if (hour < 9) {
        return "早上好";
      } else if (hour < 12) {
        return "上午好";
      } else if (hour < 14) {
        return "中午好";
      } else if (hour < 17) {
        return "下午好";
      } else if (hour < 19) {
        return "傍晚好";
      } else if (hour < 22) {
        return "晚上好";
      } else {
        return "夜里好";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.el-form-item {
  position: relative;
  .el-icon-success {
    position: absolute;
    right: -1.2rem;
    top: 0.8rem;
    color: #409eff;
  }
}
.el-image {
  width: 260px;
  height: 260px;
}
.left-carousel {
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  overflow: hidden;
  flex: 1;
  padding: 0;
}
.el-carousel {
  width: 100%;
  height: 100%;
}
.carousel-text {
  height: 40px;
  line-height: 40px;
}
.carousel-text h4 {
  font-size: 1.2rem;
  font-weight: bold;
  color: #666;
  margin: 0;
  padding: 0;
}
.el-carousel__item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px;
}
.login-forms {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 400px;
  max-width: 680px;
  background-color: #fff;
  border-radius: 10px;
  padding: 0 20px;
  opacity: 0.8;
  box-shadow: 4px 6px 12px 8px #c3cfe7;
}
.login-forms :hover {
  opacity: 1;
  cursor: pointer;
}
.login-title {
  padding: 20px 0;
  text-align: center;
  font-size: 20px;
  letter-spacing: 2px;
  color: #1a1a1a;
  font-weight: 800;
  font-family: "Times New Roman", Times, serif;
}
.login-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f0f5f5;
  padding: 0;
  margin: 0;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("/src/assets/image/maxresdefault.jpg") no-repeat center center;
  background-size: 100% 100%;
}
.background-video {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1; /* 确保视频在其他内容下方 */
  box-sizing: border-box;
  overflow: hidden;
}

.background-video video {
  width: 100%;
  height: 100%;
  object-fit: cover; /* 视频填充整个容器 */
}
.login-container .left {
  flex: 1;
  display: flex;
  justify-content: center;
  box-sizing: border-box;
  overflow: hidden;
}
.login-container .right {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.left > div > div:first-child {
  font-size: 3rem;
  font-weight: bold;
  color: #fff;
}
.left > div > div:last-child {
  color: #fff;
  font-size: 1.5rem;
}
.right .title {
}
.right > div {
}
.right .line {
  font-size: 1rem;
}
.login-form {
  width: 360px;
  height: 360px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
}

::v-deep .el-button {
  width: 130px;
  color: #fff;
  font-weight: bold;
  font-size: 1rem;
  background-color: #409eff;
  border: none;
  margin-top: 10px;
  border-radius: 10px;
}
.el-input .el-icon-lock,
.el-icon-user-solid {
  padding-left: 6px;
}
::v-deep .el-input__inner {
  width: 100%;
  border-color: #c0c4cc;
  border-radius: 10px;
  color: #000;
}
</style>
