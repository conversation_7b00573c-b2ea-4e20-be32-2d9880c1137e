<template>
  <el-row class="login-container">
    <el-col :lg="16" :md="12" class="left">
      <div>
        <div>MaintainceMe</div>
        <div class="last-text">Flyknit设备管家后台管理系统</div>
      </div>
      <div style="width:80%;height:500px;">
        <el-carousel :interval="5000" arrow="always">
          <el-carousel-item v-for="(item, index) in svgList" :key="index">
            <el-image :src="item" fit="contain" style="width: 100%; height: 100%;" />
          </el-carousel-item>
        </el-carousel>
      </div>
    </el-col>
    <el-col :lg="8" :md="12" class="right">
      <div
        class="head_img_content">
        <img  :src="circleUrl">
      </div>
      <h2 class="title">Hi，Welcome Back</h2>
      <div style="width: 42%; margin: 15px 0">
        <el-divider><span class="line">登录</span></el-divider>
      </div>
      <el-form ref="form" :model="form" :rules="rules" class="w-[250px]">
        <el-form-item prop="username">
          <el-input clearable v-model="form.username" placeholder="请输入用户账号/邮箱">
            <template #prefix>
              <i class="el-icon-user-solid"></i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input type="password" v-model="form.password" placeholder="请输入密码" show-password clearable
            @keyup="submitInfo">
            <template #prefix>
              <i class="el-icon-lock"></i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button color="#517DF7" type="primary" :loading="loading" size="medium" class="btn"
            v-on:keyup.13.native="submitInfo" @click="submitInfo">登 录</el-button>
        </el-form-item>
      </el-form>
    </el-col>
  </el-row>
</template>

<script>
import svg1 from '@/assets/image/1.svg';
import svg2 from '@/assets/image/2.svg';
import svg3 from '@/assets/image/3.svg';
import svg5 from '@/assets/image/5.svg';
export default {
  data() {
    return {
      form: {
        username: "",
        password: "",
      },
      loading: false,
      circleUrl: "../image/app_logo.png",
      fits: "fill",
      svgList: [
        svg1,
        svg2,
        svg3,
        svg5,
      ],
      rules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
          {
            min: 3,
            max: 30,
            message: "长度在 3 到 30 个字符",
            trigger: "blur",
          },
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          {
            min: 6,
            max: 20,
            message: "长度在 6 到 20 个字符",
            trigger: "blur",
          },
        ],
      },
    };
  },
  mounted() {
    // 监听键盘的点击事件
    window.addEventListener("keydown", this.keyDown);
  },
  destroyed() {
    window.removeEventListener("keydown", this.keyDown, false);
  },
  methods: {
    //绑定监听事件
    keyDown(e) {
      //如果是按回车则执行登录方法
      if (e.keyCode == 13) {
        this.submitInfo();
      }
    },
    // 登录
    async submitInfo() {
      try {
        // 验证表单
        this.$refs['form'].validate(async (valid) => {
          if (valid) {
            const res = await this.$http.userLogin(this.form);
            if (res.status !== 200) {
              return this.$message.error(res.message);
            }
            const { list } = res.data;
            if (list) {
              this.$notify({
                title: res.message,
                message: `欢迎回来，${list.username}`,
                type: 'success'
              });
              // 存储用户信息
              this.$store.commit("setUserInfo", list);
              this.$router.push("/index");
            }
          } else {
            return false;
          }
        });
      } catch (error) {
        console.log(error);
        return false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.head_img_content {
  width:120px;height:120px;border-radius:100%;background-color:#517DF7;display:flex;justify-content:center;align-items:center;margin:20px 0;
  padding: 10px;
}
.login-container {
  display: flex;
  min-height: 100vh;
}

.login-container .left {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #517DF7;
  justify-content: center;
  align-items: center;

  .el-carousel {
    height: 100%;
    box-sizing: border-box;

    .el-carousel__item {
      width: 100%;
      height: 440px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
  }
}

.login-container .right {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.left>div>div:first-child {
  font-size: 3rem;
  font-weight: bold;
  color: #fff;
}

.left>div>div:last-child {
  color: #fff;
  font-size: 1.5rem;
  padding-bottom: 20px;

}

.last-text {
  text-indent: 30px;
  text-shadow: 0 0 10px #fff;
  letter-spacing: 3px;
}

.right .title {}

.right>div {}

.right .line {
  font-size: 1rem;
}

.btn {
  flex: 1;
  width: 100%;
  background-color: #517DF7;
}

.el-input .el-icon-lock,
.el-icon-user-solid {
  padding-left: 6px;
}
</style>
