import{av as _e,_ as O,C as W,R as z,ab as ee,aD as le,i as b,a8 as _,k as Y,o as w,g as S,m as $,H as m,ad as F,c as T,X as Ee,c8 as ae,by as Se,F as Ne,h as Ae,a5 as Le,af as Te,be as Fe,j as Ve,a$ as Ie,G as j,a9 as Pe,B as y,r as He,a as X,w as V,e as se,b as E,f as q,a2 as re,d as ie,t as ue,a3 as Be}from"./index-444b28c3.js";import{E as De}from"./el-button-9bbdfcf9.js";import{E as Oe,C as We}from"./el-scrollbar-af6196f4.js";import{E as ze}from"./el-input-6b488ec7.js";import{g as te}from"./position-f84d51c4.js";import{i as Re}from"./validator-e4131fc3.js";import{U as J,d as ce}from"./event-fe80fd0c.js";import{u as je,d as Xe}from"./index-e305bb62.js";import{u as qe,b as Ue}from"./index-4d7f16ce.js";let Q=!1;function D(e,n){if(!_e)return;const o=function(l){var a;(a=n.drag)==null||a.call(n,l)},t=function(l){var a;document.removeEventListener("mousemove",o),document.removeEventListener("mouseup",t),document.removeEventListener("touchmove",o),document.removeEventListener("touchend",t),document.onselectstart=null,document.ondragstart=null,Q=!1,(a=n.end)==null||a.call(n,l)},s=function(l){var a;Q||(l.preventDefault(),document.onselectstart=()=>!1,document.ondragstart=()=>!1,document.addEventListener("mousemove",o),document.addEventListener("mouseup",t),document.addEventListener("touchmove",o),document.addEventListener("touchend",t),Q=!0,(a=n.start)==null||a.call(n,l))};e.addEventListener("mousedown",s),e.addEventListener("touchstart",s)}const Ke=W({name:"ElColorAlphaSlider",props:{color:{type:Object,required:!0},vertical:{type:Boolean,default:!1}},setup(e){const n=z("color-alpha-slider"),o=ee(),t=le(null),s=le(null),l=b(0),a=b(0),i=b(null);_(()=>e.color.get("alpha"),()=>{u()}),_(()=>e.color.value,()=>{u()});function d(){if(e.vertical)return 0;const r=o.vnode.el,c=e.color.get("alpha");return r?Math.round(c*(r.offsetWidth-t.value.offsetWidth/2)/100):0}function h(){const r=o.vnode.el;if(!e.vertical)return 0;const c=e.color.get("alpha");return r?Math.round(c*(r.offsetHeight-t.value.offsetHeight/2)/100):0}function C(){if(e.color&&e.color.value){const{r,g:c,b:g}=e.color.toRgb();return`linear-gradient(to right, rgba(${r}, ${c}, ${g}, 0) 0%, rgba(${r}, ${c}, ${g}, 1) 100%)`}return null}function p(r){r.target!==t.value&&k(r)}function k(r){const g=o.vnode.el.getBoundingClientRect(),{clientX:M,clientY:N}=te(r);if(e.vertical){let f=N-g.top;f=Math.max(t.value.offsetHeight/2,f),f=Math.min(f,g.height-t.value.offsetHeight/2),e.color.set("alpha",Math.round((f-t.value.offsetHeight/2)/(g.height-t.value.offsetHeight)*100))}else{let f=M-g.left;f=Math.max(t.value.offsetWidth/2,f),f=Math.min(f,g.width-t.value.offsetWidth/2),e.color.set("alpha",Math.round((f-t.value.offsetWidth/2)/(g.width-t.value.offsetWidth)*100))}}function u(){l.value=d(),a.value=h(),i.value=C()}return Y(()=>{const r={drag:c=>{k(c)},end:c=>{k(c)}};D(s.value,r),D(t.value,r),u()}),{thumb:t,bar:s,thumbLeft:l,thumbTop:a,background:i,handleClick:p,update:u,ns:n}}});function Ye(e,n,o,t,s,l){return w(),S("div",{class:m([e.ns.b(),e.ns.is("vertical",e.vertical)])},[$("div",{ref:"bar",class:m(e.ns.e("bar")),style:F({background:e.background}),onClick:n[0]||(n[0]=(...a)=>e.handleClick&&e.handleClick(...a))},null,6),$("div",{ref:"thumb",class:m(e.ns.e("thumb")),style:F({left:e.thumbLeft+"px",top:e.thumbTop+"px"})},null,6)],2)}var Ge=O(Ke,[["render",Ye],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/color-picker/src/components/alpha-slider.vue"]]);const Ze=W({name:"ElColorHueSlider",props:{color:{type:Object,required:!0},vertical:Boolean},setup(e){const n=z("color-hue-slider"),o=ee(),t=b(null),s=b(null),l=b(0),a=b(0),i=T(()=>e.color.get("hue"));_(()=>i.value,()=>{k()});function d(u){u.target!==t.value&&h(u)}function h(u){const c=o.vnode.el.getBoundingClientRect(),{clientX:g,clientY:M}=te(u);let N;if(e.vertical){let f=M-c.top;f=Math.min(f,c.height-t.value.offsetHeight/2),f=Math.max(t.value.offsetHeight/2,f),N=Math.round((f-t.value.offsetHeight/2)/(c.height-t.value.offsetHeight)*360)}else{let f=g-c.left;f=Math.min(f,c.width-t.value.offsetWidth/2),f=Math.max(t.value.offsetWidth/2,f),N=Math.round((f-t.value.offsetWidth/2)/(c.width-t.value.offsetWidth)*360)}e.color.set("hue",N)}function C(){const u=o.vnode.el;if(e.vertical)return 0;const r=e.color.get("hue");return u?Math.round(r*(u.offsetWidth-t.value.offsetWidth/2)/360):0}function p(){const u=o.vnode.el;if(!e.vertical)return 0;const r=e.color.get("hue");return u?Math.round(r*(u.offsetHeight-t.value.offsetHeight/2)/360):0}function k(){l.value=C(),a.value=p()}return Y(()=>{const u={drag:r=>{h(r)},end:r=>{h(r)}};D(s.value,u),D(t.value,u),k()}),{bar:s,thumb:t,thumbLeft:l,thumbTop:a,hueValue:i,handleClick:d,update:k,ns:n}}});function Je(e,n,o,t,s,l){return w(),S("div",{class:m([e.ns.b(),e.ns.is("vertical",e.vertical)])},[$("div",{ref:"bar",class:m(e.ns.e("bar")),onClick:n[0]||(n[0]=(...a)=>e.handleClick&&e.handleClick(...a))},null,2),$("div",{ref:"thumb",class:m(e.ns.e("thumb")),style:F({left:e.thumbLeft+"px",top:e.thumbTop+"px"})},null,6)],2)}var Qe=O(Ze,[["render",Je],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/color-picker/src/components/hue-slider.vue"]]);const ve=Symbol(),xe=()=>Ee(ve),he=function(e,n,o){return[e,n*o/((e=(2-n)*o)<1?e:2-e)||0,e/2]},et=function(e){return typeof e=="string"&&e.includes(".")&&Number.parseFloat(e)===1},tt=function(e){return typeof e=="string"&&e.includes("%")},I=function(e,n){et(e)&&(e="100%");const o=tt(e);return e=Math.min(n,Math.max(0,Number.parseFloat(`${e}`))),o&&(e=Number.parseInt(`${e*n}`,10)/100),Math.abs(e-n)<1e-6?1:e%n/Number.parseFloat(n)},de={10:"A",11:"B",12:"C",13:"D",14:"E",15:"F"},U=function(e){e=Math.min(Math.round(e),255);const n=Math.floor(e/16),o=e%16;return`${de[n]||n}${de[o]||o}`},fe=function({r:e,g:n,b:o}){return Number.isNaN(+e)||Number.isNaN(+n)||Number.isNaN(+o)?"":`#${U(e)}${U(n)}${U(o)}`},x={A:10,B:11,C:12,D:13,E:14,F:15},L=function(e){return e.length===2?(x[e[0].toUpperCase()]||+e[0])*16+(x[e[1].toUpperCase()]||+e[1]):x[e[1].toUpperCase()]||+e[1]},nt=function(e,n,o){n=n/100,o=o/100;let t=n;const s=Math.max(o,.01);o*=2,n*=o<=1?o:2-o,t*=s<=1?s:2-s;const l=(o+n)/2,a=o===0?2*t/(s+t):2*n/(o+n);return{h:e,s:a*100,v:l*100}},me=function(e,n,o){e=I(e,255),n=I(n,255),o=I(o,255);const t=Math.max(e,n,o),s=Math.min(e,n,o);let l;const a=t,i=t-s,d=t===0?0:i/t;if(t===s)l=0;else{switch(t){case e:{l=(n-o)/i+(n<o?6:0);break}case n:{l=(o-e)/i+2;break}case o:{l=(e-n)/i+4;break}}l/=6}return{h:l*360,s:d*100,v:a*100}},H=function(e,n,o){e=I(e,360)*6,n=I(n,100),o=I(o,100);const t=Math.floor(e),s=e-t,l=o*(1-n),a=o*(1-s*n),i=o*(1-(1-s)*n),d=t%6,h=[o,a,l,l,i,o][d],C=[i,o,o,a,l,l][d],p=[l,l,i,o,o,a][d];return{r:Math.round(h*255),g:Math.round(C*255),b:Math.round(p*255)}};class B{constructor(n){this._hue=0,this._saturation=100,this._value=100,this._alpha=100,this.enableAlpha=!1,this.format="hex",this.value="",n=n||{};for(const o in n)ae(n,o)&&(this[o]=n[o]);n.value?this.fromString(n.value):this.doOnChange()}set(n,o){if(arguments.length===1&&typeof n=="object"){for(const t in n)ae(n,t)&&this.set(t,n[t]);return}this[`_${n}`]=o,this.doOnChange()}get(n){return n==="alpha"?Math.floor(this[`_${n}`]):this[`_${n}`]}toRgb(){return H(this._hue,this._saturation,this._value)}fromString(n){if(!n){this._hue=0,this._saturation=100,this._value=100,this.doOnChange();return}const o=(t,s,l)=>{this._hue=Math.max(0,Math.min(360,t)),this._saturation=Math.max(0,Math.min(100,s)),this._value=Math.max(0,Math.min(100,l)),this.doOnChange()};if(n.includes("hsl")){const t=n.replace(/hsla|hsl|\(|\)/gm,"").split(/\s|,/g).filter(s=>s!=="").map((s,l)=>l>2?Number.parseFloat(s):Number.parseInt(s,10));if(t.length===4?this._alpha=Number.parseFloat(t[3])*100:t.length===3&&(this._alpha=100),t.length>=3){const{h:s,s:l,v:a}=nt(t[0],t[1],t[2]);o(s,l,a)}}else if(n.includes("hsv")){const t=n.replace(/hsva|hsv|\(|\)/gm,"").split(/\s|,/g).filter(s=>s!=="").map((s,l)=>l>2?Number.parseFloat(s):Number.parseInt(s,10));t.length===4?this._alpha=Number.parseFloat(t[3])*100:t.length===3&&(this._alpha=100),t.length>=3&&o(t[0],t[1],t[2])}else if(n.includes("rgb")){const t=n.replace(/rgba|rgb|\(|\)/gm,"").split(/\s|,/g).filter(s=>s!=="").map((s,l)=>l>2?Number.parseFloat(s):Number.parseInt(s,10));if(t.length===4?this._alpha=Number.parseFloat(t[3])*100:t.length===3&&(this._alpha=100),t.length>=3){const{h:s,s:l,v:a}=me(t[0],t[1],t[2]);o(s,l,a)}}else if(n.includes("#")){const t=n.replace("#","").trim();if(!/^[0-9a-fA-F]{3}$|^[0-9a-fA-F]{6}$|^[0-9a-fA-F]{8}$/.test(t))return;let s,l,a;t.length===3?(s=L(t[0]+t[0]),l=L(t[1]+t[1]),a=L(t[2]+t[2])):(t.length===6||t.length===8)&&(s=L(t.slice(0,2)),l=L(t.slice(2,4)),a=L(t.slice(4,6))),t.length===8?this._alpha=L(t.slice(6))/255*100:(t.length===3||t.length===6)&&(this._alpha=100);const{h:i,s:d,v:h}=me(s,l,a);o(i,d,h)}}compare(n){return Math.abs(n._hue-this._hue)<2&&Math.abs(n._saturation-this._saturation)<1&&Math.abs(n._value-this._value)<1&&Math.abs(n._alpha-this._alpha)<1}doOnChange(){const{_hue:n,_saturation:o,_value:t,_alpha:s,format:l}=this;if(this.enableAlpha)switch(l){case"hsl":{const a=he(n,o/100,t/100);this.value=`hsla(${n}, ${Math.round(a[1]*100)}%, ${Math.round(a[2]*100)}%, ${this.get("alpha")/100})`;break}case"hsv":{this.value=`hsva(${n}, ${Math.round(o)}%, ${Math.round(t)}%, ${this.get("alpha")/100})`;break}case"hex":{this.value=`${fe(H(n,o,t))}${U(s*255/100)}`;break}default:{const{r:a,g:i,b:d}=H(n,o,t);this.value=`rgba(${a}, ${i}, ${d}, ${this.get("alpha")/100})`}}else switch(l){case"hsl":{const a=he(n,o/100,t/100);this.value=`hsl(${n}, ${Math.round(a[1]*100)}%, ${Math.round(a[2]*100)}%)`;break}case"hsv":{this.value=`hsv(${n}, ${Math.round(o)}%, ${Math.round(t)}%)`;break}case"rgb":{const{r:a,g:i,b:d}=H(n,o,t);this.value=`rgb(${a}, ${i}, ${d})`;break}default:this.value=fe(H(n,o,t))}}}const ot=W({props:{colors:{type:Array,required:!0},color:{type:Object,required:!0}},setup(e){const n=z("color-predefine"),{currentColor:o}=xe(),t=b(l(e.colors,e.color));_(()=>o.value,a=>{const i=new B;i.fromString(a),t.value.forEach(d=>{d.selected=i.compare(d)})}),Se(()=>{t.value=l(e.colors,e.color)});function s(a){e.color.fromString(e.colors[a])}function l(a,i){return a.map(d=>{const h=new B;return h.enableAlpha=!0,h.format="rgba",h.fromString(d),h.selected=h.value===i.value,h})}return{rgbaColors:t,handleSelect:s,ns:n}}}),lt=["onClick"];function at(e,n,o,t,s,l){return w(),S("div",{class:m(e.ns.b())},[$("div",{class:m(e.ns.e("colors"))},[(w(!0),S(Ne,null,Ae(e.rgbaColors,(a,i)=>(w(),S("div",{key:e.colors[i],class:m([e.ns.e("color-selector"),e.ns.is("alpha",a._alpha<100),{selected:a.selected}]),onClick:d=>e.handleSelect(i)},[$("div",{style:F({backgroundColor:a.value})},null,4)],10,lt))),128))],2)],2)}var st=O(ot,[["render",at],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/color-picker/src/components/predefine.vue"]]);const rt=W({name:"ElSlPanel",props:{color:{type:Object,required:!0}},setup(e){const n=z("color-svpanel"),o=ee(),t=b(0),s=b(0),l=b("hsl(0, 100%, 50%)"),a=T(()=>{const h=e.color.get("hue"),C=e.color.get("value");return{hue:h,value:C}});function i(){const h=e.color.get("saturation"),C=e.color.get("value"),p=o.vnode.el,{clientWidth:k,clientHeight:u}=p;s.value=h*k/100,t.value=(100-C)*u/100,l.value=`hsl(${e.color.get("hue")}, 100%, 50%)`}function d(h){const p=o.vnode.el.getBoundingClientRect(),{clientX:k,clientY:u}=te(h);let r=k-p.left,c=u-p.top;r=Math.max(0,r),r=Math.min(r,p.width),c=Math.max(0,c),c=Math.min(c,p.height),s.value=r,t.value=c,e.color.set({saturation:r/p.width*100,value:100-c/p.height*100})}return _(()=>a.value,()=>{i()}),Y(()=>{D(o.vnode.el,{drag:h=>{d(h)},end:h=>{d(h)}}),i()}),{cursorTop:t,cursorLeft:s,background:l,colorValue:a,handleDrag:d,update:i,ns:n}}}),it=$("div",null,null,-1),ut=[it];function ct(e,n,o,t,s,l){return w(),S("div",{class:m(e.ns.b()),style:F({backgroundColor:e.background})},[$("div",{class:m(e.ns.e("white"))},null,2),$("div",{class:m(e.ns.e("black"))},null,2),$("div",{class:m(e.ns.e("cursor")),style:F({top:e.cursorTop+"px",left:e.cursorLeft+"px"})},ut,6)],6)}var ht=O(rt,[["render",ct],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/color-picker/src/components/sv-panel.vue"]]);const dt=W({name:"ElColorPicker",components:{ElButton:De,ElTooltip:Oe,ElInput:ze,ElIcon:Le,Close:Te,ArrowDown:Fe,SvPanel:ht,HueSlider:Qe,AlphaSlider:Ge,Predefine:st},directives:{ClickOutside:We},props:{modelValue:String,id:String,showAlpha:Boolean,colorFormat:String,disabled:Boolean,size:{type:String,validator:Re},popperClass:String,label:{type:String,default:void 0},tabindex:{type:[String,Number],default:0},predefine:Array,validateEvent:{type:Boolean,default:!0}},emits:["change","active-change",J],setup(e,{emit:n}){const{t:o}=je(),t=z("color"),{form:s,formItem:l}=qe(),{inputId:a,isLabeledByFormItem:i}=Ue(e,{formItemContext:l}),d=b(),h=b(),C=b(),p=b(null);let k=!0;const u=Ve(new B({enableAlpha:e.showAlpha,format:e.colorFormat||"",value:e.modelValue})),r=b(!1),c=b(!1),g=b(""),M=T(()=>!e.modelValue&&!c.value?"transparent":be(u,e.showAlpha)),N=Ie(),f=T(()=>!!(e.disabled||s!=null&&s.disabled)),G=T(()=>!e.modelValue&&!c.value?"":u.value),pe=T(()=>i.value?void 0:e.label||o("el.colorpicker.defaultLabel")),ge=T(()=>i.value?l==null?void 0:l.labelId:void 0);_(()=>e.modelValue,v=>{v?v&&v!==u.value&&(k=!1,u.fromString(v)):c.value=!1}),_(()=>G.value,v=>{g.value=v,k&&n("active-change",v),k=!0}),_(()=>u.value,()=>{!e.modelValue&&!c.value&&(c.value=!0)});function be(v,A){if(!(v instanceof B))throw new TypeError("color should be instance of _color Class");const{r:P,g:ne,b:oe}=v.toRgb();return A?`rgba(${P}, ${ne}, ${oe}, ${v.get("alpha")/100})`:`rgb(${P}, ${ne}, ${oe})`}function ke(v){r.value=v}const R=Xe(ke,100);function $e(){R(!1),Z()}function Z(){j(()=>{e.modelValue?u.fromString(e.modelValue):(u.value="",j(()=>{c.value=!1}))})}function Ce(){f.value||R(!r.value)}function we(){u.fromString(g.value)}function Me(){const v=u.value;n(J,v),n("change",v),e.validateEvent&&(l==null||l.validate("change").catch(A=>ce())),R(!1),j(()=>{const A=new B({enableAlpha:e.showAlpha,format:e.colorFormat||"",value:e.modelValue});u.compare(A)||Z()})}function ye(){R(!1),n(J,null),n("change",null),e.modelValue!==null&&e.validateEvent&&(l==null||l.validate("change").catch(v=>ce())),Z()}return Y(()=>{e.modelValue&&(g.value=G.value)}),_(()=>r.value,()=>{j(()=>{var v,A,P;(v=d.value)==null||v.update(),(A=h.value)==null||A.update(),(P=C.value)==null||P.update()})}),Pe(ve,{currentColor:G}),{color:u,colorDisabled:f,colorSize:N,displayedColor:M,showPanelColor:c,showPicker:r,customInput:g,buttonId:a,buttonAriaLabel:pe,buttonAriaLabelledby:ge,handleConfirm:we,hide:$e,handleTrigger:Ce,clear:ye,confirmValue:Me,t:o,ns:t,hue:d,svPanel:h,alpha:C,popper:p}}}),ft=["id","aria-label","aria-labelledby","aria-description","tabindex"];function mt(e,n,o,t,s,l){const a=y("hue-slider"),i=y("sv-panel"),d=y("alpha-slider"),h=y("predefine"),C=y("el-input"),p=y("el-button"),k=y("arrow-down"),u=y("el-icon"),r=y("close"),c=y("el-tooltip"),g=He("click-outside");return w(),X(c,{ref:"popper",visible:e.showPicker,"show-arrow":!1,"fallback-placements":["bottom","top","right","left"],offset:0,"gpu-acceleration":!1,"popper-class":[e.ns.be("picker","panel"),e.ns.b("dropdown"),e.popperClass],"stop-popper-mouse-event":!1,effect:"light",trigger:"click",transition:`${e.ns.namespace.value}-zoom-in-top`,persistent:""},{content:V(()=>[se((w(),S("div",null,[$("div",{class:m(e.ns.be("dropdown","main-wrapper"))},[E(a,{ref:"hue",class:"hue-slider",color:e.color,vertical:""},null,8,["color"]),E(i,{ref:"svPanel",color:e.color},null,8,["color"])],2),e.showAlpha?(w(),X(d,{key:0,ref:"alpha",color:e.color},null,8,["color"])):q("v-if",!0),e.predefine?(w(),X(h,{key:1,ref:"predefine",color:e.color,colors:e.predefine},null,8,["color","colors"])):q("v-if",!0),$("div",{class:m(e.ns.be("dropdown","btns"))},[$("span",{class:m(e.ns.be("dropdown","value"))},[E(C,{modelValue:e.customInput,"onUpdate:modelValue":n[0]||(n[0]=M=>e.customInput=M),"validate-event":!1,size:"small",onKeyup:re(e.handleConfirm,["enter"]),onBlur:e.handleConfirm},null,8,["modelValue","onKeyup","onBlur"])],2),E(p,{class:m(e.ns.be("dropdown","link-btn")),text:"",size:"small",onClick:e.clear},{default:V(()=>[ie(ue(e.t("el.colorpicker.clear")),1)]),_:1},8,["class","onClick"]),E(p,{plain:"",size:"small",class:m(e.ns.be("dropdown","btn")),onClick:e.confirmValue},{default:V(()=>[ie(ue(e.t("el.colorpicker.confirm")),1)]),_:1},8,["class","onClick"])],2)])),[[g,e.hide]])]),default:V(()=>[$("div",{id:e.buttonId,class:m([e.ns.b("picker"),e.ns.is("disabled",e.colorDisabled),e.ns.bm("picker",e.colorSize)]),role:"button","aria-label":e.buttonAriaLabel,"aria-labelledby":e.buttonAriaLabelledby,"aria-description":e.t("el.colorpicker.description",{color:e.modelValue||""}),tabindex:e.tabindex,onKeydown:n[2]||(n[2]=re((...M)=>e.handleTrigger&&e.handleTrigger(...M),["enter"]))},[e.colorDisabled?(w(),S("div",{key:0,class:m(e.ns.be("picker","mask"))},null,2)):q("v-if",!0),$("div",{class:m(e.ns.be("picker","trigger")),onClick:n[1]||(n[1]=(...M)=>e.handleTrigger&&e.handleTrigger(...M))},[$("span",{class:m([e.ns.be("picker","color"),e.ns.is("alpha",e.showAlpha)])},[$("span",{class:m(e.ns.be("picker","color-inner")),style:F({backgroundColor:e.displayedColor})},[se(E(u,{class:m([e.ns.be("picker","icon"),e.ns.is("icon-arrow-down")])},{default:V(()=>[E(k)]),_:1},8,["class"]),[[Be,e.modelValue||e.showPanelColor]]),!e.modelValue&&!e.showPanelColor?(w(),X(u,{key:0,class:m([e.ns.be("picker","empty"),e.ns.is("icon-close")])},{default:V(()=>[E(r)]),_:1},8,["class"])):q("v-if",!0)],6)],2)],2)],42,ft)]),_:1},8,["visible","popper-class","transition"])}var K=O(dt,[["render",mt],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/color-picker/src/index.vue"]]);K.install=e=>{e.component(K.name,K)};const vt=K,_t=vt;export{_t as E};
