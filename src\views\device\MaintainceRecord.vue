<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-08-15 13:02:12
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-19 08:18:07
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\device\MaintainceRecord.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-card>
            <el-form :model="form" ref="form">
                <el-row :gutter="10">
                    <el-col :span="3">
                        <el-form-item prop="keywords">
                            <el-input v-model="form.keywords" placeholder="保全工号"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item prop="device_number">
                            <el-input v-model="form.device_number" placeholder="请输入设备编号"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="3">
                        <el-form-item prop="area">
                            <!-- 区域选择 -->
                            <el-select v-model="form.area" placeholder="请选择区域">
                                <el-option label="1#织造横机区域" value="1#织造横机区域"></el-option>
                                <el-option label="2#织造横机区域" value="2#织造横机区域"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="3">
                        <el-form-item prop="depart">
                            <!-- 区域选择 -->
                            <el-select v-model="form.depart" placeholder="请选择部门">
                                <el-option label="织造车间" value="织造车间"></el-option>
                                <el-option label="整理车间" value="整理车间"></el-option>
                                <el-option label="打样车间" value="打样车间"></el-option>
                                <el-option label="综合维修" value="综合维修"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5">
                        <el-form-item prop="type">
                            <!-- 保养类型 -->
                            <el-select v-model="form.type" placeholder="请选择类型">
                                <el-option label="日保养" value="日保养"></el-option>
                                <el-option label="周保养" value="周保养"></el-option>
                                <el-option label="月保养" value="月保养"></el-option>
                                <el-option label="季度保养" value="季度保养"></el-option>
                                <el-option label="半年保养" value="半年保养"></el-option>
                                <el-option label="年保养" value="年保养"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <!-- 月份选择 -->
                        <el-form-item prop="date">
                            <el-date-picker v-model="form.date" type="daterange" range-separator="至"
                                start-placeholder="开始日期" end-placeholder="结束日期" align="right" value-format="yyyy-MM-dd"
                                style="float: right">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="3">
                        <el-form-item prop="shift">
                            <!-- 班次 -->
                            <el-select v-model="form.shift" placeholder="请选择班次">
                                <el-option label="A" value="A"></el-option>
                                <el-option label="B" value="B"></el-option>
                                <el-option label="C" value="C"></el-option>
                                <el-option label="E" value="E"></el-option>
                                <el-option label="E1" value="E1"></el-option>
                                <el-option label="E2" value="E2"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="1.5">
                        <el-form-item>
                            <el-button type="primary" @click="onSubmit" icon="el-icon-search">查询</el-button>
                        </el-form-item>
                    </el-col>
                    <el-col :span="1.5">
                        <el-form-item>
                            <el-button type="primary" plain icon="el-icon-refresh" @click="resetData('form')">重置</el-button>
                        </el-form-item>
                    </el-col>
                    <el-col :span="2">
                        <el-form-item>
                            <el-button type="primary" plain icon="el-icon-refresh-right" @click="refreshData"></el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <!-- 主体部分，表格，设备编号，区域，保养内容，开始时间，结束时间，耗时，部门，工号，班次，备注 -->
            <el-table :data="tableData" style="width: 100%" stripe v-loading="loading" element-loading-text="Flyknit"
                height="630">
                <!-- 索引 -->
                <el-table-column type="index" width="50">
                </el-table-column>
                <el-table-column prop="device_number" label="设备编号" width="100">
                </el-table-column>
                <el-table-column prop="type" label="保养类型" width="100">
                    <!-- 如果type为1，显示为保养，如果type为2，显示为维修 -->
                    <template slot-scope="scope">
                        <el-tag type="info">{{ scope.row.type === '1' ? '日保养' : scope.row.type === '2' ? '月保养' :
                            scope.row.type === '3' ? '季度保养' : scope.row.type === '4' ? '半年保养' : '年保养' }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="location" label="区域" width="130">
                </el-table-column>
                <el-table-column prop="department" label="部门">
                </el-table-column>
                <el-table-column prop="uuid" label="工号" width="100">
                </el-table-column>
                <el-table-column prop="shift" label="班次" width="80">
                </el-table-column>
                <el-table-column prop="maintaince_list" label="保养内容" width="500">
                </el-table-column>
                <el-table-column prop="start_time" label="开始时间">
                </el-table-column>
                <el-table-column prop="end_time" label="结束时间">
                </el-table-column>
                <el-table-column prop="spend" label="耗时(分)" width="80">
                </el-table-column>
                <el-table-column prop="remarks" label="备注">
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
                :page-sizes="[12, 14, 16, 18]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="total">
            </el-pagination>
        </el-card>
    </div>
</template>
<script>
export default {
    data() {
        return {
            loading: true,
            tableData: [],
            form: {
                keywords: '',
                device_number: '',
                area: '',
                depart: '',
                date: '',
                type: '',
                shift: ''
            },
            currentPage: 1,
            pageSize: 12,
            total: 12
        }
    },
    mounted() {
        this.getMaintainceList()
    },
    methods: {
        // 重置表单
        resetData(formName) {
            this.$refs[formName].resetFields()
        },
        // 刷新数据
        refreshData() {
            this.loading = true
            this.getMaintainceList()
        },
        // 获取保养记录列表
        async getMaintainceList() {
            try {
                this.form.page = this.currentPage
                this.form.pageSize = this.pageSize
                const res = await this.$http.getMaintainceLists(this.form)
                if (res.status !== 200) {
                    return this.$message.error(res.msg)
                }
                const { list, total } = res.data
                this.tableData = list
                this.total = total
                setTimeout(() => {
                    this.loading = false
                }, 1000);
            } catch (error) {
                this.loading = false
                return this.$message.error('服务器异常，请稍后重试！')
            }
        },
        onSubmit() {
            if (this.form.keywords || this.form.device_number || this.form.area || this.form.depart || this.form.date || this.form.type || this.form.shift) {
                this.loading = true
                this.getMaintainceList()
            } else {
                return false
            }
        },
        handleSizeChange(val) {
            this.loading = true
            this.pageSize = val
            this.getMaintainceList()
        },
        handleCurrentChange(val) {
            this.loading = true
            this.currentPage = val
            this.getMaintainceList()
        }
    }
}
</script>
<style>
.el-card__body {
    padding: 20px 20px 0px 20px !important;
}

.el-pagination {
    padding: 10px 0;
}
</style>