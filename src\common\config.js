/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-08-07 10:06:31
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-07 10:58:44
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\common\config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 配置公共的后端api地址
const baseURL = 'http://*************:8081/api/v1'
// 配置公共的请求头
const headers = {
    'Content-Type': 'application/json;charset=UTF-8',
    'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
}
// 配置图片上传地址接口
const uploadURL = baseURL + '/upload'
// 配置图片前缀
const imgPrefix = baseURL + '/'

// 配置请求超时时间
const timeout = 10000

// 导出配置
export default {
    baseURL,
    headers,
    uploadURL,
    imgPrefix,
    timeout
}