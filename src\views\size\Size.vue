<!--
 * @Author: flyknit <EMAIL>
 * @Date: 2024-05-07 12:47:39
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-05-20 08:24:54
 * @FilePath: \electronic-filed:\gitee\backend-demo\src\views\size\Size.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by ${<EMAIL>}, All Rights Reserved. 
-->
<template>
  <div>
    <el-card>
      <el-row :gutter="10" style="display: flex; align-items: center">
        <el-col :span="24">
          <el-form inline :form="form" v-if="User.type == 2">
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-plus"
                size="mini"
                @click="addSize"
                >添加</el-button
              >
            </el-form-item>
            <el-form-item v-show="deleteIds.length > 0">
              <el-button
                type="danger"
                icon="el-icon-delete"
                size="mini"
                @click="deleteAll"
                >删除</el-button
              >
            </el-form-item>
          </el-form>
          <el-table
            :data="sizeList"
            resizable
            stripe
            border
            v-loading="loading"
            element-loading-text="Flyknit"
            ref="multipleTable"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column
              label="编号"
              header-align="center"
              sortable
              prop="id"
              width="100"
              align="center"
            ></el-table-column>
            <el-table-column
              label="尺码"
              sortable
              prop="size"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              label="创建时间"
              sortable
              prop="create_time"
              header-align="center"
              align="center"
            ></el-table-column>
            <!-- 操作 -->
            <el-table-column
              label="操作"
              header-align="center"
              align="center"
              width="200"
              v-if="User.type == 2"
            >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="mini"
                  icon="el-icon-edit"
                  @click="handleEdit(scope.row)"
                ></el-button>
                <el-button
                  type="text"
                  style="color: #eb4c42"
                  icon="el-icon-delete"
                  size="mini"
                  @click="handleDelete(scope.row.id)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <div class="page" v-show="total > 0">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="currentchange"
              :current-page="queryInfo.currentnum"
              :page-sizes="pageSizes"
              :page-size="queryInfo.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            >
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <!-- 添加尺码弹窗 -->
    <el-dialog :title="title" :visible.sync="dialogFormVisible">
      <el-form :model="form" ref="form" :rules="rules" label-width="80px">
        <el-form-item label="尺码" prop="size">
          <el-input v-model="form.size" placeholder="请输入尺码"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button
            type="primary"
            v-on:keyup.13.native="submitForm('form')"
            @click="submitForm('form')"
            >{{ btnText }}</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { set } from "nprogress";
import { mapState } from "vuex";
export default {
  name: "Size",
  data() {
    return {
      title: "添加尺码",
      sizeList: [], //  尺码列表
      loading: true,
      checked: false,
      queryInfo: {
        currentnum: 1,
        pageNum: 1,
        pageSize: 12,
      },
      total: 0,
      pageSizes: [12, 14, 16, 18],
      form: {
        size: "",
      },
      rules: {
        size: [{ required: true, message: "请输入尺码", trigger: "blur" }],
      },
      dialogFormVisible: false,
      btnText: "添加",
      multipleSelection: [], // 选中的行
      deleteIds: [], // 选中的id
    };
  },
  computed: {
    ...mapState({
      User: (state) => state.users,
    }),
  },
  created() {
    this.getSizeList();
  },
  mounted() {
    // 监听键盘的点击事件
    window.addEventListener("keydown", this.keyDown);
  },
  destroyed() {
    window.removeEventListener("keydown", this.keyDown, false);
  },
  methods: {
    // 删除多个尺码
    deleteAll() {
      if (this.deleteIds.length === 0) {
        return this.$notify({
          title: "系统提示",
          message: "请选择要删除的尺码",
          type: "warning",
        });
      }
      this.$confirm("此操作将永久删除这些尺码, 是否继续?", "系统提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const res = await this.$http.deleteSize({
            ids: this.deleteIds,
          });
          if (res.code !== 200) {
            return this.$notify({
              title: "系统提示",
              message: res.message,
              type: "error",
            });
          }
          this.$notify({
            title: "成功",
            message: res.message,
            type: "success",
          });
          this.getSizeList();
        })
        .catch(() => {
          return false;
        });
    },
    // 选中当前行
    toggleSelection(rows) {
      if (rows) {
        rows.forEach((row) => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.multipleTable.clearSelection();
      }
    },
    // 选中事件
    handleSelectionChange(val) {
      this.multipleSelection = val;
      this.deleteIds = [];
      val.forEach((item) => {
        this.deleteIds.push(item.id);
      });
    },
    // 删除尺码
    async handleDelete(id) {
      this.$confirm("此操作将永久删除该尺码, 是否继续?", "系统提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const res = await this.$http.deleteSize({
            ids: id,
          });
          if (res.code !== 200) {
            return this.$notify({
              title: "系统提示",
              message: res.message,
              type: "error",
            });
          }
          this.$notify({
            title: "成功",
            message: res.message,
            type: "success",
          });
          this.getSizeList();
        })
        .catch(() => {
          return false;
        });
    },
    // 编辑尺码
    handleEdit(row) {
      this.title = "编辑尺码";
      this.btnText = "编辑";
      this.form.size = row.size;
      this.form.id = row.id;
      this.dialogFormVisible = true;
    },
    //绑定监听事件
    keyDown(e) {
      //如果是按回车则执行登录方法
      if (e.keyCode == 13) {
        this.submitForm("form");
      }
    },
    // 提交添加尺码
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // alert("submit!");
          this.addSizeApi();
        } else {
          // console.log("error submit!!");
          return false;
        }
      });
    },
    // 全局添加尺码接口
    async addSizeApi() {
      const res = await this.$http.addSize(this.form);
      if (res.code !== 200) {
        return this.$notify({
          title: "系统提示",
          message: res.message,
          type: "success",
        });
      }
      this.$notify.success({
        title: "成功",
        message: res.message,
      });
      this.dialogFormVisible = false;
      this.form.size = "";
      this.getSizeList();
    },
    // 添加尺码开关
    addSize() {
      this.dialogFormVisible = true;
    },
    // 获取尺码列表
    async getSizeList() {
      try {
        this.loading = true;
        const res = await this.$http.getSizeList(this.queryInfo);
        if (res.code !== 200) {
          this.loading = false;
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        const { result, total } = res.data.list;
        this.sizeList = result;
        this.total = total;
        setTimeout(() => {
          this.loading = false;
        }, 500);
      } catch (error) {
        this.loading = false;
        return this.$notify({
          title: "系统提示",
          message: "获取尺码列表失败",
          type: "error",
        });
      }
    },
    handleSizeChange(val) {
      this.queryInfo.pageSize = val;
      this.getSizeList();
    },
    currentchange(val) {
      this.queryInfo.pageNum = val;
      this.getSizeList();
    },
  },
};
</script>
<style scoped>
.page {
  margin-top: 20px;
  display: flex;
  justify-content: flex-start;
}
</style>