import r from"./MapChian-3ca07ed7.js";import{_ as l}from"./_plugin-vue_export-helper-c27b6911.js";import{i as v,k as _,o as m,g as p,m as t,b as f,K as h,z as u,A as w}from"./index-444b28c3.js";import"./useEcharts-57db09d5.js";const x=e=>(u("data-v-d975c579"),e=e(),w(),e),g={class:"dataView-container"},y=h('<div class="data-header" data-v-d975c579>开发中</div><div class="data-main" data-v-d975c579><div class="data-main-left" data-v-d975c579><div class="left-item" data-v-d975c579>开发中</div><div class="left-item" data-v-d975c579>开发中</div></div><div class="data-main-right" data-v-d975c579><div class="right-item" data-v-d975c579>开发中</div><div class="right-item" data-v-d975c579>开发中</div></div></div>',2),V={class:"echarts-map-chian"},S=x(()=>t("div",{class:"data-footer"},"开发中",-1)),k={__name:"index",setup(e){const a=v(null);_(()=>{a.value&&(a.value.style.transform=`scale(${d()}) translate(-50%, -50%)`,a.value.style.width="1920px",a.value.style.height="1080px"),window.addEventListener("resize",n)});const d=(s=1920,i=1080)=>{let c=window.innerWidth/s,o=window.innerHeight/i;return c<o?c:o},n=()=>{a.value&&(a.value.style.transform=`scale(${d()}) translate(-50%, -50%)`)};return(s,i)=>(m(),p("div",g,[t("div",{class:"dataView",ref_key:"dataViewRef",ref:a},[y,t("div",V,[f(r)]),S],512)]))}},$=l(k,[["__scopeId","data-v-d975c579"]]);export{$ as default};
