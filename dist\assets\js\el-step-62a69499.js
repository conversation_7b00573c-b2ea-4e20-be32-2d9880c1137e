import{Q as j,a7 as q,_ as x,C as H,R as L,i as C,a8 as I,a9 as F,o,g as f,S as E,H as n,n as e,aa as J,X as K,ab as O,k as Y,ac as Z,c as r,j as ee,f as k,m as g,ad as z,a as w,w as N,L as se,a5 as b,b as D,ae as te,af as ae,t as B,d as T,U as ne,ag as ie}from"./index-444b28c3.js";import{C as U}from"./event-fe80fd0c.js";const le=j({space:{type:[Number,String],default:""},active:{type:Number,default:0},direction:{type:String,default:"horizontal",values:["horizontal","vertical"]},alignCenter:{type:Boolean},simple:{type:Boolean},finishStatus:{type:String,values:["wait","process","finish","error","success"],default:"finish"},processStatus:{type:String,values:["wait","process","finish","error","success"],default:"process"}}),re={[U]:(m,S)=>[m,S].every(q)},oe={name:"ElSteps"},ce=H({...oe,props:le,emits:re,setup(m,{emit:S}){const t=m,l=L("steps"),u=C([]);return I(u,()=>{u.value.forEach((i,a)=>{i.setIndex(a)})}),F("ElSteps",{props:t,steps:u}),I(()=>t.active,(i,a)=>{S(U,i,a)}),(i,a)=>(o(),f("div",{class:n([e(l).b(),e(l).m(i.simple?"simple":i.direction)])},[E(i.$slots,"default")],2))}});var pe=x(ce,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/steps/src/steps.vue"]]);const ue=j({title:{type:String,default:""},icon:{type:J},description:{type:String,default:""},status:{type:String,values:["","wait","process","finish","error","success"],default:""}}),ve={name:"ElStep"},de=H({...ve,props:ue,setup(m){const S=m,t=L("step"),l=C(-1),u=C({}),i=C(""),a=K("ElSteps"),v=O();Y(()=>{I([()=>a.props.active,()=>a.props.processStatus,()=>a.props.finishStatus],([s])=>{R(s)},{immediate:!0})}),Z(()=>{a.steps.value=a.steps.value.filter(s=>s.uid!==(v==null?void 0:v.uid))});const d=r(()=>S.status||i.value),A=r(()=>{const s=a.steps.value[l.value-1];return s?s.currentStatus:"wait"}),$=r(()=>a.props.alignCenter),P=r(()=>a.props.direction==="vertical"),p=r(()=>a.props.simple),_=r(()=>a.steps.value.length),V=r(()=>{var s;return((s=a.steps.value[_.value-1])==null?void 0:s.uid)===(v==null?void 0:v.uid)}),y=r(()=>p.value?"":a.props.space),G=r(()=>{const s={flexBasis:typeof y.value=="number"?`${y.value}px`:y.value?y.value:`${100/(_.value-($.value?0:1))}%`};return P.value||V.value&&(s.maxWidth=`${100/_.value}%`),s}),M=s=>{l.value=s},Q=s=>{let c=100;const h={};h.transitionDelay=`${150*l.value}ms`,s===a.props.processStatus?c=0:s==="wait"&&(c=0,h.transitionDelay=`${-150*l.value}ms`),h.borderWidth=c&&!p.value?"1px":0,h[a.props.direction==="vertical"?"height":"width"]=`${c}%`,u.value=h},R=s=>{s>l.value?i.value=a.props.finishStatus:s===l.value&&A.value!=="error"?i.value=a.props.processStatus:i.value="wait";const c=a.steps.value[_.value-1];c&&c.calcProgress(i.value)},X=ee({uid:r(()=>v==null?void 0:v.uid),currentStatus:d,setIndex:M,calcProgress:Q});return a.steps.value=[...a.steps.value,X],(s,c)=>(o(),f("div",{style:z(e(G)),class:n([e(t).b(),e(t).is(e(p)?"simple":e(a).props.direction),e(t).is("flex",e(V)&&!e(y)&&!e($)),e(t).is("center",e($)&&!e(P)&&!e(p))])},[k(" icon & line "),g("div",{class:n([e(t).e("head"),e(t).is(e(d))])},[e(p)?k("v-if",!0):(o(),f("div",{key:0,class:n(e(t).e("line"))},[g("i",{class:n(e(t).e("line-inner")),style:z(u.value)},null,6)],2)),g("div",{class:n([e(t).e("icon"),e(t).is(s.icon||s.$slots.icon?"icon":"text")])},[E(s.$slots,"icon",{},()=>[s.icon?(o(),w(e(b),{key:0,class:n(e(t).e("icon-inner"))},{default:N(()=>[(o(),w(se(s.icon)))]),_:1},8,["class"])):e(d)==="success"?(o(),w(e(b),{key:1,class:n([e(t).e("icon-inner"),e(t).is("status")])},{default:N(()=>[D(e(te))]),_:1},8,["class"])):e(d)==="error"?(o(),w(e(b),{key:2,class:n([e(t).e("icon-inner"),e(t).is("status")])},{default:N(()=>[D(e(ae))]),_:1},8,["class"])):e(p)?k("v-if",!0):(o(),f("div",{key:3,class:n(e(t).e("icon-inner"))},B(l.value+1),3))])],2)],2),k(" title & description "),g("div",{class:n(e(t).e("main"))},[g("div",{class:n([e(t).e("title"),e(t).is(e(d))])},[E(s.$slots,"title",{},()=>[T(B(s.title),1)])],2),e(p)?(o(),f("div",{key:0,class:n(e(t).e("arrow"))},null,2)):(o(),f("div",{key:1,class:n([e(t).e("description"),e(t).is(e(d))])},[E(s.$slots,"description",{},()=>[T(B(s.description),1)])],2))],2)],6))}});var W=x(de,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/steps/src/item.vue"]]);const Se=ne(pe,{Step:W}),ye=ie(W);export{ye as E,Se as a};
