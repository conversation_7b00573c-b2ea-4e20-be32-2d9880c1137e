import{bn as c,o as r,g as p,m as e,b as d,w as i,n,C as l,d as u,bv as m,z as f,A as h}from"./index-444b28c3.js";import{E as b}from"./el-button-9bbdfcf9.js";import{_ as v}from"./_plugin-vue_export-helper-c27b6911.js";import"./index-4d7f16ce.js";const g=""+new URL("../png/404-dc770d81.png",import.meta.url).href;const o=t=>(f("data-v-24b16287"),t=t(),h(),t),x={class:"not-container"},w=o(()=>e("img",{src:g,class:"not-img",alt:"404"},null,-1)),y={class:"not-detail"},B=o(()=>e("h2",null,"404",-1)),C=o(()=>e("h4",null,"抱歉，您访问的页面不存在~🤷‍♂️🤷‍♀️",-1)),E=l({name:"404"}),I=Object.assign(E,{setup(t){const _=c();return(k,s)=>{const a=b;return r(),p("div",x,[w,e("div",y,[B,C,d(a,{type:"primary",onClick:s[0]||(s[0]=N=>n(_).push(n(m)))},{default:i(()=>[u("返回首页")]),_:1})])])}}}),O=v(I,[["__scopeId","data-v-24b16287"]]);export{O as default};
