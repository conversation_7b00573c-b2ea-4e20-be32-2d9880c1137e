import{a_ as T,X as ne,c as g,aS as N,a7 as D,bC as A,b8 as U,i as B,ab as M,a$ as E,ce as oe,bU as te,a8 as O,G as P,_ as F,C as V,b0 as j,R as z,o as y,a as W,w as H,m as R,H as h,n as e,e as S,g as C,I,cf as L,S as w,F as se,d as X,t as q,f as $,L as J,ad as ue,a9 as ie,bW as re,U as de,ag as K}from"./index-444b28c3.js";import{u as be,b as Q}from"./index-4d7f16ce.js";import{U as G,d as Y}from"./event-fe80fd0c.js";const ce={modelValue:{type:Array,default:()=>[]},disabled:Boolean,min:{type:Number,default:void 0},max:{type:Number,default:void 0},size:T,id:{type:String,default:void 0},label:{type:String,default:void 0},fill:{type:String,default:void 0},textColor:{type:String,default:void 0},tag:{type:String,default:"div"},validateEvent:{type:Boolean,default:!0}},Z={modelValue:{type:[Number,String,Boolean],default:()=>{}},label:{type:[String,Boolean,Number,Object]},indeterminate:Boolean,disabled:Boolean,checked:Boolean,name:{type:String,default:void 0},trueLabel:{type:[String,Number],default:void 0},falseLabel:{type:[String,Number],default:void 0},id:{type:String,default:void 0},controls:{type:String,default:void 0},border:Boolean,size:T,tabindex:[String,Number],validateEvent:{type:Boolean,default:!0}},x=()=>{const{form:l,formItem:i}=be(),t=ne("CheckboxGroup",{}),r=g(()=>t&&(t==null?void 0:t.name)==="ElCheckboxGroup"),c=g(()=>i==null?void 0:i.size);return{isGroup:r,checkboxGroup:t,elForm:l,elFormItemSize:c,elFormItem:i}},ve=(l,{elFormItem:i})=>{const{inputId:t,isLabeledByFormItem:r}=Q(l,{formItemContext:i});return{isLabeledByFormItem:r,groupId:t}},me=l=>{const i=B(!1),{emit:t}=M(),{isGroup:r,checkboxGroup:c,elFormItem:v}=x(),m=B(!1);return{model:g({get(){var s,u;return r.value?(s=c.modelValue)==null?void 0:s.value:(u=l.modelValue)!=null?u:i.value},set(s){var u;r.value&&Array.isArray(s)?(m.value=c.max!==void 0&&s.length>c.max.value,m.value===!1&&((u=c==null?void 0:c.changeEvent)==null||u.call(c,s))):(t(G,s),i.value=s)}}),isGroup:r,isLimitExceeded:m,elFormItem:v}},fe=(l,i,{model:t})=>{const{isGroup:r,checkboxGroup:c}=x(),v=B(!1),m=E(c==null?void 0:c.checkboxGroupSize,{prop:!0}),p=g(()=>{const n=t.value;return oe(n)==="[object Boolean]"?n:Array.isArray(n)?n.map(te).includes(l.label):n!=null?n===l.trueLabel:!!n}),s=E(g(()=>{var n;return r.value?(n=c==null?void 0:c.checkboxGroupSize)==null?void 0:n.value:void 0})),u=g(()=>!!(i.default||l.label));return{isChecked:p,focus:v,size:m,checkboxSize:s,hasOwnLabel:u}},pe=(l,{model:i,isChecked:t})=>{const{elForm:r,isGroup:c,checkboxGroup:v}=x(),m=g(()=>{var s,u;const n=(s=v.max)==null?void 0:s.value,k=(u=v.min)==null?void 0:u.value;return!!(n||k)&&i.value.length>=n&&!t.value||i.value.length<=k&&t.value});return{isDisabled:g(()=>{var s,u;const n=l.disabled||(r==null?void 0:r.disabled);return(u=c.value?((s=v.disabled)==null?void 0:s.value)||n||m.value:n)!=null?u:!1}),isLimitDisabled:m}},ke=(l,{model:i})=>{function t(){Array.isArray(i.value)&&!i.value.includes(l.label)?i.value.push(l.label):i.value=l.trueLabel||!0}l.checked&&t()},he=(l,{model:i,isLimitExceeded:t,hasOwnLabel:r,isDisabled:c,isLabeledByFormItem:v})=>{const{elFormItem:m,checkboxGroup:p}=x(),{emit:s}=M();function u(a){var b,f;return a===l.trueLabel||a===!0?(b=l.trueLabel)!=null?b:!0:(f=l.falseLabel)!=null?f:!1}function n(a,b){s("change",u(a),b)}function k(a){if(t.value)return;const b=a.target;s("change",u(b.checked),a)}async function d(a){t.value||!r.value&&!c.value&&v.value&&(i.value=u([!1,l.falseLabel].includes(i.value)),await P(),n(i.value,a))}const o=g(()=>{var a;return((a=p.validateEvent)==null?void 0:a.value)||l.validateEvent});return O(()=>l.modelValue,()=>{o.value&&(m==null||m.validate("change").catch(a=>Y()))}),{handleChange:k,onClickRoot:d}},_={[G]:l=>N(l)||D(l)||A(l),change:l=>N(l)||D(l)||A(l)},ge={[G]:l=>U(l),change:l=>U(l)},ee=(l,i)=>{const{model:t,isGroup:r,isLimitExceeded:c,elFormItem:v}=me(l),{focus:m,size:p,isChecked:s,checkboxSize:u,hasOwnLabel:n}=fe(l,i,{model:t}),{isDisabled:k}=pe(l,{model:t,isChecked:s}),{inputId:d,isLabeledByFormItem:o}=Q(l,{formItemContext:v,disableIdGeneration:n,disableIdManagement:r}),{handleChange:a,onClickRoot:b}=he(l,{model:t,isLimitExceeded:c,hasOwnLabel:n,isDisabled:k,isLabeledByFormItem:o});return ke(l,{model:t}),{elFormItem:v,inputId:d,isLabeledByFormItem:o,isChecked:s,isDisabled:k,isGroup:r,checkboxSize:u,hasOwnLabel:n,model:t,handleChange:a,onClickRoot:b,focus:m,size:p}},ye=["tabindex","role","aria-checked"],Ce=["id","aria-hidden","name","tabindex","disabled","true-value","false-value"],xe=["id","aria-hidden","disabled","value","name","tabindex"],Se={name:"ElCheckbox"},Ie=V({...Se,props:Z,emits:_,setup(l){const i=l,t=j(),{inputId:r,isLabeledByFormItem:c,isChecked:v,isDisabled:m,checkboxSize:p,hasOwnLabel:s,model:u,handleChange:n,onClickRoot:k,focus:d}=ee(i,t),o=z("checkbox");return(a,b)=>(y(),W(J(!e(s)&&e(c)?"span":"label"),{class:h([e(o).b(),e(o).m(e(p)),e(o).is("disabled",e(m)),e(o).is("bordered",a.border),e(o).is("checked",e(v))]),"aria-controls":a.indeterminate?a.controls:null,onClick:e(k)},{default:H(()=>[R("span",{class:h([e(o).e("input"),e(o).is("disabled",e(m)),e(o).is("checked",e(v)),e(o).is("indeterminate",a.indeterminate),e(o).is("focus",e(d))]),tabindex:a.indeterminate?0:void 0,role:a.indeterminate?"checkbox":void 0,"aria-checked":a.indeterminate?"mixed":void 0},[a.trueLabel||a.falseLabel?S((y(),C("input",{key:0,id:e(r),"onUpdate:modelValue":b[0]||(b[0]=f=>I(u)?u.value=f:null),class:h(e(o).e("original")),type:"checkbox","aria-hidden":a.indeterminate?"true":"false",name:a.name,tabindex:a.tabindex,disabled:e(m),"true-value":a.trueLabel,"false-value":a.falseLabel,onChange:b[1]||(b[1]=(...f)=>e(n)&&e(n)(...f)),onFocus:b[2]||(b[2]=f=>d.value=!0),onBlur:b[3]||(b[3]=f=>d.value=!1)},null,42,Ce)),[[L,e(u)]]):S((y(),C("input",{key:1,id:e(r),"onUpdate:modelValue":b[4]||(b[4]=f=>I(u)?u.value=f:null),class:h(e(o).e("original")),type:"checkbox","aria-hidden":a.indeterminate?"true":"false",disabled:e(m),value:a.label,name:a.name,tabindex:a.tabindex,onChange:b[5]||(b[5]=(...f)=>e(n)&&e(n)(...f)),onFocus:b[6]||(b[6]=f=>d.value=!0),onBlur:b[7]||(b[7]=f=>d.value=!1)},null,42,xe)),[[L,e(u)]]),R("span",{class:h(e(o).e("inner"))},null,2)],10,ye),e(s)?(y(),C("span",{key:0,class:h(e(o).e("label"))},[w(a.$slots,"default"),a.$slots.default?$("v-if",!0):(y(),C(se,{key:0},[X(q(a.label),1)],64))],2)):$("v-if",!0)]),_:3},8,["class","aria-controls","onClick"]))}});var Le=F(Ie,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/checkbox/src/checkbox.vue"]]);const Ge=["name","tabindex","disabled","true-value","false-value"],Be=["name","tabindex","disabled","value"],Ee={name:"ElCheckboxButton"},$e=V({...Ee,props:Z,emits:_,setup(l){const i=l,t=j(),{focus:r,isChecked:c,isDisabled:v,size:m,model:p,handleChange:s}=ee(i,t),{checkboxGroup:u}=x(),n=z("checkbox"),k=g(()=>{var d,o,a,b;const f=(o=(d=u==null?void 0:u.fill)==null?void 0:d.value)!=null?o:"";return{backgroundColor:f,borderColor:f,color:(b=(a=u==null?void 0:u.textColor)==null?void 0:a.value)!=null?b:"",boxShadow:f?`-1px 0 0 0 ${f}`:void 0}});return(d,o)=>(y(),C("label",{class:h([e(n).b("button"),e(n).bm("button",e(m)),e(n).is("disabled",e(v)),e(n).is("checked",e(c)),e(n).is("focus",e(r))])},[d.trueLabel||d.falseLabel?S((y(),C("input",{key:0,"onUpdate:modelValue":o[0]||(o[0]=a=>I(p)?p.value=a:null),class:h(e(n).be("button","original")),type:"checkbox",name:d.name,tabindex:d.tabindex,disabled:e(v),"true-value":d.trueLabel,"false-value":d.falseLabel,onChange:o[1]||(o[1]=(...a)=>e(s)&&e(s)(...a)),onFocus:o[2]||(o[2]=a=>r.value=!0),onBlur:o[3]||(o[3]=a=>r.value=!1)},null,42,Ge)),[[L,e(p)]]):S((y(),C("input",{key:1,"onUpdate:modelValue":o[4]||(o[4]=a=>I(p)?p.value=a:null),class:h(e(n).be("button","original")),type:"checkbox",name:d.name,tabindex:d.tabindex,disabled:e(v),value:d.label,onChange:o[5]||(o[5]=(...a)=>e(s)&&e(s)(...a)),onFocus:o[6]||(o[6]=a=>r.value=!0),onBlur:o[7]||(o[7]=a=>r.value=!1)},null,42,Be)),[[L,e(p)]]),d.$slots.default||d.label?(y(),C("span",{key:2,class:h(e(n).be("button","inner")),style:ue(e(c)?e(k):void 0)},[w(d.$slots,"default",{},()=>[X(q(d.label),1)])],6)):$("v-if",!0)],2))}});var ae=F($e,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/checkbox/src/checkbox-button.vue"]]);const Fe={name:"ElCheckboxGroup"},Ve=V({...Fe,props:ce,emits:ge,setup(l,{emit:i}){const t=l,{elFormItem:r}=x(),{groupId:c,isLabeledByFormItem:v}=ve(t,{elFormItem:r}),m=E(),p=z("checkbox"),s=n=>{i(G,n),P(()=>{i("change",n)})},u=g({get(){return t.modelValue},set(n){s(n)}});return ie("CheckboxGroup",{name:"ElCheckboxGroup",...re(t),modelValue:u,checkboxGroupSize:m,changeEvent:s}),O(()=>t.modelValue,()=>{t.validateEvent&&(r==null||r.validate("change").catch(n=>Y()))}),(n,k)=>{var d;return y(),W(J(n.tag),{id:e(c),class:h(e(p).b("group")),role:"group","aria-label":e(v)?void 0:n.label||"checkbox-group","aria-labelledby":e(v)?(d=e(r))==null?void 0:d.labelId:void 0},{default:H(()=>[w(n.$slots,"default")]),_:3},8,["id","class","aria-label","aria-labelledby"])}}});var le=F(Ve,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/checkbox/src/checkbox-group.vue"]]);const De=de(Le,{CheckboxButton:ae,CheckboxGroup:le});K(ae);K(le);export{De as E};
