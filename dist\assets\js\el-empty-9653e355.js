import{_ as u,C as y,R as h,o as n,g as a,m as s,c as f,S as c,b as g,H as i,n as o,ad as _,t as k,f as $,U as v}from"./index-444b28c3.js";import{u as C}from"./index-e305bb62.js";let w=0;const B=y({name:"ImgEmpty",setup(){return{ns:h("empty"),id:++w}}}),N={viewBox:"0 0 79 86",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"},V=["id"],E=["stop-color"],R=["stop-color"],S=["id"],G=["stop-color"],b=["stop-color"],z=["id"],I={id:"Illustrations",stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},D={id:"B-type",transform:"translate(-1268.000000, -535.000000)"},M={id:"Group-2",transform:"translate(1268.000000, 535.000000)"},H=["fill"],L=["fill"],O={id:"Group-Copy",transform:"translate(34.500000, 31.500000) scale(-1, 1) rotate(-25.000000) translate(-34.500000, -31.500000) translate(7.000000, 10.000000)"},P=["fill"],T=["fill"],U=["fill"],Z=["fill"],j=["fill"],q={id:"Rectangle-Copy-17",transform:"translate(53.000000, 45.000000)"},A=["fill","xlink:href"],F=["fill","mask"],J=["fill"];function K(e,r,p,t,d,m){return n(),a("svg",N,[s("defs",null,[s("linearGradient",{id:`linearGradient-1-${e.id}`,x1:"38.8503086%",y1:"0%",x2:"61.1496914%",y2:"100%"},[s("stop",{"stop-color":`var(${e.ns.cssVarBlockName("fill-color-1")})`,offset:"0%"},null,8,E),s("stop",{"stop-color":`var(${e.ns.cssVarBlockName("fill-color-4")})`,offset:"100%"},null,8,R)],8,V),s("linearGradient",{id:`linearGradient-2-${e.id}`,x1:"0%",y1:"9.5%",x2:"100%",y2:"90.5%"},[s("stop",{"stop-color":`var(${e.ns.cssVarBlockName("fill-color-1")})`,offset:"0%"},null,8,G),s("stop",{"stop-color":`var(${e.ns.cssVarBlockName("fill-color-6")})`,offset:"100%"},null,8,b)],8,S),s("rect",{id:`path-3-${e.id}`,x:"0",y:"0",width:"17",height:"36"},null,8,z)]),s("g",I,[s("g",D,[s("g",M,[s("path",{id:"Oval-Copy-2",d:"M39.5,86 C61.3152476,86 79,83.9106622 79,81.3333333 C79,78.7560045 57.3152476,78 35.5,78 C13.6847524,78 0,78.7560045 0,81.3333333 C0,83.9106622 17.6847524,86 39.5,86 Z",fill:`var(${e.ns.cssVarBlockName("fill-color-3")})`},null,8,H),s("polygon",{id:"Rectangle-Copy-14",fill:`var(${e.ns.cssVarBlockName("fill-color-7")})`,transform:"translate(27.500000, 51.500000) scale(1, -1) translate(-27.500000, -51.500000) ",points:"13 58 53 58 42 45 2 45"},null,8,L),s("g",O,[s("polygon",{id:"Rectangle-Copy-10",fill:`var(${e.ns.cssVarBlockName("fill-color-7")})`,transform:"translate(11.500000, 5.000000) scale(1, -1) translate(-11.500000, -5.000000) ",points:"2.84078316e-14 3 18 3 23 7 5 7"},null,8,P),s("polygon",{id:"Rectangle-Copy-11",fill:`var(${e.ns.cssVarBlockName("fill-color-5")})`,points:"-3.69149156e-15 7 38 7 38 43 -3.69149156e-15 43"},null,8,T),s("rect",{id:"Rectangle-Copy-12",fill:`url(#linearGradient-1-${e.id})`,transform:"translate(46.500000, 25.000000) scale(-1, 1) translate(-46.500000, -25.000000) ",x:"38",y:"7",width:"17",height:"36"},null,8,U),s("polygon",{id:"Rectangle-Copy-13",fill:`var(${e.ns.cssVarBlockName("fill-color-2")})`,transform:"translate(39.500000, 3.500000) scale(-1, 1) translate(-39.500000, -3.500000) ",points:"24 7 41 7 55 -3.63806207e-12 38 -3.63806207e-12"},null,8,Z)]),s("rect",{id:"Rectangle-Copy-15",fill:`url(#linearGradient-2-${e.id})`,x:"13",y:"45",width:"40",height:"36"},null,8,j),s("g",q,[s("use",{id:"Mask",fill:`var(${e.ns.cssVarBlockName("fill-color-8")})`,transform:"translate(8.500000, 18.000000) scale(-1, 1) translate(-8.500000, -18.000000) ","xlink:href":`#path-3-${e.id}`},null,8,A),s("polygon",{id:"Rectangle-Copy",fill:`var(${e.ns.cssVarBlockName("fill-color-9")})`,mask:`url(#mask-4-${e.id})`,transform:"translate(12.000000, 9.000000) scale(-1, 1) translate(-12.000000, -9.000000) ",points:"7 0 24 0 20 18 7 16.5"},null,8,F)]),s("polygon",{id:"Rectangle-Copy-18",fill:`var(${e.ns.cssVarBlockName("fill-color-2")})`,transform:"translate(66.000000, 51.500000) scale(-1, 1) translate(-66.000000, -51.500000) ",points:"62 45 79 45 70 58 53 58"},null,8,J)])])])])}var Q=u(B,[["render",K],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/empty/src/img-empty.vue"]]);const W={image:{type:String,default:""},imageSize:Number,description:{type:String,default:""}},X=["src"],Y={key:1},x={name:"ElEmpty"},s0=y({...x,props:W,setup(e){const r=e,{t:p}=C(),t=h("empty"),d=f(()=>r.description||p("el.table.emptyText")),m=f(()=>({width:r.imageSize?`${r.imageSize}px`:""}));return(l,l0)=>(n(),a("div",{class:i(o(t).b())},[s("div",{class:i(o(t).e("image")),style:_(o(m))},[l.image?(n(),a("img",{key:0,src:l.image,ondragstart:"return false"},null,8,X)):c(l.$slots,"image",{key:1},()=>[g(Q)])],6),s("div",{class:i(o(t).e("description"))},[l.$slots.description?c(l.$slots,"description",{key:0}):(n(),a("p",Y,k(o(d)),1))],2),l.$slots.default?(n(),a("div",{key:0,class:i(o(t).e("bottom"))},[c(l.$slots,"default")],2)):$("v-if",!0)],2))}});var e0=u(s0,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/empty/src/empty.vue"]]);const n0=v(e0);export{n0 as E};
