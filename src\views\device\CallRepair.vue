<template>
    <div>
        <el-card>
            <el-form :inline="true" ref="form" :model="form" class="demo-form-inline">
                <el-form-item label="报修单号">
                    <el-input v-model="form.oid" placeholder="报修单号"></el-input>
                </el-form-item>
                <el-form-item label="设备编号">
                    <el-input v-model="form.devicenumber" placeholder="设备编号"></el-input>
                </el-form-item>
                <el-form-item label="维修人">
                    <el-input v-model="form.username" placeholder="维修人姓名"></el-input>
                </el-form-item>
                <el-form-item label="部门">
                    <!-- 区域选择 -->
                    <el-select v-model="form.depart" placeholder="请选择部门">
                        <el-option label="设备部门" value="设备部门"></el-option>
                        <el-option label="织造车间" value="织造车间"></el-option>
                        <el-option label="整理车间" value="整理车间"></el-option>
                        <el-option label="检验车间" value="检验车间"></el-option>
                        <el-option label="行政办公室" value="行政办公室"></el-option>
                        <el-option label="打样车间" value="打样车间"></el-option>
                        <el-option label="综合维修" value="综合维修"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSubmit('form')" icon="el-icon-search">查询</el-button>
                </el-form-item>
                <!-- 重置按钮 -->
                <el-form-item>
                    <el-button type="button" @click="onReset('form')" icon="el-icon-refresh">重置</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="success" @click="callRepair" icon="el-icon-phone">报修</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" plain @click="refresh" icon="el-icon-refresh-right"></el-button>
                </el-form-item>
            </el-form>
            <!-- 内容主体 -->
            <el-table :data="tableData" style="width: 100%" stripe v-loading="loadings" element-loading-text="Flyknit">
                <el-table-column type="index" width="80"></el-table-column>
                <el-table-column prop="oid" label="报修单号" width="310">
                </el-table-column>
                <el-table-column prop="device_name" label="设备名称">
                </el-table-column>
                <el-table-column prop="device_number" label="设备编号">
                </el-table-column>
                <el-table-column prop="content" label="报修问题">
                </el-table-column>
                <el-table-column prop="department" label="报修部门" width="100">
                </el-table-column>
                <el-table-column prop="post_user" label="报修人员" width="100">
                </el-table-column>
                <el-table-column prop="create_time" label="报修日期" width="180">
                </el-table-column>
                <el-table-column prop="repair_depart" label="维修部门" width="100">
                </el-table-column>
                <!-- 维修类型，送出维修，内部维修，返厂维修 -->
                <el-table-column prop="repair_way" label="维修类型" width="120"></el-table-column>
                <el-table-column prop="status" label="状态">
                    <!-- 维修状态，待维修，维修中，维修完成 -->
                    <template slot-scope="scope">
                        <el-button size="mini" v-if="scope.row.status === 0" type="warning">待维修</el-button>
                        <el-button size="mini" v-else-if="scope.row.status === 1" type="info">维修中</el-button>
                        <el-button size="mini" v-else type="success">已完成</el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="action" label="操作">
                    <template slot-scope="scope">
                        <el-button type="primary" plain size="mini" @click="showDetail(scope.row)"
                            icon="el-icon-more"></el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
                :page-sizes="[12, 16, 20, 24]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="total">
            </el-pagination>
        </el-card>
        <!-- 报修弹窗表单 -->
        <el-dialog title="部门报修" :visible.sync="dialogFormVisible">
            <el-form ref="forms" :model="forms" :rules="rules" label-width="80px">
                <el-form-item label="设备名称" prop="devicename">
                    <el-input v-model="forms.devicename" placeholder="请输入要维修的设备名称"></el-input>
                </el-form-item>
                <el-form-item label="设备编号" prop="devicenumber">
                    <el-input v-model="forms.devicenumber" placeholder="请输入要维修的设备编号"></el-input>
                </el-form-item>
                <el-form-item label="报修问题" prop="desc">
                    <el-input type="textarea" v-model="forms.desc" placeholder="请描述故障问题，越详细维修的越快"></el-input>
                </el-form-item>
                <el-form-item label="现场图片">
                    <el-upload :action="action" :headers="headers" list-type="picture-card" name="files"
                        :class="logo.length === 5 ? 'hide' : 'no-hide'" accept=".jpg,.png,.webp,.jfif" :limit="5"
                        :on-remove="logoRemove" :on-success="logoSuccess" :on-preview="handlepreview" :multiple="true"
                        :on-error="onErr" :before-upload="project" :file-list="logo">
                        <i class="el-icon-plus"></i>
                    </el-upload>
                    <!-- 大图展开 -->
                    <el-dialog :visible.sync="dialogVisible" :modal="false">
                        <img width="100%" :src="dialogImageUrl" alt="上传失败" />
                    </el-dialog>
                </el-form-item>
                <el-form-item label="报修人员">
                    <el-input v-model="User.username" readonly></el-input>
                </el-form-item>
                <el-form-item label="维修部门" prop="departs">
                    <el-select v-model="forms.departs" placeholder="请选择部门">
                        <el-option label="IT设备部" value="IT设备部"></el-option>
                        <el-option label="织造车间" value="织造车间"></el-option>
                        <el-option label="整理车间" value="整理车间"></el-option>
                        <el-option label="检验车间" value="检验车间"></el-option>
                        <el-option label="行政办公室" value="行政办公室"></el-option>
                        <el-option label="打样车间" value="打样车间"></el-option>
                        <el-option label="综合维修" value="综合维修"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="维修方式" prop="way">
                    <el-radio-group v-model="forms.way">
                        <el-radio label="送出维修"></el-radio>
                        <el-radio label="内部维修"></el-radio>
                        <el-radio label="返厂维修"></el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="紧急程度" prop="emergency">
                    <el-radio-group v-model="forms.emergency">
                        <el-radio label="正常"></el-radio>
                        <el-radio label="很急"></el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="callNow('forms')" icon="el-icon-phone">立即报修</el-button>
                    <el-button icon="el-icon-refresh" @click="resetTable('forms')">重置表单</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>
<script>
import { mapState } from 'vuex'
import config from '../../common/config'
export default {
    data() {
        return {
            loadings: true,
            rules: {
                devicename: [{
                    required: true,
                    message: '请输入设备名称',
                    trigger: 'blur'
                }],
                devicenumber: [{
                    required: true,
                    message: '请输入设备编号',
                    trigger: 'blur'
                }],
                desc: [{
                    required: true,
                    message: '请输入报修问题',
                    trigger: 'blur'
                }],
                departs: [{
                    required: true,
                    message: '请选择报修部门',
                    trigger: 'blur'
                }],
                way: [{
                    required: true,
                    message: '请选择维修方式',
                    trigger: 'blur'
                }],
                emergency: [{
                    required: true,
                    message: '请选择紧急程度',
                    trigger: 'blur'
                }]
            },
            form: {
                oid: '',
                devicenumber: '',
                depart: '',
                username: ''
            },
            forms: {
                devicename: '',
                devicenumber: '',
                desc: '',
                departs: '',
                way: '',
                emergency: ''
            },
            action: config.uploadURL, // 上传地址
            baseURL: config.imgPrefix,
            headers: {
                Authorization: `Bearer ${localStorage.getItem("accessToken")}`
            },
            logo: [],
            dialogImageUrl: '',
            dialogVisible: false,
            dialogFormVisible: false,
            total: 20,
            currentPage: 1,
            pageSize: 12,
            tableData: [],
            loadmen: false
        }
    },
    computed: {
        ...mapState({
            User: state => state.users,
        })
    },
    mounted() {
        this.getRepairList()
    },
    methods: {
        // 跳转详情页
        showDetail(row) {
            console.log('row', row);
            this.$router.push({
                path: '/details',
                query: {
                    datas: row
                }
            })
        },
        // 获取维修单列表
        async getRepairList() {
            try {
                this.form.page = this.currentPage
                this.form.pageSize = this.pageSize
                const res = await this.$http.getRepairLists(this.form)
                if (res.status !== 200) {
                    return this.$message.error(res.message)
                } else {
                    const { list, total } = res.data;
                    this.tableData = list
                    this.total = total
                    setTimeout(() => {
                        this.loadings = false
                    }, 500)
                }
            } catch (error) {
                this.loadings = false
                return this.$message.error('服务器错误，请稍后重试！')
            }
        },
        // 重置表单
        resetTable(formName) {
            this.$refs[formName].resetFields();
        },
        // 查询
        onSubmit(formName) {
            // 如果不满足form 表单的验证规则，就不执行查询
            this.$refs[formName].validate((valid) => {
                if (!valid) {
                    return false;
                } else {
                    this.loadings = true
                    this.getRepairList()
                }
            });
        },
        callRepair() {
            this.dialogFormVisible = true;
        },
        async callNow(formName) {
            this.loadings = true
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    // 定义一个数组获取this.logo中的url
                    let arr = []
                    this.logo.forEach(item => {
                        arr.push(item.url)
                    })
                    // 将图片logo转换为字符串以'-'相连
                    this.forms.imglist = arr.join('-');
                    const res = await this.$http.callRepairs(this.forms)
                    if (res.status !== 200) {
                        this.loadings = false
                        return this.$message.error(res.message)
                    } else {
                        this.loadings = false
                        this.$message.success(res.message);
                        this.dialogFormVisible = false;
                        this.$refs[formName].resetFields();
                        this.logo = []
                        this.getRepairList()
                    }
                } else {
                    return false;
                }
            });
        },
        onReset(formName) {
            this.form.oid = ''
            this.form.devicenumber = ''
            this.form.depart = ''
            this.form.username = ''
        },
        handleSizeChange(val) {
            this.loadings = true
            this.pageSize = val
            this.getRepairList()
        },
        handleCurrentChange(val) {
            this.loadings = true
            this.currentPage = val
            this.getRepairList()
        },
        handleEdit(index, row) {
            console.log(index, row);
        },
        handleDelete(index, row) {
            console.log(index, row);
        },
        // 展开大图
        handlepreview(file) {
            this.dialogImageUrl = file.url;
            this.dialogVisible = true;
        },
        // 上传失败
        onErr(e) {
            this.loadmen = false;
            this.$message.error("上传失败,尝试重新上传");
        },
        // 上传时
        project(file) {
            this.loadmen = true;
        },
        // logo移除文件时的钩子
        logoRemove(file, fileList) {
            const { uid } = file;
            const index = this.logo.findIndex(item => item.uid === uid);
            this.logo.splice(index, 1);
        },
        // 上传成功：logo
        logoSuccess(res, file, fileList) {
            const { url } = res.data;
            this.logo.push({ url: this.baseURL + url, uid: file.uid })//element展示图片时需要数组类型的才能展示
            this.loadmen = false;
        },
        // 刷新
        refresh() {
            this.loadings = true
            this.getRepairList()
        }

    }
}
</script>
<style>
.el-card__body {
    width: 100%;
}

.el-pagination {
    margin-top: 20px;
}

.btn-area .el-button {
    position: relative;
    left: -70px;
    top: 20px;
}

.hide /deep/ .el-upload--picture-card {
    display: none;
}

.no-hide /deep/ .el-upload--picture-card {
    display: inline-block;
}
</style>