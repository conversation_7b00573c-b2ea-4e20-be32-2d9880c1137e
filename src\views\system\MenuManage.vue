<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-08-18 09:52:29
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-21 15:36:37
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\system\MenuManage.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-card>
            <div slot="header" class="header">
                <el-button type="primary" size="small" @click="addMenu" icon="el-icon-plus">添加菜单</el-button>
                <!-- 刷新按钮 -->
                <el-button type="primary" size="small" @click="refreshList" icon="el-icon-refresh">刷新</el-button>
            </div>

            <!-- 表格，菜单管理，名称，action，icon，排序，显示，类型，操作 -->
            <el-table :data="tableData" style="width: 100%" v-loading="loading" element-loading-text="Flyknit">
                <el-table-column type="expand">
                    <template slot-scope="scope">
                        <div class="menu-list" v-if="scope.row.subMenu.length > 0">
                            <h3 style="padding: 20px 0;color:#409EFF;"> <i class="el-icon-menu"></i> 二级菜单列表</h3>
                            <el-row v-for="(item, index) in scope.row.subMenu" :key="index" class="menu-list-item">
                                <el-col :span="24">
                                    <el-col :span="3">{{ item.title }}</el-col>
                                    <el-col :span="3">{{ item.router }}</el-col>
                                    <el-col :span="3"><template>
                                            <span :class="item.icon" style="color: #409EFF;font-size:18px;"></span>
                                        </template></el-col>
                                    <el-col :span="3">
                                        <template>
                                            <el-switch v-model="item.status" disabled :active-value="1" :inactive-value="0">
                                            </el-switch>
                                        </template>
                                    </el-col>
                                    <el-col :span="3">{{ item.sort }}</el-col>
                                    <el-col :span="2"><el-button type="primary" size="mini"
                                            @click="editUrl(scope.row, item)" icon="el-icon-edit">编辑</el-button></el-col>
                                    <el-col :span="2"><el-button type="danger" size="mini" @click="delUrl(item.id)"
                                            icon="el-icon-delete">删除</el-button></el-col>
                                </el-col>
                            </el-row>
                        </div>
                        <div v-else class="no-router">
                            <h5>啊哦~暂无二级菜单...</h5>
                            <el-button type="success" size="mini" @click="addUrl(scope.row)"
                                icon="el-icon-right">去添加</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="名称" prop="title">
                </el-table-column>
                <el-table-column label="路由" prop="router">
                </el-table-column>
                <el-table-column label="图标" prop="icon">
                    <template slot-scope="scope">
                        <span :class="scope.row.icon" style="color: #409EFF;font-size:25px;"></span>
                    </template>
                </el-table-column>
                <el-table-column label="显示" prop="status">
                    <template slot-scope="scope">
                        <el-switch v-model="scope.row.status" disabled :active-value="1" :inactive-value="0">
                        </el-switch>
                    </template>
                </el-table-column>
                <el-table-column label="排序" prop="sort">
                </el-table-column>
                <el-table-column label="创建时间" prop="create_time">
                </el-table-column>
                <el-table-column label="更新时间" prop="update_time">
                </el-table-column>
                <el-table-column label="操作" width="300">
                    <template slot-scope="scope">
                        <el-button type="success" size="mini" @click="addUrl(scope.row)"
                            icon="el-icon-plus">添加子菜单</el-button>
                        <el-button type="primary" size="mini" @click="edit(scope.row)" icon="el-icon-edit">编辑</el-button>
                        <el-button type="danger" size="mini" @click="del(scope.row)" icon="el-icon-delete">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
                :page-sizes="[10, 20, 30, 40, 50]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="total">
            </el-pagination>
        </el-card>
        <!-- 添加一级菜单 -->
        <el-drawer :title="title" :visible.sync="drawer" :direction="direction">
            <el-form :model="addMenuForm" :rules="addMenuRules" ref="addMenuForm" label-width="80px" class="addMenuForm">
                <el-form-item label="菜单名称" prop="title">
                    <el-input v-model="addMenuForm.title" placeholder="菜单标题名称"></el-input>
                </el-form-item>
                <el-form-item label="路由地址" prop="router">
                    <el-input v-model="addMenuForm.router" placeholder="页面路径"></el-input>
                </el-form-item>
                <el-form-item label="图标" prop="icon">
                    <el-input v-model="addMenuForm.icon" placeholder="展示图标，支持element-UI图标库"></el-input>
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                    <el-input v-model="addMenuForm.sort" placeholder="排序"></el-input>
                </el-form-item>
                <el-form-item label="是否显示" prop="status">
                    <el-switch v-model="addMenuForm.status" :active-value="1" :inactive-value="0"
                        @change="changeHandler"></el-switch>
                </el-form-item>
                <el-form-item>
                    <el-button @click="drawer = false">取 消</el-button>
                    <el-button type="primary" @click="addMenuSubmit">{{ btnText }}</el-button>
                </el-form-item>
            </el-form>

        </el-drawer>
        <!-- 添加二级菜单弹窗 -->
        <el-dialog :title="titleText" :visible.sync="addUrlDialog" width="30%">
            <el-form :model="addUrlForm" :rules="addUrlRules" ref="addUrlForm" label-width="80px" class="addUrlForm">
                <el-form-item label="父级菜单">
                    <el-input v-model="menuName" readonly disabled></el-input>
                </el-form-item>
                <el-form-item label="菜单名称" prop="title">
                    <el-input v-model="addUrlForm.title" placeholder="菜单标题名称"></el-input>
                </el-form-item>
                <el-form-item label="路由地址" prop="router">
                    <el-input v-model="addUrlForm.router" placeholder="页面路径"></el-input>
                </el-form-item>
                <el-form-item label="图标" prop="icon">
                    <el-input v-model="addUrlForm.icon" placeholder="展示图标，支持element-UI图标库"></el-input>
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                    <el-input v-model="addUrlForm.sort" placeholder="排序"></el-input>
                </el-form-item>
                <el-form-item label="是否显示" prop="status">
                    <el-switch v-model="addUrlForm.status" :active-value="1" :inactive-value="0"
                        @change="changeStatus"></el-switch>
                </el-form-item>
                <el-form-item>
                    <el-button @click="addUrlDialog = false">取 消</el-button>
                    <el-button type="primary" @click="verifyForm">{{ btnText1 }}</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>
<script>
export default {
    data() {
        return {
            titleText: '添加子菜单',
            btnText1: '添 加',
            menuName: '',
            menuId: '',
            addUrlDialog: false,
            loading: true,
            drawer: false,
            direction: 'rtl',
            tableData: [
                {
                    name: '设备管理',
                    action: 'device',
                    icon: 'el-icon-s-tools',
                    sort: 1,
                    status: 1,
                    create_time: '2020-01-01 00:00:00',
                    update_time: '2020-01-01 00:00:00',
                    subMenu: [{
                        id: 1,
                        title: '设备列表',
                        router: 'deviceList',
                        icon: 'el-icon-s-tools',
                        sort: 1,
                        status: 1,
                    }, {
                        id: 2,
                        title: '设备调拨',
                        router: 'deviceTransfer',
                        icon: 'el-icon-s-tools',
                        sort: 2,
                        status: 1,
                    }, {
                        id: 3,
                        title: '采购管理',
                        router: 'purchaseManage',
                        icon: 'el-icon-s-tools',
                        sort: 3,
                        status: 1,
                    }]
                },
                {
                    name: '设备管理',
                    action: 'device',
                    icon: 'el-icon-s-tools',
                    sort: 1,
                    status: 1,
                    create_time: '2020-01-01 00:00:00',
                    update_time: '2020-01-01 00:00:00',
                    subMenu: [{
                        id: 1,
                        title: '设备列表',
                        router: 'deviceList',
                        icon: 'el-icon-s-tools',
                        sort: 1,
                        status: 1,
                    }, {
                        id: 2,
                        title: '设备调拨',
                        router: 'deviceTransfer',
                        icon: 'el-icon-s-tools',
                        sort: 2,
                        status: 1,
                    }, {
                        id: 3,
                        title: '采购管理',
                        router: 'purchaseManage',
                        icon: 'el-icon-s-tools',
                        sort: 3,
                        status: 1,
                    }]
                }
            ],
            currentPage: 1,
            pageSize: 10,
            total: 3,
            addMenuForm: {
                title: '',
                router: '',
                icon: '',
                sort: '',
                status: 1
            },
            addMenuRules: {
                title: [
                    { required: true, message: '请输入菜单名称', trigger: 'blur' },
                    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
                ],
                router: [
                    { required: true, message: '请输入路由地址', trigger: 'blur' },
                    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
                ],
                icon: [
                    { required: true, message: '请输入图标', trigger: 'blur' },
                    { min: 2, max: 40, message: '长度在 2 到 40 个字符', trigger: 'blur' }
                ],
                sort: [
                    { required: true, message: '请输入排序', trigger: 'blur' }
                ]
            },
            btnText: '添 加',
            title: '添加菜单',
            addUrlForm: {
                title: '',
                router: '',
                icon: '',
                sort: '',
                status: 1
            },
            addUrlRules: {
                title: [
                    { required: true, message: '请输入菜单名称', trigger: 'blur' },
                    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
                ],
                router: [
                    { required: true, message: '请输入路由地址', trigger: 'blur' },
                    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
                ],
                icon: [
                    { required: true, message: '请输入图标', trigger: 'blur' },
                    { min: 2, max: 40, message: '长度在 2 到 40 个字符', trigger: 'blur' }
                ],
                sort: [
                    { required: true, message: '请输入排序', trigger: 'blur' }
                ]
            }
        }
    },
    created() {
        this.getMenuList();
    },
    methods: {
        // 刷新列表
        refreshList() {
            this.loading = true;
            this.getMenuList();
        },
        // 获取菜单列表
        async getMenuList() {
            try {
                const res = await this.$http.getMenuLists();
                if (res.status !== 200) {
                    return this.$message.error(res.message);
                }
                const { list, total } = res.data;
                this.tableData = list;
                this.total = total;
                setTimeout(() => {
                    this.loading = false;
                }, 500);
            } catch (error) {
                this.loading = false;
                return this.$message.error(error.message);
            }
        },
        // 添加菜单方法
        addMenuSubmit() {
            // 验证表单
            this.$refs.addMenuForm.validate((valid) => {
                if (valid) {
                    // 验证通过
                    this.addMenus();
                } else {
                    // 验证不通过
                    return false;
                }
            });
        },
        // 全局添加一级菜单方法
        async addMenus() {
            try {
                const res = await this.$http.addMenu(this.addMenuForm);
                if (res.status === 200) {
                    this.$message.success(res.message);
                    this.drawer = false;
                    this.$refs.addMenuForm.resetFields();
                    this.getMenuList();
                } else {
                    this.$message.error(res.message);
                }
            } catch (error) {
                return this.$message.error(error.message);
            }
        },
        // 子菜单编辑弹窗开关
        editUrl(data, row) {
            this.titleText = '编辑子菜单';
            this.btnText1 = '修 改';
            this.menuName = data.title;
            this.menuId = data.id;
            this.addUrlDialog = true
            this.addUrlForm = row
        },
        // 删除子菜单
        async delUrl(id) {
            try {
                this.$confirm('删除该菜单后将不再显示在系统中, 是否继续?', '温馨提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    const res = await this.$http.delChildMenu({ id });
                    if (res.status !== 200) {
                        return this.$message.error(res.message);
                    }
                    this.$message.success(res.message);
                    this.getMenuList();
                }).catch(() => {
                    this.$message.info('已取消删除');
                });
            } catch (error) {
                return this.$message.error(error.message);
            }
        },
        // 编辑一级菜单
        edit(data) {
            this.title = '编辑菜单';
            this.btnText = '修 改';
            this.drawer = true;
            this.addMenuForm = data;
        },
        // 删除一级菜单
        async del(row) {
            try {
                this.$confirm('删除该菜单后系统将无法正常运行, 是否继续?', '温馨提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    const res = await this.$http.delMenu({ id: row.id });
                    if (res.status === 200) {
                        this.$message.success(res.message);
                        this.getMenuList();
                    } else {
                        this.$message.error(res.message);
                    }
                }).catch(() => {
                    this.$message.info('已取消删除');
                });
            } catch (error) {
                return this.$message.error(error.message);
            }
        },
        // 添加子菜单弹窗
        addUrl(row) {
            this.addUrlDialog = true;
            this.menuName = row.title;
            this.menuId = row.id;
            this.titleText = '添加子菜单';
            this.btnText1 = '添 加';
            this.addUrlForm = {
                title: '',
                router: '',
                icon: '',
                sort: '',
                status: 1
            }
        },
        // 添加二级菜单验证
        verifyForm() {
            this.$refs.addUrlForm.validate((valid) => {
                if (valid) {
                    // 验证通过
                    this.addSecondMenus();
                } else {
                    // 验证不通过
                    return false;
                }
            });
        },
        // 全局添加二级菜单方法
        async addSecondMenus() {
            try {
                this.addUrlForm.pid = this.menuId;
                const res = await this.$http.addChildMenu(this.addUrlForm);
                if (res.status !== 200) {
                    return this.$message.error(res.message);
                }
                this.$message.success(res.message);
                this.addUrlDialog = false;
                this.$refs.addUrlForm.resetFields();
                this.getMenuList();
            } catch (error) {
                return this.$message.error(error.message);
            }
        },
        // 添加二级菜单status切换
        changeStatus(val) {
            this.addUrlForm.status = val ? 1 : 0;
        },
        // 分页
        handleSizeChange(val) {
            this.loading = true;
            this.pageSize = val;
            this.getMenuList();
        },
        // 跳转到第几页
        handleCurrentChange(val) {
            this.loading = true;
            this.currentPage = val;
            this.getMenuList();
        },
        // 打开添加一级菜单弹窗
        addMenu() {
            this.drawer = true;
            this.title = '添加菜单';
            this.btnText = '添 加';
            // 初始化表单
            this.addMenuForm = {
                title: '',
                router: '',
                icon: '',
                sort: '',
                status: 1
            };
        },
        // 是否显示
        changeHandler(val) {
            this.addMenuForm.status = val ? 1 : 0;
        }
    }
}
</script>
<style lang="scss">
.no-router {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 30px 0;

    h5 {
        color: #999999;
        margin: 20px 0;
    }
}

.addMenuForm {
    padding: 0 10px;
}

.menu-list {
    display: flex;
    flex-direction: column;
    padding: 0 0px;

    .menu-list-item {
        padding: 10px 0;

        .el-col {
            background-color: #eee;
            padding: 6px 0;
            text-align: center;
        }
    }
}

.header {
    display: flex;
    justify-content: flex-start;

    .el-button {
        margin-left: 10px;
    }

    .demo-table-expand {
        font-size: 0;
    }

    .demo-table-expand label {
        width: 90px;
        color: #99a9bf;
    }

    .demo-table-expand .el-form-item {
        margin-right: 0;
        margin-bottom: 0;
        width: 50%;
    }
}

.el-pagination {
    margin: 10px;
}
</style>