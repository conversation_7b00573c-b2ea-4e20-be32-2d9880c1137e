import{c as d,ab as $e,bH as Ke,i as V,a7 as Se,Q as je,a_ as Ue,aw as J,aa as Ce,ax as We,aS as Z,_ as _e,C as Xe,aP as Ye,b0 as qe,a$ as Qe,bI as Ge,R as Ie,aD as ee,bJ as Je,bK as Ze,bL as et,bM as tt,a8 as te,G as k,k as at,bN as ot,bh as st,e as nt,a3 as lt,o as c,g as x,f as v,F as ae,H as y,n as t,S as j,m as N,a as w,w as B,L as U,a5 as O,aQ as oe,b as rt,b9 as it,y as ut,bO as ct,t as W,ad as dt,av as pt,bz as Ee,U as ft}from"./index-444b28c3.js";import{U as se,i as vt,d as ze}from"./event-fe80fd0c.js";import{u as mt,b as yt}from"./index-4d7f16ce.js";const ht=o=>/([(\uAC00-\uD7AF)|(\u3130-\u318F)])+/gi.test(o),bt=["class","style"],gt=/^on[A-Z]/,xt=(o={})=>{const{excludeListeners:m=!1,excludeKeys:l}=o,a=d(()=>((l==null?void 0:l.value)||[]).concat(bt)),i=$e();return i?d(()=>{var p;return Ke(Object.entries((p=i.proxy)==null?void 0:p.$attrs).filter(([r])=>!a.value.includes(r)&&!(m&&gt.test(r))))}):d(()=>({}))};function wt(o){const m=V();function l(){if(o.value==null)return;const{selectionStart:i,selectionEnd:p,value:r}=o.value;if(i==null||p==null)return;const g=r.slice(0,Math.max(0,i)),u=r.slice(Math.max(0,p));m.value={selectionStart:i,selectionEnd:p,value:r,beforeTxt:g,afterTxt:u}}function a(){if(o.value==null||m.value==null)return;const{value:i}=o.value,{beforeTxt:p,afterTxt:r,selectionStart:g}=m.value;if(p==null||r==null||g==null)return;let u=i.length;if(i.endsWith(r))u=i.length-r.length;else if(i.startsWith(p))u=p.length;else{const h=p[g-1],S=i.indexOf(h,g-1);S!==-1&&(u=S+1)}o.value.setSelectionRange(u,u)}return[l,a]}let b;const St=`
  height:0 !important;
  visibility:hidden !important;
  overflow:hidden !important;
  position:absolute !important;
  z-index:-1000 !important;
  top:0 !important;
  right:0 !important;
`,Ct=["letter-spacing","line-height","padding-top","padding-bottom","font-family","font-weight","font-size","text-rendering","text-transform","width","text-indent","padding-left","padding-right","border-width","box-sizing"];function It(o){const m=window.getComputedStyle(o),l=m.getPropertyValue("box-sizing"),a=Number.parseFloat(m.getPropertyValue("padding-bottom"))+Number.parseFloat(m.getPropertyValue("padding-top")),i=Number.parseFloat(m.getPropertyValue("border-bottom-width"))+Number.parseFloat(m.getPropertyValue("border-top-width"));return{contextStyle:Ct.map(r=>`${r}:${m.getPropertyValue(r)}`).join(";"),paddingSize:a,borderSize:i,boxSizing:l}}function Pe(o,m=1,l){var a;b||(b=document.createElement("textarea"),document.body.appendChild(b));const{paddingSize:i,borderSize:p,boxSizing:r,contextStyle:g}=It(o);b.setAttribute("style",`${g};${St}`),b.value=o.value||o.placeholder||"";let u=b.scrollHeight;const h={};r==="border-box"?u=u+p:r==="content-box"&&(u=u-i),b.value="";const S=b.scrollHeight-i;if(Se(m)){let f=S*m;r==="border-box"&&(f=f+i+p),u=Math.max(f,u),h.minHeight=`${f}px`}if(Se(l)){let f=S*l;r==="border-box"&&(f=f+i+p),u=Math.min(f,u)}return h.height=`${u}px`,(a=b.parentNode)==null||a.removeChild(b),b=void 0,h}const Et=je({id:{type:String,default:void 0},size:Ue,disabled:Boolean,modelValue:{type:J([String,Number,Object]),default:""},type:{type:String,default:"text"},resize:{type:String,values:["none","both","horizontal","vertical"]},autosize:{type:J([Boolean,Object]),default:!1},autocomplete:{type:String,default:"off"},formatter:{type:Function},parser:{type:Function},placeholder:{type:String},form:{type:String,default:""},readonly:{type:Boolean,default:!1},clearable:{type:Boolean,default:!1},showPassword:{type:Boolean,default:!1},showWordLimit:{type:Boolean,default:!1},suffixIcon:{type:Ce},prefixIcon:{type:Ce},containerRole:{type:String,default:void 0},label:{type:String,default:void 0},tabindex:{type:[String,Number],default:0},validateEvent:{type:Boolean,default:!0},inputStyle:{type:J([Object,Array,String]),default:()=>We({})}}),zt={[se]:o=>Z(o),input:o=>Z(o),change:o=>Z(o),focus:o=>o instanceof FocusEvent,blur:o=>o instanceof FocusEvent,clear:()=>!0,mouseleave:o=>o instanceof MouseEvent,mouseenter:o=>o instanceof MouseEvent,keydown:o=>o instanceof Event,compositionstart:o=>o instanceof CompositionEvent,compositionupdate:o=>o instanceof CompositionEvent,compositionend:o=>o instanceof CompositionEvent},Pt=["role"],$t=["id","type","disabled","formatter","parser","readonly","autocomplete","tabindex","aria-label","placeholder"],kt=["id","tabindex","disabled","readonly","autocomplete","aria-label","placeholder"],Nt={name:"ElInput",inheritAttrs:!1},Vt=Xe({...Nt,props:Et,emits:zt,setup(o,{expose:m,emit:l}){const a=o,i={suffix:"append",prefix:"prepend"},p=$e(),r=Ye(),g=qe(),u=d(()=>{const e={};return a.containerRole==="combobox"&&(e["aria-haspopup"]=r["aria-haspopup"],e["aria-owns"]=r["aria-owns"],e["aria-expanded"]=r["aria-expanded"]),e}),h=xt({excludeKeys:d(()=>Object.keys(u.value))}),{form:S,formItem:f}=mt(),{inputId:ne}=yt(a,{formItemContext:f}),ke=Qe(),z=Ge(),s=Ie("input"),le=Ie("textarea"),R=ee(),P=ee(),T=V(!1),_=V(!1),A=V(!1),D=V(!1),re=V(),X=ee(a.inputStyle),F=d(()=>R.value||P.value),ie=d(()=>{var e;return(e=S==null?void 0:S.statusIcon)!=null?e:!1}),M=d(()=>(f==null?void 0:f.validateState)||""),ue=d(()=>M.value&&Je[M.value]),Ne=d(()=>D.value?Ze:et),Ve=d(()=>[r.style,a.inputStyle]),ce=d(()=>[a.inputStyle,X.value,{resize:a.resize}]),C=d(()=>vt(a.modelValue)?"":String(a.modelValue)),H=d(()=>a.clearable&&!z.value&&!a.readonly&&!!C.value&&(T.value||_.value)),Y=d(()=>a.showPassword&&!z.value&&!a.readonly&&!!C.value&&(!!C.value||T.value)),$=d(()=>a.showWordLimit&&!!h.value.maxlength&&(a.type==="text"||a.type==="textarea")&&!z.value&&!a.readonly&&!a.showPassword),q=d(()=>Array.from(C.value).length),Te=d(()=>!!$.value&&q.value>Number(h.value.maxlength)),Ae=d(()=>!!g.suffix||!!a.suffixIcon||H.value||a.showPassword||$.value||!!M.value&&ie.value),[Fe,Me]=wt(R);tt(P,e=>{if(!$.value||a.resize!=="both")return;const n=e[0],{width:I}=n.contentRect;re.value={right:`calc(100% - ${I+15+6}px)`}});const K=()=>{const{type:e,autosize:n}=a;if(!(!pt||e!=="textarea"))if(n){const I=Ee(n)?n.minRows:void 0,E=Ee(n)?n.maxRows:void 0;X.value={...Pe(P.value,I,E)}}else X.value={minHeight:Pe(P.value).minHeight}},L=()=>{const e=F.value;!e||e.value===C.value||(e.value=C.value)},de=e=>{const{el:n}=p.vnode;if(!n)return;const E=Array.from(n.querySelectorAll(`.${s.e(e)}`)).find(He=>He.parentNode===n);if(!E)return;const we=i[e];g[we]?E.style.transform=`translateX(${e==="suffix"?"-":""}${n.querySelector(`.${s.be("group",we)}`).offsetWidth}px)`:E.removeAttribute("style")},Q=()=>{de("prefix"),de("suffix")},G=async e=>{Fe();let{value:n}=e.target;if(a.formatter&&(n=a.parser?a.parser(n):n,n=a.formatter(n)),!A.value){if(n===C.value){L();return}l(se,n),l("input",n),await k(),L(),Me()}},pe=e=>{l("change",e.target.value)},fe=e=>{l("compositionstart",e),A.value=!0},ve=e=>{var n;l("compositionupdate",e);const I=(n=e.target)==null?void 0:n.value,E=I[I.length-1]||"";A.value=!ht(E)},me=e=>{l("compositionend",e),A.value&&(A.value=!1,G(e))},Le=()=>{D.value=!D.value,ye()},ye=async()=>{var e;await k(),(e=F.value)==null||e.focus()},Be=()=>{var e;return(e=F.value)==null?void 0:e.blur()},he=e=>{T.value=!0,l("focus",e)},be=e=>{var n;T.value=!1,l("blur",e),a.validateEvent&&((n=f==null?void 0:f.validate)==null||n.call(f,"blur").catch(I=>ze()))},Oe=e=>{_.value=!1,l("mouseleave",e)},Re=e=>{_.value=!0,l("mouseenter",e)},ge=e=>{l("keydown",e)},De=()=>{var e;(e=F.value)==null||e.select()},xe=()=>{l(se,""),l("change",""),l("clear"),l("input","")};return te(()=>a.modelValue,()=>{var e;k(()=>K()),a.validateEvent&&((e=f==null?void 0:f.validate)==null||e.call(f,"change").catch(n=>ze()))}),te(C,()=>L()),te(()=>a.type,async()=>{await k(),L(),K(),Q()}),at(async()=>{!a.formatter&&a.parser,L(),Q(),await k(),K()}),ot(async()=>{await k(),Q()}),m({input:R,textarea:P,ref:F,textareaStyle:ce,autosize:st(a,"autosize"),focus:ye,blur:Be,select:De,clear:xe,resizeTextarea:K}),(e,n)=>nt((c(),x("div",oe(t(u),{class:[e.type==="textarea"?t(le).b():t(s).b(),t(s).m(t(ke)),t(s).is("disabled",t(z)),t(s).is("exceed",t(Te)),{[t(s).b("group")]:e.$slots.prepend||e.$slots.append,[t(s).bm("group","append")]:e.$slots.append,[t(s).bm("group","prepend")]:e.$slots.prepend,[t(s).m("prefix")]:e.$slots.prefix||e.prefixIcon,[t(s).m("suffix")]:e.$slots.suffix||e.suffixIcon||e.clearable||e.showPassword,[t(s).bm("suffix","password-clear")]:t(H)&&t(Y)},e.$attrs.class],style:t(Ve),role:e.containerRole,onMouseenter:Re,onMouseleave:Oe}),[v(" input "),e.type!=="textarea"?(c(),x(ae,{key:0},[v(" prepend slot "),e.$slots.prepend?(c(),x("div",{key:0,class:y(t(s).be("group","prepend"))},[j(e.$slots,"prepend")],2)):v("v-if",!0),N("div",{class:y([t(s).e("wrapper"),t(s).is("focus",T.value)])},[v(" prefix slot "),e.$slots.prefix||e.prefixIcon?(c(),x("span",{key:0,class:y(t(s).e("prefix"))},[N("span",{class:y(t(s).e("prefix-inner"))},[j(e.$slots,"prefix"),e.prefixIcon?(c(),w(t(O),{key:0,class:y(t(s).e("icon"))},{default:B(()=>[(c(),w(U(e.prefixIcon)))]),_:1},8,["class"])):v("v-if",!0)],2)],2)):v("v-if",!0),N("input",oe({id:t(ne),ref_key:"input",ref:R,class:t(s).e("inner")},t(h),{type:e.showPassword?D.value?"text":"password":e.type,disabled:t(z),formatter:e.formatter,parser:e.parser,readonly:e.readonly,autocomplete:e.autocomplete,tabindex:e.tabindex,"aria-label":e.label,placeholder:e.placeholder,style:e.inputStyle,onCompositionstart:fe,onCompositionupdate:ve,onCompositionend:me,onInput:G,onFocus:he,onBlur:be,onChange:pe,onKeydown:ge}),null,16,$t),v(" suffix slot "),t(Ae)?(c(),x("span",{key:1,class:y(t(s).e("suffix"))},[N("span",{class:y(t(s).e("suffix-inner"))},[!t(H)||!t(Y)||!t($)?(c(),x(ae,{key:0},[j(e.$slots,"suffix"),e.suffixIcon?(c(),w(t(O),{key:0,class:y(t(s).e("icon"))},{default:B(()=>[(c(),w(U(e.suffixIcon)))]),_:1},8,["class"])):v("v-if",!0)],64)):v("v-if",!0),t(H)?(c(),w(t(O),{key:1,class:y([t(s).e("icon"),t(s).e("clear")]),onMousedown:ut(t(ct),["prevent"]),onClick:xe},{default:B(()=>[rt(t(it))]),_:1},8,["class","onMousedown"])):v("v-if",!0),t(Y)?(c(),w(t(O),{key:2,class:y([t(s).e("icon"),t(s).e("password")]),onClick:Le},{default:B(()=>[(c(),w(U(t(Ne))))]),_:1},8,["class"])):v("v-if",!0),t($)?(c(),x("span",{key:3,class:y(t(s).e("count"))},[N("span",{class:y(t(s).e("count-inner"))},W(t(q))+" / "+W(t(h).maxlength),3)],2)):v("v-if",!0),t(M)&&t(ue)&&t(ie)?(c(),w(t(O),{key:4,class:y([t(s).e("icon"),t(s).e("validateIcon"),t(s).is("loading",t(M)==="validating")])},{default:B(()=>[(c(),w(U(t(ue))))]),_:1},8,["class"])):v("v-if",!0)],2)],2)):v("v-if",!0)],2),v(" append slot "),e.$slots.append?(c(),x("div",{key:1,class:y(t(s).be("group","append"))},[j(e.$slots,"append")],2)):v("v-if",!0)],64)):(c(),x(ae,{key:1},[v(" textarea "),N("textarea",oe({id:t(ne),ref_key:"textarea",ref:P,class:t(le).e("inner")},t(h),{tabindex:e.tabindex,disabled:t(z),readonly:e.readonly,autocomplete:e.autocomplete,style:t(ce),"aria-label":e.label,placeholder:e.placeholder,onCompositionstart:fe,onCompositionupdate:ve,onCompositionend:me,onInput:G,onFocus:he,onBlur:be,onChange:pe,onKeydown:ge}),null,16,kt),t($)?(c(),x("span",{key:0,style:dt(re.value),class:y(t(s).e("count"))},W(t(q))+" / "+W(t(h).maxlength),7)):v("v-if",!0)],64))],16,Pt)),[[lt,e.type!=="hidden"]])}});var Tt=_e(Vt,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/input/src/input.vue"]]);const Lt=ft(Tt);export{Lt as E,ht as i,xt as u};
