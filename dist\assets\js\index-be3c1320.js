import{a8 as q,I as me,G as A,aN as pe,_ as ve,C as ge,a5 as be,cX as ye,R as Ee,i as h,aB as Ce,j as he,c as B,cY as x,a$ as Be,k as we,ac as Me,bW as Se,B as w,o as d,a as v,w as c,e as F,b,m as f,H as i,ad as _,y as V,g as H,L as z,f as M,t as I,a2 as P,S as ke,d as K,a3 as j,aK as Te,av as Ie,aS as Ae,cq as ae,cZ as le,c8 as ee,bz as Re,bf as Le,bk as ne}from"./index-444b28c3.js";import{E as Oe}from"./el-button-9bbdfcf9.js";import{E as $e}from"./el-input-6b488ec7.js";import{E as Ve,a as ze,u as Pe}from"./index-eba6e623.js";import{E as Ne}from"./focus-trap-6de7266c.js";import{i as De}from"./validator-e4131fc3.js";import{u as Ue}from"./index-e305bb62.js";import{a as se}from"./index-4d7f16ce.js";import{u as Fe}from"./index-11a84590.js";const He='a[href],button:not([disabled]),button:not([hidden]),:not([tabindex="-1"]),input:not([disabled]),input:not([type="hidden"]),select:not([disabled]),textarea:not([disabled])',Ke=e=>getComputedStyle(e).position==="fixed"?!1:e.offsetParent!==null,oe=e=>Array.from(e.querySelectorAll(He)).filter(n=>je(n)&&Ke(n)),je=e=>{if(e.tabIndex>0||e.tabIndex===0&&e.getAttribute("tabIndex")!==null)return!0;if(e.disabled)return!1;switch(e.nodeName){case"A":return!!e.href&&e.rel!=="ignore";case"INPUT":return!(e.type==="hidden"||e.type==="file");case"BUTTON":case"SELECT":case"TEXTAREA":return!0;default:return!1}},pn=function(e,n,...t){let a;n.includes("mouse")||n.includes("click")?a="MouseEvents":n.includes("key")?a="KeyboardEvent":a="HTMLEvents";const o=document.createEvent(a);return o.initEvent(n,...t),e.dispatchEvent(o),e},qe=(e,n)=>{let t;q(()=>e.value,a=>{var o,l;a?(t=document.activeElement,me(n)&&((l=(o=n.value).focus)==null||l.call(o))):t.focus()})},X="_trap-focus-children",y=[],te=e=>{if(y.length===0)return;const n=y[y.length-1][X];if(n.length>0&&e.code===pe.tab){if(n.length===1){e.preventDefault(),document.activeElement!==n[0]&&n[0].focus();return}const t=e.shiftKey,a=e.target===n[0],o=e.target===n[n.length-1];a&&t&&(e.preventDefault(),n[n.length-1].focus()),o&&!t&&(e.preventDefault(),n[0].focus())}},Xe={beforeMount(e){e[X]=oe(e),y.push(e),y.length<=1&&document.addEventListener("keydown",te)},updated(e){A(()=>{e[X]=oe(e)})},unmounted(){y.shift(),y.length===0&&document.removeEventListener("keydown",te)}},Ge=ge({name:"ElMessageBox",directives:{TrapFocus:Xe},components:{ElButton:Oe,ElFocusTrap:Ne,ElInput:$e,ElOverlay:Ve,ElIcon:be,...ye},inheritAttrs:!1,props:{buttonSize:{type:String,validator:De},modal:{type:Boolean,default:!0},lockScroll:{type:Boolean,default:!0},showClose:{type:Boolean,default:!0},closeOnClickModal:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},closeOnHashChange:{type:Boolean,default:!0},center:Boolean,draggable:Boolean,roundButton:{default:!1,type:Boolean},container:{type:String,default:"body"},boxType:{type:String,default:""}},emits:["vanish","action"],setup(e,{emit:n}){const{t}=Ue(),a=Ee("message-box"),o=h(!1),{nextZIndex:l}=Ce(),s=he({autofocus:!0,beforeClose:null,callback:null,cancelButtonText:"",cancelButtonClass:"",confirmButtonText:"",confirmButtonClass:"",customClass:"",customStyle:{},dangerouslyUseHTMLString:!1,distinguishCancelAndClose:!1,icon:"",inputPattern:null,inputPlaceholder:"",inputType:"text",inputValue:null,inputValidator:null,inputErrorMessage:"",message:null,modalFade:!0,modalClass:"",showCancelButton:!1,showConfirmButton:!0,type:"",title:void 0,showInput:!1,action:"",confirmButtonLoading:!1,cancelButtonLoading:!1,confirmButtonDisabled:!1,editorErrorMessage:"",validateError:!1,zIndex:l()}),m=B(()=>{const r=s.type;return{[a.bm("icon",r)]:r&&x[r]}}),N=se(),L=se(),D=Be(B(()=>e.buttonSize),{prop:!0,form:!0,formItem:!0}),U=B(()=>s.icon||x[s.type]||""),u=B(()=>!!s.message),E=h(),G=h(),k=h(),O=h(),Z=h(),re=B(()=>s.confirmButtonClass);q(()=>s.inputValue,async r=>{await A(),e.boxType==="prompt"&&r!==null&&Y()},{immediate:!0}),q(()=>o.value,r=>{var p,C;r&&(e.boxType!=="prompt"&&(s.autofocus?k.value=(C=(p=Z.value)==null?void 0:p.$el)!=null?C:E.value:k.value=E.value),s.zIndex=l()),e.boxType==="prompt"&&(r?A().then(()=>{var Q;O.value&&O.value.$el&&(s.autofocus?k.value=(Q=ce())!=null?Q:E.value:k.value=E.value)}):(s.editorErrorMessage="",s.validateError=!1))});const ie=B(()=>e.draggable);Fe(E,G,ie),we(async()=>{await A(),e.closeOnHashChange&&window.addEventListener("hashchange",T)}),Me(()=>{e.closeOnHashChange&&window.removeEventListener("hashchange",T)});function T(){o.value&&(o.value=!1,A(()=>{s.action&&n("action",s.action)}))}const W=()=>{e.closeOnClickModal&&$(s.distinguishCancelAndClose?"close":"cancel")},ue=Pe(W),de=r=>{if(s.inputType!=="textarea")return r.preventDefault(),$("confirm")},$=r=>{var p;e.boxType==="prompt"&&r==="confirm"&&!Y()||(s.action=r,s.beforeClose?(p=s.beforeClose)==null||p.call(s,r,s,T):T())},Y=()=>{if(e.boxType==="prompt"){const r=s.inputPattern;if(r&&!r.test(s.inputValue||""))return s.editorErrorMessage=s.inputErrorMessage||t("el.messagebox.error"),s.validateError=!0,!1;const p=s.inputValidator;if(typeof p=="function"){const C=p(s.inputValue);if(C===!1)return s.editorErrorMessage=s.inputErrorMessage||t("el.messagebox.error"),s.validateError=!0,!1;if(typeof C=="string")return s.editorErrorMessage=C,s.validateError=!0,!1}}return s.editorErrorMessage="",s.validateError=!1,!0},ce=()=>{const r=O.value.$refs;return r.input||r.textarea},J=()=>{$("close")},fe=()=>{e.closeOnPressEscape&&J()};return e.lockScroll&&ze(o),qe(o),{...Se(s),ns:a,overlayEvent:ue,visible:o,hasMessage:u,typeClass:m,contentId:N,inputId:L,btnSize:D,iconComponent:U,confirmButtonClasses:re,rootRef:E,focusStartRef:k,headerRef:G,inputRef:O,confirmRef:Z,doClose:T,handleClose:J,onCloseRequested:fe,handleWrapperClick:W,handleInputEnter:de,handleAction:$,t}}}),Ze=["aria-label","aria-describedby"],We=["aria-label"],Ye=["id"];function Je(e,n,t,a,o,l){const s=w("el-icon"),m=w("close"),N=w("el-input"),L=w("el-button"),D=w("el-focus-trap"),U=w("el-overlay");return d(),v(Te,{name:"fade-in-linear",onAfterLeave:n[11]||(n[11]=u=>e.$emit("vanish")),persisted:""},{default:c(()=>[F(b(U,{"z-index":e.zIndex,"overlay-class":[e.ns.is("message-box"),e.modalClass],mask:e.modal},{default:c(()=>[f("div",{role:"dialog","aria-label":e.title,"aria-modal":"true","aria-describedby":e.showInput?void 0:e.contentId,class:i(`${e.ns.namespace.value}-overlay-message-box`),onClick:n[8]||(n[8]=(...u)=>e.overlayEvent.onClick&&e.overlayEvent.onClick(...u)),onMousedown:n[9]||(n[9]=(...u)=>e.overlayEvent.onMousedown&&e.overlayEvent.onMousedown(...u)),onMouseup:n[10]||(n[10]=(...u)=>e.overlayEvent.onMouseup&&e.overlayEvent.onMouseup(...u))},[b(D,{loop:"",trapped:e.visible,"focus-trap-el":e.rootRef,"focus-start-el":e.focusStartRef,onReleaseRequested:e.onCloseRequested},{default:c(()=>[f("div",{ref:"rootRef",class:i([e.ns.b(),e.customClass,e.ns.is("draggable",e.draggable),{[e.ns.m("center")]:e.center}]),style:_(e.customStyle),tabindex:"-1",onClick:n[7]||(n[7]=V(()=>{},["stop"]))},[e.title!==null&&e.title!==void 0?(d(),H("div",{key:0,ref:"headerRef",class:i(e.ns.e("header"))},[f("div",{class:i(e.ns.e("title"))},[e.iconComponent&&e.center?(d(),v(s,{key:0,class:i([e.ns.e("status"),e.typeClass])},{default:c(()=>[(d(),v(z(e.iconComponent)))]),_:1},8,["class"])):M("v-if",!0),f("span",null,I(e.title),1)],2),e.showClose?(d(),H("button",{key:0,type:"button",class:i(e.ns.e("headerbtn")),"aria-label":e.t("el.messagebox.close"),onClick:n[0]||(n[0]=u=>e.handleAction(e.distinguishCancelAndClose?"close":"cancel")),onKeydown:n[1]||(n[1]=P(V(u=>e.handleAction(e.distinguishCancelAndClose?"close":"cancel"),["prevent"]),["enter"]))},[b(s,{class:i(e.ns.e("close"))},{default:c(()=>[b(m)]),_:1},8,["class"])],42,We)):M("v-if",!0)],2)):M("v-if",!0),f("div",{id:e.contentId,class:i(e.ns.e("content"))},[f("div",{class:i(e.ns.e("container"))},[e.iconComponent&&!e.center&&e.hasMessage?(d(),v(s,{key:0,class:i([e.ns.e("status"),e.typeClass])},{default:c(()=>[(d(),v(z(e.iconComponent)))]),_:1},8,["class"])):M("v-if",!0),e.hasMessage?(d(),H("div",{key:1,class:i(e.ns.e("message"))},[ke(e.$slots,"default",{},()=>[e.dangerouslyUseHTMLString?(d(),v(z(e.showInput?"label":"p"),{key:1,for:e.showInput?e.inputId:void 0,innerHTML:e.message},null,8,["for","innerHTML"])):(d(),v(z(e.showInput?"label":"p"),{key:0,for:e.showInput?e.inputId:void 0},{default:c(()=>[K(I(e.dangerouslyUseHTMLString?"":e.message),1)]),_:1},8,["for"]))])],2)):M("v-if",!0)],2),F(f("div",{class:i(e.ns.e("input"))},[b(N,{id:e.inputId,ref:"inputRef",modelValue:e.inputValue,"onUpdate:modelValue":n[2]||(n[2]=u=>e.inputValue=u),type:e.inputType,placeholder:e.inputPlaceholder,"aria-invalid":e.validateError,class:i({invalid:e.validateError}),onKeydown:P(e.handleInputEnter,["enter"])},null,8,["id","modelValue","type","placeholder","aria-invalid","class","onKeydown"]),f("div",{class:i(e.ns.e("errormsg")),style:_({visibility:e.editorErrorMessage?"visible":"hidden"})},I(e.editorErrorMessage),7)],2),[[j,e.showInput]])],10,Ye),f("div",{class:i(e.ns.e("btns"))},[e.showCancelButton?(d(),v(L,{key:0,loading:e.cancelButtonLoading,class:i([e.cancelButtonClass]),round:e.roundButton,size:e.btnSize,onClick:n[3]||(n[3]=u=>e.handleAction("cancel")),onKeydown:n[4]||(n[4]=P(V(u=>e.handleAction("cancel"),["prevent"]),["enter"]))},{default:c(()=>[K(I(e.cancelButtonText||e.t("el.messagebox.cancel")),1)]),_:1},8,["loading","class","round","size"])):M("v-if",!0),F(b(L,{ref:"confirmRef",type:"primary",loading:e.confirmButtonLoading,class:i([e.confirmButtonClasses]),round:e.roundButton,disabled:e.confirmButtonDisabled,size:e.btnSize,onClick:n[5]||(n[5]=u=>e.handleAction("confirm")),onKeydown:n[6]||(n[6]=P(V(u=>e.handleAction("confirm"),["prevent"]),["enter"]))},{default:c(()=>[K(I(e.confirmButtonText||e.t("el.messagebox.confirm")),1)]),_:1},8,["loading","class","round","disabled","size"]),[[j,e.showConfirmButton]])],2)],6)]),_:3},8,["trapped","focus-trap-el","focus-start-el","onReleaseRequested"])],42,Ze)]),_:3},8,["z-index","overlay-class","mask"]),[[j,e.visible]])]),_:3})}var Qe=ve(Ge,[["render",Je],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/message-box/src/index.vue"]]);const R=new Map,xe=(e,n,t=null)=>{const a=b(Qe,e,ne(e.message)||ae(e.message)?{default:ne(e.message)?e.message:()=>e.message}:null);return a.appContext=t,le(a,n),document.body.appendChild(n.firstElementChild),a.component},_e=()=>document.createElement("div"),en=(e,n)=>{const t=_e();e.onVanish=()=>{le(null,t),R.delete(o)},e.onAction=l=>{const s=R.get(o);let m;e.showInput?m={value:o.inputValue,action:l}:m=l,e.callback?e.callback(m,a.proxy):l==="cancel"||l==="close"?e.distinguishCancelAndClose&&l!=="cancel"?s.reject("close"):s.reject("cancel"):s.resolve(m)};const a=xe(e,t,n),o=a.proxy;for(const l in e)ee(e,l)&&!ee(o.$props,l)&&(o[l]=e[l]);return o.visible=!0,o};function S(e,n=null){if(!Ie)return Promise.reject();let t;return Ae(e)||ae(e)?e={message:e}:t=e.callback,new Promise((a,o)=>{const l=en(e,n??S._context);R.set(l,{options:e,callback:t,resolve:a,reject:o})})}const nn=["alert","confirm","prompt"],sn={alert:{closeOnPressEscape:!1,closeOnClickModal:!1},confirm:{showCancelButton:!0},prompt:{showCancelButton:!0,showInput:!0}};nn.forEach(e=>{S[e]=on(e)});function on(e){return(n,t,a,o)=>{let l="";return Re(t)?(a=t,l=""):Le(t)?l="":l=t,S(Object.assign({title:l,message:n,type:"",...sn[e]},a,{boxType:e}),o)}}S.close=()=>{R.forEach((e,n)=>{n.doClose()}),R.clear()};S._context=null;const g=S;g.install=e=>{g._context=e._context,e.config.globalProperties.$msgbox=g,e.config.globalProperties.$messageBox=g,e.config.globalProperties.$alert=g.alert,e.config.globalProperties.$confirm=g.confirm,e.config.globalProperties.$prompt=g.prompt};const vn=g;export{vn as E,pn as t};
