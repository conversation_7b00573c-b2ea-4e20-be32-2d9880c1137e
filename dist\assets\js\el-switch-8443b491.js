import{Q as J,aa as F,aw as X,bC as C,aS as S,a7 as V,_ as Y,C as x,ab as ee,a$ as ae,R as ie,c as v,bI as te,i as T,aZ as se,a8 as E,k as ne,o as s,g as u,m as N,n as a,H as c,a2 as oe,a as d,w as h,L as k,a5 as m,f as l,t as w,F as M,b as le,cd as re,ad as A,y as ce,G as ue,cO as K,U as de}from"./index-444b28c3.js";import{i as ve}from"./validator-e4131fc3.js";import{U as B,C as P,I as _,d as fe,t as pe}from"./event-fe80fd0c.js";import{u as he,b as me}from"./index-4d7f16ce.js";import{u as ye}from"./el-button-9bbdfcf9.js";const be=J({modelValue:{type:[<PERSON>olean,String,Number],default:!1},value:{type:[Boolean,String,Number],default:!1},disabled:{type:Boolean,default:!1},width:{type:[String,Number],default:""},inlinePrompt:{type:Boolean,default:!1},activeIcon:{type:F},inactiveIcon:{type:F},activeText:{type:String,default:""},inactiveText:{type:String,default:""},activeColor:{type:String,default:""},inactiveColor:{type:String,default:""},borderColor:{type:String,default:""},activeValue:{type:[Boolean,String,Number],default:!0},inactiveValue:{type:[Boolean,String,Number],default:!1},name:{type:String,default:""},validateEvent:{type:Boolean,default:!0},id:String,loading:{type:Boolean,default:!1},beforeChange:{type:X(Function)},size:{type:String,validator:ve},tabindex:{type:[String,Number]}}),ge={[B]:o=>C(o)||S(o)||V(o),[P]:o=>C(o)||S(o)||V(o),[_]:o=>C(o)||S(o)||V(o)},ke=["onClick"],we=["id","aria-checked","aria-disabled","name","true-value","false-value","disabled","tabindex","onKeydown"],Ce=["aria-hidden"],Ie=["aria-hidden"],Se=["aria-hidden"],Ve=["aria-hidden"],Te={name:"ElSwitch"},Ee=x({...Te,props:be,emits:ge,setup(o,{expose:G,emit:f}){const t=o,D="ElSwitch",H=ee(),{formItem:y}=he(),L=ae(),i=ie("switch");ye({from:'"value"',replacement:'"model-value" or "v-model"',scope:D,version:"2.3.0",ref:"https://element-plus.org/en-US/component/switch.html#attributes",type:"Attribute"},v(()=>{var e;return!!((e=H.vnode.props)!=null&&e.value)}));const{inputId:Q}=me(t,{formItemContext:y}),b=te(v(()=>t.loading)),I=T(t.modelValue!==!1),p=T(),R=T(),W=v(()=>[i.b(),i.m(L.value),i.is("disabled",b.value),i.is("checked",n.value)]),Z=v(()=>({width:se(t.width)}));E(()=>t.modelValue,()=>{I.value=!0}),E(()=>t.value,()=>{I.value=!1});const z=v(()=>I.value?t.modelValue:t.value),n=v(()=>z.value===t.activeValue);[t.activeValue,t.inactiveValue].includes(z.value)||(f(B,t.inactiveValue),f(P,t.inactiveValue),f(_,t.inactiveValue)),E(n,e=>{var r;p.value.checked=e,t.validateEvent&&((r=y==null?void 0:y.validate)==null||r.call(y,"change").catch(q=>fe()))});const g=()=>{const e=n.value?t.inactiveValue:t.activeValue;f(B,e),f(P,e),f(_,e),ue(()=>{p.value.checked=n.value})},O=()=>{if(b.value)return;const{beforeChange:e}=t;if(!e){g();return}const r=e();[K(r),C(r)].includes(!0)||pe(D,"beforeChange must return type `Promise<boolean>` or `boolean`"),K(r)?r.then(U=>{U&&g()}).catch(U=>{}):r&&g()},$=v(()=>i.cssVarBlock({...t.activeColor?{"on-color":t.activeColor}:null,...t.inactiveColor?{"off-color":t.inactiveColor}:null,...t.borderColor?{"border-color":t.borderColor}:null})),j=()=>{var e,r;(r=(e=p.value)==null?void 0:e.focus)==null||r.call(e)};return ne(()=>{p.value.checked=n.value}),G({focus:j,checked:n}),(e,r)=>(s(),u("div",{class:c(a(W)),style:A(a($)),onClick:ce(O,["prevent"])},[N("input",{id:a(Q),ref_key:"input",ref:p,class:c(a(i).e("input")),type:"checkbox",role:"switch","aria-checked":a(n),"aria-disabled":a(b),name:e.name,"true-value":e.activeValue,"false-value":e.inactiveValue,disabled:a(b),tabindex:e.tabindex,onChange:g,onKeydown:oe(O,["enter"])},null,42,we),!e.inlinePrompt&&(e.inactiveIcon||e.inactiveText)?(s(),u("span",{key:0,class:c([a(i).e("label"),a(i).em("label","left"),a(i).is("active",!a(n))])},[e.inactiveIcon?(s(),d(a(m),{key:0},{default:h(()=>[(s(),d(k(e.inactiveIcon)))]),_:1})):l("v-if",!0),!e.inactiveIcon&&e.inactiveText?(s(),u("span",{key:1,"aria-hidden":a(n)},w(e.inactiveText),9,Ce)):l("v-if",!0)],2)):l("v-if",!0),N("span",{ref_key:"core",ref:R,class:c(a(i).e("core")),style:A(a(Z))},[e.inlinePrompt?(s(),u("div",{key:0,class:c(a(i).e("inner"))},[e.activeIcon||e.inactiveIcon?(s(),u(M,{key:0},[e.activeIcon?(s(),d(a(m),{key:0,class:c([a(i).is("icon"),a(n)?a(i).is("show"):a(i).is("hide")])},{default:h(()=>[(s(),d(k(e.activeIcon)))]),_:1},8,["class"])):l("v-if",!0),e.inactiveIcon?(s(),d(a(m),{key:1,class:c([a(i).is("icon"),a(n)?a(i).is("hide"):a(i).is("show")])},{default:h(()=>[(s(),d(k(e.inactiveIcon)))]),_:1},8,["class"])):l("v-if",!0)],64)):e.activeText||e.inactiveIcon?(s(),u(M,{key:1},[e.activeText?(s(),u("span",{key:0,class:c([a(i).is("text"),a(n)?a(i).is("show"):a(i).is("hide")]),"aria-hidden":!a(n)},w(e.activeText.substring(0,3)),11,Ie)):l("v-if",!0),e.inactiveText?(s(),u("span",{key:1,class:c([a(i).is("text"),a(n)?a(i).is("hide"):a(i).is("show")]),"aria-hidden":a(n)},w(e.inactiveText.substring(0,3)),11,Se)):l("v-if",!0)],64)):l("v-if",!0)],2)):l("v-if",!0),N("div",{class:c(a(i).e("action"))},[e.loading?(s(),d(a(m),{key:0,class:c(a(i).is("loading"))},{default:h(()=>[le(a(re))]),_:1},8,["class"])):l("v-if",!0)],2)],6),!e.inlinePrompt&&(e.activeIcon||e.activeText)?(s(),u("span",{key:1,class:c([a(i).e("label"),a(i).em("label","right"),a(i).is("active",a(n))])},[e.activeIcon?(s(),d(a(m),{key:0},{default:h(()=>[(s(),d(k(e.activeIcon)))]),_:1})):l("v-if",!0),!e.activeIcon&&e.activeText?(s(),u("span",{key:1,"aria-hidden":!a(n)},w(e.activeText),9,Ve)):l("v-if",!0)],2)):l("v-if",!0)],14,ke))}});var Ne=Y(Ee,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/switch/src/switch.vue"]]);const Oe=de(Ne);export{Oe as E};
