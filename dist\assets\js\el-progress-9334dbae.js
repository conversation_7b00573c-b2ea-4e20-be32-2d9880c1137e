import{Q as q,aw as h,_ as W,C as z,R as I,c as P,N as ye,d4 as re,b9 as ge,ae as le,af as ie,bk as he,aS as Z,o as g,g as S,H as y,n as e,m as B,ad as j,S as F,t as K,f as $,a as D,w as L,L as be,a5 as A,U as ue,ax as G,bO as C,i as de,F as ke,h as $e,a2 as ce,y as M,b as O,d5 as we,aH as Ee,d6 as Se,T as Ce,b8 as Re,X as Pe,aD as J,d7 as Fe,d8 as Le,a8 as x,b0 as _e,bI as Ue,ac as Te,a9 as De,bh as Oe,bB as ee,aQ as te}from"./index-444b28c3.js";import{t as Y,i as Be,d as Ne}from"./event-fe80fd0c.js";import{u as je}from"./index-e305bb62.js";const pe=Symbol("uploadContextKey"),Ae=q({type:{type:String,default:"line",values:["line","circle","dashboard"]},percentage:{type:Number,default:0,validator:o=>o>=0&&o<=100},status:{type:String,default:"",values:["","success","exception","warning"]},indeterminate:{type:Boolean,default:!1},duration:{type:Number,default:3},strokeWidth:{type:Number,default:6},strokeLinecap:{type:h(String),default:"round"},textInside:{type:Boolean,default:!1},width:{type:Number,default:126},showText:{type:Boolean,default:!0},color:{type:h([String,Array,Function]),default:""},format:{type:h(Function),default:o=>`${o}%`}}),Ie=["aria-valuenow"],Me={viewBox:"0 0 100 100"},qe=["d","stroke","stroke-width"],He=["d","stroke","opacity","stroke-linecap","stroke-width"],We={key:0},ze={name:"ElProgress"},Ke=z({...ze,props:Ae,setup(o){const s=o,n={success:"#13ce66",exception:"#ff4949",warning:"#e6a23c",default:"#20a0ff"},a=I("progress"),d=P(()=>({width:`${s.percentage}%`,animationDuration:`${s.duration}s`,backgroundColor:E(s.percentage)})),p=P(()=>(s.strokeWidth/s.width*100).toFixed(1)),f=P(()=>["circle","dashboard"].includes(s.type)?Number.parseInt(`${50-Number.parseFloat(p.value)/2}`,10):0),R=P(()=>{const l=f.value,_=s.type==="dashboard";return`
          M 50 50
          m 0 ${_?"":"-"}${l}
          a ${l} ${l} 0 1 1 0 ${_?"-":""}${l*2}
          a ${l} ${l} 0 1 1 0 ${_?"":"-"}${l*2}
          `}),u=P(()=>2*Math.PI*f.value),b=P(()=>s.type==="dashboard"?.75:1),c=P(()=>`${-1*u.value*(1-b.value)/2}px`),w=P(()=>({strokeDasharray:`${u.value*b.value}px, ${u.value}px`,strokeDashoffset:c.value})),i=P(()=>({strokeDasharray:`${u.value*b.value*(s.percentage/100)}px, ${u.value}px`,strokeDashoffset:c.value,transition:"stroke-dasharray 0.6s ease 0s, stroke 0.6s ease, opacity ease 0.6s"})),t=P(()=>{let l;return s.color?l=E(s.percentage):l=n[s.status]||n.default,l}),r=P(()=>s.status==="warning"?ye:s.type==="line"?s.status==="success"?re:ge:s.status==="success"?le:ie),v=P(()=>s.type==="line"?12+s.strokeWidth*.4:s.width*.111111+2),k=P(()=>s.format(s.percentage));function m(l){const _=100/l.length;return l.map((T,N)=>Z(T)?{color:T,percentage:(N+1)*_}:T).sort((T,N)=>T.percentage-N.percentage)}const E=l=>{var _;const{color:U}=s;if(he(U))return U(l);if(Z(U))return U;{const T=m(U);for(const N of T)if(N.percentage>l)return N.color;return(_=T[T.length-1])==null?void 0:_.color}};return(l,_)=>(g(),S("div",{class:y([e(a).b(),e(a).m(l.type),e(a).is(l.status),{[e(a).m("without-text")]:!l.showText,[e(a).m("text-inside")]:l.textInside}]),role:"progressbar","aria-valuenow":l.percentage,"aria-valuemin":"0","aria-valuemax":"100"},[l.type==="line"?(g(),S("div",{key:0,class:y(e(a).b("bar"))},[B("div",{class:y(e(a).be("bar","outer")),style:j({height:`${l.strokeWidth}px`})},[B("div",{class:y([e(a).be("bar","inner"),{[e(a).bem("bar","inner","indeterminate")]:l.indeterminate}]),style:j(e(d))},[(l.showText||l.$slots.default)&&l.textInside?(g(),S("div",{key:0,class:y(e(a).be("bar","innerText"))},[F(l.$slots,"default",{percentage:l.percentage},()=>[B("span",null,K(e(k)),1)])],2)):$("v-if",!0)],6)],6)],2)):(g(),S("div",{key:1,class:y(e(a).b("circle")),style:j({height:`${l.width}px`,width:`${l.width}px`})},[(g(),S("svg",Me,[B("path",{class:y(e(a).be("circle","track")),d:e(R),stroke:`var(${e(a).cssVarName("fill-color-light")}, #e5e9f2)`,"stroke-width":e(p),fill:"none",style:j(e(w))},null,14,qe),B("path",{class:y(e(a).be("circle","path")),d:e(R),stroke:e(t),fill:"none",opacity:l.percentage?1:0,"stroke-linecap":l.strokeLinecap,"stroke-width":e(p),style:j(e(i))},null,14,He)]))],6)),(l.showText||l.$slots.default)&&!l.textInside?(g(),S("div",{key:2,class:y(e(a).e("text")),style:j({fontSize:`${e(v)}px`})},[F(l.$slots,"default",{percentage:l.percentage},()=>[l.status?(g(),D(e(A),{key:1},{default:L(()=>[(g(),D(be(e(r))))]),_:1})):(g(),S("span",We,K(e(k)),1))])],6)):$("v-if",!0)],10,Ie))}});var Ve=W(Ke,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/progress/src/progress.vue"]]);const Xe=ue(Ve),Qe="ElUpload";class Ge extends Error{constructor(s,n,a,d){super(s),this.name="UploadAjaxError",this.status=n,this.method=a,this.url=d}}function se(o,s,n){let a;return n.response?a=`${n.response.error||n.response}`:n.responseText?a=`${n.responseText}`:a=`fail to ${s.method} ${o} ${n.status}`,new Ge(a,n.status,s.method,o)}function Je(o){const s=o.responseText||o.response;if(!s)return s;try{return JSON.parse(s)}catch{return s}}const Ye=o=>{typeof XMLHttpRequest>"u"&&Y(Qe,"XMLHttpRequest is undefined");const s=new XMLHttpRequest,n=o.action;s.upload&&s.upload.addEventListener("progress",p=>{const f=p;f.percent=p.total>0?p.loaded/p.total*100:0,o.onProgress(f)});const a=new FormData;if(o.data)for(const[p,f]of Object.entries(o.data))Array.isArray(f)?a.append(p,...f):a.append(p,f);a.append(o.filename,o.file,o.file.name),s.addEventListener("error",()=>{o.onError(se(n,o,s))}),s.addEventListener("load",()=>{if(s.status<200||s.status>=300)return o.onError(se(n,o,s));o.onSuccess(Je(s))}),s.open(o.method,n,!0),o.withCredentials&&"withCredentials"in s&&(s.withCredentials=!0);const d=o.headers||{};if(d instanceof Headers)d.forEach((p,f)=>s.setRequestHeader(f,p));else for(const[p,f]of Object.entries(d))Be(f)||s.setRequestHeader(p,String(f));return s.send(a),s},fe=["text","picture","picture-card"];let Ze=1;const ve=()=>Date.now()+Ze++,me=q({action:{type:String,default:"#"},headers:{type:h(Object)},method:{type:String,default:"post"},data:{type:Object,default:()=>G({})},multiple:{type:Boolean,default:!1},name:{type:String,default:"file"},drag:{type:Boolean,default:!1},withCredentials:Boolean,showFileList:{type:Boolean,default:!0},accept:{type:String,default:""},type:{type:String,default:"select"},fileList:{type:h(Array),default:()=>G([])},autoUpload:{type:Boolean,default:!0},listType:{type:String,values:fe,default:"text"},httpRequest:{type:h(Function),default:Ye},disabled:Boolean,limit:Number}),xe=q({...me,beforeUpload:{type:h(Function),default:C},beforeRemove:{type:h(Function)},onRemove:{type:h(Function),default:C},onChange:{type:h(Function),default:C},onPreview:{type:h(Function),default:C},onSuccess:{type:h(Function),default:C},onProgress:{type:h(Function),default:C},onError:{type:h(Function),default:C},onExceed:{type:h(Function),default:C}}),et=q({files:{type:h(Array),default:()=>G([])},disabled:{type:Boolean,default:!1},handlePreview:{type:h(Function),default:C},listType:{type:String,values:fe,default:"text"}}),tt={remove:o=>!!o},st=["onKeydown"],at=["src"],ot=["onClick"],nt=["onClick"],rt=["onClick"],lt={name:"ElUploadList"},it=z({...lt,props:et,emits:tt,setup(o,{emit:s}){const{t:n}=je(),a=I("upload"),d=I("icon"),p=I("list"),f=de(!1),R=u=>{s("remove",u)};return(u,b)=>(g(),D(Ce,{tag:"ul",class:y([e(a).b("list"),e(a).bm("list",u.listType),e(a).is("disabled",u.disabled)]),name:e(p).b()},{default:L(()=>[(g(!0),S(ke,null,$e(u.files,c=>(g(),S("li",{key:c.uid||c.name,class:y([e(a).be("list","item"),e(a).is(c.status),{focusing:f.value}]),tabindex:"0",onKeydown:ce(w=>!u.disabled&&R(c),["delete"]),onFocus:b[0]||(b[0]=w=>f.value=!0),onBlur:b[1]||(b[1]=w=>f.value=!1),onClick:b[2]||(b[2]=w=>f.value=!1)},[F(u.$slots,"default",{file:c},()=>[u.listType==="picture"||c.status!=="uploading"&&u.listType==="picture-card"?(g(),S("img",{key:0,class:y(e(a).be("list","item-thumbnail")),src:c.url,alt:""},null,10,at)):$("v-if",!0),c.status==="uploading"||u.listType!=="picture-card"?(g(),S("div",{key:1,class:y(e(a).be("list","item-info"))},[B("a",{class:y(e(a).be("list","item-name")),onClick:M(w=>u.handlePreview(c),["prevent"])},[O(e(A),{class:y(e(d).m("document"))},{default:L(()=>[O(e(we))]),_:1},8,["class"]),B("span",{class:y(e(a).be("list","item-file-name"))},K(c.name),3)],10,ot),c.status==="uploading"?(g(),D(e(Xe),{key:0,type:u.listType==="picture-card"?"circle":"line","stroke-width":u.listType==="picture-card"?6:2,percentage:Number(c.percentage),style:j(u.listType==="picture-card"?"":"margin-top: 0.5rem")},null,8,["type","stroke-width","percentage","style"])):$("v-if",!0)],2)):$("v-if",!0),B("label",{class:y(e(a).be("list","item-status-label"))},[u.listType==="text"?(g(),D(e(A),{key:0,class:y([e(d).m("upload-success"),e(d).m("circle-check")])},{default:L(()=>[O(e(re))]),_:1},8,["class"])):["picture-card","picture"].includes(u.listType)?(g(),D(e(A),{key:1,class:y([e(d).m("upload-success"),e(d).m("check")])},{default:L(()=>[O(e(le))]),_:1},8,["class"])):$("v-if",!0)],2),u.disabled?$("v-if",!0):(g(),D(e(A),{key:2,class:y(e(d).m("close")),onClick:w=>R(c)},{default:L(()=>[O(e(ie))]),_:2},1032,["class","onClick"])),$(" Due to close btn only appears when li gets focused disappears after li gets blurred, thus keyboard navigation can never reach close btn"),$(" This is a bug which needs to be fixed "),$(" TODO: Fix the incorrect navigation interaction "),u.disabled?$("v-if",!0):(g(),S("i",{key:3,class:y(e(d).m("close-tip"))},K(e(n)("el.upload.deleteTip")),3)),u.listType==="picture-card"?(g(),S("span",{key:4,class:y(e(a).be("list","item-actions"))},[B("span",{class:y(e(a).be("list","item-preview")),onClick:w=>u.handlePreview(c)},[O(e(A),{class:y(e(d).m("zoom-in"))},{default:L(()=>[O(e(Ee))]),_:1},8,["class"])],10,nt),u.disabled?$("v-if",!0):(g(),S("span",{key:0,class:y(e(a).be("list","item-delete")),onClick:w=>R(c)},[O(e(A),{class:y(e(d).m("delete"))},{default:L(()=>[O(e(Se))]),_:1},8,["class"])],10,rt))],2)):$("v-if",!0)])],42,st))),128)),F(u.$slots,"append")]),_:3},8,["class","name"]))}});var ae=W(it,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/upload/src/upload-list.vue"]]);const ut=q({disabled:{type:Boolean,default:!1}}),dt={file:o=>Re(o)},ct=["onDrop","onDragover"],pt={name:"ElUploadDrag"},ft=z({...pt,props:ut,emits:dt,setup(o,{emit:s}){const n=o,a="ElUploadDrag",d=Pe(pe);d||Y(a,"usage: <el-upload><el-upload-dragger /></el-upload>");const p=I("upload"),f=de(!1),R=b=>{if(n.disabled)return;f.value=!1;const c=Array.from(b.dataTransfer.files),w=d.accept.value;if(!w){s("file",c);return}const i=c.filter(t=>{const{type:r,name:v}=t,k=v.includes(".")?`.${v.split(".").pop()}`:"",m=r.replace(/\/.*$/,"");return w.split(",").map(E=>E.trim()).filter(E=>E).some(E=>E.startsWith(".")?k===E:/\/\*$/.test(E)?m===E.replace(/\/\*$/,""):/^[^/]+\/[^/]+$/.test(E)?r===E:!1)});s("file",i)},u=()=>{n.disabled||(f.value=!0)};return(b,c)=>(g(),S("div",{class:y([e(p).b("dragger"),e(p).is("dragover",f.value)]),onDrop:M(R,["prevent"]),onDragover:M(u,["prevent"]),onDragleave:c[0]||(c[0]=M(w=>f.value=!1,["prevent"]))},[F(b.$slots,"default")],42,ct))}});var vt=W(ft,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/upload/src/upload-dragger.vue"]]);const mt=q({...me,beforeUpload:{type:h(Function),default:C},onRemove:{type:h(Function),default:C},onStart:{type:h(Function),default:C},onSuccess:{type:h(Function),default:C},onProgress:{type:h(Function),default:C},onError:{type:h(Function),default:C},onExceed:{type:h(Function),default:C}}),yt=["onKeydown"],gt=["name","multiple","accept"],ht={name:"ElUploadContent",inheritAttrs:!1},bt=z({...ht,props:mt,setup(o,{expose:s}){const n=o,a=I("upload"),d=J({}),p=J(),f=t=>{if(t.length===0)return;const{autoUpload:r,limit:v,fileList:k,multiple:m,onStart:E,onExceed:l}=n;if(v&&k.length+t.length>v){l(t,k);return}m||(t=t.slice(0,1));for(const _ of t){const U=_;U.uid=ve(),E(U),r&&R(U)}},R=async t=>{if(p.value.value="",!n.beforeUpload)return u(t);let r;try{r=await n.beforeUpload(t)}catch{r=!1}if(r===!1){n.onRemove(t);return}let v=t;r instanceof Blob&&(r instanceof File?v=r:v=new File([r],t.name,{type:t.type})),u(Object.assign(v,{uid:t.uid}))},u=t=>{const{headers:r,data:v,method:k,withCredentials:m,name:E,action:l,onProgress:_,onSuccess:U,onError:T,httpRequest:N}=n,{uid:V}=t,X={headers:r||{},withCredentials:m,file:t,data:v,method:k,filename:E,action:l,onProgress:H=>{_(H,t)},onSuccess:H=>{U(H,t),delete d.value[V]},onError:H=>{T(H,t),delete d.value[V]}},Q=N(X);d.value[V]=Q,Q instanceof Promise&&Q.then(X.onSuccess,X.onError)},b=t=>{const r=t.target.files;r&&f(Array.from(r))},c=()=>{n.disabled||(p.value.value="",p.value.click())},w=()=>{c()};return s({abort:t=>{Fe(d.value).filter(t?([v])=>String(t.uid)===v:()=>!0).forEach(([v,k])=>{k instanceof XMLHttpRequest&&k.abort(),delete d.value[v]})},upload:R}),(t,r)=>(g(),S("div",{class:y([e(a).b(),e(a).m(t.listType),e(a).is("drag",t.drag)]),tabindex:"0",onClick:c,onKeydown:ce(M(w,["self"]),["enter","space"])},[t.drag?(g(),D(vt,{key:0,disabled:t.disabled,onFile:f},{default:L(()=>[F(t.$slots,"default")]),_:3},8,["disabled"])):F(t.$slots,"default",{key:1}),B("input",{ref_key:"inputRef",ref:p,class:y(e(a).e("input")),name:t.name,multiple:t.multiple,accept:t.accept,type:"file",onChange:b,onClick:r[0]||(r[0]=M(()=>{},["stop"]))},null,42,gt)],42,yt))}});var oe=W(bt,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/upload/src/upload-content.vue"]]);const ne="ElUpload",kt=o=>{var s;(s=o.url)!=null&&s.startsWith("blob:")&&URL.revokeObjectURL(o.url)},$t=(o,s)=>{const n=Le(o,"fileList",void 0,{passive:!0}),a=i=>n.value.find(t=>t.uid===i.uid);function d(i){var t;(t=s.value)==null||t.abort(i)}function p(i=["ready","uploading","success","fail"]){n.value=n.value.filter(t=>!i.includes(t.status))}const f=(i,t)=>{const r=a(t);r&&(console.error(i),r.status="fail",n.value.splice(n.value.indexOf(r),1),o.onError(i,r,n.value),o.onChange(r,n.value))},R=(i,t)=>{const r=a(t);r&&(o.onProgress(i,r,n.value),r.status="uploading",r.percentage=Math.round(i.percent))},u=(i,t)=>{const r=a(t);r&&(r.status="success",r.response=i,o.onSuccess(i,r,n.value),o.onChange(r,n.value))},b=i=>{const t={name:i.name,percentage:0,status:"ready",size:i.size,raw:i,uid:i.uid};if(o.listType==="picture-card"||o.listType==="picture")try{t.url=URL.createObjectURL(i)}catch(r){Ne(ne,r.message),o.onError(r,t,n.value)}n.value=[...n.value,t],o.onChange(t,n.value)},c=async i=>{const t=i instanceof File?a(i):i;t||Y(ne,"file to be removed not found");const r=v=>{d(v);const k=n.value;k.splice(k.indexOf(v),1),o.onRemove(v,k),kt(v)};o.beforeRemove?await o.beforeRemove(t,n.value)!==!1&&r(t):r(t)};function w(){n.value.filter(({status:i})=>i==="ready").forEach(({raw:i})=>{var t;return i&&((t=s.value)==null?void 0:t.upload(i))})}return x(()=>o.listType,i=>{i!=="picture-card"&&i!=="picture"||(n.value=n.value.map(t=>{const{raw:r,url:v}=t;if(!v&&r)try{t.url=URL.createObjectURL(r)}catch(k){o.onError(k,t,n.value)}return t}))}),x(n,i=>{for(const t of i)t.uid||(t.uid=ve()),t.status||(t.status="success")},{immediate:!0,deep:!0}),{uploadFiles:n,abort:d,clearFiles:p,handleError:f,handleProgress:R,handleStart:b,handleSuccess:u,handleRemove:c,submit:w}},wt={name:"ElUpload"},Et=z({...wt,props:xe,setup(o,{expose:s}){const n=o,a=_e(),d=Ue(),p=J(),{abort:f,submit:R,clearFiles:u,uploadFiles:b,handleStart:c,handleError:w,handleRemove:i,handleSuccess:t,handleProgress:r}=$t(n,p),v=P(()=>n.listType==="picture-card"),k=P(()=>({...n,fileList:b.value,onStart:c,onProgress:r,onSuccess:t,onError:w,onRemove:i}));return Te(()=>{b.value.forEach(({url:m})=>{m!=null&&m.startsWith("blob:")&&URL.revokeObjectURL(m)})}),De(pe,{accept:Oe(n,"accept")}),s({abort:f,submit:R,clearFiles:u,handleStart:c,handleRemove:i}),(m,E)=>(g(),S("div",null,[e(v)&&m.showFileList?(g(),D(ae,{key:0,disabled:e(d),"list-type":m.listType,files:e(b),"handle-preview":m.onPreview,onRemove:e(i)},ee({append:L(()=>[O(oe,te({ref_key:"uploadRef",ref:p},e(k)),{default:L(()=>[e(a).trigger?F(m.$slots,"trigger",{key:0}):$("v-if",!0),!e(a).trigger&&e(a).default?F(m.$slots,"default",{key:1}):$("v-if",!0)]),_:3},16)]),_:2},[m.$slots.file?{name:"default",fn:L(({file:l})=>[F(m.$slots,"file",{file:l})])}:void 0]),1032,["disabled","list-type","files","handle-preview","onRemove"])):$("v-if",!0),!e(v)||e(v)&&!m.showFileList?(g(),D(oe,te({key:1,ref_key:"uploadRef",ref:p},e(k)),{default:L(()=>[e(a).trigger?F(m.$slots,"trigger",{key:0}):$("v-if",!0),!e(a).trigger&&e(a).default?F(m.$slots,"default",{key:1}):$("v-if",!0)]),_:3},16)):$("v-if",!0),m.$slots.trigger?F(m.$slots,"default",{key:2}):$("v-if",!0),F(m.$slots,"tip"),!e(v)&&m.showFileList?(g(),D(ae,{key:3,disabled:e(d),"list-type":m.listType,files:e(b),"handle-preview":m.onPreview,onRemove:e(i)},ee({_:2},[m.$slots.file?{name:"default",fn:L(({file:l})=>[F(m.$slots,"file",{file:l})])}:void 0]),1032,["disabled","list-type","files","handle-preview","onRemove"])):$("v-if",!0)]))}});var St=W(Et,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/upload/src/upload.vue"]]);const Ft=ue(St);export{Ft as E};
