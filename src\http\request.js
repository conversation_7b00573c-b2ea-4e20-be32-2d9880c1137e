/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2022-11-23 09:15:45
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-21 09:01:35
 * @FilePath: \electronic-filed:\AppServ\www\WeTalk-Admin\src\http\request.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// request.js
import axios from 'axios'
import { Message } from 'element-ui'
// 导入config配置文件
import config from '../common/config.js'
import nProgress from 'nprogress'
import 'nprogress/nprogress.css'
// 创建axios实例
let instance = axios.create({
	/**
	 * 是否携带cookie,注意若携带cookie后端必须配置
	 * 1.Access-Control-Allow-Origin为单一域名(具体到IP + port,用localhost貌似不行)
	 * 2.需要带上响应头Access-Control-Allow-Credentials
	 */
	withCredentials: true,
	timeout: config.timeout,
	baseURL: config.baseURL,
	headers: config.headers
})
// 添加请求拦截器(post只能接受字符串类型数据)
instance.interceptors.request.use(
	config => {
		const token = localStorage.getItem('accessToken')
		if (token) {
			config.headers.Authorization = `Bearer ${token}`
			nProgress.start();
		}
		return config
	},
	error => {
		return Promise.reject(error)
	}
)
const errorHandle = (status, message) => {
	switch (status) {
		case 400:
			Message.error('信息校验失败')
			break
		case 401:
			// @ts-nocheck
			Message.error('身份认证失败')
			localStorage.removeItem('accessToken')
			localStorage.removeItem('userInfo')
			setTimeout(() => {
				window.location.href = '/'
			}, 1000)
			break
		case 403:
			Message.error('token校验失败')
			localStorage.removeItem('accessToken')
			localStorage.removeItem('userInfo')
			setTimeout(() => {
				window.location.href = '/'
			}, 1000)
			break
		case 404:
			Message.error('请求的服务器资源不存在')
			break
		default:
			Message.error(message || '服务器错误')
			break
	}
}

// 添加响应拦截器
instance.interceptors.response.use(
	response => {
		nProgress.done()
		
		// 如果是文件下载类型的响应，直接返回完整响应
		if (response.config.responseType === 'blob') {
			// 检查响应头的内容类型
			const contentType = response.headers['content-type'];
			if (contentType && contentType.includes('application/json')) {
				// 如果是 JSON 响应，说明可能是错误信息
				return new Promise((resolve, reject) => {
					const reader = new FileReader();
					reader.onload = () => {
						try {
							const result = JSON.parse(reader.result);
							if (result.status !== 200) {
								Message.error(result.message || '导出失败');
								reject(new Error(result.message));
							}
						} catch (e) {
							reject(e);
						}
					};
					reader.onerror = () => reject(new Error('文件读取失败'));
					reader.readAsText(response.data);
				});
			}
			return response;  // 返回二进制数据流
		}

		const res = response.data
		if (res.status !== 200) {
			Message.error(res.message || 'Error')
			return Promise.reject(new Error(res.message || 'Error'))
		}
		return res
	},
	error => {
		nProgress.done()
		
		if (error.response) {
			// 服务器返回了错误状态码
			errorHandle(
				error.response.status,
				error.response.data?.message || '服务器错误'
			)
		} else if (error.request) {
			// 请求已发出，但没有收到响应
			Message.error('服务器无响应')
		} else {
			// 请求配置出错
			Message.error('请求配置错误')
		}
		
		return Promise.reject(error)
	}
)

export default instance
