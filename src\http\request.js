/*
 * @Author: flyknit <EMAIL>
 * @Date: 2023-12-02 13:54:12
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-05-29 13:24:02
 * @FilePath: \electronic-filed:\gitee\backend-demo\src\http\request.js
 * @Description: 
 * 
 * Copyright (c) 2023 by ${<EMAIL>}, All Rights Reserved. 
 */
// request.js
import axios from "axios";
import { Message, Loading} from "element-ui";
// 引入进度条的样式
import "nprogress/nprogress.css";
import nProgress, { set } from "nprogress";
import config from "../config/config";
import _ from "lodash";

let needLoadingRequestCount = 0;
let loading;

// 创建axios实例
let instance = axios.create({
  /**
   * 是否携带cookie,注意若携带cookie后端必须配置
   * 1.Access-Control-Allow-Origin为单一域名(具体到IP + port,用localhost貌似不行)
   * 2.需要带上响应头Access-Control-Allow-Credentials
   */
  withCredentials: true,
  timeout: config.timeout,
  baseURL: config.baseURL,
  headers: config.headers,
});
// 添加请求拦截器(post只能接受字符串类型数据)
instance.interceptors.request.use(
  (config) => {
    nProgress.start();
    // 加载
    // showFullScreenLoading();
    const token = localStorage.getItem("accessToken");
    // console.log('token', token);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    // 隐藏
    // tryHideFullScreenLoading();
    return Promise.reject(error);
  }
);
const errorHandle = (status, other) => {
  switch (status) {
    case 400:
      Message.error("请求服务器失败");
      break;
    case 401:
      // @ts-nocheck
      Message.error("身份认证失败");
      localStorage.removeItem("accessToken");
      // 跳转到登录页面
      setTimeout(() => {
        window.location.href = "/login";
      }, 1000);
      break;
    case 403:
      Message.error("该资源拒绝访问");
      break;
    case 404:
      Message.error("请求的资源不存在");
      break;
    default:
      Message.error(other);
      break;
  }
};

// 添加响应拦截器
instance.interceptors.response.use(
  (res) => {
    nProgress.done();
    // 隐藏
    // tryHideFullScreenLoading();
    // 请求通用处理
    if (res.data.code === 200) {
      // 正常返回数据
      return res.data;
    } else {
      // 遇到错误
      return Promise.reject(Message.error(res.data.message || "Error"));
    }
  },
  (err) => {
    nProgress.done();
    // 隐藏
    // tryHideFullScreenLoading();
    const { response } = err;
    if (response) {
      errorHandle(response.status, response.data, response.message);
      return Promise.reject(response);
    } else {
      // 处理断网的情况
      errorHandle(500, "啊哦，服务器开小差了~");
      // 如果没有response，那么我们应该拒绝一个新的Error，而不是返回true。
      return Promise.reject(new Error("请求失败"));
    }
  }
);


function startLoading() {
  loading = Loading.service({
    lock: true,
    text: '正在努力加载数据...',
    background: 'rgba(0, 0, 0, 0.7)'
  })
}
 
function endLoading() {
  loading.close()
}
 
const tryCloseLoading = () => {
  if (needLoadingRequestCount === 0) {
    endLoading()
  }
}
 
export function showFullScreenLoading() {
  if (needLoadingRequestCount === 0) {
    startLoading()
  }
  needLoadingRequestCount++
}
 
export function tryHideFullScreenLoading() {
  if (needLoadingRequestCount <= 0) return
  needLoadingRequestCount--
  if (needLoadingRequestCount === 0) {
    _.debounce(tryCloseLoading, 100)()  //延迟100ms
  }
}


export default instance;
