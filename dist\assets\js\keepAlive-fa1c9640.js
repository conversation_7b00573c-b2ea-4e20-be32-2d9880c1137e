import{cB as t}from"./index-444b28c3.js";const a=t({id:"keepAliveStore",state:()=>({keepLiveName:["test"]}),getters:{},actions:{async addKeepLiveName(e){!this.keepLiveName.includes(e)&&this.keepLiveName.push(e)},async removeKeepLiveName(e){this.keepLiveName=this.keepLiveName.filter(i=>i!==e)},async clearMultipleKeepAlive(e=[]){this.keepLiveName=e},async setKeepAliveName(e=[]){this.keepLiveName=e}}});export{a as K};
