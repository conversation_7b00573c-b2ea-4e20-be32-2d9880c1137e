import{Q as Ge,aw as Ae,bO as pa,aS as Ye,bz as Eo,_ as Se,C as ie,aP as fa,bI as ma,R as Ee,i as k,c as E,bc as ha,k as Ue,o as g,a as D,w as p,m as y,H as ne,n as C,ad as ft,b as d,g as P,a5 as fe,cd as va,F as Ce,h as vt,S as Z,d as he,t as W,aQ as nt,a2 as wt,y as Ln,bB as Co,G as $t,b8 as An,U as nn,aa as ut,a9 as Fe,ab as _t,X as se,bW as So,L as $e,ag as gt,ac as on,aN as K,c6 as _a,bh as dt,a8 as Ve,aM as ga,B as Y,de as ba,df as ya,be as Io,aZ as Aa,a$ as wa,f as ge,aK as Gn,c9 as xt,ca as wn,bg as Ea,aF as Ca,j as an,D as _e,e as mt,a3 as Nt,bF as ao,ax as Sa,by as Ia,b<PERSON> as ka,dg as Ta,dh as ko,bo as be,V as Xt,di as Ma,dj as Da,z as Oa,A as $a,W as ot,bn as qe,bv as Na,dk as at,br as Rt,dl as Ra,T as Fa,dm as La,s as lo,u as To,dn as Pa,dp as En,bZ as xa,dq as Ba,aU as Pn,cl as Va,bq as so,dr as Wa,E as ro,ds as za,dt as Ga,du as io,dv as ln,cI as Ua}from"./index-444b28c3.js";import{E as Ya}from"./el-drawer-12f56ca7.js";import"./el-overlay-9f4b42b1.js";import{E as Ha}from"./el-switch-8443b491.js";import"./el-tooltip-4ed993c7.js";import{u as Mo,g as Ka,a as Ft,E as Le,c as Re,d as ja,O as Qa,w as uo,e as Xa}from"./el-scrollbar-af6196f4.js";import{E as Za}from"./el-divider-d33f4e37.js";/* empty css                */import{_ as me}from"./_plugin-vue_export-helper-c27b6911.js";import{T as Do,_ as sn,a as rn}from"./tabs-15514514.js";import{b as qa,E as Ja,a as un,c as Un,d as cn}from"./el-footer-ef9d7e15.js";import{K as Yn}from"./keepAlive-fa1c9640.js";import{E as el,a as tl}from"./el-tab-pane-5c4fc173.js";import{E as bt,T as nl}from"./el-button-9bbdfcf9.js";import{d as ol,u as al}from"./index-e305bb62.js";import{a as Oo}from"./index-4d7f16ce.js";import{c as $o,E as No}from"./el-dialog-e35c112f.js";import{F as ll}from"./focus-trap-6de7266c.js";import{u as sl,E as Ro}from"./el-input-6b488ec7.js";import{U as Gt,I as xn,C as Fo,t as Ot}from"./event-fe80fd0c.js";import{t as Ut,E as rl}from"./index-be3c1320.js";import{E as il}from"./el-color-picker-1a8d3543.js";import{_ as ul}from"./index-73693531.js";import{f as cl}from"./vnode-b9ec7db4.js";import"./index-eba6e623.js";import"./scroll-a66dde9b.js";import"./validator-e4131fc3.js";import"./index-11a84590.js";import"./position-f84d51c4.js";const Lo=Symbol("breadcrumbKey"),dl=Ge({valueKey:{type:String,default:"value"},modelValue:{type:[String,Number],default:""},debounce:{type:Number,default:300},placement:{type:Ae(String),values:["top","top-start","top-end","bottom","bottom-start","bottom-end"],default:"bottom-start"},fetchSuggestions:{type:Ae([Function,Array]),default:pa},popperClass:{type:String,default:""},triggerOnFocus:{type:Boolean,default:!0},selectWhenUnmatched:{type:Boolean,default:!1},hideLoading:{type:Boolean,default:!1},label:{type:String},teleported:Mo.teleported,highlightFirstItem:{type:Boolean,default:!1},fitInputWidth:{type:Boolean,default:!1}}),pl={[Gt]:e=>Ye(e),[xn]:e=>Ye(e),[Fo]:e=>Ye(e),focus:e=>e instanceof FocusEvent,blur:e=>e instanceof FocusEvent,clear:()=>!0,select:e=>Eo(e)},fl=["aria-expanded","aria-owns"],ml={key:0},hl=["id","aria-selected","onClick"],vl={name:"ElAutocomplete",inheritAttrs:!1},_l=ie({...vl,props:dl,emits:pl,setup(e,{expose:t,emit:n}){const o=e,a="ElAutocomplete",l=sl(),s=fa(),i=ma(),r=Ee("autocomplete"),u=k(),m=k(),c=k(),v=k();let f=!1,h=!1;const _=k([]),b=k(-1),F=k(""),S=k(!1),w=k(!1),$=k(!1),B=E(()=>r.b(String(Ka()))),V=E(()=>s.style),G=E(()=>(_.value.length>0||$.value)&&S.value),q=E(()=>!o.hideLoading&&$.value),z=E(()=>u.value?Array.from(u.value.$el.querySelectorAll("input")):[]),T=async()=>{await $t(),G.value&&(F.value=`${u.value.$el.offsetWidth}px`)},I=()=>{h=!0},O=()=>{h=!1,b.value=-1},j=ol(async M=>{if(w.value)return;const de=De=>{$.value=!1,!w.value&&(An(De)?(_.value=De,b.value=o.highlightFirstItem?0:-1):Ot(a,"autocomplete suggestions must be an array"))};if($.value=!0,An(o.fetchSuggestions))de(o.fetchSuggestions);else{const De=await o.fetchSuggestions(M,de);An(De)&&de(De)}},o.debounce),x=M=>{const de=!!M;if(n(xn,M),n(Gt,M),w.value=!1,S.value||(S.value=de),!o.triggerOnFocus&&!M){w.value=!0,_.value=[];return}j(M)},ee=M=>{var de;i.value||(((de=M.target)==null?void 0:de.tagName)!=="INPUT"||z.value.includes(document.activeElement))&&(S.value=!0)},ue=M=>{n(Fo,M)},ce=M=>{h||(S.value=!0,n("focus",M),o.triggerOnFocus&&!f&&j(String(o.modelValue)))},X=M=>{h||n("blur",M)},le=()=>{S.value=!1,n(Gt,""),n("clear")},oe=async()=>{G.value&&b.value>=0&&b.value<_.value.length?_n(_.value[b.value]):o.selectWhenUnmatched&&(n("select",{value:o.modelValue}),_.value=[],b.value=-1)},yt=M=>{G.value&&(M.preventDefault(),M.stopPropagation(),xe())},xe=()=>{S.value=!1},At=()=>{var M;(M=u.value)==null||M.focus()},da=()=>{var M;(M=u.value)==null||M.blur()},_n=async M=>{n(xn,M[o.valueKey]),n(Gt,M[o.valueKey]),n("select",M),_.value=[],b.value=-1},gn=M=>{if(!G.value||$.value)return;if(M<0){b.value=-1;return}M>=_.value.length&&(M=_.value.length-1);const de=m.value.querySelector(`.${r.be("suggestion","wrap")}`),lt=de.querySelectorAll(`.${r.be("suggestion","list")} li`)[M],bn=de.scrollTop,{offsetTop:oo,scrollHeight:yn}=lt;oo+yn>bn+de.clientHeight&&(de.scrollTop+=yn),oo<bn&&(de.scrollTop-=yn),b.value=M,u.value.ref.setAttribute("aria-activedescendant",`${B.value}-item-${b.value}`)};return ha(v,()=>{G.value&&xe()}),Ue(()=>{u.value.ref.setAttribute("role","textbox"),u.value.ref.setAttribute("aria-autocomplete","list"),u.value.ref.setAttribute("aria-controls","id"),u.value.ref.setAttribute("aria-activedescendant",`${B.value}-item-${b.value}`),f=u.value.ref.hasAttribute("readonly")}),t({highlightedIndex:b,activated:S,loading:$,inputRef:u,popperRef:c,suggestions:_,handleSelect:_n,handleKeyEnter:oe,focus:At,blur:da,close:xe,highlight:gn}),(M,de)=>(g(),D(C(Le),{ref_key:"popperRef",ref:c,visible:C(G),placement:M.placement,"fallback-placements":["bottom-start","top-start"],"popper-class":[C(r).e("popper"),M.popperClass],teleported:M.teleported,"gpu-acceleration":!1,pure:"","manual-mode":"",effect:"light",trigger:"click",transition:`${C(r).namespace.value}-zoom-in-top`,persistent:"",onBeforeShow:T,onShow:I,onHide:O},{content:p(()=>[y("div",{ref_key:"regionRef",ref:m,class:ne([C(r).b("suggestion"),C(r).is("loading",C(q))]),style:ft({[M.fitInputWidth?"width":"minWidth"]:F.value,outline:"none"}),role:"region"},[d(C(Ft),{id:C(B),tag:"ul","wrap-class":C(r).be("suggestion","wrap"),"view-class":C(r).be("suggestion","list"),role:"listbox"},{default:p(()=>[C(q)?(g(),P("li",ml,[d(C(fe),{class:ne(C(r).is("loading"))},{default:p(()=>[d(C(va))]),_:1},8,["class"])])):(g(!0),P(Ce,{key:1},vt(_.value,(De,lt)=>(g(),P("li",{id:`${C(B)}-item-${lt}`,key:lt,class:ne({highlighted:b.value===lt}),role:"option","aria-selected":b.value===lt,onClick:bn=>_n(De)},[Z(M.$slots,"default",{item:De},()=>[he(W(De[M.valueKey]),1)])],10,hl))),128))]),_:3},8,["id","wrap-class","view-class"])],6)]),default:p(()=>[y("div",{ref_key:"listboxRef",ref:v,class:ne([C(r).b(),M.$attrs.class]),style:ft(C(V)),role:"combobox","aria-haspopup":"listbox","aria-expanded":C(G),"aria-owns":C(B)},[d(C(Ro),nt({ref_key:"inputRef",ref:u},C(l),{"model-value":M.modelValue,onInput:x,onChange:ue,onFocus:ce,onBlur:X,onClear:le,onKeydown:[de[0]||(de[0]=wt(Ln(De=>gn(b.value-1),["prevent"]),["up"])),de[1]||(de[1]=wt(Ln(De=>gn(b.value+1),["prevent"]),["down"])),wt(oe,["enter"]),wt(xe,["tab"]),wt(yt,["esc"])],onMousedown:ee}),Co({_:2},[M.$slots.prepend?{name:"prepend",fn:p(()=>[Z(M.$slots,"prepend")])}:void 0,M.$slots.append?{name:"append",fn:p(()=>[Z(M.$slots,"append")])}:void 0,M.$slots.prefix?{name:"prefix",fn:p(()=>[Z(M.$slots,"prefix")])}:void 0,M.$slots.suffix?{name:"suffix",fn:p(()=>[Z(M.$slots,"suffix")])}:void 0]),1040,["model-value","onKeydown"])],14,fl)]),_:3},8,["visible","placement","popper-class","teleported","transition"]))}});var gl=Se(_l,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/autocomplete/src/autocomplete.vue"]]);const bl=nn(gl),yl=Ge({separator:{type:String,default:"/"},separatorIcon:{type:ut}}),Al={name:"ElBreadcrumb"},wl=ie({...Al,props:yl,setup(e){const t=e,n=Ee("breadcrumb"),o=k();return Fe(Lo,t),Ue(()=>{const a=o.value.querySelectorAll(`.${n.e("item")}`);a.length&&a[a.length-1].setAttribute("aria-current","page")}),(a,l)=>(g(),P("div",{ref_key:"breadcrumb",ref:o,class:ne(C(n).b()),"aria-label":"Breadcrumb",role:"navigation"},[Z(a.$slots,"default")],2))}});var El=Se(wl,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/breadcrumb/src/breadcrumb.vue"]]);const Cl=Ge({to:{type:Ae([String,Object]),default:""},replace:{type:Boolean,default:!1}}),Sl={name:"ElBreadcrumbItem"},Il=ie({...Sl,props:Cl,setup(e){const t=e,n=_t(),o=se(Lo,void 0),a=Ee("breadcrumb"),{separator:l,separatorIcon:s}=So(o),i=n.appContext.config.globalProperties.$router,r=k(),u=()=>{!t.to||!i||(t.replace?i.replace(t.to):i.push(t.to))};return(m,c)=>(g(),P("span",{class:ne(C(a).e("item"))},[y("span",{ref_key:"link",ref:r,class:ne([C(a).e("inner"),C(a).is("link",!!m.to)]),role:"link",onClick:u},[Z(m.$slots,"default")],2),C(s)?(g(),D(C(fe),{key:0,class:ne(C(a).e("separator"))},{default:p(()=>[(g(),D($e(C(s))))]),_:1},8,["class"])):(g(),P("span",{key:1,class:ne(C(a).e("separator")),role:"presentation"},W(C(l)),3))],2))}});var Po=Se(Il,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/breadcrumb/src/breadcrumb-item.vue"]]);const kl=nn(El,{BreadcrumbItem:Po}),Tl=gt(Po),Ml={inheritAttrs:!1};function Dl(e,t,n,o,a,l){return Z(e.$slots,"default")}var Ol=Se(Ml,[["render",Dl],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/collection/src/collection.vue"]]);const $l={name:"ElCollectionItem",inheritAttrs:!1};function Nl(e,t,n,o,a,l){return Z(e.$slots,"default")}var Rl=Se($l,[["render",Nl],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/collection/src/collection-item.vue"]]);const xo="data-el-collection-item",Bo=e=>{const t=`El${e}Collection`,n=`${t}Item`,o=Symbol(t),a=Symbol(n),l={...Ol,name:t,setup(){const i=k(null),r=new Map;Fe(o,{itemMap:r,getItems:()=>{const m=C(i);if(!m)return[];const c=Array.from(m.querySelectorAll(`[${xo}]`));return[...r.values()].sort((f,h)=>c.indexOf(f.ref)-c.indexOf(h.ref))},collectionRef:i})}},s={...Rl,name:n,setup(i,{attrs:r}){const u=k(null),m=se(o,void 0);Fe(a,{collectionItemRef:u}),Ue(()=>{const c=C(u);c&&m.itemMap.set(c,{ref:c,...r})}),on(()=>{const c=C(u);m.itemMap.delete(c)})}};return{COLLECTION_INJECTION_KEY:o,COLLECTION_ITEM_INJECTION_KEY:a,ElCollection:l,ElCollectionItem:s}},Fl=Ge({style:{type:Ae([String,Array,Object])},currentTabId:{type:Ae(String)},defaultCurrentTabId:String,loop:Boolean,dir:{type:String,values:["ltr","rtl"],default:"ltr"},orientation:{type:Ae(String)},onBlur:Function,onFocus:Function,onMousedown:Function}),{ElCollection:Ll,ElCollectionItem:Pl,COLLECTION_INJECTION_KEY:Hn,COLLECTION_ITEM_INJECTION_KEY:xl}=Bo("RovingFocusGroup"),Kn=Symbol("elRovingFocusGroup"),Vo=Symbol("elRovingFocusGroupItem"),Bl={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"},Vl=(e,t)=>{if(t!=="rtl")return e;switch(e){case K.right:return K.left;case K.left:return K.right;default:return e}},Wl=(e,t,n)=>{const o=Vl(e.key,n);if(!(t==="vertical"&&[K.left,K.right].includes(o))&&!(t==="horizontal"&&[K.up,K.down].includes(o)))return Bl[o]},zl=(e,t)=>e.map((n,o)=>e[(o+t)%e.length]),jn=e=>{const{activeElement:t}=document;for(const n of e)if(n===t||(n.focus(),t!==document.activeElement))return},co="currentTabIdChange",po="rovingFocusGroup.entryFocus",Gl={bubbles:!1,cancelable:!0},Ul=ie({name:"ElRovingFocusGroupImpl",inheritAttrs:!1,props:Fl,emits:[co,"entryFocus"],setup(e,{emit:t}){var n;const o=k((n=e.currentTabId||e.defaultCurrentTabId)!=null?n:null),a=k(!1),l=k(!1),s=k(null),{getItems:i}=se(Hn,void 0),r=E(()=>[{outline:"none"},e.style]),u=_=>{t(co,_)},m=()=>{a.value=!0},c=Re(_=>{var b;(b=e.onMousedown)==null||b.call(e,_)},()=>{l.value=!0}),v=Re(_=>{var b;(b=e.onFocus)==null||b.call(e,_)},_=>{const b=!C(l),{target:F,currentTarget:S}=_;if(F===S&&b&&!C(a)){const w=new Event(po,Gl);if(S==null||S.dispatchEvent(w),!w.defaultPrevented){const $=i().filter(z=>z.focusable),B=$.find(z=>z.active),V=$.find(z=>z.id===C(o)),q=[B,V,...$].filter(Boolean).map(z=>z.ref);jn(q)}}l.value=!1}),f=Re(_=>{var b;(b=e.onBlur)==null||b.call(e,_)},()=>{a.value=!1}),h=(..._)=>{t("entryFocus",..._)};Fe(Kn,{currentTabbedId:_a(o),loop:dt(e,"loop"),tabIndex:E(()=>C(a)?-1:0),rovingFocusGroupRef:s,rovingFocusGroupRootStyle:r,orientation:dt(e,"orientation"),dir:dt(e,"dir"),onItemFocus:u,onItemShiftTab:m,onBlur:f,onFocus:v,onMousedown:c}),Ve(()=>e.currentTabId,_=>{o.value=_??null}),ga(s,po,h)}});function Yl(e,t,n,o,a,l){return Z(e.$slots,"default")}var Hl=Se(Ul,[["render",Yl],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/roving-focus-group/src/roving-focus-group-impl.vue"]]);const Kl=ie({name:"ElRovingFocusGroup",components:{ElFocusGroupCollection:Ll,ElRovingFocusGroupImpl:Hl}});function jl(e,t,n,o,a,l){const s=Y("el-roving-focus-group-impl"),i=Y("el-focus-group-collection");return g(),D(i,null,{default:p(()=>[d(s,ba(ya(e.$attrs)),{default:p(()=>[Z(e.$slots,"default")]),_:3},16)]),_:3})}var Ql=Se(Kl,[["render",jl],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/roving-focus-group/src/roving-focus-group.vue"]]);const Xl=ie({components:{ElRovingFocusCollectionItem:Pl},props:{focusable:{type:Boolean,default:!0},active:{type:Boolean,default:!1}},emits:["mousedown","focus","keydown"],setup(e,{emit:t}){const{currentTabbedId:n,loop:o,onItemFocus:a,onItemShiftTab:l}=se(Kn,void 0),{getItems:s}=se(Hn,void 0),i=Oo(),r=k(null),u=Re(f=>{t("mousedown",f)},f=>{e.focusable?a(C(i)):f.preventDefault()}),m=Re(f=>{t("focus",f)},()=>{a(C(i))}),c=Re(f=>{t("keydown",f)},f=>{const{key:h,shiftKey:_,target:b,currentTarget:F}=f;if(h===K.tab&&_){l();return}if(b!==F)return;const S=Wl(f);if(S){f.preventDefault();let $=s().filter(B=>B.focusable).map(B=>B.ref);switch(S){case"last":{$.reverse();break}case"prev":case"next":{S==="prev"&&$.reverse();const B=$.indexOf(F);$=o.value?zl($,B+1):$.slice(B+1);break}}$t(()=>{jn($)})}}),v=E(()=>n.value===C(i));return Fe(Vo,{rovingFocusGroupItemRef:r,tabIndex:E(()=>C(v)?0:-1),handleMousedown:u,handleFocus:m,handleKeydown:c}),{id:i,handleKeydown:c,handleFocus:m,handleMousedown:u}}});function Zl(e,t,n,o,a,l){const s=Y("el-roving-focus-collection-item");return g(),D(s,{id:e.id,focusable:e.focusable,active:e.active},{default:p(()=>[Z(e.$slots,"default")]),_:3},8,["id","focusable","active"])}var ql=Se(Xl,[["render",Zl],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/roving-focus-group/src/roving-focus-item.vue"]]);const Jl=Ge({trigger:ja.trigger,effect:{...Mo.effect,default:"light"},type:{type:Ae(String)},placement:{type:Ae(String),default:"bottom"},popperOptions:{type:Ae(Object),default:()=>({})},id:String,size:{type:String,default:""},splitButton:Boolean,hideOnClick:{type:Boolean,default:!0},loop:{type:Boolean,default:!0},showTimeout:{type:Number,default:150},hideTimeout:{type:Number,default:150},tabindex:{type:Ae([Number,String]),default:0},maxHeight:{type:Ae([Number,String]),default:""},popperClass:{type:String,default:""},disabled:{type:Boolean,default:!1},role:{type:String,default:"menu"},buttonProps:{type:Ae(Object)}}),Wo=Ge({command:{type:[Object,String,Number],default:()=>({})},disabled:Boolean,divided:Boolean,textValue:String,icon:{type:ut}}),es=Ge({onKeydown:{type:Ae(Function)}}),ts=[K.down,K.pageDown,K.home],zo=[K.up,K.pageUp,K.end],ns=[...ts,...zo],{ElCollection:os,ElCollectionItem:as,COLLECTION_INJECTION_KEY:ls,COLLECTION_ITEM_INJECTION_KEY:ss}=Bo("Dropdown"),dn=Symbol("elDropdown"),{ButtonGroup:rs}=bt,is=ie({name:"ElDropdown",components:{ElButton:bt,ElButtonGroup:rs,ElScrollbar:Ft,ElDropdownCollection:os,ElTooltip:Le,ElRovingFocusGroup:Ql,ElOnlyChild:Qa,ElIcon:fe,ArrowDown:Io},props:Jl,emits:["visible-change","click","command"],setup(e,{emit:t}){const n=_t(),o=Ee("dropdown"),{t:a}=al(),l=k(),s=k(),i=k(null),r=k(null),u=k(null),m=k(null),c=k(!1),v=[K.enter,K.space,K.down],f=E(()=>({maxHeight:Aa(e.maxHeight)})),h=E(()=>[o.m($.value)]),_=Oo().value,b=E(()=>e.id||_);function F(){S()}function S(){var x;(x=i.value)==null||x.onClose()}function w(){var x;(x=i.value)==null||x.onOpen()}const $=wa();function B(...x){t("command",...x)}function V(){}function G(){const x=C(r);x==null||x.focus(),m.value=null}function q(x){m.value=x}function z(x){c.value||(x.preventDefault(),x.stopImmediatePropagation())}function T(){t("visible-change",!0)}function I(x){(x==null?void 0:x.type)==="keydown"&&r.value.focus()}function O(){t("visible-change",!1)}return Fe(dn,{contentRef:r,role:E(()=>e.role),triggerId:b,isUsingKeyboard:c,onItemEnter:V,onItemLeave:G}),Fe("elDropdown",{instance:n,dropdownSize:$,handleClick:F,commandHandler:B,trigger:dt(e,"trigger"),hideOnClick:dt(e,"hideOnClick")}),{t:a,ns:o,scrollbar:u,wrapStyle:f,dropdownTriggerKls:h,dropdownSize:$,triggerId:b,triggerKeys:v,currentTabId:m,handleCurrentTabIdChange:q,handlerMainButtonClick:x=>{t("click",x)},handleEntryFocus:z,handleClose:S,handleOpen:w,handleBeforeShowTooltip:T,handleShowTooltip:I,handleBeforeHideTooltip:O,onFocusAfterTrapped:x=>{var ee,ue;x.preventDefault(),(ue=(ee=r.value)==null?void 0:ee.focus)==null||ue.call(ee,{preventScroll:!0})},popperRef:i,contentRef:r,triggeringElementRef:l,referenceElementRef:s}}});function us(e,t,n,o,a,l){var s;const i=Y("el-dropdown-collection"),r=Y("el-roving-focus-group"),u=Y("el-scrollbar"),m=Y("el-only-child"),c=Y("el-tooltip"),v=Y("el-button"),f=Y("arrow-down"),h=Y("el-icon"),_=Y("el-button-group");return g(),P("div",{class:ne([e.ns.b(),e.ns.is("disabled",e.disabled)])},[d(c,{ref:"popperRef",role:e.role,effect:e.effect,"fallback-placements":["bottom","top"],"popper-options":e.popperOptions,"gpu-acceleration":!1,"hide-after":e.trigger==="hover"?e.hideTimeout:0,"manual-mode":!0,placement:e.placement,"popper-class":[e.ns.e("popper"),e.popperClass],"reference-element":(s=e.referenceElementRef)==null?void 0:s.$el,trigger:e.trigger,"trigger-keys":e.triggerKeys,"trigger-target-el":e.contentRef,"show-after":e.trigger==="hover"?e.showTimeout:0,"stop-popper-mouse-event":!1,"virtual-ref":e.triggeringElementRef,"virtual-triggering":e.splitButton,disabled:e.disabled,transition:`${e.ns.namespace.value}-zoom-in-top`,teleported:"",pure:"",persistent:"",onBeforeShow:e.handleBeforeShowTooltip,onShow:e.handleShowTooltip,onBeforeHide:e.handleBeforeHideTooltip},Co({content:p(()=>[d(u,{ref:"scrollbar","wrap-style":e.wrapStyle,tag:"div","view-class":e.ns.e("list")},{default:p(()=>[d(r,{loop:e.loop,"current-tab-id":e.currentTabId,orientation:"horizontal",onCurrentTabIdChange:e.handleCurrentTabIdChange,onEntryFocus:e.handleEntryFocus},{default:p(()=>[d(i,null,{default:p(()=>[Z(e.$slots,"dropdown")]),_:3})]),_:3},8,["loop","current-tab-id","onCurrentTabIdChange","onEntryFocus"])]),_:3},8,["wrap-style","view-class"])]),_:2},[e.splitButton?void 0:{name:"default",fn:p(()=>[d(m,{id:e.triggerId,role:"button",tabindex:e.tabindex},{default:p(()=>[Z(e.$slots,"default")]),_:3},8,["id","tabindex"])])}]),1032,["role","effect","popper-options","hide-after","placement","popper-class","reference-element","trigger","trigger-keys","trigger-target-el","show-after","virtual-ref","virtual-triggering","disabled","transition","onBeforeShow","onShow","onBeforeHide"]),e.splitButton?(g(),D(_,{key:0},{default:p(()=>[d(v,nt({ref:"referenceElementRef"},e.buttonProps,{size:e.dropdownSize,type:e.type,disabled:e.disabled,tabindex:e.tabindex,onClick:e.handlerMainButtonClick}),{default:p(()=>[Z(e.$slots,"default")]),_:3},16,["size","type","disabled","tabindex","onClick"]),d(v,nt({id:e.triggerId,ref:"triggeringElementRef"},e.buttonProps,{role:"button",size:e.dropdownSize,type:e.type,class:e.ns.e("caret-button"),disabled:e.disabled,tabindex:e.tabindex,"aria-label":e.t("el.dropdown.toggleDropdown")}),{default:p(()=>[d(h,{class:ne(e.ns.e("icon"))},{default:p(()=>[d(f)]),_:1},8,["class"])]),_:1},16,["id","size","type","class","disabled","tabindex","aria-label"])]),_:3})):ge("v-if",!0)],2)}var cs=Se(is,[["render",us],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/dropdown/src/dropdown.vue"]]);const ds=ie({name:"DropdownItemImpl",components:{ElIcon:fe},props:Wo,emits:["pointermove","pointerleave","click","clickimpl"],setup(e,{emit:t}){const n=Ee("dropdown"),{role:o}=se(dn,void 0),{collectionItemRef:a}=se(ss,void 0),{collectionItemRef:l}=se(xl,void 0),{rovingFocusGroupItemRef:s,tabIndex:i,handleFocus:r,handleKeydown:u,handleMousedown:m}=se(Vo,void 0),c=$o(a,l,s),v=E(()=>o.value==="menu"?"menuitem":o.value==="navigation"?"link":"button"),f=Re(h=>{const{code:_}=h;if(_===K.enter||_===K.space)return h.preventDefault(),h.stopImmediatePropagation(),t("clickimpl",h),!0},u);return{ns:n,itemRef:c,dataset:{[xo]:""},role:v,tabIndex:i,handleFocus:r,handleKeydown:f,handleMousedown:m}}}),ps=["aria-disabled","tabindex","role"];function fs(e,t,n,o,a,l){const s=Y("el-icon");return g(),P(Ce,null,[e.divided?(g(),P("li",nt({key:0,role:"separator",class:e.ns.bem("menu","item","divided")},e.$attrs),null,16)):ge("v-if",!0),y("li",nt({ref:e.itemRef},{...e.dataset,...e.$attrs},{"aria-disabled":e.disabled,class:[e.ns.be("menu","item"),e.ns.is("disabled",e.disabled)],tabindex:e.tabIndex,role:e.role,onClick:t[0]||(t[0]=i=>e.$emit("clickimpl",i)),onFocus:t[1]||(t[1]=(...i)=>e.handleFocus&&e.handleFocus(...i)),onKeydown:t[2]||(t[2]=(...i)=>e.handleKeydown&&e.handleKeydown(...i)),onMousedown:t[3]||(t[3]=(...i)=>e.handleMousedown&&e.handleMousedown(...i)),onPointermove:t[4]||(t[4]=i=>e.$emit("pointermove",i)),onPointerleave:t[5]||(t[5]=i=>e.$emit("pointerleave",i))}),[e.icon?(g(),D(s,{key:0},{default:p(()=>[(g(),D($e(e.icon)))]),_:1})):ge("v-if",!0),Z(e.$slots,"default")],16,ps)],64)}var ms=Se(ds,[["render",fs],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/dropdown/src/dropdown-item-impl.vue"]]);const Go=()=>{const e=se("elDropdown",{}),t=E(()=>e==null?void 0:e.dropdownSize);return{elDropdown:e,_elDropdownSize:t}},hs=ie({name:"ElDropdownItem",components:{ElDropdownCollectionItem:as,ElRovingFocusItem:ql,ElDropdownItemImpl:ms},inheritAttrs:!1,props:Wo,emits:["pointermove","pointerleave","click"],setup(e,{emit:t,attrs:n}){const{elDropdown:o}=Go(),a=_t(),l=k(null),s=E(()=>{var f,h;return(h=(f=C(l))==null?void 0:f.textContent)!=null?h:""}),{onItemEnter:i,onItemLeave:r}=se(dn,void 0),u=Re(f=>(t("pointermove",f),f.defaultPrevented),uo(f=>{var h;e.disabled?r(f):(i(f),f.defaultPrevented||(h=f.currentTarget)==null||h.focus())})),m=Re(f=>(t("pointerleave",f),f.defaultPrevented),uo(f=>{r(f)})),c=Re(f=>(t("click",f),f.type!=="keydown"&&f.defaultPrevented),f=>{var h,_,b;if(e.disabled){f.stopImmediatePropagation();return}(h=o==null?void 0:o.hideOnClick)!=null&&h.value&&((_=o.handleClick)==null||_.call(o)),(b=o.commandHandler)==null||b.call(o,e.command,a,f)}),v=E(()=>({...e,...n}));return{handleClick:c,handlePointerMove:u,handlePointerLeave:m,textContent:s,propsAndAttrs:v}}});function vs(e,t,n,o,a,l){var s;const i=Y("el-dropdown-item-impl"),r=Y("el-roving-focus-item"),u=Y("el-dropdown-collection-item");return g(),D(u,{disabled:e.disabled,"text-value":(s=e.textValue)!=null?s:e.textContent},{default:p(()=>[d(r,{focusable:!e.disabled},{default:p(()=>[d(i,nt(e.propsAndAttrs,{onPointerleave:e.handlePointerLeave,onPointermove:e.handlePointerMove,onClickimpl:e.handleClick}),{default:p(()=>[Z(e.$slots,"default")]),_:3},16,["onPointerleave","onPointermove","onClickimpl"])]),_:3},8,["focusable"])]),_:3},8,["disabled","text-value"])}var Uo=Se(hs,[["render",vs],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/dropdown/src/dropdown-item.vue"]]);const _s=ie({name:"ElDropdownMenu",props:es,setup(e){const t=Ee("dropdown"),{_elDropdownSize:n}=Go(),o=n.value,{focusTrapRef:a,onKeydown:l}=se(ll,void 0),{contentRef:s,role:i,triggerId:r}=se(dn,void 0),{collectionRef:u,getItems:m}=se(ls,void 0),{rovingFocusGroupRef:c,rovingFocusGroupRootStyle:v,tabIndex:f,onBlur:h,onFocus:_,onMousedown:b}=se(Kn,void 0),{collectionRef:F}=se(Hn,void 0),S=E(()=>[t.b("menu"),t.bm("menu",o==null?void 0:o.value)]),w=$o(s,u,a,c,F),$=Re(V=>{var G;(G=e.onKeydown)==null||G.call(e,V)},V=>{const{currentTarget:G,code:q,target:z}=V;if(G.contains(z),K.tab===q&&V.stopImmediatePropagation(),V.preventDefault(),z!==C(s)||!ns.includes(q))return;const I=m().filter(O=>!O.disabled).map(O=>O.ref);zo.includes(q)&&I.reverse(),jn(I)});return{size:o,rovingFocusGroupRootStyle:v,tabIndex:f,dropdownKls:S,role:i,triggerId:r,dropdownListWrapperRef:w,handleKeydown:V=>{$(V),l(V)},onBlur:h,onFocus:_,onMousedown:b}}}),gs=["role","aria-labelledby"];function bs(e,t,n,o,a,l){return g(),P("ul",{ref:e.dropdownListWrapperRef,class:ne(e.dropdownKls),style:ft(e.rovingFocusGroupRootStyle),tabindex:-1,role:e.role,"aria-labelledby":e.triggerId,onBlur:t[0]||(t[0]=(...s)=>e.onBlur&&e.onBlur(...s)),onFocus:t[1]||(t[1]=(...s)=>e.onFocus&&e.onFocus(...s)),onKeydown:t[2]||(t[2]=(...s)=>e.handleKeydown&&e.handleKeydown(...s)),onMousedown:t[3]||(t[3]=(...s)=>e.onMousedown&&e.onMousedown(...s))},[Z(e.$slots,"default")],46,gs)}var Yo=Se(_s,[["render",bs],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/dropdown/src/dropdown-menu.vue"]]);const Qn=nn(cs,{DropdownItem:Uo,DropdownMenu:Yo}),Xn=gt(Uo),Zn=gt(Yo);let ys=class{constructor(t,n){this.parent=t,this.domNode=n,this.subIndex=0,this.subIndex=0,this.init()}init(){this.subMenuItems=this.domNode.querySelectorAll("li"),this.addListeners()}gotoSubIndex(t){t===this.subMenuItems.length?t=0:t<0&&(t=this.subMenuItems.length-1),this.subMenuItems[t].focus(),this.subIndex=t}addListeners(){const t=this.parent.domNode;Array.prototype.forEach.call(this.subMenuItems,n=>{n.addEventListener("keydown",o=>{let a=!1;switch(o.code){case K.down:{this.gotoSubIndex(this.subIndex+1),a=!0;break}case K.up:{this.gotoSubIndex(this.subIndex-1),a=!0;break}case K.tab:{Ut(t,"mouseleave");break}case K.enter:case K.space:{a=!0,o.currentTarget.click();break}}return a&&(o.preventDefault(),o.stopPropagation()),!1})})}},As=class{constructor(t,n){this.domNode=t,this.submenu=null,this.submenu=null,this.init(n)}init(t){this.domNode.setAttribute("tabindex","0");const n=this.domNode.querySelector(`.${t}-menu`);n&&(this.submenu=new ys(this,n)),this.addListeners()}addListeners(){this.domNode.addEventListener("keydown",t=>{let n=!1;switch(t.code){case K.down:{Ut(t.currentTarget,"mouseenter"),this.submenu&&this.submenu.gotoSubIndex(0),n=!0;break}case K.up:{Ut(t.currentTarget,"mouseenter"),this.submenu&&this.submenu.gotoSubIndex(this.submenu.subMenuItems.length-1),n=!0;break}case K.tab:{Ut(t.currentTarget,"mouseleave");break}case K.enter:case K.space:{n=!0,t.currentTarget.click();break}}n&&t.preventDefault()})}},ws=class{constructor(t,n){this.domNode=t,this.init(n)}init(t){const n=this.domNode.childNodes;Array.from(n).forEach(o=>{o.nodeType===1&&new As(o,t)})}};const Es=ie({name:"ElMenuCollapseTransition",setup(){const e=Ee("menu");return{listeners:{onBeforeEnter:n=>n.style.opacity="0.2",onEnter(n,o){xt(n,`${e.namespace.value}-opacity-transition`),n.style.opacity="1",o()},onAfterEnter(n){wn(n,`${e.namespace.value}-opacity-transition`),n.style.opacity=""},onBeforeLeave(n){n.dataset||(n.dataset={}),Ea(n,e.m("collapse"))?(wn(n,e.m("collapse")),n.dataset.oldOverflow=n.style.overflow,n.dataset.scrollWidth=n.clientWidth.toString(),xt(n,e.m("collapse"))):(xt(n,e.m("collapse")),n.dataset.oldOverflow=n.style.overflow,n.dataset.scrollWidth=n.clientWidth.toString(),wn(n,e.m("collapse"))),n.style.width=`${n.scrollWidth}px`,n.style.overflow="hidden"},onLeave(n){xt(n,"horizontal-collapse-transition"),n.style.width=`${n.dataset.scrollWidth}px`}}}}});function Cs(e,t,n,o,a,l){return g(),D(Gn,nt({mode:"out-in"},e.listeners),{default:p(()=>[Z(e.$slots,"default")]),_:3},16)}var Ss=Se(Es,[["render",Cs],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/menu/src/menu-collapse-transition.vue"]]);function Ho(e,t){const n=E(()=>{let a=e.parent;const l=[t.value];for(;a.type.name!=="ElMenu";)a.props.index&&l.unshift(a.props.index),a=a.parent;return l});return{parentMenu:E(()=>{let a=e.parent;for(;a&&!["ElMenu","ElSubMenu"].includes(a.type.name);)a=a.parent;return a}),indexPath:n}}function Is(e){return E(()=>{const n=e.backgroundColor;return n?new nl(n).shade(20).toString():""})}const Ko=(e,t)=>{const n=Ee("menu");return E(()=>n.cssVarBlock({"text-color":e.textColor||"","hover-text-color":e.textColor||"","bg-color":e.backgroundColor||"","hover-bg-color":Is(e).value||"","active-color":e.activeTextColor||"",level:`${t}`}))},ks=Ge({index:{type:String,required:!0},showTimeout:{type:Number,default:300},hideTimeout:{type:Number,default:300},popperClass:String,disabled:Boolean,popperAppendToBody:{type:Boolean,default:void 0},popperOffset:{type:Number,default:6},expandCloseIcon:{type:ut},expandOpenIcon:{type:ut},collapseCloseIcon:{type:ut},collapseOpenIcon:{type:ut}}),Cn="ElSubMenu";var qn=ie({name:Cn,props:ks,setup(e,{slots:t,expose:n}){const o=_t(),{indexPath:a,parentMenu:l}=Ho(o,E(()=>e.index)),s=Ee("menu"),i=Ee("sub-menu"),r=se("rootMenu");r||Ot(Cn,"can not inject root menu");const u=se(`subMenu:${l.value.uid}`);u||Ot(Cn,"can not inject sub menu");const m=k({}),c=k({});let v;const f=k(!1),h=k(),_=k(null),b=E(()=>I.value==="horizontal"&&S.value?"bottom-start":"right-start"),F=E(()=>I.value==="horizontal"&&S.value||I.value==="vertical"&&!r.props.collapse?e.expandCloseIcon&&e.expandOpenIcon?V.value?e.expandOpenIcon:e.expandCloseIcon:Io:e.collapseCloseIcon&&e.collapseOpenIcon?V.value?e.collapseOpenIcon:e.collapseCloseIcon:Ca),S=E(()=>u.level===0),w=E(()=>e.popperAppendToBody===void 0?S.value:!!e.popperAppendToBody),$=E(()=>r.props.collapse?`${s.namespace.value}-zoom-in-left`:`${s.namespace.value}-zoom-in-top`),B=E(()=>I.value==="horizontal"&&S.value?["bottom-start","bottom-end","top-start","top-end","right-start","left-start"]:["right-start","left-start","bottom-start","bottom-end","top-start","top-end"]),V=E(()=>r.openedMenus.includes(e.index)),G=E(()=>{let X=!1;return Object.values(m.value).forEach(le=>{le.active&&(X=!0)}),Object.values(c.value).forEach(le=>{le.active&&(X=!0)}),X}),q=E(()=>r.props.backgroundColor||""),z=E(()=>r.props.activeTextColor||""),T=E(()=>r.props.textColor||""),I=E(()=>r.props.mode),O=an({index:e.index,indexPath:a,active:G}),Q=E(()=>I.value!=="horizontal"?{color:T.value}:{borderBottomColor:G.value?r.props.activeTextColor?z.value:"":"transparent",color:G.value?z.value:T.value}),j=()=>{var X,le,oe;return(oe=(le=(X=_.value)==null?void 0:X.popperRef)==null?void 0:le.popperInstanceRef)==null?void 0:oe.destroy()},x=X=>{X||j()},ee=()=>{r.props.menuTrigger==="hover"&&r.props.mode==="horizontal"||r.props.collapse&&r.props.mode==="vertical"||e.disabled||r.handleSubMenuClick({index:e.index,indexPath:a.value,active:G.value})},ue=(X,le=e.showTimeout)=>{var oe;X.type!=="focus"&&(r.props.menuTrigger==="click"&&r.props.mode==="horizontal"||!r.props.collapse&&r.props.mode==="vertical"||e.disabled||(u.mouseInChild.value=!0,v==null||v(),{stop:v}=ao(()=>{r.openMenu(e.index,a.value)},le),w.value&&((oe=l.value.vnode.el)==null||oe.dispatchEvent(new MouseEvent("mouseenter")))))},ce=(X=!1)=>{var le,oe;r.props.menuTrigger==="click"&&r.props.mode==="horizontal"||!r.props.collapse&&r.props.mode==="vertical"||(v==null||v(),u.mouseInChild.value=!1,{stop:v}=ao(()=>!f.value&&r.closeMenu(e.index,a.value),e.hideTimeout),w.value&&X&&((le=o.parent)==null?void 0:le.type.name)==="ElSubMenu"&&((oe=u.handleMouseleave)==null||oe.call(u,!0)))};Ve(()=>r.props.collapse,X=>x(!!X));{const X=oe=>{c.value[oe.index]=oe},le=oe=>{delete c.value[oe.index]};Fe(`subMenu:${o.uid}`,{addSubMenu:X,removeSubMenu:le,handleMouseleave:ce,mouseInChild:f,level:u.level+1})}return n({opened:V}),Ue(()=>{r.addSubMenu(O),u.addSubMenu(O)}),on(()=>{u.removeSubMenu(O),r.removeSubMenu(O)}),()=>{var X;const le=[(X=t.title)==null?void 0:X.call(t),_e(fe,{class:i.e("icon-arrow"),style:{transform:V.value?e.expandCloseIcon&&e.expandOpenIcon||e.collapseCloseIcon&&e.collapseOpenIcon&&r.props.collapse?"none":"rotateZ(180deg)":"none"}},{default:()=>Ye(F.value)?_e(o.appContext.components[F.value]):_e(F.value)})],oe=Ko(r.props,u.level+1),yt=r.isMenuPopup?_e(Le,{ref:_,visible:V.value,effect:"light",pure:!0,offset:e.popperOffset,showArrow:!1,persistent:!0,popperClass:e.popperClass,placement:b.value,teleported:w.value,fallbackPlacements:B.value,transition:$.value,gpuAcceleration:!1},{content:()=>{var xe;return _e("div",{class:[s.m(I.value),s.m("popup-container"),e.popperClass],onMouseenter:At=>ue(At,100),onMouseleave:()=>ce(!0),onFocus:At=>ue(At,100)},[_e("ul",{class:[s.b(),s.m("popup"),s.m(`popup-${b.value}`)],style:oe.value},[(xe=t.default)==null?void 0:xe.call(t)])])},default:()=>_e("div",{class:i.e("title"),style:[Q.value,{backgroundColor:q.value}],onClick:ee},le)}):_e(Ce,{},[_e("div",{class:i.e("title"),style:[Q.value,{backgroundColor:q.value}],ref:h,onClick:ee},le),_e(ul,{},{default:()=>{var xe;return mt(_e("ul",{role:"menu",class:[s.b(),s.m("inline")],style:oe.value},[(xe=t.default)==null?void 0:xe.call(t)]),[[Nt,V.value]])}})]);return _e("li",{class:[i.b(),i.is("active",G.value),i.is("opened",V.value),i.is("disabled",e.disabled)],role:"menuitem",ariaHaspopup:!0,ariaExpanded:V.value,onMouseenter:ue,onMouseleave:()=>ce(!0),onFocus:ue},[yt])}}});const Ts=Ge({mode:{type:String,values:["horizontal","vertical"],default:"vertical"},defaultActive:{type:String,default:""},defaultOpeneds:{type:Ae(Array),default:()=>Sa([])},uniqueOpened:Boolean,router:Boolean,menuTrigger:{type:String,values:["hover","click"],default:"hover"},collapse:Boolean,backgroundColor:String,textColor:String,activeTextColor:String,collapseTransition:{type:Boolean,default:!0},ellipsis:{type:Boolean,default:!0}}),Sn=e=>Array.isArray(e)&&e.every(t=>Ye(t)),Ms={close:(e,t)=>Ye(e)&&Sn(t),open:(e,t)=>Ye(e)&&Sn(t),select:(e,t,n,o)=>Ye(e)&&Sn(t)&&Eo(n)&&(o===void 0||o instanceof Promise)};var Ds=ie({name:"ElMenu",props:Ts,emits:Ms,setup(e,{emit:t,slots:n,expose:o}){const a=_t(),l=a.appContext.config.globalProperties.$router,s=k(),i=Ee("menu"),r=Ee("sub-menu"),u=k(-1),m=k(e.defaultOpeneds&&!e.collapse?e.defaultOpeneds.slice(0):[]),c=k(e.defaultActive),v=k({}),f=k({}),h=E(()=>e.mode==="horizontal"||e.mode==="vertical"&&e.collapse),_=()=>{const T=c.value&&v.value[c.value];if(!T||e.mode==="horizontal"||e.collapse)return;T.indexPath.forEach(O=>{const Q=f.value[O];Q&&b(O,Q.indexPath)})},b=(T,I)=>{m.value.includes(T)||(e.uniqueOpened&&(m.value=m.value.filter(O=>I.includes(O))),m.value.push(T),t("open",T,I))},F=(T,I)=>{const O=m.value.indexOf(T);O!==-1&&m.value.splice(O,1),t("close",T,I)},S=({index:T,indexPath:I})=>{m.value.includes(T)?F(T,I):b(T,I)},w=T=>{(e.mode==="horizontal"||e.collapse)&&(m.value=[]);const{index:I,indexPath:O}=T;if(!(I===void 0||O===void 0))if(e.router&&l){const Q=T.route||I,j=l.push(Q).then(x=>(x||(c.value=I),x));t("select",I,O,{index:I,indexPath:O,route:Q},j)}else c.value=I,t("select",I,O,{index:I,indexPath:O})},$=T=>{const I=v.value,O=I[T]||c.value&&I[c.value]||I[e.defaultActive];O?c.value=O.index:c.value=T},B=()=>{var T,I;if(!s.value)return-1;const O=Array.from((I=(T=s.value)==null?void 0:T.childNodes)!=null?I:[]).filter(X=>X.nodeName!=="#text"||X.nodeValue),Q=64,j=Number.parseInt(getComputedStyle(s.value).paddingLeft,10),x=Number.parseInt(getComputedStyle(s.value).paddingRight,10),ee=s.value.clientWidth-j-x;let ue=0,ce=0;return O.forEach((X,le)=>{ue+=X.offsetWidth||0,ue<=ee-Q&&(ce=le+1)}),ce===O.length?-1:ce},V=(T,I=33.34)=>{let O;return()=>{O&&clearTimeout(O),O=setTimeout(()=>{T()},I)}};let G=!0;const q=()=>{const T=()=>{u.value=-1,$t(()=>{u.value=B()})};G?T():V(T)(),G=!1};Ve(()=>e.defaultActive,T=>{v.value[T]||(c.value=""),$(T)}),Ve(()=>e.collapse,T=>{T&&(m.value=[])}),Ve(v.value,_);let z;Ia(()=>{e.mode==="horizontal"&&e.ellipsis?z=ka(s,q).stop:z==null||z()});{const T=j=>{f.value[j.index]=j},I=j=>{delete f.value[j.index]};Fe("rootMenu",an({props:e,openedMenus:m,items:v,subMenus:f,activeIndex:c,isMenuPopup:h,addMenuItem:j=>{v.value[j.index]=j},removeMenuItem:j=>{delete v.value[j.index]},addSubMenu:T,removeSubMenu:I,openMenu:b,closeMenu:F,handleMenuItemClick:w,handleSubMenuClick:S})),Fe(`subMenu:${a.uid}`,{addSubMenu:T,removeSubMenu:I,mouseInChild:k(!1),level:0})}return Ue(()=>{e.mode==="horizontal"&&new ws(a.vnode.el,i.namespace.value)}),o({open:I=>{const{indexPath:O}=f.value[I];O.forEach(Q=>b(Q,O))},close:F,handleResize:q}),()=>{var T,I;let O=(I=(T=n.default)==null?void 0:T.call(n))!=null?I:[];const Q=[];if(e.mode==="horizontal"&&s.value){const ee=cl(O),ue=u.value===-1?ee:ee.slice(0,u.value),ce=u.value===-1?[]:ee.slice(u.value);ce!=null&&ce.length&&e.ellipsis&&(O=ue,Q.push(_e(qn,{index:"sub-menu-more",class:r.e("hide-arrow")},{title:()=>_e(fe,{class:r.e("icon-more")},{default:()=>_e(Ta)}),default:()=>ce})))}const j=Ko(e,0),x=_e("ul",{key:String(e.collapse),role:"menubar",ref:s,style:j.value,class:{[i.b()]:!0,[i.m(e.mode)]:!0,[i.m("collapse")]:e.collapse}},[...O,...Q]);return e.collapseTransition&&e.mode==="vertical"?_e(Ss,()=>x):x}}});const Os=Ge({index:{type:Ae([String,null]),default:null},route:{type:Ae([String,Object])},disabled:Boolean}),$s={click:e=>Ye(e.index)&&Array.isArray(e.indexPath)},In="ElMenuItem",Ns=ie({name:In,components:{ElTooltip:Le},props:Os,emits:$s,setup(e,{emit:t}){const n=_t(),o=se("rootMenu"),a=Ee("menu"),l=Ee("menu-item");o||Ot(In,"can not inject root menu");const{parentMenu:s,indexPath:i}=Ho(n,dt(e,"index")),r=se(`subMenu:${s.value.uid}`);r||Ot(In,"can not inject sub menu");const u=E(()=>e.index===o.activeIndex),m=an({index:e.index,indexPath:i,active:u}),c=()=>{e.disabled||(o.handleMenuItemClick({index:e.index,indexPath:i.value,route:e.route}),t("click",m))};return Ue(()=>{r.addSubMenu(m),o.addMenuItem(m)}),on(()=>{r.removeSubMenu(m),o.removeMenuItem(m)}),{Effect:Xa,parentMenu:s,rootMenu:o,active:u,nsMenu:a,nsMenuItem:l,handleClick:c}}});function Rs(e,t,n,o,a,l){const s=Y("el-tooltip");return g(),P("li",{class:ne([e.nsMenuItem.b(),e.nsMenuItem.is("active",e.active),e.nsMenuItem.is("disabled",e.disabled)]),role:"menuitem",tabindex:"-1",onClick:t[0]||(t[0]=(...i)=>e.handleClick&&e.handleClick(...i))},[e.parentMenu.type.name==="ElMenu"&&e.rootMenu.props.collapse&&e.$slots.title?(g(),D(s,{key:0,effect:e.Effect.DARK,placement:"right","fallback-placements":["left"],persistent:""},{content:p(()=>[Z(e.$slots,"title")]),default:p(()=>[y("div",{class:ne(e.nsMenu.be("tooltip","trigger"))},[Z(e.$slots,"default")],2)]),_:3},8,["effect"])):(g(),P(Ce,{key:1},[Z(e.$slots,"default"),Z(e.$slots,"title")],64))],2)}var jo=Se(Ns,[["render",Rs],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/menu/src/menu-item.vue"]]);const Fs={title:String},Ls="ElMenuItemGroup",Ps=ie({name:Ls,props:Fs,setup(){return{ns:Ee("menu-item-group")}}});function xs(e,t,n,o,a,l){return g(),P("li",{class:ne(e.ns.b())},[y("div",{class:ne(e.ns.e("title"))},[e.$slots.title?Z(e.$slots,"title",{key:1}):(g(),P(Ce,{key:0},[he(W(e.title),1)],64))],2),y("ul",null,[Z(e.$slots,"default")])],2)}var Qo=Se(Ps,[["render",xs],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/menu/src/menu-item-group.vue"]]);const pn=nn(Ds,{MenuItem:jo,MenuItemGroup:Qo,SubMenu:qn}),Xo=gt(jo);gt(Qo);const Zo=gt(qn);const Pe=e=>(Oa("data-v-e0a3559d"),e=e(),$a(),e),Bs={class:"layout-box"},Vs=Pe(()=>y("div",{class:"layout-dark"},null,-1)),Ws=Pe(()=>y("div",{class:"layout-container"},[y("div",{class:"layout-light"}),y("div",{class:"layout-content"})],-1)),zs=Pe(()=>y("div",{class:"layout-dark"},null,-1)),Gs=Pe(()=>y("div",{class:"layout-container"},[y("div",{class:"layout-light"}),y("div",{class:"layout-content"})],-1)),Us=Pe(()=>y("div",{class:"layout-dark"},null,-1)),Ys=Pe(()=>y("div",{class:"layout-content"},null,-1)),Hs=Pe(()=>y("div",{class:"layout-dark"},null,-1)),Ks=Pe(()=>y("div",{class:"layout-light"},null,-1)),js=Pe(()=>y("div",{class:"layout-content"},null,-1)),Qs=Pe(()=>y("br",null,null,-1)),Xs={class:"theme-item"},Zs=Pe(()=>y("br",null,null,-1)),qs={class:"theme-item"},Js={class:"theme-item"},er={class:"theme-item"},tr={class:"theme-item"},nr={class:"theme-item"},or={class:"theme-item"},ar={__name:"index",setup(e){const{switchDark:t,switchLight:n}=ko(),o=be(),a=E(()=>o.themeConfig),l=r=>{o.setThemeConfig({...a.value,layout:r})};Ve(()=>a.value.layout,()=>{document.body.setAttribute("class",a.value.layout)},{immediate:!0});const s=k(!1);Xt.on("openThemeDrawer",()=>s.value=!0);const i=()=>{a.value.isDark?t():n(),Xt.emit("resizeChart")};return(r,u)=>{const m=Y("Notification"),c=fe,v=Za,f=Y("CircleCheckFilled"),h=Le,_=Y("ColdDrink"),b=Ha,F=Y("Setting"),S=Ya;return g(),P("div",null,[d(S,{modelValue:s.value,"onUpdate:modelValue":u[11]||(u[11]=w=>s.value=w),title:r.$t("system.buju"),size:"450px"},{default:p(()=>[d(v,{class:"divider","content-position":"center"},{default:p(()=>[d(c,null,{default:p(()=>[d(m)]),_:1}),he(" "+W(r.$t("system.bujuqiehuan")),1)]),_:1}),y("div",Bs,[d(h,{effect:"dark",content:r.$t("system.zongxiang"),placement:"top","show-after":200},{default:p(()=>[y("div",{class:ne(["layout-item layout-vertical",a.value.layout=="vertical"?"is-active":""]),onClick:u[0]||(u[0]=w=>l("vertical"))},[Vs,Ws,a.value.layout=="vertical"?(g(),D(c,{key:0},{default:p(()=>[d(f)]),_:1})):ge("",!0)],2)]),_:1},8,["content"]),d(h,{effect:"dark",content:r.$t("system.jingdian"),placement:"top","show-after":200},{default:p(()=>[y("div",{class:ne(["layout-item layout-classic",a.value.layout=="classic"?"is-active":""]),onClick:u[1]||(u[1]=w=>l("classic"))},[zs,Gs,a.value.layout=="classic"?(g(),D(c,{key:0},{default:p(()=>[d(f)]),_:1})):ge("",!0)],2)]),_:1},8,["content"]),d(h,{effect:"dark",content:r.$t("system.hengxiang"),placement:"top","show-after":200},{default:p(()=>[y("div",{class:ne(["layout-item layout-transverse",a.value.layout=="transverse"?"is-active":""]),onClick:u[2]||(u[2]=w=>l("transverse"))},[Us,Ys,a.value.layout=="transverse"?(g(),D(c,{key:0},{default:p(()=>[d(f)]),_:1})):ge("",!0)],2)]),_:1},8,["content"]),d(h,{effect:"dark",content:r.$t("system.fenlan"),placement:"top","show-after":200},{default:p(()=>[y("div",{class:ne(["layout-item layout-columns",a.value.layout=="columns"?"is-active":""]),onClick:u[3]||(u[3]=w=>l("columns"))},[Hs,Ks,js,a.value.layout=="columns"?(g(),D(c,{key:0},{default:p(()=>[d(f)]),_:1})):ge("",!0)],2)]),_:1},8,["content"])]),Qs,d(v,{class:"divider","content-position":"center"},{default:p(()=>[d(c,null,{default:p(()=>[d(_)]),_:1}),he(" "+W(r.$t("system.systemtheme")),1)]),_:1}),y("div",Xs,[y("span",null,W(r.$t("system.darkmode")),1),d(b,{modelValue:a.value.isDark,"onUpdate:modelValue":u[4]||(u[4]=w=>a.value.isDark=w),"active-icon":C(Ma),"inactive-icon":C(Da),onChange:i,"inline-prompt":""},null,8,["modelValue","active-icon","inactive-icon"])]),Zs,d(v,{class:"divider","content-position":"center"},{default:p(()=>[d(c,null,{default:p(()=>[d(F)]),_:1}),he(" "+W(r.$t("system.jiemianshezhi")),1)]),_:1}),y("div",qs,[y("span",null,W(r.$t("system.zhediecaidan")),1),d(b,{modelValue:a.value.isCollapse,"onUpdate:modelValue":u[5]||(u[5]=w=>a.value.isCollapse=w)},null,8,["modelValue"])]),y("div",Js,[y("span",null,W(r.$t("system.mianbaoxiedaohang")),1),d(b,{modelValue:a.value.breadcrumb,"onUpdate:modelValue":u[6]||(u[6]=w=>a.value.breadcrumb=w)},null,8,["modelValue"])]),y("div",er,[y("span",null,W(r.$t("system.mianbaoxietubiao")),1),d(b,{modelValue:a.value.breadcrumbIcon,"onUpdate:modelValue":u[7]||(u[7]=w=>a.value.breadcrumbIcon=w)},null,8,["modelValue"])]),y("div",tr,[y("span",null,W(r.$t("system.biaoqiandaohanglan")),1),d(b,{modelValue:a.value.tabs,"onUpdate:modelValue":u[8]||(u[8]=w=>a.value.tabs=w)},null,8,["modelValue"])]),y("div",nr,[y("span",null,W(r.$t("system.biaoqianlantubiao")),1),d(b,{modelValue:a.value.tabsIcon,"onUpdate:modelValue":u[9]||(u[9]=w=>a.value.tabsIcon=w)},null,8,["modelValue"])]),y("div",or,[y("span",null,W(r.$t("system.wangzhanyejiao")),1),d(b,{modelValue:a.value.footer,"onUpdate:modelValue":u[10]||(u[10]=w=>a.value.footer=w)},null,8,["modelValue"])])]),_:1},8,["modelValue","title"])])}}},lr=me(ar,[["__scopeId","data-v-e0a3559d"]]);const sr={__name:"Maximize",setup(e){const t=be(),n=E(()=>t.themeConfig),o=()=>{t.setThemeConfig({...n.value,maximize:!1})};return Ve(()=>n.value.maximize,()=>{const a=document.getElementById("app");n.value.maximize?a.classList.add("main-maximize"):a.classList.remove("main-maximize")},{immediate:!0}),(a,l)=>{const s=Y("Close"),i=fe;return g(),P("div",{class:"maximize",onClick:o},[d(i,null,{default:p(()=>[d(s)]),_:1})])}}},rr=me(sr,[["__scopeId","data-v-79fa66d2"]]);/**!
 * Sortable 1.15.0
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function fo(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),n.push.apply(n,o)}return n}function ze(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?fo(Object(n),!0).forEach(function(o){ir(e,o,n[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):fo(Object(n)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(n,o))})}return e}function Yt(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Yt=function(t){return typeof t}:Yt=function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yt(e)}function ir(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ke(){return Ke=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},Ke.apply(this,arguments)}function ur(e,t){if(e==null)return{};var n={},o=Object.keys(e),a,l;for(l=0;l<o.length;l++)a=o[l],!(t.indexOf(a)>=0)&&(n[a]=e[a]);return n}function cr(e,t){if(e==null)return{};var n=ur(e,t),o,a;if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(a=0;a<l.length;a++)o=l[a],!(t.indexOf(o)>=0)&&Object.prototype.propertyIsEnumerable.call(e,o)&&(n[o]=e[o])}return n}var dr="1.15.0";function He(e){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(e)}var je=He(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),Lt=He(/Edge/i),mo=He(/firefox/i),It=He(/safari/i)&&!He(/chrome/i)&&!He(/android/i),qo=He(/iP(ad|od|hone)/i),Jo=He(/chrome/i)&&He(/android/i),ea={capture:!1,passive:!1};function H(e,t,n){e.addEventListener(t,n,!je&&ea)}function U(e,t,n){e.removeEventListener(t,n,!je&&ea)}function Zt(e,t){if(t){if(t[0]===">"&&(t=t.substring(1)),e)try{if(e.matches)return e.matches(t);if(e.msMatchesSelector)return e.msMatchesSelector(t);if(e.webkitMatchesSelector)return e.webkitMatchesSelector(t)}catch{return!1}return!1}}function pr(e){return e.host&&e!==document&&e.host.nodeType?e.host:e.parentNode}function Be(e,t,n,o){if(e){n=n||document;do{if(t!=null&&(t[0]===">"?e.parentNode===n&&Zt(e,t):Zt(e,t))||o&&e===n)return e;if(e===n)break}while(e=pr(e))}return null}var ho=/\s+/g;function ke(e,t,n){if(e&&t)if(e.classList)e.classList[n?"add":"remove"](t);else{var o=(" "+e.className+" ").replace(ho," ").replace(" "+t+" "," ");e.className=(o+(n?" "+t:"")).replace(ho," ")}}function N(e,t,n){var o=e&&e.style;if(o){if(n===void 0)return document.defaultView&&document.defaultView.getComputedStyle?n=document.defaultView.getComputedStyle(e,""):e.currentStyle&&(n=e.currentStyle),t===void 0?n:n[t];!(t in o)&&t.indexOf("webkit")===-1&&(t="-webkit-"+t),o[t]=n+(typeof n=="string"?"":"px")}}function pt(e,t){var n="";if(typeof e=="string")n=e;else do{var o=N(e,"transform");o&&o!=="none"&&(n=o+" "+n)}while(!t&&(e=e.parentNode));var a=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return a&&new a(n)}function ta(e,t,n){if(e){var o=e.getElementsByTagName(t),a=0,l=o.length;if(n)for(;a<l;a++)n(o[a],a);return o}return[]}function We(){var e=document.scrollingElement;return e||document.documentElement}function pe(e,t,n,o,a){if(!(!e.getBoundingClientRect&&e!==window)){var l,s,i,r,u,m,c;if(e!==window&&e.parentNode&&e!==We()?(l=e.getBoundingClientRect(),s=l.top,i=l.left,r=l.bottom,u=l.right,m=l.height,c=l.width):(s=0,i=0,r=window.innerHeight,u=window.innerWidth,m=window.innerHeight,c=window.innerWidth),(t||n)&&e!==window&&(a=a||e.parentNode,!je))do if(a&&a.getBoundingClientRect&&(N(a,"transform")!=="none"||n&&N(a,"position")!=="static")){var v=a.getBoundingClientRect();s-=v.top+parseInt(N(a,"border-top-width")),i-=v.left+parseInt(N(a,"border-left-width")),r=s+l.height,u=i+l.width;break}while(a=a.parentNode);if(o&&e!==window){var f=pt(a||e),h=f&&f.a,_=f&&f.d;f&&(s/=_,i/=h,c/=h,m/=_,r=s+m,u=i+c)}return{top:s,left:i,bottom:r,right:u,width:c,height:m}}}function vo(e,t,n){for(var o=Ze(e,!0),a=pe(e)[t];o;){var l=pe(o)[n],s=void 0;if(n==="top"||n==="left"?s=a>=l:s=a<=l,!s)return o;if(o===We())break;o=Ze(o,!1)}return!1}function ht(e,t,n,o){for(var a=0,l=0,s=e.children;l<s.length;){if(s[l].style.display!=="none"&&s[l]!==R.ghost&&(o||s[l]!==R.dragged)&&Be(s[l],n.draggable,e,!1)){if(a===t)return s[l];a++}l++}return null}function Jn(e,t){for(var n=e.lastElementChild;n&&(n===R.ghost||N(n,"display")==="none"||t&&!Zt(n,t));)n=n.previousElementSibling;return n||null}function Oe(e,t){var n=0;if(!e||!e.parentNode)return-1;for(;e=e.previousElementSibling;)e.nodeName.toUpperCase()!=="TEMPLATE"&&e!==R.clone&&(!t||Zt(e,t))&&n++;return n}function _o(e){var t=0,n=0,o=We();if(e)do{var a=pt(e),l=a.a,s=a.d;t+=e.scrollLeft*l,n+=e.scrollTop*s}while(e!==o&&(e=e.parentNode));return[t,n]}function fr(e,t){for(var n in e)if(e.hasOwnProperty(n)){for(var o in t)if(t.hasOwnProperty(o)&&t[o]===e[n][o])return Number(n)}return-1}function Ze(e,t){if(!e||!e.getBoundingClientRect)return We();var n=e,o=!1;do if(n.clientWidth<n.scrollWidth||n.clientHeight<n.scrollHeight){var a=N(n);if(n.clientWidth<n.scrollWidth&&(a.overflowX=="auto"||a.overflowX=="scroll")||n.clientHeight<n.scrollHeight&&(a.overflowY=="auto"||a.overflowY=="scroll")){if(!n.getBoundingClientRect||n===document.body)return We();if(o||t)return n;o=!0}}while(n=n.parentNode);return We()}function mr(e,t){if(e&&t)for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e}function kn(e,t){return Math.round(e.top)===Math.round(t.top)&&Math.round(e.left)===Math.round(t.left)&&Math.round(e.height)===Math.round(t.height)&&Math.round(e.width)===Math.round(t.width)}var kt;function na(e,t){return function(){if(!kt){var n=arguments,o=this;n.length===1?e.call(o,n[0]):e.apply(o,n),kt=setTimeout(function(){kt=void 0},t)}}}function hr(){clearTimeout(kt),kt=void 0}function oa(e,t,n){e.scrollLeft+=t,e.scrollTop+=n}function aa(e){var t=window.Polymer,n=window.jQuery||window.Zepto;return t&&t.dom?t.dom(e).cloneNode(!0):n?n(e).clone(!0)[0]:e.cloneNode(!0)}var Me="Sortable"+new Date().getTime();function vr(){var e=[],t;return{captureAnimationState:function(){if(e=[],!!this.options.animation){var o=[].slice.call(this.el.children);o.forEach(function(a){if(!(N(a,"display")==="none"||a===R.ghost)){e.push({target:a,rect:pe(a)});var l=ze({},e[e.length-1].rect);if(a.thisAnimationDuration){var s=pt(a,!0);s&&(l.top-=s.f,l.left-=s.e)}a.fromRect=l}})}},addAnimationState:function(o){e.push(o)},removeAnimationState:function(o){e.splice(fr(e,{target:o}),1)},animateAll:function(o){var a=this;if(!this.options.animation){clearTimeout(t),typeof o=="function"&&o();return}var l=!1,s=0;e.forEach(function(i){var r=0,u=i.target,m=u.fromRect,c=pe(u),v=u.prevFromRect,f=u.prevToRect,h=i.rect,_=pt(u,!0);_&&(c.top-=_.f,c.left-=_.e),u.toRect=c,u.thisAnimationDuration&&kn(v,c)&&!kn(m,c)&&(h.top-c.top)/(h.left-c.left)===(m.top-c.top)/(m.left-c.left)&&(r=gr(h,v,f,a.options)),kn(c,m)||(u.prevFromRect=m,u.prevToRect=c,r||(r=a.options.animation),a.animate(u,h,c,r)),r&&(l=!0,s=Math.max(s,r),clearTimeout(u.animationResetTimer),u.animationResetTimer=setTimeout(function(){u.animationTime=0,u.prevFromRect=null,u.fromRect=null,u.prevToRect=null,u.thisAnimationDuration=null},r),u.thisAnimationDuration=r)}),clearTimeout(t),l?t=setTimeout(function(){typeof o=="function"&&o()},s):typeof o=="function"&&o(),e=[]},animate:function(o,a,l,s){if(s){N(o,"transition",""),N(o,"transform","");var i=pt(this.el),r=i&&i.a,u=i&&i.d,m=(a.left-l.left)/(r||1),c=(a.top-l.top)/(u||1);o.animatingX=!!m,o.animatingY=!!c,N(o,"transform","translate3d("+m+"px,"+c+"px,0)"),this.forRepaintDummy=_r(o),N(o,"transition","transform "+s+"ms"+(this.options.easing?" "+this.options.easing:"")),N(o,"transform","translate3d(0,0,0)"),typeof o.animated=="number"&&clearTimeout(o.animated),o.animated=setTimeout(function(){N(o,"transition",""),N(o,"transform",""),o.animated=!1,o.animatingX=!1,o.animatingY=!1},s)}}}}function _r(e){return e.offsetWidth}function gr(e,t,n,o){return Math.sqrt(Math.pow(t.top-e.top,2)+Math.pow(t.left-e.left,2))/Math.sqrt(Math.pow(t.top-n.top,2)+Math.pow(t.left-n.left,2))*o.animation}var st=[],Tn={initializeByDefault:!0},Pt={mount:function(t){for(var n in Tn)Tn.hasOwnProperty(n)&&!(n in t)&&(t[n]=Tn[n]);st.forEach(function(o){if(o.pluginName===t.pluginName)throw"Sortable: Cannot mount plugin ".concat(t.pluginName," more than once")}),st.push(t)},pluginEvent:function(t,n,o){var a=this;this.eventCanceled=!1,o.cancel=function(){a.eventCanceled=!0};var l=t+"Global";st.forEach(function(s){n[s.pluginName]&&(n[s.pluginName][l]&&n[s.pluginName][l](ze({sortable:n},o)),n.options[s.pluginName]&&n[s.pluginName][t]&&n[s.pluginName][t](ze({sortable:n},o)))})},initializePlugins:function(t,n,o,a){st.forEach(function(i){var r=i.pluginName;if(!(!t.options[r]&&!i.initializeByDefault)){var u=new i(t,n,t.options);u.sortable=t,u.options=t.options,t[r]=u,Ke(o,u.defaults)}});for(var l in t.options)if(t.options.hasOwnProperty(l)){var s=this.modifyOption(t,l,t.options[l]);typeof s<"u"&&(t.options[l]=s)}},getEventProperties:function(t,n){var o={};return st.forEach(function(a){typeof a.eventProperties=="function"&&Ke(o,a.eventProperties.call(n[a.pluginName],t))}),o},modifyOption:function(t,n,o){var a;return st.forEach(function(l){t[l.pluginName]&&l.optionListeners&&typeof l.optionListeners[n]=="function"&&(a=l.optionListeners[n].call(t[l.pluginName],o))}),a}};function br(e){var t=e.sortable,n=e.rootEl,o=e.name,a=e.targetEl,l=e.cloneEl,s=e.toEl,i=e.fromEl,r=e.oldIndex,u=e.newIndex,m=e.oldDraggableIndex,c=e.newDraggableIndex,v=e.originalEvent,f=e.putSortable,h=e.extraEventProperties;if(t=t||n&&n[Me],!!t){var _,b=t.options,F="on"+o.charAt(0).toUpperCase()+o.substr(1);window.CustomEvent&&!je&&!Lt?_=new CustomEvent(o,{bubbles:!0,cancelable:!0}):(_=document.createEvent("Event"),_.initEvent(o,!0,!0)),_.to=s||n,_.from=i||n,_.item=a||n,_.clone=l,_.oldIndex=r,_.newIndex=u,_.oldDraggableIndex=m,_.newDraggableIndex=c,_.originalEvent=v,_.pullMode=f?f.lastPutMode:void 0;var S=ze(ze({},h),Pt.getEventProperties(o,t));for(var w in S)_[w]=S[w];n&&n.dispatchEvent(_),b[F]&&b[F].call(t,_)}}var yr=["evt"],Ie=function(t,n){var o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},a=o.evt,l=cr(o,yr);Pt.pluginEvent.bind(R)(t,n,ze({dragEl:A,parentEl:ae,ghostEl:L,rootEl:J,nextEl:tt,lastDownEl:Ht,cloneEl:te,cloneHidden:Xe,dragStarted:Et,putSortable:ve,activeSortable:R.active,originalEvent:a,oldIndex:ct,oldDraggableIndex:Tt,newIndex:Te,newDraggableIndex:Qe,hideGhostForTarget:ia,unhideGhostForTarget:ua,cloneNowHidden:function(){Xe=!0},cloneNowShown:function(){Xe=!1},dispatchSortableEvent:function(i){we({sortable:n,name:i,originalEvent:a})}},l))};function we(e){br(ze({putSortable:ve,cloneEl:te,targetEl:A,rootEl:J,oldIndex:ct,oldDraggableIndex:Tt,newIndex:Te,newDraggableIndex:Qe},e))}var A,ae,L,J,tt,Ht,te,Xe,ct,Te,Tt,Qe,Bt,ve,it=!1,qt=!1,Jt=[],Je,Ne,Mn,Dn,go,bo,Et,rt,Mt,Dt=!1,Vt=!1,Kt,ye,On=[],Bn=!1,en=[],fn=typeof document<"u",Wt=qo,yo=Lt||je?"cssFloat":"float",Ar=fn&&!Jo&&!qo&&"draggable"in document.createElement("div"),la=function(){if(fn){if(je)return!1;var e=document.createElement("x");return e.style.cssText="pointer-events:auto",e.style.pointerEvents==="auto"}}(),sa=function(t,n){var o=N(t),a=parseInt(o.width)-parseInt(o.paddingLeft)-parseInt(o.paddingRight)-parseInt(o.borderLeftWidth)-parseInt(o.borderRightWidth),l=ht(t,0,n),s=ht(t,1,n),i=l&&N(l),r=s&&N(s),u=i&&parseInt(i.marginLeft)+parseInt(i.marginRight)+pe(l).width,m=r&&parseInt(r.marginLeft)+parseInt(r.marginRight)+pe(s).width;if(o.display==="flex")return o.flexDirection==="column"||o.flexDirection==="column-reverse"?"vertical":"horizontal";if(o.display==="grid")return o.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(l&&i.float&&i.float!=="none"){var c=i.float==="left"?"left":"right";return s&&(r.clear==="both"||r.clear===c)?"vertical":"horizontal"}return l&&(i.display==="block"||i.display==="flex"||i.display==="table"||i.display==="grid"||u>=a&&o[yo]==="none"||s&&o[yo]==="none"&&u+m>a)?"vertical":"horizontal"},wr=function(t,n,o){var a=o?t.left:t.top,l=o?t.right:t.bottom,s=o?t.width:t.height,i=o?n.left:n.top,r=o?n.right:n.bottom,u=o?n.width:n.height;return a===i||l===r||a+s/2===i+u/2},Er=function(t,n){var o;return Jt.some(function(a){var l=a[Me].options.emptyInsertThreshold;if(!(!l||Jn(a))){var s=pe(a),i=t>=s.left-l&&t<=s.right+l,r=n>=s.top-l&&n<=s.bottom+l;if(i&&r)return o=a}}),o},ra=function(t){function n(l,s){return function(i,r,u,m){var c=i.options.group.name&&r.options.group.name&&i.options.group.name===r.options.group.name;if(l==null&&(s||c))return!0;if(l==null||l===!1)return!1;if(s&&l==="clone")return l;if(typeof l=="function")return n(l(i,r,u,m),s)(i,r,u,m);var v=(s?i:r).options.group.name;return l===!0||typeof l=="string"&&l===v||l.join&&l.indexOf(v)>-1}}var o={},a=t.group;(!a||Yt(a)!="object")&&(a={name:a}),o.name=a.name,o.checkPull=n(a.pull,!0),o.checkPut=n(a.put),o.revertClone=a.revertClone,t.group=o},ia=function(){!la&&L&&N(L,"display","none")},ua=function(){!la&&L&&N(L,"display","")};fn&&!Jo&&document.addEventListener("click",function(e){if(qt)return e.preventDefault(),e.stopPropagation&&e.stopPropagation(),e.stopImmediatePropagation&&e.stopImmediatePropagation(),qt=!1,!1},!0);var et=function(t){if(A){t=t.touches?t.touches[0]:t;var n=Er(t.clientX,t.clientY);if(n){var o={};for(var a in t)t.hasOwnProperty(a)&&(o[a]=t[a]);o.target=o.rootEl=n,o.preventDefault=void 0,o.stopPropagation=void 0,n[Me]._onDragOver(o)}}},Cr=function(t){A&&A.parentNode[Me]._isOutsideThisEl(t.target)};function R(e,t){if(!(e&&e.nodeType&&e.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(e));this.el=e,this.options=t=Ke({},t),e[Me]=this;var n={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(e.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return sa(e,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(s,i){s.setData("Text",i.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:R.supportPointer!==!1&&"PointerEvent"in window&&!It,emptyInsertThreshold:5};Pt.initializePlugins(this,e,n);for(var o in n)!(o in t)&&(t[o]=n[o]);ra(t);for(var a in this)a.charAt(0)==="_"&&typeof this[a]=="function"&&(this[a]=this[a].bind(this));this.nativeDraggable=t.forceFallback?!1:Ar,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?H(e,"pointerdown",this._onTapStart):(H(e,"mousedown",this._onTapStart),H(e,"touchstart",this._onTapStart)),this.nativeDraggable&&(H(e,"dragover",this),H(e,"dragenter",this)),Jt.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),Ke(this,vr())}R.prototype={constructor:R,_isOutsideThisEl:function(t){!this.el.contains(t)&&t!==this.el&&(rt=null)},_getDirection:function(t,n){return typeof this.options.direction=="function"?this.options.direction.call(this,t,n,A):this.options.direction},_onTapStart:function(t){if(t.cancelable){var n=this,o=this.el,a=this.options,l=a.preventOnFilter,s=t.type,i=t.touches&&t.touches[0]||t.pointerType&&t.pointerType==="touch"&&t,r=(i||t).target,u=t.target.shadowRoot&&(t.path&&t.path[0]||t.composedPath&&t.composedPath()[0])||r,m=a.filter;if($r(o),!A&&!(/mousedown|pointerdown/.test(s)&&t.button!==0||a.disabled)&&!u.isContentEditable&&!(!this.nativeDraggable&&It&&r&&r.tagName.toUpperCase()==="SELECT")&&(r=Be(r,a.draggable,o,!1),!(r&&r.animated)&&Ht!==r)){if(ct=Oe(r),Tt=Oe(r,a.draggable),typeof m=="function"){if(m.call(this,t,r,this)){we({sortable:n,rootEl:u,name:"filter",targetEl:r,toEl:o,fromEl:o}),Ie("filter",n,{evt:t}),l&&t.cancelable&&t.preventDefault();return}}else if(m&&(m=m.split(",").some(function(c){if(c=Be(u,c.trim(),o,!1),c)return we({sortable:n,rootEl:c,name:"filter",targetEl:r,fromEl:o,toEl:o}),Ie("filter",n,{evt:t}),!0}),m)){l&&t.cancelable&&t.preventDefault();return}a.handle&&!Be(u,a.handle,o,!1)||this._prepareDragStart(t,i,r)}}},_prepareDragStart:function(t,n,o){var a=this,l=a.el,s=a.options,i=l.ownerDocument,r;if(o&&!A&&o.parentNode===l){var u=pe(o);if(J=l,A=o,ae=A.parentNode,tt=A.nextSibling,Ht=o,Bt=s.group,R.dragged=A,Je={target:A,clientX:(n||t).clientX,clientY:(n||t).clientY},go=Je.clientX-u.left,bo=Je.clientY-u.top,this._lastX=(n||t).clientX,this._lastY=(n||t).clientY,A.style["will-change"]="all",r=function(){if(Ie("delayEnded",a,{evt:t}),R.eventCanceled){a._onDrop();return}a._disableDelayedDragEvents(),!mo&&a.nativeDraggable&&(A.draggable=!0),a._triggerDragStart(t,n),we({sortable:a,name:"choose",originalEvent:t}),ke(A,s.chosenClass,!0)},s.ignore.split(",").forEach(function(m){ta(A,m.trim(),$n)}),H(i,"dragover",et),H(i,"mousemove",et),H(i,"touchmove",et),H(i,"mouseup",a._onDrop),H(i,"touchend",a._onDrop),H(i,"touchcancel",a._onDrop),mo&&this.nativeDraggable&&(this.options.touchStartThreshold=4,A.draggable=!0),Ie("delayStart",this,{evt:t}),s.delay&&(!s.delayOnTouchOnly||n)&&(!this.nativeDraggable||!(Lt||je))){if(R.eventCanceled){this._onDrop();return}H(i,"mouseup",a._disableDelayedDrag),H(i,"touchend",a._disableDelayedDrag),H(i,"touchcancel",a._disableDelayedDrag),H(i,"mousemove",a._delayedDragTouchMoveHandler),H(i,"touchmove",a._delayedDragTouchMoveHandler),s.supportPointer&&H(i,"pointermove",a._delayedDragTouchMoveHandler),a._dragStartTimer=setTimeout(r,s.delay)}else r()}},_delayedDragTouchMoveHandler:function(t){var n=t.touches?t.touches[0]:t;Math.max(Math.abs(n.clientX-this._lastX),Math.abs(n.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){A&&$n(A),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var t=this.el.ownerDocument;U(t,"mouseup",this._disableDelayedDrag),U(t,"touchend",this._disableDelayedDrag),U(t,"touchcancel",this._disableDelayedDrag),U(t,"mousemove",this._delayedDragTouchMoveHandler),U(t,"touchmove",this._delayedDragTouchMoveHandler),U(t,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(t,n){n=n||t.pointerType=="touch"&&t,!this.nativeDraggable||n?this.options.supportPointer?H(document,"pointermove",this._onTouchMove):n?H(document,"touchmove",this._onTouchMove):H(document,"mousemove",this._onTouchMove):(H(A,"dragend",this),H(J,"dragstart",this._onDragStart));try{document.selection?jt(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(t,n){if(it=!1,J&&A){Ie("dragStarted",this,{evt:n}),this.nativeDraggable&&H(document,"dragover",Cr);var o=this.options;!t&&ke(A,o.dragClass,!1),ke(A,o.ghostClass,!0),R.active=this,t&&this._appendGhost(),we({sortable:this,name:"start",originalEvent:n})}else this._nulling()},_emulateDragOver:function(){if(Ne){this._lastX=Ne.clientX,this._lastY=Ne.clientY,ia();for(var t=document.elementFromPoint(Ne.clientX,Ne.clientY),n=t;t&&t.shadowRoot&&(t=t.shadowRoot.elementFromPoint(Ne.clientX,Ne.clientY),t!==n);)n=t;if(A.parentNode[Me]._isOutsideThisEl(t),n)do{if(n[Me]){var o=void 0;if(o=n[Me]._onDragOver({clientX:Ne.clientX,clientY:Ne.clientY,target:t,rootEl:n}),o&&!this.options.dragoverBubble)break}t=n}while(n=n.parentNode);ua()}},_onTouchMove:function(t){if(Je){var n=this.options,o=n.fallbackTolerance,a=n.fallbackOffset,l=t.touches?t.touches[0]:t,s=L&&pt(L,!0),i=L&&s&&s.a,r=L&&s&&s.d,u=Wt&&ye&&_o(ye),m=(l.clientX-Je.clientX+a.x)/(i||1)+(u?u[0]-On[0]:0)/(i||1),c=(l.clientY-Je.clientY+a.y)/(r||1)+(u?u[1]-On[1]:0)/(r||1);if(!R.active&&!it){if(o&&Math.max(Math.abs(l.clientX-this._lastX),Math.abs(l.clientY-this._lastY))<o)return;this._onDragStart(t,!0)}if(L){s?(s.e+=m-(Mn||0),s.f+=c-(Dn||0)):s={a:1,b:0,c:0,d:1,e:m,f:c};var v="matrix(".concat(s.a,",").concat(s.b,",").concat(s.c,",").concat(s.d,",").concat(s.e,",").concat(s.f,")");N(L,"webkitTransform",v),N(L,"mozTransform",v),N(L,"msTransform",v),N(L,"transform",v),Mn=m,Dn=c,Ne=l}t.cancelable&&t.preventDefault()}},_appendGhost:function(){if(!L){var t=this.options.fallbackOnBody?document.body:J,n=pe(A,!0,Wt,!0,t),o=this.options;if(Wt){for(ye=t;N(ye,"position")==="static"&&N(ye,"transform")==="none"&&ye!==document;)ye=ye.parentNode;ye!==document.body&&ye!==document.documentElement?(ye===document&&(ye=We()),n.top+=ye.scrollTop,n.left+=ye.scrollLeft):ye=We(),On=_o(ye)}L=A.cloneNode(!0),ke(L,o.ghostClass,!1),ke(L,o.fallbackClass,!0),ke(L,o.dragClass,!0),N(L,"transition",""),N(L,"transform",""),N(L,"box-sizing","border-box"),N(L,"margin",0),N(L,"top",n.top),N(L,"left",n.left),N(L,"width",n.width),N(L,"height",n.height),N(L,"opacity","0.8"),N(L,"position",Wt?"absolute":"fixed"),N(L,"zIndex","100000"),N(L,"pointerEvents","none"),R.ghost=L,t.appendChild(L),N(L,"transform-origin",go/parseInt(L.style.width)*100+"% "+bo/parseInt(L.style.height)*100+"%")}},_onDragStart:function(t,n){var o=this,a=t.dataTransfer,l=o.options;if(Ie("dragStart",this,{evt:t}),R.eventCanceled){this._onDrop();return}Ie("setupClone",this),R.eventCanceled||(te=aa(A),te.removeAttribute("id"),te.draggable=!1,te.style["will-change"]="",this._hideClone(),ke(te,this.options.chosenClass,!1),R.clone=te),o.cloneId=jt(function(){Ie("clone",o),!R.eventCanceled&&(o.options.removeCloneOnHide||J.insertBefore(te,A),o._hideClone(),we({sortable:o,name:"clone"}))}),!n&&ke(A,l.dragClass,!0),n?(qt=!0,o._loopId=setInterval(o._emulateDragOver,50)):(U(document,"mouseup",o._onDrop),U(document,"touchend",o._onDrop),U(document,"touchcancel",o._onDrop),a&&(a.effectAllowed="move",l.setData&&l.setData.call(o,a,A)),H(document,"drop",o),N(A,"transform","translateZ(0)")),it=!0,o._dragStartId=jt(o._dragStarted.bind(o,n,t)),H(document,"selectstart",o),Et=!0,It&&N(document.body,"user-select","none")},_onDragOver:function(t){var n=this.el,o=t.target,a,l,s,i=this.options,r=i.group,u=R.active,m=Bt===r,c=i.sort,v=ve||u,f,h=this,_=!1;if(Bn)return;function b(ce,X){Ie(ce,h,ze({evt:t,isOwner:m,axis:f?"vertical":"horizontal",revert:s,dragRect:a,targetRect:l,canSort:c,fromSortable:v,target:o,completed:S,onMove:function(oe,yt){return zt(J,n,A,a,oe,pe(oe),t,yt)},changed:w},X))}function F(){b("dragOverAnimationCapture"),h.captureAnimationState(),h!==v&&v.captureAnimationState()}function S(ce){return b("dragOverCompleted",{insertion:ce}),ce&&(m?u._hideClone():u._showClone(h),h!==v&&(ke(A,ve?ve.options.ghostClass:u.options.ghostClass,!1),ke(A,i.ghostClass,!0)),ve!==h&&h!==R.active?ve=h:h===R.active&&ve&&(ve=null),v===h&&(h._ignoreWhileAnimating=o),h.animateAll(function(){b("dragOverAnimationComplete"),h._ignoreWhileAnimating=null}),h!==v&&(v.animateAll(),v._ignoreWhileAnimating=null)),(o===A&&!A.animated||o===n&&!o.animated)&&(rt=null),!i.dragoverBubble&&!t.rootEl&&o!==document&&(A.parentNode[Me]._isOutsideThisEl(t.target),!ce&&et(t)),!i.dragoverBubble&&t.stopPropagation&&t.stopPropagation(),_=!0}function w(){Te=Oe(A),Qe=Oe(A,i.draggable),we({sortable:h,name:"change",toEl:n,newIndex:Te,newDraggableIndex:Qe,originalEvent:t})}if(t.preventDefault!==void 0&&t.cancelable&&t.preventDefault(),o=Be(o,i.draggable,n,!0),b("dragOver"),R.eventCanceled)return _;if(A.contains(t.target)||o.animated&&o.animatingX&&o.animatingY||h._ignoreWhileAnimating===o)return S(!1);if(qt=!1,u&&!i.disabled&&(m?c||(s=ae!==J):ve===this||(this.lastPutMode=Bt.checkPull(this,u,A,t))&&r.checkPut(this,u,A,t))){if(f=this._getDirection(t,o)==="vertical",a=pe(A),b("dragOverValid"),R.eventCanceled)return _;if(s)return ae=J,F(),this._hideClone(),b("revert"),R.eventCanceled||(tt?J.insertBefore(A,tt):J.appendChild(A)),S(!0);var $=Jn(n,i.draggable);if(!$||Tr(t,f,this)&&!$.animated){if($===A)return S(!1);if($&&n===t.target&&(o=$),o&&(l=pe(o)),zt(J,n,A,a,o,l,t,!!o)!==!1)return F(),$&&$.nextSibling?n.insertBefore(A,$.nextSibling):n.appendChild(A),ae=n,w(),S(!0)}else if($&&kr(t,f,this)){var B=ht(n,0,i,!0);if(B===A)return S(!1);if(o=B,l=pe(o),zt(J,n,A,a,o,l,t,!1)!==!1)return F(),n.insertBefore(A,B),ae=n,w(),S(!0)}else if(o.parentNode===n){l=pe(o);var V=0,G,q=A.parentNode!==n,z=!wr(A.animated&&A.toRect||a,o.animated&&o.toRect||l,f),T=f?"top":"left",I=vo(o,"top","top")||vo(A,"top","top"),O=I?I.scrollTop:void 0;rt!==o&&(G=l[T],Dt=!1,Vt=!z&&i.invertSwap||q),V=Mr(t,o,l,f,z?1:i.swapThreshold,i.invertedSwapThreshold==null?i.swapThreshold:i.invertedSwapThreshold,Vt,rt===o);var Q;if(V!==0){var j=Oe(A);do j-=V,Q=ae.children[j];while(Q&&(N(Q,"display")==="none"||Q===L))}if(V===0||Q===o)return S(!1);rt=o,Mt=V;var x=o.nextElementSibling,ee=!1;ee=V===1;var ue=zt(J,n,A,a,o,l,t,ee);if(ue!==!1)return(ue===1||ue===-1)&&(ee=ue===1),Bn=!0,setTimeout(Ir,30),F(),ee&&!x?n.appendChild(A):o.parentNode.insertBefore(A,ee?x:o),I&&oa(I,0,O-I.scrollTop),ae=A.parentNode,G!==void 0&&!Vt&&(Kt=Math.abs(G-pe(o)[T])),w(),S(!0)}if(n.contains(A))return S(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){U(document,"mousemove",this._onTouchMove),U(document,"touchmove",this._onTouchMove),U(document,"pointermove",this._onTouchMove),U(document,"dragover",et),U(document,"mousemove",et),U(document,"touchmove",et)},_offUpEvents:function(){var t=this.el.ownerDocument;U(t,"mouseup",this._onDrop),U(t,"touchend",this._onDrop),U(t,"pointerup",this._onDrop),U(t,"touchcancel",this._onDrop),U(document,"selectstart",this)},_onDrop:function(t){var n=this.el,o=this.options;if(Te=Oe(A),Qe=Oe(A,o.draggable),Ie("drop",this,{evt:t}),ae=A&&A.parentNode,Te=Oe(A),Qe=Oe(A,o.draggable),R.eventCanceled){this._nulling();return}it=!1,Vt=!1,Dt=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),Vn(this.cloneId),Vn(this._dragStartId),this.nativeDraggable&&(U(document,"drop",this),U(n,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),It&&N(document.body,"user-select",""),N(A,"transform",""),t&&(Et&&(t.cancelable&&t.preventDefault(),!o.dropBubble&&t.stopPropagation()),L&&L.parentNode&&L.parentNode.removeChild(L),(J===ae||ve&&ve.lastPutMode!=="clone")&&te&&te.parentNode&&te.parentNode.removeChild(te),A&&(this.nativeDraggable&&U(A,"dragend",this),$n(A),A.style["will-change"]="",Et&&!it&&ke(A,ve?ve.options.ghostClass:this.options.ghostClass,!1),ke(A,this.options.chosenClass,!1),we({sortable:this,name:"unchoose",toEl:ae,newIndex:null,newDraggableIndex:null,originalEvent:t}),J!==ae?(Te>=0&&(we({rootEl:ae,name:"add",toEl:ae,fromEl:J,originalEvent:t}),we({sortable:this,name:"remove",toEl:ae,originalEvent:t}),we({rootEl:ae,name:"sort",toEl:ae,fromEl:J,originalEvent:t}),we({sortable:this,name:"sort",toEl:ae,originalEvent:t})),ve&&ve.save()):Te!==ct&&Te>=0&&(we({sortable:this,name:"update",toEl:ae,originalEvent:t}),we({sortable:this,name:"sort",toEl:ae,originalEvent:t})),R.active&&((Te==null||Te===-1)&&(Te=ct,Qe=Tt),we({sortable:this,name:"end",toEl:ae,originalEvent:t}),this.save()))),this._nulling()},_nulling:function(){Ie("nulling",this),J=A=ae=L=tt=te=Ht=Xe=Je=Ne=Et=Te=Qe=ct=Tt=rt=Mt=ve=Bt=R.dragged=R.ghost=R.clone=R.active=null,en.forEach(function(t){t.checked=!0}),en.length=Mn=Dn=0},handleEvent:function(t){switch(t.type){case"drop":case"dragend":this._onDrop(t);break;case"dragenter":case"dragover":A&&(this._onDragOver(t),Sr(t));break;case"selectstart":t.preventDefault();break}},toArray:function(){for(var t=[],n,o=this.el.children,a=0,l=o.length,s=this.options;a<l;a++)n=o[a],Be(n,s.draggable,this.el,!1)&&t.push(n.getAttribute(s.dataIdAttr)||Or(n));return t},sort:function(t,n){var o={},a=this.el;this.toArray().forEach(function(l,s){var i=a.children[s];Be(i,this.options.draggable,a,!1)&&(o[l]=i)},this),n&&this.captureAnimationState(),t.forEach(function(l){o[l]&&(a.removeChild(o[l]),a.appendChild(o[l]))}),n&&this.animateAll()},save:function(){var t=this.options.store;t&&t.set&&t.set(this)},closest:function(t,n){return Be(t,n||this.options.draggable,this.el,!1)},option:function(t,n){var o=this.options;if(n===void 0)return o[t];var a=Pt.modifyOption(this,t,n);typeof a<"u"?o[t]=a:o[t]=n,t==="group"&&ra(o)},destroy:function(){Ie("destroy",this);var t=this.el;t[Me]=null,U(t,"mousedown",this._onTapStart),U(t,"touchstart",this._onTapStart),U(t,"pointerdown",this._onTapStart),this.nativeDraggable&&(U(t,"dragover",this),U(t,"dragenter",this)),Array.prototype.forEach.call(t.querySelectorAll("[draggable]"),function(n){n.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Jt.splice(Jt.indexOf(this.el),1),this.el=t=null},_hideClone:function(){if(!Xe){if(Ie("hideClone",this),R.eventCanceled)return;N(te,"display","none"),this.options.removeCloneOnHide&&te.parentNode&&te.parentNode.removeChild(te),Xe=!0}},_showClone:function(t){if(t.lastPutMode!=="clone"){this._hideClone();return}if(Xe){if(Ie("showClone",this),R.eventCanceled)return;A.parentNode==J&&!this.options.group.revertClone?J.insertBefore(te,A):tt?J.insertBefore(te,tt):J.appendChild(te),this.options.group.revertClone&&this.animate(A,te),N(te,"display",""),Xe=!1}}};function Sr(e){e.dataTransfer&&(e.dataTransfer.dropEffect="move"),e.cancelable&&e.preventDefault()}function zt(e,t,n,o,a,l,s,i){var r,u=e[Me],m=u.options.onMove,c;return window.CustomEvent&&!je&&!Lt?r=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(r=document.createEvent("Event"),r.initEvent("move",!0,!0)),r.to=t,r.from=e,r.dragged=n,r.draggedRect=o,r.related=a||t,r.relatedRect=l||pe(t),r.willInsertAfter=i,r.originalEvent=s,e.dispatchEvent(r),m&&(c=m.call(u,r,s)),c}function $n(e){e.draggable=!1}function Ir(){Bn=!1}function kr(e,t,n){var o=pe(ht(n.el,0,n.options,!0)),a=10;return t?e.clientX<o.left-a||e.clientY<o.top&&e.clientX<o.right:e.clientY<o.top-a||e.clientY<o.bottom&&e.clientX<o.left}function Tr(e,t,n){var o=pe(Jn(n.el,n.options.draggable)),a=10;return t?e.clientX>o.right+a||e.clientX<=o.right&&e.clientY>o.bottom&&e.clientX>=o.left:e.clientX>o.right&&e.clientY>o.top||e.clientX<=o.right&&e.clientY>o.bottom+a}function Mr(e,t,n,o,a,l,s,i){var r=o?e.clientY:e.clientX,u=o?n.height:n.width,m=o?n.top:n.left,c=o?n.bottom:n.right,v=!1;if(!s){if(i&&Kt<u*a){if(!Dt&&(Mt===1?r>m+u*l/2:r<c-u*l/2)&&(Dt=!0),Dt)v=!0;else if(Mt===1?r<m+Kt:r>c-Kt)return-Mt}else if(r>m+u*(1-a)/2&&r<c-u*(1-a)/2)return Dr(t)}return v=v||s,v&&(r<m+u*l/2||r>c-u*l/2)?r>m+u/2?1:-1:0}function Dr(e){return Oe(A)<Oe(e)?1:-1}function Or(e){for(var t=e.tagName+e.className+e.src+e.href+e.textContent,n=t.length,o=0;n--;)o+=t.charCodeAt(n);return o.toString(36)}function $r(e){en.length=0;for(var t=e.getElementsByTagName("input"),n=t.length;n--;){var o=t[n];o.checked&&en.push(o)}}function jt(e){return setTimeout(e,0)}function Vn(e){return clearTimeout(e)}fn&&H(document,"touchmove",function(e){(R.active||it)&&e.cancelable&&e.preventDefault()});R.utils={on:H,off:U,css:N,find:ta,is:function(t,n){return!!Be(t,n,t,!1)},extend:mr,throttle:na,closest:Be,toggleClass:ke,clone:aa,index:Oe,nextTick:jt,cancelNextTick:Vn,detectDirection:sa,getChild:ht};R.get=function(e){return e[Me]};R.mount=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];t[0].constructor===Array&&(t=t[0]),t.forEach(function(o){if(!o.prototype||!o.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(o));o.utils&&(R.utils=ze(ze({},R.utils),o.utils)),Pt.mount(o)})};R.create=function(e,t){return new R(e,t)};R.version=dr;var re=[],Ct,Wn,zn=!1,Nn,Rn,tn,St;function Nr(){function e(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var t in this)t.charAt(0)==="_"&&typeof this[t]=="function"&&(this[t]=this[t].bind(this))}return e.prototype={dragStarted:function(n){var o=n.originalEvent;this.sortable.nativeDraggable?H(document,"dragover",this._handleAutoScroll):this.options.supportPointer?H(document,"pointermove",this._handleFallbackAutoScroll):o.touches?H(document,"touchmove",this._handleFallbackAutoScroll):H(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(n){var o=n.originalEvent;!this.options.dragOverBubble&&!o.rootEl&&this._handleAutoScroll(o)},drop:function(){this.sortable.nativeDraggable?U(document,"dragover",this._handleAutoScroll):(U(document,"pointermove",this._handleFallbackAutoScroll),U(document,"touchmove",this._handleFallbackAutoScroll),U(document,"mousemove",this._handleFallbackAutoScroll)),Ao(),Qt(),hr()},nulling:function(){tn=Wn=Ct=zn=St=Nn=Rn=null,re.length=0},_handleFallbackAutoScroll:function(n){this._handleAutoScroll(n,!0)},_handleAutoScroll:function(n,o){var a=this,l=(n.touches?n.touches[0]:n).clientX,s=(n.touches?n.touches[0]:n).clientY,i=document.elementFromPoint(l,s);if(tn=n,o||this.options.forceAutoScrollFallback||Lt||je||It){Fn(n,this.options,i,o);var r=Ze(i,!0);zn&&(!St||l!==Nn||s!==Rn)&&(St&&Ao(),St=setInterval(function(){var u=Ze(document.elementFromPoint(l,s),!0);u!==r&&(r=u,Qt()),Fn(n,a.options,u,o)},10),Nn=l,Rn=s)}else{if(!this.options.bubbleScroll||Ze(i,!0)===We()){Qt();return}Fn(n,this.options,Ze(i,!1),!1)}}},Ke(e,{pluginName:"scroll",initializeByDefault:!0})}function Qt(){re.forEach(function(e){clearInterval(e.pid)}),re=[]}function Ao(){clearInterval(St)}var Fn=na(function(e,t,n,o){if(t.scroll){var a=(e.touches?e.touches[0]:e).clientX,l=(e.touches?e.touches[0]:e).clientY,s=t.scrollSensitivity,i=t.scrollSpeed,r=We(),u=!1,m;Wn!==n&&(Wn=n,Qt(),Ct=t.scroll,m=t.scrollFn,Ct===!0&&(Ct=Ze(n,!0)));var c=0,v=Ct;do{var f=v,h=pe(f),_=h.top,b=h.bottom,F=h.left,S=h.right,w=h.width,$=h.height,B=void 0,V=void 0,G=f.scrollWidth,q=f.scrollHeight,z=N(f),T=f.scrollLeft,I=f.scrollTop;f===r?(B=w<G&&(z.overflowX==="auto"||z.overflowX==="scroll"||z.overflowX==="visible"),V=$<q&&(z.overflowY==="auto"||z.overflowY==="scroll"||z.overflowY==="visible")):(B=w<G&&(z.overflowX==="auto"||z.overflowX==="scroll"),V=$<q&&(z.overflowY==="auto"||z.overflowY==="scroll"));var O=B&&(Math.abs(S-a)<=s&&T+w<G)-(Math.abs(F-a)<=s&&!!T),Q=V&&(Math.abs(b-l)<=s&&I+$<q)-(Math.abs(_-l)<=s&&!!I);if(!re[c])for(var j=0;j<=c;j++)re[j]||(re[j]={});(re[c].vx!=O||re[c].vy!=Q||re[c].el!==f)&&(re[c].el=f,re[c].vx=O,re[c].vy=Q,clearInterval(re[c].pid),(O!=0||Q!=0)&&(u=!0,re[c].pid=setInterval((function(){o&&this.layer===0&&R.active._onTouchMove(tn);var x=re[this.layer].vy?re[this.layer].vy*i:0,ee=re[this.layer].vx?re[this.layer].vx*i:0;typeof m=="function"&&m.call(R.dragged.parentNode[Me],ee,x,e,tn,re[this.layer].el)!=="continue"||oa(re[this.layer].el,ee,x)}).bind({layer:c}),24))),c++}while(t.bubbleScroll&&v!==r&&(v=Ze(v,!1)));zn=u}},30),ca=function(t){var n=t.originalEvent,o=t.putSortable,a=t.dragEl,l=t.activeSortable,s=t.dispatchSortableEvent,i=t.hideGhostForTarget,r=t.unhideGhostForTarget;if(n){var u=o||l;i();var m=n.changedTouches&&n.changedTouches.length?n.changedTouches[0]:n,c=document.elementFromPoint(m.clientX,m.clientY);r(),u&&!u.el.contains(c)&&(s("spill"),this.onSpill({dragEl:a,putSortable:o}))}};function eo(){}eo.prototype={startIndex:null,dragStart:function(t){var n=t.oldDraggableIndex;this.startIndex=n},onSpill:function(t){var n=t.dragEl,o=t.putSortable;this.sortable.captureAnimationState(),o&&o.captureAnimationState();var a=ht(this.sortable.el,this.startIndex,this.options);a?this.sortable.el.insertBefore(n,a):this.sortable.el.appendChild(n),this.sortable.animateAll(),o&&o.animateAll()},drop:ca};Ke(eo,{pluginName:"revertOnSpill"});function to(){}to.prototype={onSpill:function(t){var n=t.dragEl,o=t.putSortable,a=o||this.sortable;a.captureAnimationState(),n.parentNode&&n.parentNode.removeChild(n),a.animateAll()},drop:ca};Ke(to,{pluginName:"removeOnSpill"});R.mount(new Nr);R.mount(to,eo);const Rr={__name:"MoreButton",setup(e){const t=ot(),n=qe(),o=Do(),a=be(),l=Yn(),s=E(()=>a.themeConfig),i=se("refresh"),r=()=>{setTimeout(()=>{l.removeKeepLiveName(t.name),i(!1),$t(()=>{l.addKeepLiveName(t.name),i(!0)})},0)},u=()=>{a.setThemeConfig({...s.value,maximize:!0})},m=()=>{t.meta.isAffix||(o.removeTabs(t.fullPath),l.removeKeepLiveName(t.name))},c=()=>{o.closeMultipleTab(t.fullPath),l.clearMultipleKeepAlive([t.name])},v=()=>{o.closeMultipleTab(),l.clearMultipleKeepAlive(),n.push(Na)};return(f,h)=>{const _=Y("arrow-down"),b=fe,F=bt,S=Y("Refresh"),w=Xn,$=Y("FullScreen"),B=Y("Remove"),V=Y("CircleClose"),G=Y("FolderDelete"),q=Zn,z=Qn;return g(),D(z,{trigger:"click"},{dropdown:p(()=>[d(q,null,{default:p(()=>[d(w,{onClick:r},{default:p(()=>[d(b,null,{default:p(()=>[d(S)]),_:1}),he(W(f.$t("action.fresh")),1)]),_:1}),d(w,{onClick:u},{default:p(()=>[d(b,null,{default:p(()=>[d($)]),_:1}),he(W(f.$t("action.max")),1)]),_:1}),d(w,{divided:"",onClick:m},{default:p(()=>[d(b,null,{default:p(()=>[d(B)]),_:1}),he(W(f.$t("action.close")),1)]),_:1}),d(w,{onClick:c},{default:p(()=>[d(b,null,{default:p(()=>[d(V)]),_:1}),he(W(f.$t("action.closeOther")),1)]),_:1}),d(w,{onClick:v},{default:p(()=>[d(b,null,{default:p(()=>[d(G)]),_:1}),he(W(f.$t("action.closeAll")),1)]),_:1})]),_:1})]),default:p(()=>[d(F,{size:"small",type:"primary"},{default:p(()=>[y("span",null,W(f.$t("action.more")),1),d(b,{class:"el-icon--right"},{default:p(()=>[d(_)]),_:1})]),_:1})]),_:1})}}},Fr=me(Rr,[["__scopeId","data-v-d0f2a9c6"]]);const Lr={class:"tabs-box"},Pr={class:"tabs-menu"},xr={__name:"index",setup(e){const t=ot(),n=qe(),o=Do(),a=be(),l=at(),s=Yn(),i=k(t.fullPath),r=E(()=>o.tabsMenuList),u=E(()=>a.themeConfig);Ue(()=>{m(),c()});const m=()=>{R.create(document.querySelector(".el-tabs__nav"),{draggable:".el-tabs__item",animation:300,onEnd({newIndex:h,oldIndex:_}){const b=[...o.tabsMenuList],F=b.splice(_,1)[0];b.splice(h,0,F),o.setTabs(b)}})},c=()=>{l.flatMenuListGet.forEach(h=>{if(h.meta.isAffix&&!h.meta.isHide&&!h.meta.isFull){const _={icon:h.meta.icon,title:h.title,path:h.path,name:h.name,close:!h.meta.isAffix};o.addTabs(_)}})};Ve(()=>t.fullPath,()=>{if(t.meta.isFull)return;i.value=t.fullPath;const h=l.flatMenuListGet.find(b=>b.path===t.fullPath),_={icon:t.meta.icon,title:h.title,path:t.fullPath,name:t.name,close:!t.meta.isAffix};o.addTabs(_),t.meta.isKeepAlive&&s.addKeepLiveName(t.name)},{immediate:!0});const v=h=>{const _=h.props.name;n.push(_)},f=h=>{const _=o.tabsMenuList.filter(b=>b.path==h)[0].name||"";s.removeKeepLiveName(_),o.removeTabs(h,h==t.fullPath)};return(h,_)=>{const b=bt,F=el,S=tl;return g(),P("div",Lr,[y("div",Pr,[d(S,{modelValue:i.value,"onUpdate:modelValue":_[0]||(_[0]=w=>i.value=w),type:"card",onTabClick:v,onTabRemove:f},{default:p(()=>[(g(!0),P(Ce,null,vt(r.value,w=>(g(),D(F,{key:w.path,label:w.title,name:w.path,closable:w.close},{label:p(()=>[d(b,{size:"default",icon:u.value.tabsIcon?w.icon:"",type:i.value===w.path?"primary":""},{default:p(()=>[he(W(h.$t(`menus.${w.name}`)),1)]),_:2},1032,["icon","type"])]),_:2},1032,["label","name","closable"]))),128))]),_:1},8,["modelValue"]),d(Fr)])])}}},Br=me(xr,[["__scopeId","data-v-c7cc3e85"]]);const Vr={class:"footer flx-center"},Wr={href:"https://github.com/1164095457/vue-diverse-admin",target:"_blank"},zr={__name:"index",setup(e){return(t,n)=>(g(),P("div",Vr,[y("a",Wr,W(C(Rt)),1)]))}},Gr=me(zr,[["__scopeId","data-v-e505f1fa"]]);const Ur={__name:"index",setup(e){const t=be(),n=Yn(),o=E(()=>t.themeConfig),a=E(()=>t.themeConfig.isCollapse),l=k(!0);Fe("refresh",u=>{l.value=u});const i=k(0),r=()=>{i.value=document.body.clientWidth,!a.value&&i.value<1200&&t.setThemeConfig({...o.value,isCollapse:!0}),a.value&&i.value>1200&&t.setThemeConfig({...o.value,isCollapse:!1})};return window.addEventListener("resize",r),on(()=>{window.removeEventListener("resize",r)}),(u,m)=>{const c=Y("router-view"),v=qa,f=Ja;return g(),P(Ce,null,[o.value.maximize?(g(),D(rr,{key:0})):ge("",!0),o.value.tabs?(g(),D(Br,{key:1})):ge("",!0),d(v,null,{default:p(()=>[d(c,null,{default:p(({Component:h,route:_})=>[h?(g(),D(Gn,{key:0,appear:"",name:"fade-transform"},{default:p(()=>[(g(),D(Ra,{include:C(n).keepLiveName},[l.value?(g(),D($e(h),{key:_.path})):ge("",!0)],1032,["include"]))]),_:2},1024)):ge("",!0)]),_:1})]),_:1}),o.value.footer?(g(),D(f,{key:2},{default:p(()=>[d(Gr)]),_:1})):ge("",!0)],64)}}},mn=me(Ur,[["__scopeId","data-v-8183edee"]]);const Yr={__name:"CollapseIcon",setup(e){const t=be(),n=E(()=>t.themeConfig),o=()=>{t.setThemeConfig({...n.value,isCollapse:!n.value.isCollapse})};return(a,l)=>{const s=fe;return g(),D(s,{class:"collapse-icon",onClick:o},{default:p(()=>[(g(),D($e("fold")))]),_:1})}}},Hr=me(Yr,[["__scopeId","data-v-bfbbb07c"]]);const Kr=["onClick"],jr={class:"breadcrumb-title"},Qr={__name:"Breadcrumb",setup(e){const t=ot(),n=qe(),o=at(),a=be(),l=E(()=>a.themeConfig),s=E(()=>o.breadcrumbListGet[t.matched[t.matched.length-1].path]),i=(r,u)=>{u!==s.value.length-1&&n.push(r.path)};return(r,u)=>{const m=fe,c=Tl,v=kl;return g(),D(v,{"separator-icon":C(La)},{default:p(()=>[d(Fa,{name:"breadcrumb",mode:"out-in",tag:"div"},{default:p(()=>[s.value?(g(!0),P(Ce,{key:0},vt(s.value,(f,h)=>(g(),D(c,{key:f.path},{default:p(()=>[y("div",{class:"breadcrumb-item el-breadcrumb__inner is-link",onClick:_=>i(f,h)},[f.meta.icon&&l.value.breadcrumbIcon?(g(),D(m,{key:0,class:"breadcrumb-icon"},{default:p(()=>[(g(),D($e(f.meta.icon)))]),_:2},1024)):ge("",!0),y("span",jr,W(r.$t(`menus.${f.name}`)),1)],8,Kr)]),_:2},1024))),128)):ge("",!0)]),_:1})]),_:1},8,["separator-icon"])}}},Xr=me(Qr,[["__scopeId","data-v-cb2fd2a1"]]);const Zr={class:"tool-bar-lf"},qr={__name:"ToolBarLeft",setup(e){const t=be(),n=E(()=>t.themeConfig);return(o,a)=>(g(),P("div",Zr,[d(Hr,{id:"collapseIcon"}),n.value.breadcrumb?(g(),D(Xr,{key:0,id:"breadcrumb"})):ge("",!0)]))}},no=me(qr,[["__scopeId","data-v-877f7c30"]]);const Jr={class:"layout-search-dialog"},ei={__name:"SearchMenu",setup(e){const t=qe(),n=at(),o=E(()=>n.flatMenuListGet.filter(v=>!v.meta.isHide)),a=(v,f)=>{const h=v?o.value.filter(m(v)):o.value;f(h)},l=k(!1),s=k(),i=k(""),r=()=>{l.value=!0,i.value="",$t(()=>{setTimeout(()=>{s.value.focus()})})},u=()=>{l.value=!1},m=v=>f=>f.path.toLowerCase().indexOf(v.toLowerCase())>-1||f.title.toLowerCase().indexOf(v.toLowerCase())>-1,c=v=>{i.value="",v.meta.isLink?window.open(v.meta.isLink,"_blank"):t.push(v.path),u()};return(v,f)=>{const h=fe,_=Le,b=bl,F=No;return g(),P("div",Jr,[d(_,{class:"box-item",effect:"dark",content:v.$t("header.menus"),placement:"top-start"},{default:p(()=>[d(h,{size:20,onClick:r},{default:p(()=>[d(C(lo))]),_:1})]),_:1},8,["content"]),d(F,{modelValue:l.value,"onUpdate:modelValue":f[2]||(f[2]=S=>l.value=S),width:"300px","destroy-on-close":"",modal:!1,"show-close":!1,fullscreen:"",onClick:u},{default:p(()=>[d(b,{modelValue:i.value,"onUpdate:modelValue":f[0]||(f[0]=S=>i.value=S),ref_key:"menuInputRef",ref:s,placeholder:v.$t("system.menusearch"),"fetch-suggestions":a,onSelect:c,onClick:f[1]||(f[1]=Ln(()=>{},["stop"]))},{prefix:p(()=>[d(h,null,{default:p(()=>[d(C(lo))]),_:1})]),default:p(({item:S})=>[d(h,null,{default:p(()=>[(g(),D($e(S.meta.icon)))]),_:2},1024),y("span",null,W(v.$t(`menus.${S.name}`)),1)]),_:1},8,["modelValue","placeholder"])]),_:1},8,["modelValue"])])}}},ti=me(ei,[["__scopeId","data-v-862ebc09"]]),ni="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAAAqCAMAAAA02K3QAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAA81BMVEXeKRDfLRDeKhDjQw7hNw/1qAX7yQLiPA7lUA3fMA/4uQP+2wDwjAfgNA/3tQThOw7gNw/vhwjiPQ7/3QDsdQnfKxDhOQ/eKxDseQn/3gD3sQTlTQ31pAXxkgfgMg/teQnufgj6wQP/3ADufwjtfAntewntegnkSg3hOA/0ogX0owXkTQ36xAL+2gD0oQXgMQ/znAb6wgLkTA3iQg7+3AD/2wDrcgrgMw/rbgr2rwTvhQj2rQTylQb1pQXtfQnmVAz6xgLgNg/gNQ/0nwbiPw7iQA7oYAvxjwfhOg7qawr2qwXrbwr1pwX90QHmWAzfLBD///91wG3qAAAAAWJLR0RQ425MvAAAAAlwSFlzAAAASgAAAEoBTjCYIQAAAAd0SU1FB+MBCQEII3cQNPsAAADcSURBVEjH7ZHHFoIwEEVjFAuKItZYEHvvvffe/v9vFBFkScLGRe5q5p3zXiYzAPwjFmi1mQtg7A4Sn1MtXKzbwxEEeH3fguf8JAMIgaBWhwAIR3ADoiimb+OJpHGvmJIkKY0y2Vy+UFSkUhlVoPGEag0p1FOaq+EsYczfbH387c5P4jFXwMoBXdzF6d7roT5CA50yxAsYobEw6U91ygxiHXK+eG9vuVqr/WYbYHcYfqhcj9tryoE5ki9E5gQ2xF5ROJObP1x6V9GMn7/dH08zARAczP2AQqFQKIS8AI09EWJJ81gFAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE5LTAxLTA5VDAxOjA4OjM1KzA4OjAwCckKUwAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxOS0wMS0wOVQwMTowODozNSswODowMHiUsu8AAABDdEVYdHNvZnR3YXJlAC91c3IvbG9jYWwvaW1hZ2VtYWdpY2svc2hhcmUvZG9jL0ltYWdlTWFnaWNrLTcvL2luZGV4Lmh0bWy9tXkKAAAAGHRFWHRUaHVtYjo6RG9jdW1lbnQ6OlBhZ2VzADGn/7svAAAAF3RFWHRUaHVtYjo6SW1hZ2U6OkhlaWdodAA0Mme1bjMAAAAWdEVYdFRodW1iOjpJbWFnZTo6V2lkdGgANjRET2kJAAAAGXRFWHRUaHVtYjo6TWltZXR5cGUAaW1hZ2UvcG5nP7JWTgAAABd0RVh0VGh1bWI6Ok1UaW1lADE1NDY5NjczMTWCt58gAAAAEHRFWHRUaHVtYjo6U2l6ZQA4MDdCI3HaPgAAAGB0RVh0VGh1bWI6OlVSSQBmaWxlOi8vL2hvbWUvd3d3cm9vdC9uZXdzaXRlL3d3dy5lYXN5aWNvbi5uZXQvY2RuLWltZy5lYXN5aWNvbi5jbi9maWxlcy81My81MzkyNzYucG5nVJtbpgAAAABJRU5ErkJggg==",oi="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAAAqCAMAAAA02K3QAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAACClBMVEUAJmRMHlW7Ez4gQXdmfqIDKWYSNW9pgKQMMGsJLmlnfqIWOXFWb5gxT4IhQ3lje6ECKGUTNnBpf6RddZy4wtQVN3HP1uIGK2jHz9wsS3+hr8Z/k7FheZ+yvdEYOnLO1uIFKmcILWkpSX0JLmooSHwNMWwaPHRhaZLwy9VAXIvU2uULL2vZ3+ghQngEKWbK0t9jeqCZqMGwvM9DX43S2eRnfqP///8XOnIrS34BJ2U2VIUkRXocPnUPM20qSn5bc5u4w9QiQ3nHz93DzNsyUYKlssh8kK9fd561wNLGztxYS3nbgZguTYBieaB6jq5sgqZRa5U+W4owT4FcdJwOMm14jK0tTH+ms8kfQHecq8M/XIpof6OBlLIvToGkssiruMy5w9QKL2qaqcJCXox+kbGntMpUPG3QXHoUN3BTbZZXcJlUbZdFYY4jRHkVOHFlfKHBytnV2+UzUYOtuc2ImrdqgKS9x9ceQHYQNG4RNG5leJ778PM8WYjQ1+LFztySor3O1eEZO3MmRnsYOnM1U4RYcZmzvtHAydm+yNd3i6yxvdAlRXpziKqMnbmAk7IbPXRedp1JZJA3VYVtg6aLnLhjb5b02uGNnrqHmbYxUIK3wtPEzduJm7cHLGg9WokEKmcEKmY7WIhWQnLUa4ZrgaXI0N7Y3ufT2uQ5V4ePoLtvhKcdP3ZPQHGFUXnFfSfbAAAAAWJLR0Q2R7+I0QAAAAlwSFlzAAAA+AAAAPgBz8HmZQAAAAd0SU1FB+MBCQEIH1h/SHwAAAItSURBVEjHxZZnUxNRFIYfxaw3BvXGAoKNRBeVkLWLIYAVxRqailHUFdEogoANFRsqitiwoWLv9T96Gf3KzsnwIe/M3dk5M/vO3uc9e/bCSBozViTIGufDGq/wTwhA9kSYNFkTnKLEBlOnwfScXPSMPMifCbNmw5y5BWKDUIhw7jzC5ma+WXbh/8KChSIN73ZRUYRAcRRn8RJYugyWr/DDylUimeetktVYsVK/RbysXGdFsQIVWHKDNWt9rFtvEdmwESo3weYqTXyLtXWbSLB9B47aiUOi0KG6Bl9tHU79LnmMsLvSMNjTgLM3Cfv2Q+MBfxp9ABUHzeWQC4ebIHHEFJpN4egxkSB13Ic6ESK3pR5OtkJbtUZlh8QQT7VDWUeEgpxOOH0Gzp6D8x2u2MC2aQ110YodiaKUG1DKFC5w8ZJIwwy6L7u0FWXhXKmDqwZI8lpaEO3rpdixJr+isSES6wmjbtzElhvc6nWI3w4SuWMY5PeZzrqr6W+**************************/LsuUjDDPoHXVKDhTgvXkJLAnoG0/qYyHsFsSEFzQPQ99p0dy9pGLx569NO8l3sfXeB1rUprUMfTKHG9/GTSPD5iyb6tQv/t+9Q9QN+DsWIF5eLIaZS4P4y71xSDsHgv+WWiWP8PZL+CLcgQ+UBcdQGsnbxaCQZKg+ImTeQjQ2PgZL5GGXD22OsZz4F2S/U4+ea+RRkBxmPI07mUxitwV/Jbea7cLbqZAAAACV0RVh0ZGF0ZTpjcmVhdGUAMjAxOS0wMS0wOVQwMTowODozMSswODowMP2GLkAAAAAldEVYdGRhdGU6bW9kaWZ5ADIwMTktMDEtMDlUMDE6MDg6MzErMDg6MDCM25b8AAAAQ3RFWHRzb2Z0d2FyZQAvdXNyL2xvY2FsL2ltYWdlbWFnaWNrL3NoYXJlL2RvYy9JbWFnZU1hZ2ljay03Ly9pbmRleC5odG1svbV5CgAAABh0RVh0VGh1bWI6OkRvY3VtZW50OjpQYWdlcwAxp/+7LwAAABd0RVh0VGh1bWI6OkltYWdlOjpIZWlnaHQANDJntW4zAAAAFnRFWHRUaHVtYjo6SW1hZ2U6OldpZHRoADY0RE9pCQAAABl0RVh0VGh1bWI6Ok1pbWV0eXBlAGltYWdlL3BuZz+yVk4AAAAXdEVYdFRodW1iOjpNVGltZQAxNTQ2OTY3MzExhdpbOQAAABF0RVh0VGh1bWI6OlNpemUAMTkyMUL5U2ZlAAAAYHRFWHRUaHVtYjo6VVJJAGZpbGU6Ly8vaG9tZS93d3dyb290L25ld3NpdGUvd3d3LmVhc3lpY29uLm5ldC9jZG4taW1nLmVhc3lpY29uLmNuL2ZpbGVzLzUzLzUzOTIxMi5wbmd3Qh57AAAAAElFTkSuQmCC",ai="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAMAAACdt4HsAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAdVBMVEX////OESbWMyPlcB7lch70rxn80Rb0sRnmdB7TJSTtlBvzqxn5xhfSIyTRHyXulhviZR/7zBbXNyP2thjQGiXsjxvtkhvriRzxpBr7zxbhYB/4wRfumRvwnRrvmxvjaR/eVSD1tBjXNSPqhBzQGCXbRyH////nhGP9AAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAHdElNRQfjAQkCDgphvrVIAAAAzElEQVRYw+3RyQ6CMBCAYawwUISyyqqy+v6vKA0QNUalU2/Of4BymC+0NQyKopZ2GhHwJwBjmsDe1AQsSw9gAEwLMAFMLcAGsHWAaQef9/ANcCTgqAPcPcx5EvCWD9ff/gcigJcCobSFMHoej0LVM4iTx/kkVj/E9Hifz1LULeTrfK56C0vFChQ4gJcrUHIUUMnZupbPCgWcpskz9y/T64QBRAlRIxdNBKVAAC10/bzqO2gRQDaM63IcXARwffux9RC3RAABvwMoipq6ASfMEXgnwQEiAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE5LTAxLTA5VDAyOjE0OjEwKzA4OjAwf6xxCQAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxOS0wMS0wOVQwMjoxNDoxMCswODowMA7xybUAAABDdEVYdHNvZnR3YXJlAC91c3IvbG9jYWwvaW1hZ2VtYWdpY2svc2hhcmUvZG9jL0ltYWdlTWFnaWNrLTcvL2luZGV4Lmh0bWy9tXkKAAAAGHRFWHRUaHVtYjo6RG9jdW1lbnQ6OlBhZ2VzADGn/7svAAAAF3RFWHRUaHVtYjo6SW1hZ2U6OkhlaWdodAA2NLzgqYQAAAAWdEVYdFRodW1iOjpJbWFnZTo6V2lkdGgANjRET2kJAAAAGXRFWHRUaHVtYjo6TWltZXR5cGUAaW1hZ2UvcG5nP7JWTgAAABd0RVh0VGh1bWI6Ok1UaW1lADE1NDY5NzEyNTCPeLLwAAAAEHRFWHRUaHVtYjo6U2l6ZQA0OTlC+PVzhwAAAGJ0RVh0VGh1bWI6OlVSSQBmaWxlOi8vL2hvbWUvd3d3cm9vdC9uZXdzaXRlL3d3dy5lYXN5aWNvbi5uZXQvY2RuLWltZy5lYXN5aWNvbi5jbi9maWxlcy8xMDgvMTA4ODI0Ny5wbmdiCOREAAAAAElFTkSuQmCC",li=y("img",{src:ni,width:"20px",style:{padding:"0 5px"},alt:""},null,-1),si=y("img",{src:oi,width:"20px",style:{padding:"0 5px"},alt:""},null,-1),ri=y("img",{src:ai,width:"20px",style:{padding:"0 5px"},alt:""},null,-1),ii={__name:"Language",setup(e){const t=To(),n=be(),o=E(()=>n.language),a=l=>{t.locale.value=l,n.updateLanguage(l),Xt.emit("changeLanguage",l)};return Ue(()=>{a(o.value||Pa())}),(l,s)=>{const i=Xn,r=Zn,u=Qn,m=Le;return g(),D(m,{class:"box-item",effect:"dark",content:l.$t("header.lan"),placement:"top-start"},{default:p(()=>[d(u,{trigger:"click",onCommand:a},{dropdown:p(()=>[d(r,null,{default:p(()=>[d(i,{disabled:o.value&&o.value==="zh",command:"zh"},{default:p(()=>[li,he(" 简体中文")]),_:1},8,["disabled"]),d(i,{disabled:o.value==="en",command:"en"},{default:p(()=>[si,he(" English")]),_:1},8,["disabled"]),d(i,{disabled:o.value==="vi",command:"vi"},{default:p(()=>[ri,he(" Vietnamese")]),_:1},8,["disabled"])]),_:1})]),default:p(()=>[y("i",{class:ne(["iconfont icon-zhongyingwen","toolBar-icon"])})]),_:1})]),_:1},8,["content"])}}},ui={__name:"ThemeSetting",setup(e){const t=()=>{Xt.emit("openThemeDrawer")};return(n,o)=>{const a=Y("Setting"),l=fe,s=Le;return g(),D(s,{class:"box-item",effect:"dark",content:n.$t("header.system"),placement:"top-start"},{default:p(()=>[d(l,{onClick:t,size:18},{default:p(()=>[d(a)]),_:1})]),_:1},8,["content"])}}};const ci={class:"messageBox-box"},di={class:"lockscreen-item"},pi={class:"loc-date"},fi={class:"loc-week"},mi={class:"loc-time"},hi={class:"loc-input"},vi={class:"loc-btn"},_i={__name:"LockScreen",setup(e,{expose:t}){const n=an({visible:!1,promptValue:"",type:"",lockValue:""}),{visible:o}=So(n),a=u=>{n.visible=u},l=()=>{if(localStorage.getItem("lockScreen")===n.lockValue)localStorage.removeItem("lockScreen"),a(!1);else return n.lockValue?Pn.error("密码错误"):Pn.error("请输入密码")};t({setVisible:a,state:n});const s=k("loading.."),i=k("loading.."),r=k("loading..");return Ue(()=>{setInterval(()=>{s.value=En(1),i.value=En(2),r.value=En(3)},1e3)}),(u,m)=>{const c=bt;return g(),D(Gn,{name:"messagebox-fade"},{default:p(()=>[mt(y("div",ci,[y("div",di,[y("div",pi,W(s.value),1),y("div",fi,W(r.value),1),y("div",mi,W(i.value),1),y("div",hi,[mt(y("input",{type:"password",clearable:"",placeholder:"请输入解锁密码","onUpdate:modelValue":m[0]||(m[0]=v=>n.lockValue=v)},null,512),[[xa,n.lockValue]])]),y("div",vi,[d(c,{type:"primary",icon:C(Ba),onClick:l},{default:p(()=>[he(" 解 锁 ")]),_:1},8,["icon"])])])],512),[[Nt,C(o)]])]),_:1})}}},gi=me(_i,[["__scopeId","data-v-bc514573"]]),wo=e=>{const t=Va(gi,e);bi(t)},bi=e=>{const t=document.createDocumentFragment(),n=e.mount(t);document.body.appendChild(t),n.setVisible(!0),Ve(n.state,o=>{o.visible||yi(e)})},yi=e=>{e.unmount()};const Ai={class:"dialog-footer"},wi={__name:"LockScreen",setup(e){const t=k(!1),n=k(""),o=()=>{if(!n.value){Pn({message:"请输入锁屏密码",type:"warning"});return}localStorage.setItem("lockScreen",n.value),wo(),t.value=!1},a=()=>{n.value="",t.value=!1};return Ue(()=>{(localStorage.getItem("lockScreen")||"")&&wo()}),(l,s)=>{const i=fe,r=Le,u=Ro,m=bt,c=No;return g(),P(Ce,null,[d(r,{class:"box-item",effect:"dark",content:l.$t("header.lock"),placement:"top-start"},{default:p(()=>[d(i,{onClick:s[0]||(s[0]=v=>t.value=!0),size:20},{default:p(()=>[d(C(so))]),_:1})]),_:1},8,["content"]),d(c,{title:"请输入锁屏密码",modelValue:t.value,"onUpdate:modelValue":s[2]||(s[2]=v=>t.value=v),width:"30%",onClose:a},{default:p(()=>[d(u,{modelValue:n.value,"onUpdate:modelValue":s[1]||(s[1]=v=>n.value=v),placeholder:"请输入锁屏密码",type:"password","show-password":!0},null,8,["modelValue"]),y("span",Ai,[d(m,{onClick:a},{default:p(()=>[he("取消")]),_:1}),d(m,{type:"primary",icon:C(so),onClick:o},{default:p(()=>[he("锁 屏")]),_:1},8,["icon"])])]),_:1},8,["modelValue"])],64)}}},Ei=me(wi,[["__scopeId","data-v-a149f056"]]),Ci={__name:"FullScreen",setup(e){const t=k(!0),n=()=>{const o=document.getElementById("app");t.value=!t.value,o.requestFullscreen?o.requestFullscreen():o.mozRequestFullScreen?o.mozRequestFullScreen():o.msRequestFullscreen?o.msRequestFullscreen():o.webkitRequestFullscreen&&o.webkitRequestFullScreen(),document.exitFullscreen?document.exitFullscreen():document.msExitFullscreen?document.msExitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitExitFullscreen&&document.webkitExitFullscreen()};return(o,a)=>{const l=Y("FullScreen",!0),s=fe,i=Le;return g(),D(i,{class:"box-item",effect:"dark",content:o.$t("header.full"),placement:"top-start"},{default:p(()=>[d(s,{size:20,onClick:n},{default:p(()=>[d(l)]),_:1})]),_:1},8,["content"])}}};const Si={class:"avatar"},Ii=["src"],ki={__name:"Avatar",setup(e){const{t}=To(),n=qe(),o=be(),a=JSON.parse(JSON.stringify(o.userInfo)).avatar_url,l=async()=>{try{await rl.confirm(t("login.querentuichu"),t("login.queren"),{confirmButtonText:t("login.lijituichu"),cancelButtonText:t("login.quxiao"),type:"warning"});const s=await Wa();if(s.code===200)o.setUserInfo(""),o.setToken(""),localStorage.removeItem("router"),localStorage.removeItem("menuList"),ro({message:s.msg||t("login.tuichuchenggong"),type:"success",duration:2e3}),n.replace(za);else return ro({message:s.msg||t("login.tuichushibai"),type:"error",duration:2e3}),!1}catch(s){return console.error(s),!1}};return(s,i)=>{const r=Y("SwitchButton"),u=fe,m=Xn,c=Zn,v=Qn;return g(),D(v,{trigger:"click"},{dropdown:p(()=>[d(c,null,{default:p(()=>[d(m,{onClick:l},{default:p(()=>[d(u,null,{default:p(()=>[d(r)]),_:1}),he(W(s.$t("header.logout")),1)]),_:1})]),_:1})]),default:p(()=>[y("div",Si,[y("img",{src:C(a),alt:"avatar"},null,8,Ii)])]),_:1})}}},Ti=me(ki,[["__scopeId","data-v-599a6481"]]),Mi={__name:"ColorPicker",setup(e){const t=[Ga,"#F44336","#E91E63","#9C27B0","#673AB7","#2196F3","#03A9F4","#00BCD4","#009688","#4CAF50","#FFEB3B","#FFC107","#FF5722","#9E9E9E","#607D8B"],n=be(),o=E(()=>n.themeConfig),{changePrimary:a}=ko();return(l,s)=>{const i=il,r=Le;return g(),P("div",null,[d(r,{class:"box-item",effect:"dark",content:l.$t("header.theme"),placement:"top-start"},{default:p(()=>[d(i,{modelValue:o.value.primary,"onUpdate:modelValue":s[0]||(s[0]=u=>o.value.primary=u),size:"small",predefine:t,onChange:C(a)},null,8,["modelValue","onChange"])]),_:1},8,["content"])])}}};const Di={class:"tool-bar-ri"},Oi={class:"header-icon"},$i={class:"username"},Ni={__name:"ToolBarRight",setup(e){const t=be(),n=JSON.parse(JSON.stringify(t.userInfo)).nickname,o=k(null),a=k("");return(()=>{o.value&&clearInterval(o.value);const s=io();a.value=s,o.value=setInterval(()=>{const i=io();a.value=i},1e3*60*60*1)})(),(s,i)=>(g(),P("div",Di,[y("div",Oi,[d(Ei),d(Ci),d(ii,{id:"language"}),d(ti,{id:"searchMenu"}),d(Mi),d(ui,{id:"themeSetting"})]),y("span",$i,W(a.value)+" "+W(C(n)),1),d(Ti)]))}},hn=me(Ni,[["__scopeId","data-v-493feb39"]]);const vn={__name:"SubMenu",props:{menuList:{type:Array,default(){return[]}}},setup(e){const t=qe(),n=o=>{if(o.meta.isLink)return window.open(o.meta.isLink,"_blank");t.push(o.path)};return(o,a)=>{const l=fe,s=Y("SubMenu",!0),i=Zo,r=Xo;return g(!0),P(Ce,null,vt(e.menuList,u=>(g(),P(Ce,{key:u.path},[u.children&&u.children.length>0?(g(),D(i,{key:0,index:u.path},{title:p(()=>[u.meta.icon?(g(),D(l,{key:0},{default:p(()=>[(g(),D($e(u.meta.icon)))]),_:2},1024)):ge("",!0),y("span",null,W(o.$t(`menus.${u.name}`)),1)]),default:p(()=>[d(s,{menuList:u.children},null,8,["menuList"])]),_:2},1032,["index"])):(g(),D(r,{key:1,index:u.path,onClick:m=>n(u)},{title:p(()=>[y("span",null,W(o.$t(`menus.${u.name}`)),1)]),default:p(()=>[u.meta.icon?(g(),D(l,{key:0},{default:p(()=>[(g(),D($e(u.meta.icon)))]),_:2},1024)):ge("",!0)]),_:2},1032,["index","onClick"]))],64))),128)}}};const Ri={class:"logo flx-center"},Fi={key:0,src:sn,alt:"logo"},Li={key:1,src:rn,alt:"logo"},Pi=ie({name:"layoutVertical"}),xi=Object.assign(Pi,{setup(e){const t=Rt,n=ot(),o=at(),a=be(),l=E(()=>n.meta.activeMenu?n.meta.activeMenu:n.path),s=E(()=>ln(o.showMenuListGet)),i=E(()=>a.themeConfig.isCollapse),r=E(()=>a.themeConfig.isDark);return(u,m)=>{const c=pn,v=Ft,f=Un,h=cn,_=un;return g(),D(_,{class:"layout"},{default:p(()=>[d(f,null,{default:p(()=>[y("div",{class:"menu",style:ft({width:i.value?"65px":"210px"})},[y("div",Ri,[r.value?(g(),P("img",Li)):(g(),P("img",Fi)),mt(y("span",null,W(C(t)),513),[[Nt,!i.value]])]),d(v,null,{default:p(()=>[d(c,{"default-active":l.value,router:!1,collapse:i.value,"collapse-transition":!1,"unique-opened":!0,"background-color":"#feffff","text-color":"#303133","active-text-color":"#ffffff"},{default:p(()=>[d(vn,{menuList:s.value},null,8,["menuList"])]),_:1},8,["default-active","collapse"])]),_:1})],4)]),_:1}),d(_,null,{default:p(()=>[d(h,null,{default:p(()=>[d(no),d(hn)]),_:1}),d(mn)]),_:1})]),_:1})}}}),Bi=me(xi,[["__scopeId","data-v-8f42204e"]]);const Vi={class:"header-lf"},Wi={class:"logo flx-center"},zi={key:0,src:sn,alt:"logo"},Gi={key:1,src:rn,alt:"logo"},Ui=ie({name:"layoutClassic"}),Yi=Object.assign(Ui,{setup(e){const t=Rt,n=ot(),o=at(),a=be(),l=E(()=>n.meta.activeMenu?n.meta.activeMenu:n.path),s=E(()=>ln(o.showMenuListGet)),i=E(()=>a.themeConfig.isCollapse),r=E(()=>a.themeConfig.isDark);return(u,m)=>{const c=cn,v=pn,f=Ft,h=Un,_=un;return g(),D(_,{class:"layout"},{default:p(()=>[d(c,null,{default:p(()=>[y("div",Vi,[y("div",Wi,[r.value?(g(),P("img",Gi)):(g(),P("img",zi)),y("span",null,W(C(t)),1)]),d(no)]),d(hn)]),_:1}),d(_,{class:"classic-content"},{default:p(()=>[d(h,null,{default:p(()=>[y("div",{class:"menu",style:ft({width:i.value?"65px":"210px"})},[d(f,null,{default:p(()=>[d(v,{"default-active":l.value,router:!1,collapse:i.value,"collapse-transition":!1,"unique-opened":!0,"background-color":"#ffffff","text-color":"#303133"},{default:p(()=>[d(vn,{menuList:s.value},null,8,["menuList"])]),_:1},8,["default-active","collapse"])]),_:1})],4)]),_:1}),d(_,{class:"classic-main"},{default:p(()=>[d(mn)]),_:1})]),_:1})]),_:1})}}}),Hi=me(Yi,[["__scopeId","data-v-d7bfc08f"]]);const Ki={class:"logo flx-center"},ji={key:0,src:sn,alt:"logo"},Qi={key:1,src:rn,alt:"logo"},Xi=ie({name:"layoutTransverse"}),Zi=Object.assign(Xi,{setup(e){const t=Rt,n=ot(),o=qe(),a=at(),l=be(),s=E(()=>n.meta.activeMenu?n.meta.activeMenu:n.path),i=E(()=>ln(a.showMenuListGet)),r=E(()=>l.themeConfig.isCollapse),u=E(()=>l.themeConfig.isDark),m=c=>{if(c.meta.isLink)return window.open(c.meta.isLink,"_blank");o.push(c.path)};return(c,v)=>{const f=fe,h=Zo,_=Xo,b=pn,F=cn,S=un;return g(),D(S,{class:"layout"},{default:p(()=>[d(F,null,{default:p(()=>[y("div",Ki,[u.value?(g(),P("img",Qi)):(g(),P("img",ji)),mt(y("span",null,W(C(t)),513),[[Nt,!r.value]])]),d(b,{mode:"horizontal","default-active":s.value,router:!1,"unique-opened":!0,"background-color":"#ffffff","text-color":"#666","active-text-color":"#ffffff"},{default:p(()=>[(g(!0),P(Ce,null,vt(i.value,w=>{var $;return g(),P(Ce,{key:w.path},[($=w.children)!=null&&$.length?(g(),D(h,{index:w.path,key:w.path+"el-sub-menu"},{title:p(()=>[d(f,null,{default:p(()=>[(g(),D($e(w.meta.icon)))]),_:2},1024),y("span",null,W(c.$t(`menus.${w.name}`)),1)]),default:p(()=>[d(vn,{menuList:w.children},null,8,["menuList"])]),_:2},1032,["index"])):(g(),D(_,{index:w.path,key:w.path+"el-menu-item",onClick:B=>m(w)},{title:p(()=>[y("span",null,W(c.$t(`menus.${w.name}`)),1)]),default:p(()=>[d(f,null,{default:p(()=>[(g(),D($e(w.meta.icon)))]),_:2},1024)]),_:2},1032,["index","onClick"]))],64)}),128))]),_:1},8,["default-active"]),d(hn)]),_:1}),d(mn)]),_:1})}}}),qi=me(Zi,[["__scopeId","data-v-949d6457"]]);const Ji={class:"aside-split"},eu={class:"logo flx-center"},tu={key:0,src:sn,alt:"logo"},nu={key:1,src:rn,alt:"logo"},ou={class:"split-list"},au=["onClick"],lu={class:"title"},su={class:"logo flx-center"},ru=ie({name:"layoutColumns"}),iu=Object.assign(ru,{setup(e){const t=Rt,n=ot(),o=qe(),a=at(),l=be(),s=E(()=>n.meta.activeMenu?n.meta.activeMenu:n.path),i=E(()=>ln(a.showMenuListGet)),r=E(()=>l.themeConfig.isCollapse),u=E(()=>l.themeConfig.isDark),m=k([]),c=k("");Ve(()=>[i,n],()=>{var h;if(Ua.includes(n.path)||!i.value.length)return;c.value=n.path;const f=i.value.filter(_=>n.path.includes(_.path));if((h=f[0].children)!=null&&h.length)return m.value=f[0].children;m.value=[]},{deep:!0,immediate:!0});const v=f=>{var h;if(c.value=f.path,(h=f.children)!=null&&h.length)return m.value=f.children;m.value=[],o.push(f.path)};return(f,h)=>{const _=fe,b=Ft,F=pn,S=Un,w=cn,$=un;return g(),D($,{class:"layout"},{default:p(()=>[y("div",Ji,[y("div",eu,[u.value?(g(),P("img",nu)):(g(),P("img",tu))]),d(b,null,{default:p(()=>[y("div",ou,[(g(!0),P(Ce,null,vt(i.value,B=>(g(),P("div",{class:ne(["split-item",{"split-active":c.value==B.path||`/${c.value.split("/")[1]}`==B.path}]),key:B.path,onClick:V=>v(B)},[d(_,null,{default:p(()=>[(g(),D($e(B.meta.icon)))]),_:2},1024),y("span",lu,W(f.$t(`menus.${B.name}`)),1)],10,au))),128))])]),_:1})]),d(S,{class:ne({"not-aside":!m.value.length}),style:ft({width:r.value?"65px":"210px"})},{default:p(()=>[y("div",su,[mt(y("span",null,W(C(t)),513),[[Nt,m.value.length]])]),d(b,null,{default:p(()=>[d(F,{"default-active":s.value,router:!1,collapse:r.value,"collapse-transition":!1,"unique-opened":!0,"background-color":"#ffffff"},{default:p(()=>[d(vn,{menuList:m.value},null,8,["menuList"])]),_:1},8,["default-active","collapse"])]),_:1})]),_:1},8,["class","style"]),d($,null,{default:p(()=>[d(w,null,{default:p(()=>[d(no),d(hn)]),_:1}),d(mn)]),_:1})]),_:1})}}}),uu=me(iu,[["__scopeId","data-v-c410819c"]]);const cu=ie({name:"layout"}),du=Object.assign(cu,{setup(e){const t={vertical:Bi,classic:Hi,transverse:qi,columns:uu},n=be(),o=E(()=>n.themeConfig);return(a,l)=>(g(),P(Ce,null,[(g(),D($e(t[o.value.layout]))),d(lr)],64))}}),Uu=me(du,[["__scopeId","data-v-bfa68663"]]);export{Uu as default};
