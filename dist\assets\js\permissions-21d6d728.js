import{i as p,j as W,k as X,B as Y,o as _,a as g,w as l,b as e,n as c,cM as x,d as i,e as Z,m as b,L as N,t as h,cN as F,ar as q,E as ee,a5 as le}from"./index-444b28c3.js";/* empty css                   */import{E as te,a as oe}from"./el-col-bd5e5418.js";import{E as ae}from"./el-drawer-12f56ca7.js";import"./el-overlay-9f4b42b1.js";import{E as ne,a as re}from"./el-form-10dec954.js";import{E as ie}from"./el-switch-8443b491.js";import"./el-form-item-4ed993c7.js";import{E as de}from"./el-input-6b488ec7.js";import{E as se}from"./el-card-6f02be36.js";import{E as pe}from"./el-pagination-6fc73be7.js";import{E as me}from"./el-tag-29cbefd8.js";import"./el-select-980e5896.js";import"./el-scrollbar-af6196f4.js";import{E as ue,a as ce}from"./el-table-column-fa1764a8.js";import"./el-checkbox-f3df62fa.js";import"./el-tooltip-4ed993c7.js";/* empty css                */import{E as fe}from"./el-button-9bbdfcf9.js";import{v as _e}from"./directive-ce1b251f.js";import"./index-eba6e623.js";import"./event-fe80fd0c.js";import"./scroll-a66dde9b.js";import"./vnode-b9ec7db4.js";import"./focus-trap-6de7266c.js";import"./index-e305bb62.js";import"./index-4d7f16ce.js";import"./_Uint8Array-55276dff.js";import"./validator-e4131fc3.js";const ge={m:"4"},be={class:"demo-pagination-block"},we={class:"dialog-footer"},Qe={__name:"permissions",setup(ve){const w=p([]),E=p(!0),f=p(!1),y=p(1),k=p(14),C=p(0),M=p("small"),S=p(!1),n=W({path:"",name:"",icon:"",redirect:"",component:"",title:"",parentId:"",disabled:!1,meta:{icon:"",isHide:!1,isFull:!1,isAffix:!1,isKeepAlive:!1}}),T={path:[{required:!0,message:"请输入菜单路径",trigger:"blur"}],icon:[{required:!0,message:"请输入菜单图标",trigger:"blur"}],name:[{required:!0,message:"请输入菜单名称",trigger:"blur"}],title:[{required:!0,message:"请输入菜单标题",trigger:"blur"}],parentId:[{required:!0,message:"请输入父级菜单 ID",trigger:"blur"}],meta:[{required:!0,message:"请输入图标",trigger:"blur"}]},$=()=>"icon-no",A=()=>{f.value=!0},B=m=>{},I=m=>{},D=m=>{},H=()=>{f.value=!1,ee({title:"",message:"新增成功",type:"success"})};return X(()=>{setTimeout(()=>{E.value=!1},500),w.value=JSON.parse(localStorage.getItem("menuList"))||[],C.value=w.value.length}),(m,o)=>{const d=fe,z=te,P=Y("DArrowRight"),v=le,a=ue,V=me,U=ce,R=pe,L=se,u=de,s=ne,j=ie,J=re,K=ae,O=oe,G=_e;return _(),g(O,{gutter:20},{default:l(()=>[e(z,{xs:24,sm:24,md:24,lg:24},{default:l(()=>[e(L,{shadow:"hover"},{default:l(()=>[e(z,{span:6},{default:l(()=>[e(d,{type:"primary",icon:c(x),onClick:A},{default:l(()=>[i("新增菜单")]),_:1},8,["icon"])]),_:1}),Z((_(),g(U,{data:w.value,"row-class-name":$,stripe:"",style:{"margin-top":"1rem"},"element-loading-text":"Flyknit..."},{default:l(()=>[e(a,{type:"expand"},{default:l(t=>[b("div",ge,[b("h4",null,[e(v,null,{default:l(()=>[e(P)]),_:1}),i(" 子页面详情 ")]),e(U,{data:t.row.children,border:!1},{default:l(()=>[e(a,{label:"菜单标题",prop:"title",width:"130"}),e(a,{label:"图标",prop:"meta.icon",width:"60"},{default:l(r=>[e(v,null,{default:l(()=>[(_(),g(N(r.row.meta.icon)))]),_:2},1024)]),_:2},1024),e(a,{label:"页面路径",prop:"path",width:"200"}),e(a,{label:"页面ID",prop:"id",width:"150"}),e(a,{label:"父级ID",prop:"parentId",width:"120"}),e(a,{label:"英文标题",prop:"name",width:"100"}),e(a,{label:"组件路径",prop:"component",width:"180"}),e(a,{label:"是否禁用",prop:"disabled",width:"90"},{default:l(r=>[e(V,{type:r.row.disabled?"danger":"success"},{default:l(()=>[i(h(r.row.disabled?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),e(a,{label:"是否隐藏",prop:"meta.isHide",width:"90"},{default:l(r=>[e(V,{type:r.row.meta.isHide?"danger":"success"},{default:l(()=>[i(h(r.row.meta.isHide?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),e(a,{label:"操作",width:"180",fixed:"right"},{default:l(r=>[e(d,{type:"warning",size:"small",icon:c(F),onClick:Q=>D(r.row)},{default:l(()=>[i("编 辑")]),_:2},1032,["icon","onClick"]),e(d,{type:"danger",size:"small",icon:c(q),onClick:Q=>I(r.row)},{default:l(()=>[i("删 除")]),_:2},1032,["icon","onClick"])]),_:1})]),_:2},1032,["data"])])]),_:1}),e(a,{prop:"title",label:"菜单标题",width:"120"}),e(a,{prop:"meta.icon",label:"图标",width:"120"},{default:l(t=>[e(v,null,{default:l(()=>[(_(),g(N(t.row.meta.icon)))]),_:2},1024)]),_:1}),e(a,{prop:"id",label:"页面ID",width:"130"}),e(a,{prop:"path",label:"页面路径",width:"130"}),e(a,{prop:"redirect",label:"路由地址",width:"220"}),e(a,{prop:"name",label:"英文名称",width:"170"}),e(a,{prop:"component",label:"组件路径",width:"120"}),e(a,{prop:"disabled",label:"是否禁用"},{default:l(t=>[e(V,{type:t.row.disabled?"danger":"success"},{default:l(()=>[i(h(t.row.disabled?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),e(a,{label:"操作",width:"270",fixed:"right"},{default:l(t=>[e(d,{type:"primary",size:"small",icon:c(x),onClick:r=>B(t.row)},{default:l(()=>[i("新 增")]),_:2},1032,["icon","onClick"]),e(d,{type:"warning",size:"small",icon:c(F),onClick:r=>D(t.row)},{default:l(()=>[i("编 辑")]),_:2},1032,["icon","onClick"]),e(d,{type:"danger",size:"small",icon:c(q),onClick:r=>I(t.row)},{default:l(()=>[i("删 除")]),_:2},1032,["icon","onClick"])]),_:1})]),_:1},8,["data"])),[[G,E.value]]),b("div",be,[e(R,{style:{"margin-top":"1rem"},"current-page":y.value,"onUpdate:currentPage":o[0]||(o[0]=t=>y.value=t),"page-size":k.value,"onUpdate:pageSize":o[1]||(o[1]=t=>k.value=t),"page-sizes":[14,16,18,20],size:M.value,background:S.value,layout:"total, sizes, prev, pager, next, jumper",total:C.value},null,8,["current-page","page-size","size","background","total"])])]),_:1})]),_:1}),e(K,{modelValue:f.value,"onUpdate:modelValue":o[11]||(o[11]=t=>f.value=t),title:"新增菜单",width:"40%"},{footer:l(()=>[b("span",we,[e(d,{onClick:o[10]||(o[10]=t=>f.value=!1)},{default:l(()=>[i("取 消")]),_:1}),e(d,{type:"primary",onClick:H},{default:l(()=>[i("确 定")]),_:1})])]),default:l(()=>[e(J,{model:m.form,ref:"menuForm",rules:T,"label-width":"auto"},{default:l(()=>[e(s,{label:"Path",prop:"path"},{default:l(()=>[e(u,{modelValue:n.path,"onUpdate:modelValue":o[2]||(o[2]=t=>n.path=t),placeholder:"菜单路径"},null,8,["modelValue"])]),_:1}),e(s,{label:"Icon",prop:"icon"},{default:l(()=>[e(u,{modelValue:n.icon,"onUpdate:modelValue":o[3]||(o[3]=t=>n.icon=t),placeholder:"菜单图标"},null,8,["modelValue"])]),_:1}),e(s,{label:"Name",prop:"name"},{default:l(()=>[e(u,{modelValue:n.name,"onUpdate:modelValue":o[4]||(o[4]=t=>n.name=t),placeholder:"菜单名称"},null,8,["modelValue"])]),_:1}),e(s,{label:"Redirect",prop:"redirect"},{default:l(()=>[e(u,{modelValue:n.redirect,"onUpdate:modelValue":o[5]||(o[5]=t=>n.redirect=t),placeholder:"直接跳转路径"},null,8,["modelValue"])]),_:1}),e(s,{label:"Component"},{default:l(()=>[e(u,{modelValue:n.component,"onUpdate:modelValue":o[6]||(o[6]=t=>n.component=t),placeholder:"组件路径"},null,8,["modelValue"])]),_:1}),e(s,{label:"Title",prop:"title"},{default:l(()=>[e(u,{modelValue:n.title,"onUpdate:modelValue":o[7]||(o[7]=t=>n.title=t),placeholder:"菜单标题"},null,8,["modelValue"])]),_:1}),e(s,{label:"Parent ID",prop:"parentId"},{default:l(()=>[e(u,{modelValue:n.parentId,"onUpdate:modelValue":o[8]||(o[8]=t=>n.parentId=t),placeholder:"父级菜单 ID"},null,8,["modelValue"])]),_:1}),e(s,{label:"Disabled",prop:"disabled"},{default:l(()=>[e(j,{modelValue:n.disabled,"onUpdate:modelValue":o[9]||(o[9]=t=>n.disabled=t)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])]),_:1})}}};export{Qe as default};
