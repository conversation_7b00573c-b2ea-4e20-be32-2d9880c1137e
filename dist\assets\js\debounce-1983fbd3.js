import{r as a,o as e,g as c,e as n,a as r,w as i,d as _,C as l,aU as u}from"./index-444b28c3.js";import{E as p}from"./el-button-9bbdfcf9.js";import"./index-4d7f16ce.js";const d=l({name:"debounce"}),x=Object.assign(d,{setup(m){const t=()=>{u({message:"我是防抖指令",type:"success"})};return(f,b)=>{const s=p,o=a("debounce");return e(),c("div",null,[n((e(),r(s,{type:"primary"},{default:i(()=>[_("防抖按钮 (0.2秒后执行)")]),_:1})),[[o,t]])])}}});export{x as default};
