<template>
    <div>
        <el-card>
            <el-form :inline="true" :model="formInline" class="demo-form-inline">
                <!-- 按照工号搜索 -->
                <el-form-item label="工号">
                    <el-input v-model="formInline.uuid" placeholder="请输入工号" style="width: 200px;"></el-input>
                </el-form-item>
                <el-form-item label="日期">
                    <el-date-picker v-model="formInline.date" type="datetimerange" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss">
                    </el-date-picker>
                </el-form-item>
                <!-- 部门选择 -->
                <el-form-item label="部门">
                    <el-select v-model="formInline.department" placeholder="请选择部门" style="width: 200px;">
                        <el-option label="织造车间" value="织造车间"></el-option>
                        <el-option label="整理车间" value="整理车间"></el-option>
                        <el-option label="打样车间" value="打样车间"></el-option>
                        <el-option label="综合维修" value="综合维修"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSubmit" icon="el-icon-search">查询</el-button>
                </el-form-item>
                <!-- 重置按钮 -->
                <el-form-item>
                    <el-button icon="el-icon-refresh-right" @click="resetDatas">重置</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" plain icon="el-icon-refresh-right" @click="refresh"></el-button>
                </el-form-item>
            </el-form>
            <!-- 主体区域表格数据 -->
            <el-table :data="tableData" style="width: 100%" stripe v-loading="loading" element-loading-text="Flyknit">
                <el-table-column type="index" width="50"></el-table-column>
                <el-table-column prop="department" label="部门" width="180">
                </el-table-column>
                <el-table-column prop="name" label="姓名" width="180">
                </el-table-column>
                <el-table-column prop="idcard" label="工号" width="180">
                </el-table-column>
                <el-table-column prop="location" label="加油区域">
                </el-table-column>
                <el-table-column prop="consume_time" label="加油耗时">
                </el-table-column>
                <el-table-column prop="create_time" label="加油日期">
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
                :page-sizes="pageSizes" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="total">
            </el-pagination>
        </el-card>
    </div>
</template>
<script>
export default {
    data() {
        return {
            formInline: {
                date: '',
                department: '',
                uuid: ''
            },
            currentPage: 1,
            pageSize: 12,
            pageSizes: [12, 16, 20, 24],
            total: 0,
            tableData: [], // 表格数据
            loading: true
        }
    },
    mounted() {
        this.getBrushOilRecord();
    },
    methods: {
        // 获取加油记录
        async getBrushOilRecord() {
            try {
                const obj = {
                    page: this.currentPage,
                    pageSize: this.pageSize,
                    department: this.formInline.department,
                    uuid: this.formInline.uuid,
                    dates: this.formInline.date
                }
                const res = await this.$http.getOilRecord(obj);
                if (res.status !== 200) {
                    return this.$message.error(res.message);
                }
                const { list } = res.data;
                this.total = res.data.total;
                this.tableData = list;
                setTimeout(() => {
                    this.loading = false;
                }, 500);
            } catch (error) {
                this.loading = false;
                return this.$message.error('服务器错误，请稍后重试！');
            }
        },
        onSubmit() {
            this.loading = true
            this.getBrushOilRecord();
        },
        handleSizeChange(val) {
            this.loading = true
            this.pageSize = val
            this.getBrushOilRecord()
        },
        handleCurrentChange(val) {
            this.loading = true
            this.currentPage = val
            this.getBrushOilRecord()
        },
        resetDatas() {
            this.formInline = {
                date: '',
                department: '',
                uuid: ''
            }
            // this.loading = true
            // this.getBrushOilRecord();
        },
        refresh() {
            this.loading = true
            this.getBrushOilRecord();
        }
    }
}
</script>
<style lang="scss">
.el-card__body {
    padding: 20px 0 0 20px;
}

.el-pagination {
    margin-top: 20px;
    padding-bottom: 20px;
}

.float-btn {
    float: right;
    padding-right: 20px;
}
</style>