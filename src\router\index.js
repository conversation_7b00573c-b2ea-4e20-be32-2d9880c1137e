/*
 * @Author: flyknit <EMAIL>
 * @Date: 2024-04-09 09:02:42
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-05-09 07:48:26
 * @FilePath: \electronic-filed:\gitee\backend-demo\src\router\index.js
 * @Description: 
 * 
 * Copyright (c) 2024 by ${<EMAIL>}, All Rights Reserved. 
 */
// src/router/index.js
import Vue from "vue";
import VueRouter from "vue-router";
Vue.use(VueRouter);
// 引入NProgress进度条
import NProgress from "nprogress";
import "nprogress/nprogress.css";
const originalPush = VueRouter.prototype.push;
const originalReplace = VueRouter.prototype.replace;

VueRouter.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject) {
    return originalPush.call(this, location, onResolve, onReject);
  }
  return originalPush.call(this, location).catch((err) => err);
};

VueRouter.prototype.replace = function push(location, onResolve, onReject) {
  if (onResolve || onReject) {
    return originalReplace.call(this, location, onResolve, onReject);
  }
  return originalReplace.call(this, location).catch((err) => err);
};

// 引入404页面
import NotFound from "../views/404.vue";
import Index from "../views/index/Index.vue";
const router = new VueRouter({
  mode: "history",
  base: "/",
  routes: [
    {
      path: "/",
      name: "index",
      component: Index,
      redirect: "/console",
      meta: {
        title: "巡检管理后台",
      },
      // 二级路由
      children: [
        {
          // 控制台
          path: "/console",
          name: "console",
          component: () => import("@/views/Console.vue"),
        },
        // model初始化
        {
          path: "/sites",
          name: "sites",
          component: () => import("@/views/sites/Sites.vue"),
        },
        // 巡检订单
        {
          path: "/tasty",
          name: "tasty",
          component: () => import("@/views/tasty/Tasty.vue"),
        },
        // 产品列表
        {
          path: "/way",
          name: "way",
          component: () => import("@/views/way/Way.vue"),
        },
        // 系统管理 - 用户管理
        {
          path: "/users",
          name: "users",
          component: () => import("@/views/system/User.vue"),
        },
        // 员工列表
        {
          path: "/staff",
          name: "staff",
          component: () => import("@/views/staff/Staff.vue"),
        },
        // 标准列表
        {
          path: "/standard",
          name: "standard",
          component: () => import("@/views/standard/Standard.vue"),
        },
        // 尺码初始化
        {
          path: "/size",
          name: "size",
          component: () => import("@/views/size/Size.vue"),
        },
        // 品名初始化
        {
          path: "/style",
          name: "style",
          component: () => import("@/views/style/Style.vue"),
        },
        // 巡检操作
        {
          path: "/inspection",
          name: "inspection",
          component: () => import("@/views/inspection/Inspection.vue"),
        },
        // 图片管理
        {
          path: "/image",
          name: "image",
          component: () => import("@/views/images/Image.vue"),
        },
        // 测量点位初始化
        {
          path: "/point",
          name: "point",
          component: () => import("@/views/point/Point.vue"),
        },
        // 规格标准类型
        {
          path: "/type",
          name: "type",
          component: () => import("@/views/type/Type.vue"),
        },
        // 系统设置
        {
          path: "/setting",
          name: "setting",
          component: () => import("@/views/setting/Setting.vue"),
        }
      ],
    },
    {
      path: "/login",
      name: "login",
      component: () => import("@/views/login.vue"),
    },
    {
      // 404路由
      path: "/:pathMatch(.*)*",
      name: "NotFound",
      component: NotFound,
    },
  ],
});

//路由守卫鉴权
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem("accessToken");
  //获取所有需要验证登录状态的路由,数组中为路由的name/path
  const routers = ["index", "console", "sites", "tasty", "way", "users", "feedback", "standard", "size", "style", "inspection"];
  //to.name 为所要到达的路由的名称
  if (routers.indexOf(to.name) != -1) {
    //路由为要验证的路由
    if (!token||token==null) {
      //为没有登录状态
      router.push("/login"); //router为路由的方法，router.push()实现路由的跳转
    } else {
      next();
    }
  } else {
    // 进度条
    NProgress.start();
    next();
  }
});

router.afterEach(() => {
  setTimeout(() => {
    NProgress.done();
  }, 1000);
});

export default router;
