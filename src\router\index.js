/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-07-24 09:14:24
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-24 17:40:54
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\router\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// src/router/index.js
import Vue from 'vue'
import VueRouter from 'vue-router'
Vue.use(VueRouter)
// 引入NProgress进度条
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
const originalPush = VueRouter.prototype.push
const originalReplace = VueRouter.prototype.replace

VueRouter.prototype.push = function push(location, onResolve, onReject) {
	if (onResolve || onReject) {
		return originalPush.call(this, location, onResolve, onReject)
	}
	return originalPush.call(this, location).catch(err => err)
}

VueRouter.prototype.replace = function push(location, onResolve, onReject) {
	if (onResolve || onReject) {
		return originalReplace.call(this, location, onResolve, onReject)
	}
	return originalReplace.call(this, location).catch(err => err)
}

import Index from '../views/Index.vue'
const router = new VueRouter({
	mode: 'history',
	base: '/',
	routes: [
		{
			path: '/index',
			name: 'index',
			component: Index,
			redirect: '/console',
			meta: {
				title: '管理后台',
			},
			// 二级路由
			children: [
				{ // 控制台
					path: '/console',
					name: 'console',
					component: () => import('@/views/index/Console.vue')
				},
				{// 无权限页面
					path: '/noPermission',
					name: 'noPermission',
					component: () => import('@/views/rights/Nopermission.vue')
				},
				{// 部门管理-设备列表
					path: '/deviceList',
					name: 'deviceList',
					component: () => import('@/views/department/DeviceList.vue')
				}, {// 部门管理-用户列表
					path: '/userList',
					name: 'userList',
					component: () => import('@/views/department/UserList.vue')
				}, {// 部门管理-保养总览
					path: '/maintaince',
					name: 'maintaince',
					component: () => import('@/views/department/Maintaince.vue')
				}, {// 部门管理-项目管理
					path: '/maintainceList',
					name: 'maintainceList',
					component: () => import('@/views/department/MaintainceList.vue')
				},
				{// 部门管理-区域管理
					path: '/maintainceArea',
					name: 'maintainceArea',
					component: () => import('@/views/department/MaintainceArea.vue')
				},
				{// 部门管理-工作日志
					path: '/dailyworkLog',
					name: 'dailyworkLog',
					component: () => import('@/views/department/DailyworkLog.vue')
				}, {// 部门管理-工作项目
					path: '/workItem',
					name: 'workItem',
					component: () => import('@/views/department/WorkItem.vue')
				},
				{ // 部门管理-刷油管理
					path: '/brushOil',
					name: 'brushOil',
					component: () => import('@/views/department/BrushOil.vue')
				},
				{// 报表分析-保养报表
					path: '/dataCenter',
					name: 'dataCenter',
					component: () => import('@/views/department/DataCenter.vue')
				},
				{// 部门管理-功效分析
					path: '/efficiencyAnalysis',
					name: 'efficiencyAnalysis',
					component: () => import('@/views/department/EfficiencyAnalysis.vue')
				},
				{ // 系统管理
					path: '/operateLog',
					name: 'operateLog',
					component: () => import('@/views/system/OperateLog.vue'),
					meta: { requiresAuth: true, requiresAdmin: true }
				},
				{// 意见反馈
					path: '/feedback',
					name: 'feedback',
					component: () => import('@/views/system/Feedback.vue'),
					meta: { requiresAuth: true, requiresAdmin: true }
				},
				{// 部门管理-app首页配置
					path: '/appConfig',
					name: 'appConfig',
					component: () => import('@/views/system/AppConfig.vue'),
					meta: { requiresAuth: true, requiresAdmin: true }
				},
				{// 维修管理-报修管理
					path: '/callRepair',
					name: 'callRepair',
					component: () => import('@/views/device/CallRepair.vue')
				},
				{// 维修管理-维修单详情
					path: '/details',
					name: 'details',
					component: () => import('@/views/device/Details.vue')
				},
				{// 设备管理-设备调拨记录
					path: '/transferRecord',
					name: 'transferRecord',
					component: () => import('@/views/device/TransferRecord.vue')
				},
				{ // 设备管理 - 采购记录
					path: '/purchase',
					name: 'purchase',
					component: () => import('@/views/device/Purchase.vue')
				},
				{ // 综合管理 - 保养记录
					path: '/maintainceRecord',
					name: 'maintainceRecord',
					component: () => import('@/views/device/MaintainceRecord.vue')
				},
				{ // 设备管理 - 设备巡检
					path: '/inspection',
					name: 'inspection',
					component: () => import('@/views/device/Inspection.vue')
				},
				{ // 设备管理 - 设备分析
					path: '/deviceAnalysis',
					name: 'deviceAnalysis',
					component: () => import('@/views/device/DeviceAnalysis.vue')
				},
				{ // 设备管理 - 年度巡查结论分析
					path: '/yearAnalyse',
					name: 'yearAnalyse',
					component: () => import('@/views/device/YearAnalyse.vue')
				},
				{ // 备件管理 - 备件列表
					path: '/spareParts',
					name: 'spareParts',
					component: () => import('@/views/device/SpareParts.vue')
				},
				{ // 备件管理 - 分类管理
					path: '/sparePartsClassify',
					name: 'sparePartsClassify',
					component: () => import('@/views/device/SparePartsClassify.vue')
				},
				{ // 备件管理 - 库位管理
					path: '/sparePartsLocation',
					name: 'sparePartsLocation',
					component: () => import('@/views/device/SparePartsLocation.vue')
				},
				{ // 备件管理 - 备件入库
					path: '/sparePartsIn',
					name: 'sparePartsIn',
					component: () => import('@/views/device/SparePartsIn.vue')
				},
				{ // 备件管理 - 备件出库
					path: '/sparePartsOut',
					name: 'sparePartsOut',
					component: () => import('@/views/device/SparePartsOut.vue')
				},
				{ // 备件管理 - 入库记录
					path: '/sparePartsCheck',
					name: 'sparePartsCheck',
					component: () => import('@/views/device/SparePartsCheck.vue')
				},
				{
					path: '/sparePartsScrap',
					name: 'sparePartsScrap',
					component: () => import('@/views/device/SparePartsScrap.vue')
				},
				{ // 设备管理采购单预览
					path: '/purchasePreview',
					name: 'purchasePreview',
					component: () => import('@/views/device/PurchasePreview.vue')
				},
				{ // 系统管理 - 用户管理
					path: '/userManage',
					name: 'userManage',
					component: () => import('@/views/system/UserManage.vue'),
				},
				{ // 系统管理 - 角色管理
					path: '/roleManage',
					name: 'roleManage',
					component: () => import('@/views/system/RoleManage.vue'),
				},
				{ // 系统管理 - 菜单管理
					path: '/menuManage',
					name: 'menuManage',
					component: () => import('@/views/system/MenuManage.vue'),
				},
				{ // 系统管理 - 部门管理
					path: '/departmentManage',
					name: 'departmentManage',
					component: () => import('@/views/system/DepartmentManage.vue'),
				},
				{ // 备件管理 - 维修管理
					path: '/sparePartsRepair',
					name: 'sparePartsRepair',
					component: () => import('@/views/device/SparePartsRepair.vue'),
				}
			]
		},
		{
			path: '/',
			redirect: '/index'
		},
		{
			path: '/login',
			name: 'login',
			component: () => import('@/views/login.vue')
		},
		{ // 404路由
			path: '/notFound',
			name: 'notFound',
			component: () => import('@/views/NotFound.vue')
		}
	]
})

//路由守卫鉴权
router.beforeEach((to, from, next) => {
	const token = localStorage.getItem('accessToken');
	const allowedRoutes = ["index", "console", "DeviceList", "userList", "maintaince", "maintainceList", "maintainceArea", "dailyworkLog", "efficiencyAnalysis", "operateLog", "feedback", "appConfig", "workItem", "brushOil", "dataCenter", "callRepair", "details", "transferRecord", "purchase", "maintainceRecord", "inspection", "deviceAnalysis", "spareParts", "sparePartsClassify", "sparePartsLocation", "sparePartsIn", "sparePartsOut", "sparePartsCheck", "sparePartsScrap", "yearAnalyse", "purchasePreview", "userManage", "roleManage", "menuManage", "departmentManage", "sparePartsRepair"];
	if (to.path === "/" && token) {
		next("/index"); // 已登录时访问'/'路由，直接跳转首页
	} else if (to.name === "login" && token) {
		next("/index"); // 已登录时访问登录页，直接跳转首页
	} else if (allowedRoutes.includes(to.name) || to.name === "login") {
		if (!token && to.name !== "login") {
			next("/login"); // 未登录时访问非登录页，跳转登录页
		} else {
			next(); // 已登录或访问登录页，继续访问该页面
		}
	} else {
		NProgress.start();
		next();
	}
});

router.afterEach(() => {
	NProgress.done()
})


export default router