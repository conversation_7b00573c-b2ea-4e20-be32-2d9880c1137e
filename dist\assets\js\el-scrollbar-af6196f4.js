import{b_ as qt,aw as H,ab as <PERSON>,c as L,bk as Ue,a8 as I,k as Ge,av as de,bC as et,b$ as _o,bD as $o,bE as Ho,c0 as Io,Q as U,n as w,a9 as Te,_ as Q,C as X,X as ie,R as he,i as A,ac as je,aM as tt,bh as He,o as D,a as oe,w as G,e as at,m as ot,H as se,ad as Xe,a3 as Xt,aK as Yt,g as Se,b as ge,F as Gt,a7 as Le,aZ as At,bM as No,G as kt,j as Do,bN as jo,S as le,L as zo,f as ve,bz as Jt,U as st,bO as nt,c1 as Wo,c2 as Fo,c3 as Ko,c4 as Zt,aR as Ie,aQ as Qt,c5 as Rt,aB as Uo,bW as Vo,aN as Bt,B as Ee,aL as qo,bc as Xo,b8 as <PERSON>,bf as <PERSON>,c6 as <PERSON>,c7 as <PERSON>,t as Zo}from"./index-444b28c3.js";import{t as Qo,i as eo}from"./event-fe80fd0c.js";import{E as en}from"./focus-trap-6de7266c.js";import{a as tn}from"./index-4d7f16ce.js";const te=(e,t,{checkForDefaultPrevented:o=!0}={})=>r=>{const s=e==null?void 0:e(r);if(o===!1||!s)return t==null?void 0:t(r)},ba=e=>t=>t.pointerType==="mouse"?e(t):void 0,on=()=>Math.floor(Math.random()*1e4),to=Symbol("scrollbarContextKey"),it=Symbol("popper"),oo=Symbol("popperContent"),nn=qt({type:H(Boolean),default:null}),rn=qt({type:H(Function)}),no=e=>{const t=`update:${e}`,o=`onUpdate:${e}`,n=[t],r={[e]:nn,[o]:rn};return{useModelToggle:({indicator:a,toggleReason:l,shouldHideWhenRouteChanges:i,shouldProceed:u,onShow:c,onHide:h})=>{const p=Lo(),{emit:g}=p,v=p.props,d=L(()=>Ue(v[o])),E=L(()=>v[e]===null),b=m=>{a.value!==!0&&(a.value=!0,l&&(l.value=m),Ue(c)&&c(m))},y=m=>{a.value!==!1&&(a.value=!1,l&&(l.value=m),Ue(h)&&h(m))},S=m=>{if(v.disabled===!0||Ue(u)&&!u())return;const T=d.value&&de;T&&g(t,!0),(E.value||!T)&&b(m)},O=m=>{if(v.disabled===!0||!de)return;const T=d.value&&de;T&&g(t,!1),(E.value||!T)&&y(m)},f=m=>{et(m)&&(v.disabled&&m?d.value&&g(t,!1):a.value!==m&&(m?b():y()))},C=()=>{a.value?O():S()};return I(()=>v[e],f),i&&p.appContext.config.globalProperties.$route!==void 0&&I(()=>({...p.proxy.$route}),()=>{i.value&&a.value&&O()}),Ge(()=>{f(v[e])}),{hide:O,show:S,toggle:C,hasUpdateHandler:d}},useModelToggleProps:r,useModelToggleEmits:n}};no("modelValue");function an(){let e;const t=(n,r)=>{o(),e=window.setTimeout(n,r)},o=()=>window.clearTimeout(e);return _o(()=>o()),{registerTimeout:t,cancelTimeout:o}}let xt;const sn=$o("namespace",Ho),ro=`${sn.value}-popper-container-${on()}`,ao=`#${ro}`,ln=()=>{const e=document.createElement("div");return e.id=ro,document.body.appendChild(e),e},un=()=>{Io(()=>{de&&(!xt||!document.body.querySelector(ao))&&(xt=ln())})},cn=U({showAfter:{type:Number,default:0},hideAfter:{type:Number,default:200}}),pn=({showAfter:e,hideAfter:t,open:o,close:n})=>{const{registerTimeout:r}=an();return{onOpen:l=>{r(()=>{o(l)},w(e))},onClose:l=>{r(()=>{n(l)},w(t))}}},so=Symbol("elForwardRef"),fn=e=>{Te(so,{setForwardRef:o=>{e.value=o}})},dn=e=>({mounted(t){e(t)},updated(t){e(t)},unmounted(){e(null)}}),Ce=4,vn={vertical:{offset:"offsetHeight",scroll:"scrollTop",scrollSize:"scrollHeight",size:"height",key:"vertical",axis:"Y",client:"clientY",direction:"top"},horizontal:{offset:"offsetWidth",scroll:"scrollLeft",scrollSize:"scrollWidth",size:"width",key:"horizontal",axis:"X",client:"clientX",direction:"left"}},mn=({move:e,size:t,bar:o})=>({[o.size]:t,transform:`translate${o.axis}(${e}%)`}),gn=U({vertical:Boolean,size:String,move:Number,ratio:{type:Number,required:!0},always:Boolean}),hn=X({__name:"thumb",props:gn,setup(e){const t=e,o="Thumb",n=ie(to),r=he("scrollbar");n||Qo(o,"can not inject scrollbar context");const s=A(),a=A(),l=A({}),i=A(!1);let u=!1,c=!1,h=de?document.onselectstart:null;const p=L(()=>vn[t.vertical?"vertical":"horizontal"]),g=L(()=>mn({size:t.size,move:t.move,bar:p.value})),v=L(()=>s.value[p.value.offset]**2/n.wrapElement[p.value.scrollSize]/t.ratio/a.value[p.value.offset]),d=m=>{var T;if(m.stopPropagation(),m.ctrlKey||[1,2].includes(m.button))return;(T=window.getSelection())==null||T.removeAllRanges(),b(m);const R=m.currentTarget;R&&(l.value[p.value.axis]=R[p.value.offset]-(m[p.value.client]-R.getBoundingClientRect()[p.value.direction]))},E=m=>{if(!a.value||!s.value||!n.wrapElement)return;const T=Math.abs(m.target.getBoundingClientRect()[p.value.direction]-m[p.value.client]),R=a.value[p.value.offset]/2,M=(T-R)*100*v.value/s.value[p.value.offset];n.wrapElement[p.value.scroll]=M*n.wrapElement[p.value.scrollSize]/100},b=m=>{m.stopImmediatePropagation(),u=!0,document.addEventListener("mousemove",y),document.addEventListener("mouseup",S),h=document.onselectstart,document.onselectstart=()=>!1},y=m=>{if(!s.value||!a.value||u===!1)return;const T=l.value[p.value.axis];if(!T)return;const R=(s.value.getBoundingClientRect()[p.value.direction]-m[p.value.client])*-1,M=a.value[p.value.offset]-T,_=(R-M)*100*v.value/s.value[p.value.offset];n.wrapElement[p.value.scroll]=_*n.wrapElement[p.value.scrollSize]/100},S=()=>{u=!1,l.value[p.value.axis]=0,document.removeEventListener("mousemove",y),document.removeEventListener("mouseup",S),C(),c&&(i.value=!1)},O=()=>{c=!1,i.value=!!t.size},f=()=>{c=!0,i.value=u};je(()=>{C(),document.removeEventListener("mouseup",S)});const C=()=>{document.onselectstart!==h&&(document.onselectstart=h)};return tt(He(n,"scrollbarElement"),"mousemove",O),tt(He(n,"scrollbarElement"),"mouseleave",f),(m,T)=>(D(),oe(Yt,{name:w(r).b("fade"),persisted:""},{default:G(()=>[at(ot("div",{ref_key:"instance",ref:s,class:se([w(r).e("bar"),w(r).is(w(p).key)]),onMousedown:E},[ot("div",{ref_key:"thumb",ref:a,class:se(w(r).e("thumb")),style:Xe(w(g)),onMousedown:d},null,38)],34),[[Xt,m.always||i.value]])]),_:1},8,["name"]))}});var Lt=Q(hn,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/scrollbar/src/thumb.vue"]]);const bn=U({always:{type:Boolean,default:!0},width:String,height:String,ratioX:{type:Number,default:1},ratioY:{type:Number,default:1}}),yn=X({__name:"bar",props:bn,setup(e,{expose:t}){const o=e,n=A(0),r=A(0);return t({handleScroll:a=>{if(a){const l=a.offsetHeight-Ce,i=a.offsetWidth-Ce;r.value=a.scrollTop*100/l*o.ratioY,n.value=a.scrollLeft*100/i*o.ratioX}}}),(a,l)=>(D(),Se(Gt,null,[ge(Lt,{move:n.value,ratio:a.ratioX,size:a.width,always:a.always},null,8,["move","ratio","size","always"]),ge(Lt,{move:r.value,ratio:a.ratioY,size:a.height,vertical:"",always:a.always},null,8,["move","ratio","size","always"])],64))}});var wn=Q(yn,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/scrollbar/src/bar.vue"]]);const On=U({height:{type:[String,Number],default:""},maxHeight:{type:[String,Number],default:""},native:Boolean,wrapStyle:{type:H([String,Object,Array]),default:""},wrapClass:{type:[String,Array],default:""},viewClass:{type:[String,Array],default:""},viewStyle:{type:[String,Array,Object],default:""},noresize:Boolean,tag:{type:String,default:"div"},always:Boolean,minSize:{type:Number,default:20}}),En={scroll:({scrollTop:e,scrollLeft:t})=>[e,t].every(Le)},Cn={name:"ElScrollbar"},Tn=X({...Cn,props:On,emits:En,setup(e,{expose:t,emit:o}){const n=e,r=he("scrollbar");let s,a;const l=A(),i=A(),u=A(),c=A("0"),h=A("0"),p=A(),g=A(1),v=A(1),d=L(()=>{const f={};return n.height&&(f.height=At(n.height)),n.maxHeight&&(f.maxHeight=At(n.maxHeight)),[n.wrapStyle,f]}),E=()=>{var f;i.value&&((f=p.value)==null||f.handleScroll(i.value),o("scroll",{scrollTop:i.value.scrollTop,scrollLeft:i.value.scrollLeft}))};function b(f,C){Jt(f)?i.value.scrollTo(f):Le(f)&&Le(C)&&i.value.scrollTo(f,C)}const y=f=>{Le(f)&&(i.value.scrollTop=f)},S=f=>{Le(f)&&(i.value.scrollLeft=f)},O=()=>{if(!i.value)return;const f=i.value.offsetHeight-Ce,C=i.value.offsetWidth-Ce,m=f**2/i.value.scrollHeight,T=C**2/i.value.scrollWidth,R=Math.max(m,n.minSize),M=Math.max(T,n.minSize);g.value=m/(f-m)/(R/(f-R)),v.value=T/(C-T)/(M/(C-M)),h.value=R+Ce<f?`${R}px`:"",c.value=M+Ce<C?`${M}px`:""};return I(()=>n.noresize,f=>{f?(s==null||s(),a==null||a()):({stop:s}=No(u,O),a=tt("resize",O))},{immediate:!0}),I(()=>[n.maxHeight,n.height],()=>{n.native||kt(()=>{var f;O(),i.value&&((f=p.value)==null||f.handleScroll(i.value))})}),Te(to,Do({scrollbarElement:l,wrapElement:i})),Ge(()=>{n.native||kt(()=>{O()})}),jo(()=>O()),t({wrap$:i,update:O,scrollTo:b,setScrollTop:y,setScrollLeft:S,handleScroll:E}),(f,C)=>(D(),Se("div",{ref_key:"scrollbar$",ref:l,class:se(w(r).b())},[ot("div",{ref_key:"wrap$",ref:i,class:se([f.wrapClass,w(r).e("wrap"),{[w(r).em("wrap","hidden-default")]:!f.native}]),style:Xe(w(d)),onScroll:E},[(D(),oe(zo(f.tag),{ref_key:"resize$",ref:u,class:se([w(r).e("view"),f.viewClass]),style:Xe(f.viewStyle)},{default:G(()=>[le(f.$slots,"default")]),_:3},8,["class","style"]))],38),f.native?ve("v-if",!0):(D(),oe(wn,{key:0,ref_key:"barRef",ref:p,height:h.value,width:c.value,always:f.always,"ratio-x":v.value,"ratio-y":g.value},null,8,["height","width","always","ratio-x","ratio-y"]))],2))}});var Sn=Q(Tn,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/scrollbar/src/scrollbar.vue"]]);const ya=st(Sn),wa={LIGHT:"light",DARK:"dark"},Pn=["dialog","grid","listbox","menu","tooltip","tree"],io=U({role:{type:String,values:Pn,default:"tooltip"}}),An={name:"ElPopperRoot",inheritAttrs:!1},kn=X({...An,props:io,setup(e,{expose:t}){const o=e,n=A(),r=A(),s=A(),a=A(),l=L(()=>o.role),i={triggerRef:n,popperInstanceRef:r,contentRef:s,referenceRef:a,role:l};return t(i),Te(it,i),(u,c)=>le(u.$slots,"default")}});var Rn=Q(kn,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/popper/src/popper.vue"]]);const lo=U({arrowOffset:{type:Number,default:5}}),Bn={name:"ElPopperArrow",inheritAttrs:!1},Mn=X({...Bn,props:lo,setup(e,{expose:t}){const o=e,n=he("popper"),{arrowOffset:r,arrowRef:s}=ie(oo,void 0);return I(()=>o.arrowOffset,a=>{r.value=a}),je(()=>{s.value=void 0}),t({arrowRef:s}),(a,l)=>(D(),Se("span",{ref_key:"arrowRef",ref:s,class:se(w(n).e("arrow")),"data-popper-arrow":""},null,2))}});var xn=Q(Mn,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/popper/src/arrow.vue"]]);const Ln="ElOnlyChild",_n=X({name:Ln,setup(e,{slots:t,attrs:o}){var n;const r=ie(so),s=dn((n=r==null?void 0:r.setForwardRef)!=null?n:nt);return()=>{var a;const l=(a=t.default)==null?void 0:a.call(t,o);if(!l||l.length>1)return null;const i=uo(l);return i?at(Wo(i,o),[[s]]):null}}});function uo(e){if(!e)return null;const t=e;for(const o of t){if(Jt(o))switch(o.type){case Ko:continue;case Fo:case"svg":return _t(o);case Gt:return uo(o.children);default:return o}return _t(o)}return null}function _t(e){const t=he("only-child");return ge("span",{class:t.e("content")},[e])}const co=U({virtualRef:{type:H(Object)},virtualTriggering:Boolean,onMouseenter:Function,onMouseleave:Function,onClick:Function,onKeydown:Function,onFocus:Function,onBlur:Function,onContextmenu:Function,id:String,open:Boolean}),$n={name:"ElPopperTrigger",inheritAttrs:!1},Hn=X({...$n,props:co,setup(e,{expose:t}){const o=e,{role:n,triggerRef:r}=ie(it,void 0);fn(r);const s=L(()=>l.value?o.id:void 0),a=L(()=>{if(n&&n.value==="tooltip")return o.open&&o.id?o.id:void 0}),l=L(()=>{if(n&&n.value!=="tooltip")return n.value}),i=L(()=>l.value?`${o.open}`:void 0);let u;return Ge(()=>{I(()=>o.virtualRef,c=>{c&&(r.value=Zt(c))},{immediate:!0}),I(()=>r.value,(c,h)=>{u==null||u(),u=void 0,Ie(c)&&(["onMouseenter","onMouseleave","onClick","onKeydown","onFocus","onBlur","onContextmenu"].forEach(p=>{var g;const v=o[p];v&&(c.addEventListener(p.slice(2).toLowerCase(),v),(g=h==null?void 0:h.removeEventListener)==null||g.call(h,p.slice(2).toLowerCase(),v))}),u=I([s,a,l,i],p=>{["aria-controls","aria-describedby","aria-haspopup","aria-expanded"].forEach((g,v)=>{eo(p[v])?c.removeAttribute(g):c.setAttribute(g,p[v])})},{immediate:!0})),Ie(h)&&["aria-controls","aria-describedby","aria-haspopup","aria-expanded"].forEach(p=>h.removeAttribute(p))},{immediate:!0})}),je(()=>{u==null||u(),u=void 0}),t({triggerRef:r}),(c,h)=>c.virtualTriggering?ve("v-if",!0):(D(),oe(w(_n),Qt({key:0},c.$attrs,{"aria-controls":w(s),"aria-describedby":w(a),"aria-expanded":w(i),"aria-haspopup":w(l)}),{default:G(()=>[le(c.$slots,"default")]),_:3},16,["aria-controls","aria-describedby","aria-expanded","aria-haspopup"]))}});var In=Q(Hn,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/popper/src/trigger.vue"]]),j="top",F="bottom",K="right",z="left",lt="auto",ze=[j,F,K,z],Pe="start",Ne="end",Nn="clippingParents",po="viewport",xe="popper",Dn="reference",$t=ze.reduce(function(e,t){return e.concat([t+"-"+Pe,t+"-"+Ne])},[]),ut=[].concat(ze,[lt]).reduce(function(e,t){return e.concat([t,t+"-"+Pe,t+"-"+Ne])},[]),jn="beforeRead",zn="read",Wn="afterRead",Fn="beforeMain",Kn="main",Un="afterMain",Vn="beforeWrite",qn="write",Xn="afterWrite",Yn=[jn,zn,Wn,Fn,Kn,Un,Vn,qn,Xn];function Z(e){return e?(e.nodeName||"").toLowerCase():null}function Y(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Ae(e){var t=Y(e).Element;return e instanceof t||e instanceof Element}function W(e){var t=Y(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function ct(e){if(typeof ShadowRoot>"u")return!1;var t=Y(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function Gn(e){var t=e.state;Object.keys(t.elements).forEach(function(o){var n=t.styles[o]||{},r=t.attributes[o]||{},s=t.elements[o];!W(s)||!Z(s)||(Object.assign(s.style,n),Object.keys(r).forEach(function(a){var l=r[a];l===!1?s.removeAttribute(a):s.setAttribute(a,l===!0?"":l)}))})}function Jn(e){var t=e.state,o={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,o.popper),t.styles=o,t.elements.arrow&&Object.assign(t.elements.arrow.style,o.arrow),function(){Object.keys(t.elements).forEach(function(n){var r=t.elements[n],s=t.attributes[n]||{},a=Object.keys(t.styles.hasOwnProperty(n)?t.styles[n]:o[n]),l=a.reduce(function(i,u){return i[u]="",i},{});!W(r)||!Z(r)||(Object.assign(r.style,l),Object.keys(s).forEach(function(i){r.removeAttribute(i)}))})}}var fo={name:"applyStyles",enabled:!0,phase:"write",fn:Gn,effect:Jn,requires:["computeStyles"]};function J(e){return e.split("-")[0]}var me=Math.max,Ye=Math.min,ke=Math.round;function Re(e,t){t===void 0&&(t=!1);var o=e.getBoundingClientRect(),n=1,r=1;if(W(e)&&t){var s=e.offsetHeight,a=e.offsetWidth;a>0&&(n=ke(o.width)/a||1),s>0&&(r=ke(o.height)/s||1)}return{width:o.width/n,height:o.height/r,top:o.top/r,right:o.right/n,bottom:o.bottom/r,left:o.left/n,x:o.left/n,y:o.top/r}}function pt(e){var t=Re(e),o=e.offsetWidth,n=e.offsetHeight;return Math.abs(t.width-o)<=1&&(o=t.width),Math.abs(t.height-n)<=1&&(n=t.height),{x:e.offsetLeft,y:e.offsetTop,width:o,height:n}}function vo(e,t){var o=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(o&&ct(o)){var n=t;do{if(n&&e.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function ne(e){return Y(e).getComputedStyle(e)}function Zn(e){return["table","td","th"].indexOf(Z(e))>=0}function ue(e){return((Ae(e)?e.ownerDocument:e.document)||window.document).documentElement}function Je(e){return Z(e)==="html"?e:e.assignedSlot||e.parentNode||(ct(e)?e.host:null)||ue(e)}function Ht(e){return!W(e)||ne(e).position==="fixed"?null:e.offsetParent}function Qn(e){var t=navigator.userAgent.toLowerCase().indexOf("firefox")!==-1,o=navigator.userAgent.indexOf("Trident")!==-1;if(o&&W(e)){var n=ne(e);if(n.position==="fixed")return null}var r=Je(e);for(ct(r)&&(r=r.host);W(r)&&["html","body"].indexOf(Z(r))<0;){var s=ne(r);if(s.transform!=="none"||s.perspective!=="none"||s.contain==="paint"||["transform","perspective"].indexOf(s.willChange)!==-1||t&&s.willChange==="filter"||t&&s.filter&&s.filter!=="none")return r;r=r.parentNode}return null}function We(e){for(var t=Y(e),o=Ht(e);o&&Zn(o)&&ne(o).position==="static";)o=Ht(o);return o&&(Z(o)==="html"||Z(o)==="body"&&ne(o).position==="static")?t:o||Qn(e)||t}function ft(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function _e(e,t,o){return me(e,Ye(t,o))}function er(e,t,o){var n=_e(e,t,o);return n>o?o:n}function mo(){return{top:0,right:0,bottom:0,left:0}}function go(e){return Object.assign({},mo(),e)}function ho(e,t){return t.reduce(function(o,n){return o[n]=e,o},{})}var tr=function(e,t){return e=typeof e=="function"?e(Object.assign({},t.rects,{placement:t.placement})):e,go(typeof e!="number"?e:ho(e,ze))};function or(e){var t,o=e.state,n=e.name,r=e.options,s=o.elements.arrow,a=o.modifiersData.popperOffsets,l=J(o.placement),i=ft(l),u=[z,K].indexOf(l)>=0,c=u?"height":"width";if(!(!s||!a)){var h=tr(r.padding,o),p=pt(s),g=i==="y"?j:z,v=i==="y"?F:K,d=o.rects.reference[c]+o.rects.reference[i]-a[i]-o.rects.popper[c],E=a[i]-o.rects.reference[i],b=We(s),y=b?i==="y"?b.clientHeight||0:b.clientWidth||0:0,S=d/2-E/2,O=h[g],f=y-p[c]-h[v],C=y/2-p[c]/2+S,m=_e(O,C,f),T=i;o.modifiersData[n]=(t={},t[T]=m,t.centerOffset=m-C,t)}}function nr(e){var t=e.state,o=e.options,n=o.element,r=n===void 0?"[data-popper-arrow]":n;r!=null&&(typeof r=="string"&&(r=t.elements.popper.querySelector(r),!r)||!vo(t.elements.popper,r)||(t.elements.arrow=r))}var rr={name:"arrow",enabled:!0,phase:"main",fn:or,effect:nr,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Be(e){return e.split("-")[1]}var ar={top:"auto",right:"auto",bottom:"auto",left:"auto"};function sr(e){var t=e.x,o=e.y,n=window,r=n.devicePixelRatio||1;return{x:ke(t*r)/r||0,y:ke(o*r)/r||0}}function It(e){var t,o=e.popper,n=e.popperRect,r=e.placement,s=e.variation,a=e.offsets,l=e.position,i=e.gpuAcceleration,u=e.adaptive,c=e.roundOffsets,h=e.isFixed,p=a.x,g=p===void 0?0:p,v=a.y,d=v===void 0?0:v,E=typeof c=="function"?c({x:g,y:d}):{x:g,y:d};g=E.x,d=E.y;var b=a.hasOwnProperty("x"),y=a.hasOwnProperty("y"),S=z,O=j,f=window;if(u){var C=We(o),m="clientHeight",T="clientWidth";if(C===Y(o)&&(C=ue(o),ne(C).position!=="static"&&l==="absolute"&&(m="scrollHeight",T="scrollWidth")),C=C,r===j||(r===z||r===K)&&s===Ne){O=F;var R=h&&C===f&&f.visualViewport?f.visualViewport.height:C[m];d-=R-n.height,d*=i?1:-1}if(r===z||(r===j||r===F)&&s===Ne){S=K;var M=h&&C===f&&f.visualViewport?f.visualViewport.width:C[T];g-=M-n.width,g*=i?1:-1}}var _=Object.assign({position:l},u&&ar),N=c===!0?sr({x:g,y:d}):{x:g,y:d};if(g=N.x,d=N.y,i){var $;return Object.assign({},_,($={},$[O]=y?"0":"",$[S]=b?"0":"",$.transform=(f.devicePixelRatio||1)<=1?"translate("+g+"px, "+d+"px)":"translate3d("+g+"px, "+d+"px, 0)",$))}return Object.assign({},_,(t={},t[O]=y?d+"px":"",t[S]=b?g+"px":"",t.transform="",t))}function ir(e){var t=e.state,o=e.options,n=o.gpuAcceleration,r=n===void 0?!0:n,s=o.adaptive,a=s===void 0?!0:s,l=o.roundOffsets,i=l===void 0?!0:l,u={placement:J(t.placement),variation:Be(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:r,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,It(Object.assign({},u,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:a,roundOffsets:i})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,It(Object.assign({},u,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:i})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}var bo={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:ir,data:{}},Ve={passive:!0};function lr(e){var t=e.state,o=e.instance,n=e.options,r=n.scroll,s=r===void 0?!0:r,a=n.resize,l=a===void 0?!0:a,i=Y(t.elements.popper),u=[].concat(t.scrollParents.reference,t.scrollParents.popper);return s&&u.forEach(function(c){c.addEventListener("scroll",o.update,Ve)}),l&&i.addEventListener("resize",o.update,Ve),function(){s&&u.forEach(function(c){c.removeEventListener("scroll",o.update,Ve)}),l&&i.removeEventListener("resize",o.update,Ve)}}var yo={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:lr,data:{}},ur={left:"right",right:"left",bottom:"top",top:"bottom"};function qe(e){return e.replace(/left|right|bottom|top/g,function(t){return ur[t]})}var cr={start:"end",end:"start"};function Nt(e){return e.replace(/start|end/g,function(t){return cr[t]})}function dt(e){var t=Y(e),o=t.pageXOffset,n=t.pageYOffset;return{scrollLeft:o,scrollTop:n}}function vt(e){return Re(ue(e)).left+dt(e).scrollLeft}function pr(e){var t=Y(e),o=ue(e),n=t.visualViewport,r=o.clientWidth,s=o.clientHeight,a=0,l=0;return n&&(r=n.width,s=n.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(a=n.offsetLeft,l=n.offsetTop)),{width:r,height:s,x:a+vt(e),y:l}}function fr(e){var t,o=ue(e),n=dt(e),r=(t=e.ownerDocument)==null?void 0:t.body,s=me(o.scrollWidth,o.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),a=me(o.scrollHeight,o.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0),l=-n.scrollLeft+vt(e),i=-n.scrollTop;return ne(r||o).direction==="rtl"&&(l+=me(o.clientWidth,r?r.clientWidth:0)-s),{width:s,height:a,x:l,y:i}}function mt(e){var t=ne(e),o=t.overflow,n=t.overflowX,r=t.overflowY;return/auto|scroll|overlay|hidden/.test(o+r+n)}function wo(e){return["html","body","#document"].indexOf(Z(e))>=0?e.ownerDocument.body:W(e)&&mt(e)?e:wo(Je(e))}function $e(e,t){var o;t===void 0&&(t=[]);var n=wo(e),r=n===((o=e.ownerDocument)==null?void 0:o.body),s=Y(n),a=r?[s].concat(s.visualViewport||[],mt(n)?n:[]):n,l=t.concat(a);return r?l:l.concat($e(Je(a)))}function rt(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function dr(e){var t=Re(e);return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function Dt(e,t){return t===po?rt(pr(e)):Ae(t)?dr(t):rt(fr(ue(e)))}function vr(e){var t=$e(Je(e)),o=["absolute","fixed"].indexOf(ne(e).position)>=0,n=o&&W(e)?We(e):e;return Ae(n)?t.filter(function(r){return Ae(r)&&vo(r,n)&&Z(r)!=="body"}):[]}function mr(e,t,o){var n=t==="clippingParents"?vr(e):[].concat(t),r=[].concat(n,[o]),s=r[0],a=r.reduce(function(l,i){var u=Dt(e,i);return l.top=me(u.top,l.top),l.right=Ye(u.right,l.right),l.bottom=Ye(u.bottom,l.bottom),l.left=me(u.left,l.left),l},Dt(e,s));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function Oo(e){var t=e.reference,o=e.element,n=e.placement,r=n?J(n):null,s=n?Be(n):null,a=t.x+t.width/2-o.width/2,l=t.y+t.height/2-o.height/2,i;switch(r){case j:i={x:a,y:t.y-o.height};break;case F:i={x:a,y:t.y+t.height};break;case K:i={x:t.x+t.width,y:l};break;case z:i={x:t.x-o.width,y:l};break;default:i={x:t.x,y:t.y}}var u=r?ft(r):null;if(u!=null){var c=u==="y"?"height":"width";switch(s){case Pe:i[u]=i[u]-(t[c]/2-o[c]/2);break;case Ne:i[u]=i[u]+(t[c]/2-o[c]/2);break}}return i}function De(e,t){t===void 0&&(t={});var o=t,n=o.placement,r=n===void 0?e.placement:n,s=o.boundary,a=s===void 0?Nn:s,l=o.rootBoundary,i=l===void 0?po:l,u=o.elementContext,c=u===void 0?xe:u,h=o.altBoundary,p=h===void 0?!1:h,g=o.padding,v=g===void 0?0:g,d=go(typeof v!="number"?v:ho(v,ze)),E=c===xe?Dn:xe,b=e.rects.popper,y=e.elements[p?E:c],S=mr(Ae(y)?y:y.contextElement||ue(e.elements.popper),a,i),O=Re(e.elements.reference),f=Oo({reference:O,element:b,strategy:"absolute",placement:r}),C=rt(Object.assign({},b,f)),m=c===xe?C:O,T={top:S.top-m.top+d.top,bottom:m.bottom-S.bottom+d.bottom,left:S.left-m.left+d.left,right:m.right-S.right+d.right},R=e.modifiersData.offset;if(c===xe&&R){var M=R[r];Object.keys(T).forEach(function(_){var N=[K,F].indexOf(_)>=0?1:-1,$=[j,F].indexOf(_)>=0?"y":"x";T[_]+=M[$]*N})}return T}function gr(e,t){t===void 0&&(t={});var o=t,n=o.placement,r=o.boundary,s=o.rootBoundary,a=o.padding,l=o.flipVariations,i=o.allowedAutoPlacements,u=i===void 0?ut:i,c=Be(n),h=c?l?$t:$t.filter(function(v){return Be(v)===c}):ze,p=h.filter(function(v){return u.indexOf(v)>=0});p.length===0&&(p=h);var g=p.reduce(function(v,d){return v[d]=De(e,{placement:d,boundary:r,rootBoundary:s,padding:a})[J(d)],v},{});return Object.keys(g).sort(function(v,d){return g[v]-g[d]})}function hr(e){if(J(e)===lt)return[];var t=qe(e);return[Nt(e),t,Nt(t)]}function br(e){var t=e.state,o=e.options,n=e.name;if(!t.modifiersData[n]._skip){for(var r=o.mainAxis,s=r===void 0?!0:r,a=o.altAxis,l=a===void 0?!0:a,i=o.fallbackPlacements,u=o.padding,c=o.boundary,h=o.rootBoundary,p=o.altBoundary,g=o.flipVariations,v=g===void 0?!0:g,d=o.allowedAutoPlacements,E=t.options.placement,b=J(E),y=b===E,S=i||(y||!v?[qe(E)]:hr(E)),O=[E].concat(S).reduce(function(pe,ee){return pe.concat(J(ee)===lt?gr(t,{placement:ee,boundary:c,rootBoundary:h,padding:u,flipVariations:v,allowedAutoPlacements:d}):ee)},[]),f=t.rects.reference,C=t.rects.popper,m=new Map,T=!0,R=O[0],M=0;M<O.length;M++){var _=O[M],N=J(_),$=Be(_)===Pe,P=[j,F].indexOf(N)>=0,k=P?"width":"height",B=De(t,{placement:_,boundary:c,rootBoundary:h,altBoundary:p,padding:u}),x=P?$?K:z:$?F:j;f[k]>C[k]&&(x=qe(x));var V=qe(x),q=[];if(s&&q.push(B[N]<=0),l&&q.push(B[x]<=0,B[V]<=0),q.every(function(pe){return pe})){R=_,T=!1;break}m.set(_,q)}if(T)for(var be=v?3:1,ye=function(pe){var ee=O.find(function(Fe){var Me=m.get(Fe);if(Me)return Me.slice(0,pe).every(function(we){return we})});if(ee)return R=ee,"break"},re=be;re>0;re--){var ce=ye(re);if(ce==="break")break}t.placement!==R&&(t.modifiersData[n]._skip=!0,t.placement=R,t.reset=!0)}}var yr={name:"flip",enabled:!0,phase:"main",fn:br,requiresIfExists:["offset"],data:{_skip:!1}};function jt(e,t,o){return o===void 0&&(o={x:0,y:0}),{top:e.top-t.height-o.y,right:e.right-t.width+o.x,bottom:e.bottom-t.height+o.y,left:e.left-t.width-o.x}}function zt(e){return[j,K,F,z].some(function(t){return e[t]>=0})}function wr(e){var t=e.state,o=e.name,n=t.rects.reference,r=t.rects.popper,s=t.modifiersData.preventOverflow,a=De(t,{elementContext:"reference"}),l=De(t,{altBoundary:!0}),i=jt(a,n),u=jt(l,r,s),c=zt(i),h=zt(u);t.modifiersData[o]={referenceClippingOffsets:i,popperEscapeOffsets:u,isReferenceHidden:c,hasPopperEscaped:h},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":c,"data-popper-escaped":h})}var Or={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:wr};function Er(e,t,o){var n=J(e),r=[z,j].indexOf(n)>=0?-1:1,s=typeof o=="function"?o(Object.assign({},t,{placement:e})):o,a=s[0],l=s[1];return a=a||0,l=(l||0)*r,[z,K].indexOf(n)>=0?{x:l,y:a}:{x:a,y:l}}function Cr(e){var t=e.state,o=e.options,n=e.name,r=o.offset,s=r===void 0?[0,0]:r,a=ut.reduce(function(c,h){return c[h]=Er(h,t.rects,s),c},{}),l=a[t.placement],i=l.x,u=l.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=i,t.modifiersData.popperOffsets.y+=u),t.modifiersData[n]=a}var Tr={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Cr};function Sr(e){var t=e.state,o=e.name;t.modifiersData[o]=Oo({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})}var Eo={name:"popperOffsets",enabled:!0,phase:"read",fn:Sr,data:{}};function Pr(e){return e==="x"?"y":"x"}function Ar(e){var t=e.state,o=e.options,n=e.name,r=o.mainAxis,s=r===void 0?!0:r,a=o.altAxis,l=a===void 0?!1:a,i=o.boundary,u=o.rootBoundary,c=o.altBoundary,h=o.padding,p=o.tether,g=p===void 0?!0:p,v=o.tetherOffset,d=v===void 0?0:v,E=De(t,{boundary:i,rootBoundary:u,padding:h,altBoundary:c}),b=J(t.placement),y=Be(t.placement),S=!y,O=ft(b),f=Pr(O),C=t.modifiersData.popperOffsets,m=t.rects.reference,T=t.rects.popper,R=typeof d=="function"?d(Object.assign({},t.rects,{placement:t.placement})):d,M=typeof R=="number"?{mainAxis:R,altAxis:R}:Object.assign({mainAxis:0,altAxis:0},R),_=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,N={x:0,y:0};if(C){if(s){var $,P=O==="y"?j:z,k=O==="y"?F:K,B=O==="y"?"height":"width",x=C[O],V=x+E[P],q=x-E[k],be=g?-T[B]/2:0,ye=y===Pe?m[B]:T[B],re=y===Pe?-T[B]:-m[B],ce=t.elements.arrow,pe=g&&ce?pt(ce):{width:0,height:0},ee=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:mo(),Fe=ee[P],Me=ee[k],we=_e(0,m[B],pe[B]),Po=S?m[B]/2-be-we-Fe-M.mainAxis:ye-we-Fe-M.mainAxis,Ao=S?-m[B]/2+be+we+Me+M.mainAxis:re+we+Me+M.mainAxis,Ze=t.elements.arrow&&We(t.elements.arrow),ko=Ze?O==="y"?Ze.clientTop||0:Ze.clientLeft||0:0,bt=($=_==null?void 0:_[O])!=null?$:0,Ro=x+Po-bt-ko,Bo=x+Ao-bt,yt=_e(g?Ye(V,Ro):V,x,g?me(q,Bo):q);C[O]=yt,N[O]=yt-x}if(l){var wt,Mo=O==="x"?j:z,xo=O==="x"?F:K,fe=C[f],Ke=f==="y"?"height":"width",Ot=fe+E[Mo],Et=fe-E[xo],Qe=[j,z].indexOf(b)!==-1,Ct=(wt=_==null?void 0:_[f])!=null?wt:0,Tt=Qe?Ot:fe-m[Ke]-T[Ke]-Ct+M.altAxis,St=Qe?fe+m[Ke]+T[Ke]-Ct-M.altAxis:Et,Pt=g&&Qe?er(Tt,fe,St):_e(g?Tt:Ot,fe,g?St:Et);C[f]=Pt,N[f]=Pt-fe}t.modifiersData[n]=N}}var kr={name:"preventOverflow",enabled:!0,phase:"main",fn:Ar,requiresIfExists:["offset"]};function Rr(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function Br(e){return e===Y(e)||!W(e)?dt(e):Rr(e)}function Mr(e){var t=e.getBoundingClientRect(),o=ke(t.width)/e.offsetWidth||1,n=ke(t.height)/e.offsetHeight||1;return o!==1||n!==1}function xr(e,t,o){o===void 0&&(o=!1);var n=W(t),r=W(t)&&Mr(t),s=ue(t),a=Re(e,r),l={scrollLeft:0,scrollTop:0},i={x:0,y:0};return(n||!n&&!o)&&((Z(t)!=="body"||mt(s))&&(l=Br(t)),W(t)?(i=Re(t,!0),i.x+=t.clientLeft,i.y+=t.clientTop):s&&(i.x=vt(s))),{x:a.left+l.scrollLeft-i.x,y:a.top+l.scrollTop-i.y,width:a.width,height:a.height}}function Lr(e){var t=new Map,o=new Set,n=[];e.forEach(function(s){t.set(s.name,s)});function r(s){o.add(s.name);var a=[].concat(s.requires||[],s.requiresIfExists||[]);a.forEach(function(l){if(!o.has(l)){var i=t.get(l);i&&r(i)}}),n.push(s)}return e.forEach(function(s){o.has(s.name)||r(s)}),n}function _r(e){var t=Lr(e);return Yn.reduce(function(o,n){return o.concat(t.filter(function(r){return r.phase===n}))},[])}function $r(e){var t;return function(){return t||(t=new Promise(function(o){Promise.resolve().then(function(){t=void 0,o(e())})})),t}}function Hr(e){var t=e.reduce(function(o,n){var r=o[n.name];return o[n.name]=r?Object.assign({},r,n,{options:Object.assign({},r.options,n.options),data:Object.assign({},r.data,n.data)}):n,o},{});return Object.keys(t).map(function(o){return t[o]})}var Wt={placement:"bottom",modifiers:[],strategy:"absolute"};function Ft(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return!t.some(function(n){return!(n&&typeof n.getBoundingClientRect=="function")})}function gt(e){e===void 0&&(e={});var t=e,o=t.defaultModifiers,n=o===void 0?[]:o,r=t.defaultOptions,s=r===void 0?Wt:r;return function(a,l,i){i===void 0&&(i=s);var u={placement:"bottom",orderedModifiers:[],options:Object.assign({},Wt,s),modifiersData:{},elements:{reference:a,popper:l},attributes:{},styles:{}},c=[],h=!1,p={state:u,setOptions:function(d){var E=typeof d=="function"?d(u.options):d;v(),u.options=Object.assign({},s,u.options,E),u.scrollParents={reference:Ae(a)?$e(a):a.contextElement?$e(a.contextElement):[],popper:$e(l)};var b=_r(Hr([].concat(n,u.options.modifiers)));return u.orderedModifiers=b.filter(function(y){return y.enabled}),g(),p.update()},forceUpdate:function(){if(!h){var d=u.elements,E=d.reference,b=d.popper;if(Ft(E,b)){u.rects={reference:xr(E,We(b),u.options.strategy==="fixed"),popper:pt(b)},u.reset=!1,u.placement=u.options.placement,u.orderedModifiers.forEach(function(T){return u.modifiersData[T.name]=Object.assign({},T.data)});for(var y=0;y<u.orderedModifiers.length;y++){if(u.reset===!0){u.reset=!1,y=-1;continue}var S=u.orderedModifiers[y],O=S.fn,f=S.options,C=f===void 0?{}:f,m=S.name;typeof O=="function"&&(u=O({state:u,options:C,name:m,instance:p})||u)}}}},update:$r(function(){return new Promise(function(d){p.forceUpdate(),d(u)})}),destroy:function(){v(),h=!0}};if(!Ft(a,l))return p;p.setOptions(i).then(function(d){!h&&i.onFirstUpdate&&i.onFirstUpdate(d)});function g(){u.orderedModifiers.forEach(function(d){var E=d.name,b=d.options,y=b===void 0?{}:b,S=d.effect;if(typeof S=="function"){var O=S({state:u,name:E,instance:p,options:y}),f=function(){};c.push(O||f)}})}function v(){c.forEach(function(d){return d()}),c=[]}return p}}gt();var Ir=[yo,Eo,bo,fo];gt({defaultModifiers:Ir});var Nr=[yo,Eo,bo,fo,Tr,yr,kr,rr,Or],Dr=gt({defaultModifiers:Nr});const jr=["fixed","absolute"],zr=U({boundariesPadding:{type:Number,default:0},fallbackPlacements:{type:H(Array),default:void 0},gpuAcceleration:{type:Boolean,default:!0},offset:{type:Number,default:12},placement:{type:String,values:ut,default:"bottom"},popperOptions:{type:H(Object),default:()=>({})},strategy:{type:String,values:jr,default:"absolute"}}),Co=U({...zr,id:String,style:{type:H([String,Array,Object])},className:{type:H([String,Array,Object])},effect:{type:String,default:"dark"},visible:Boolean,enterable:{type:Boolean,default:!0},pure:Boolean,focusOnShow:{type:Boolean,default:!1},trapping:{type:Boolean,default:!1},popperClass:{type:H([String,Array,Object])},popperStyle:{type:H([String,Array,Object])},referenceEl:{type:H(Object)},triggerTargetEl:{type:H(Object)},stopPopperMouseEvent:{type:Boolean,default:!0},ariaLabel:{type:String,default:void 0},virtualTriggering:Boolean,zIndex:Number}),Wr=["mouseenter","mouseleave","focus","blur","close"],Kt=(e,t)=>{const{placement:o,strategy:n,popperOptions:r}=e,s={placement:o,strategy:n,...r,modifiers:Kr(e)};return Ur(s,t),Vr(s,r==null?void 0:r.modifiers),s},Fr=e=>{if(de)return Zt(e)};function Kr(e){const{offset:t,gpuAcceleration:o,fallbackPlacements:n}=e;return[{name:"offset",options:{offset:[0,t??12]}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5,fallbackPlacements:n}},{name:"computeStyles",options:{gpuAcceleration:o,adaptive:o}}]}function Ur(e,{arrowEl:t,arrowOffset:o}){e.modifiers.push({name:"arrow",options:{element:t,padding:o??5}})}function Vr(e,t){t&&(e.modifiers=[...e.modifiers,...t??[]])}const qr={name:"ElPopperContent"},Xr=X({...qr,props:Co,emits:Wr,setup(e,{expose:t,emit:o}){const n=e,{popperInstanceRef:r,contentRef:s,triggerRef:a,role:l}=ie(it,void 0),i=ie(Rt,void 0),{nextZIndex:u}=Uo(),c=he("popper"),h=A(),p=A("first"),g=A(),v=A();Te(oo,{arrowRef:g,arrowOffset:v}),i&&(i.addInputId||i.removeInputId)&&Te(Rt,{...i,addInputId:nt,removeInputId:nt});const d=A(n.zIndex||u()),E=A(!1);let b;const y=L(()=>Fr(n.referenceEl)||w(a)),S=L(()=>[{zIndex:w(d)},n.popperStyle]),O=L(()=>[c.b(),c.is("pure",n.pure),c.is(n.effect),n.popperClass]),f=L(()=>l&&l.value==="dialog"?"false":void 0),C=({referenceEl:P,popperContentEl:k,arrowEl:B})=>{const x=Kt(n,{arrowEl:B,arrowOffset:w(v)});return Dr(P,k,x)},m=(P=!0)=>{var k;(k=w(r))==null||k.update(),P&&(d.value=n.zIndex||u())},T=()=>{var P,k;const B={name:"eventListeners",enabled:n.visible};(k=(P=w(r))==null?void 0:P.setOptions)==null||k.call(P,x=>({...x,modifiers:[...x.modifiers||[],B]})),m(!1),n.visible&&n.focusOnShow?E.value=!0:n.visible===!1&&(E.value=!1)},R=()=>{o("focus")},M=()=>{p.value="first",o("blur")},_=P=>{var k;n.visible&&!E.value&&(P.target&&(p.value=P.target),E.value=!0,P.relatedTarget&&((k=P.relatedTarget)==null||k.focus()))},N=()=>{n.trapping||(E.value=!1)},$=()=>{E.value=!1,o("close")};return Ge(()=>{let P;I(y,k=>{var B;P==null||P();const x=w(r);if((B=x==null?void 0:x.destroy)==null||B.call(x),k){const V=w(h);s.value=V,r.value=C({referenceEl:k,popperContentEl:V,arrowEl:w(g)}),P=I(()=>k.getBoundingClientRect(),()=>m(),{immediate:!0})}else r.value=void 0},{immediate:!0}),I(()=>n.triggerTargetEl,(k,B)=>{b==null||b(),b=void 0;const x=w(k||h.value),V=w(B||h.value);if(Ie(x)){const{ariaLabel:q,id:be}=Vo(n);b=I([l,q,f,be],ye=>{["role","aria-label","aria-modal","id"].forEach((re,ce)=>{eo(ye[ce])?x.removeAttribute(re):x.setAttribute(re,ye[ce])})},{immediate:!0})}V!==x&&Ie(V)&&["role","aria-label","aria-modal","id"].forEach(q=>{V.removeAttribute(q)})},{immediate:!0}),I(()=>n.visible,T,{immediate:!0}),I(()=>Kt(n,{arrowEl:w(g),arrowOffset:w(v)}),k=>{var B;return(B=r.value)==null?void 0:B.setOptions(k)})}),je(()=>{b==null||b(),b=void 0}),t({popperContentRef:h,popperInstanceRef:r,updatePopper:m,contentStyle:S}),(P,k)=>(D(),Se("div",{ref_key:"popperContentRef",ref:h,style:Xe(w(S)),class:se(w(O)),tabindex:"-1",onMouseenter:k[0]||(k[0]=B=>P.$emit("mouseenter",B)),onMouseleave:k[1]||(k[1]=B=>P.$emit("mouseleave",B))},[ge(w(en),{trapped:E.value,"trap-on-focus-in":!0,"focus-trap-el":h.value,"focus-start-el":p.value,onFocusAfterTrapped:R,onFocusAfterReleased:M,onFocusin:_,onFocusoutPrevented:N,onReleaseRequested:$},{default:G(()=>[le(P.$slots,"default")]),_:3},8,["trapped","focus-trap-el","focus-start-el"])],38))}});var Yr=Q(Xr,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/popper/src/content.vue"]]);const Gr=st(Rn),Jr=he("tooltip"),To=U({...cn,...Co,appendTo:{type:H([String,Object]),default:ao},content:{type:String,default:""},rawContent:{type:Boolean,default:!1},persistent:Boolean,ariaLabel:String,visible:{type:H(Boolean),default:null},transition:{type:String,default:`${Jr.namespace.value}-fade-in-linear`},teleported:{type:Boolean,default:!0},disabled:{type:Boolean}}),So=U({...co,disabled:Boolean,trigger:{type:H([String,Array]),default:"hover"},triggerKeys:{type:H(Array),default:()=>[Bt.enter,Bt.space]}}),Zr=U({openDelay:{type:Number},visibleArrow:{type:Boolean,default:void 0},hideAfter:{type:Number,default:200},showArrow:{type:Boolean,default:!0}}),ht=Symbol("elTooltip"),Qr=X({name:"ElTooltipContent",components:{ElPopperContent:Yr},inheritAttrs:!1,props:To,setup(e){const t=A(null),o=A(!1),n=A(!1),r=A(!1),s=A(!1),{controlled:a,id:l,open:i,trigger:u,onClose:c,onOpen:h,onShow:p,onHide:g,onBeforeShow:v,onBeforeHide:d}=ie(ht,void 0),E=L(()=>e.persistent);je(()=>{s.value=!0});const b=L(()=>w(E)?!0:w(i)),y=L(()=>e.disabled?!1:w(i)),S=L(()=>{var P;return(P=e.style)!=null?P:{}}),O=L(()=>!w(i)),f=()=>{g()},C=()=>{if(w(a))return!0},m=te(C,()=>{e.enterable&&w(u)==="hover"&&h()}),T=te(C,()=>{w(u)==="hover"&&c()}),R=()=>{var P,k;(k=(P=t.value)==null?void 0:P.updatePopper)==null||k.call(P),v==null||v()},M=()=>{d==null||d()},_=()=>{p(),$=Xo(L(()=>{var P;return(P=t.value)==null?void 0:P.popperContentRef}),()=>{if(w(a))return;w(u)!=="hover"&&c()})},N=()=>{e.virtualTriggering||c()};let $;return I(()=>w(i),P=>{P||$==null||$()},{flush:"post"}),{ariaHidden:O,entering:n,leaving:r,id:l,intermediateOpen:o,contentStyle:S,contentRef:t,destroyed:s,shouldRender:b,shouldShow:y,onClose:c,open:i,onAfterShow:_,onBeforeEnter:R,onBeforeLeave:M,onContentEnter:m,onContentLeave:T,onTransitionLeave:f,onBlur:N}}});function ea(e,t,o,n,r,s){const a=Ee("el-popper-content");return D(),oe(qo,{disabled:!e.teleported,to:e.appendTo},[ge(Yt,{name:e.transition,onAfterLeave:e.onTransitionLeave,onBeforeEnter:e.onBeforeEnter,onAfterEnter:e.onAfterShow,onBeforeLeave:e.onBeforeLeave},{default:G(()=>[e.shouldRender?at((D(),oe(a,Qt({key:0,id:e.id,ref:"contentRef"},e.$attrs,{"aria-label":e.ariaLabel,"aria-hidden":e.ariaHidden,"boundaries-padding":e.boundariesPadding,"fallback-placements":e.fallbackPlacements,"gpu-acceleration":e.gpuAcceleration,offset:e.offset,placement:e.placement,"popper-options":e.popperOptions,strategy:e.strategy,effect:e.effect,enterable:e.enterable,pure:e.pure,"popper-class":e.popperClass,"popper-style":[e.popperStyle,e.contentStyle],"reference-el":e.referenceEl,"trigger-target-el":e.triggerTargetEl,visible:e.shouldShow,"z-index":e.zIndex,onMouseenter:e.onContentEnter,onMouseleave:e.onContentLeave,onBlur:e.onBlur,onClose:e.onClose}),{default:G(()=>[ve(" Workaround bug #6378 "),e.destroyed?ve("v-if",!0):le(e.$slots,"default",{key:0})]),_:3},16,["id","aria-label","aria-hidden","boundaries-padding","fallback-placements","gpu-acceleration","offset","placement","popper-options","strategy","effect","enterable","pure","popper-class","popper-style","reference-el","trigger-target-el","visible","z-index","onMouseenter","onMouseleave","onBlur","onClose"])),[[Xt,e.shouldShow]]):ve("v-if",!0)]),_:3},8,["name","onAfterLeave","onBeforeEnter","onAfterEnter","onBeforeLeave"])],8,["disabled","to"])}var ta=Q(Qr,[["render",ea],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/tooltip/src/content.vue"]]);const oa=(e,t)=>Yo(e)?e.includes(t):e===t,Oe=(e,t,o)=>n=>{oa(w(e),t)&&o(n)},na=X({name:"ElTooltipTrigger",components:{ElPopperTrigger:In},props:So,setup(e){const t=he("tooltip"),{controlled:o,id:n,open:r,onOpen:s,onClose:a,onToggle:l}=ie(ht,void 0),i=A(null),u=()=>{if(w(o)||e.disabled)return!0},c=He(e,"trigger"),h=te(u,Oe(c,"hover",s)),p=te(u,Oe(c,"hover",a)),g=te(u,Oe(c,"click",y=>{y.button===0&&l(y)})),v=te(u,Oe(c,"focus",s)),d=te(u,Oe(c,"focus",a)),E=te(u,Oe(c,"contextmenu",y=>{y.preventDefault(),l(y)})),b=te(u,y=>{const{code:S}=y;e.triggerKeys.includes(S)&&(y.preventDefault(),l(y))});return{onBlur:d,onContextMenu:E,onFocus:v,onMouseenter:h,onMouseleave:p,onClick:g,onKeydown:b,open:r,id:n,triggerRef:i,ns:t}}});function ra(e,t,o,n,r,s){const a=Ee("el-popper-trigger");return D(),oe(a,{id:e.id,"virtual-ref":e.virtualRef,open:e.open,"virtual-triggering":e.virtualTriggering,class:se(e.ns.e("trigger")),onBlur:e.onBlur,onClick:e.onClick,onContextmenu:e.onContextMenu,onFocus:e.onFocus,onMouseenter:e.onMouseenter,onMouseleave:e.onMouseleave,onKeydown:e.onKeydown},{default:G(()=>[le(e.$slots,"default")]),_:3},8,["id","virtual-ref","open","virtual-triggering","class","onBlur","onClick","onContextmenu","onFocus","onMouseenter","onMouseleave","onKeydown"])}var aa=Q(na,[["render",ra],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/tooltip/src/trigger.vue"]]);const{useModelToggleProps:sa,useModelToggle:ia,useModelToggleEmits:la}=no("visible"),ua=X({name:"ElTooltip",components:{ElPopper:Gr,ElPopperArrow:xn,ElTooltipContent:ta,ElTooltipTrigger:aa},props:{...io,...sa,...To,...So,...lo,...Zr},emits:[...la,"before-show","before-hide","show","hide","open","close"],setup(e,{emit:t}){un();const o=L(()=>(Mt(e.openDelay),e.openDelay||e.showAfter)),n=L(()=>(Mt(e.visibleArrow),et(e.visibleArrow)?e.visibleArrow:e.showArrow)),r=tn(),s=A(null),a=A(null),l=()=>{var b;const y=w(s);y&&((b=y.popperInstanceRef)==null||b.update())},i=A(!1),u=A(void 0),{show:c,hide:h,hasUpdateHandler:p}=ia({indicator:i,toggleReason:u}),{onOpen:g,onClose:v}=pn({showAfter:o,hideAfter:He(e,"hideAfter"),open:c,close:h}),d=L(()=>et(e.visible)&&!p.value);Te(ht,{controlled:d,id:r,open:Go(i),trigger:He(e,"trigger"),onOpen:b=>{g(b)},onClose:b=>{v(b)},onToggle:b=>{w(i)?v(b):g(b)},onShow:()=>{t("show",u.value)},onHide:()=>{t("hide",u.value)},onBeforeShow:()=>{t("before-show",u.value)},onBeforeHide:()=>{t("before-hide",u.value)},updatePopper:l}),I(()=>e.disabled,b=>{b&&i.value&&(i.value=!1)});const E=()=>{var b,y;const S=(y=(b=a.value)==null?void 0:b.contentRef)==null?void 0:y.popperContentRef;return S&&S.contains(document.activeElement)};return Jo(()=>i.value&&h()),{compatShowAfter:o,compatShowArrow:n,popperRef:s,contentRef:a,open:i,hide:h,isFocusInsideContent:E,updatePopper:l,onOpen:g,onClose:v}}}),ca=["innerHTML"],pa={key:1};function fa(e,t,o,n,r,s){const a=Ee("el-tooltip-trigger"),l=Ee("el-popper-arrow"),i=Ee("el-tooltip-content"),u=Ee("el-popper");return D(),oe(u,{ref:"popperRef",role:e.role},{default:G(()=>[ge(a,{disabled:e.disabled,trigger:e.trigger,"trigger-keys":e.triggerKeys,"virtual-ref":e.virtualRef,"virtual-triggering":e.virtualTriggering},{default:G(()=>[e.$slots.default?le(e.$slots,"default",{key:0}):ve("v-if",!0)]),_:3},8,["disabled","trigger","trigger-keys","virtual-ref","virtual-triggering"]),ge(i,{ref:"contentRef","aria-label":e.ariaLabel,"boundaries-padding":e.boundariesPadding,content:e.content,disabled:e.disabled,effect:e.effect,enterable:e.enterable,"fallback-placements":e.fallbackPlacements,"hide-after":e.hideAfter,"gpu-acceleration":e.gpuAcceleration,offset:e.offset,persistent:e.persistent,"popper-class":e.popperClass,"popper-style":e.popperStyle,placement:e.placement,"popper-options":e.popperOptions,pure:e.pure,"raw-content":e.rawContent,"reference-el":e.referenceEl,"trigger-target-el":e.triggerTargetEl,"show-after":e.compatShowAfter,strategy:e.strategy,teleported:e.teleported,transition:e.transition,"virtual-triggering":e.virtualTriggering,"z-index":e.zIndex,"append-to":e.appendTo},{default:G(()=>[le(e.$slots,"content",{},()=>[e.rawContent?(D(),Se("span",{key:0,innerHTML:e.content},null,8,ca)):(D(),Se("span",pa,Zo(e.content),1))]),e.compatShowArrow?(D(),oe(l,{key:0,"arrow-offset":e.arrowOffset},null,8,["arrow-offset"])):ve("v-if",!0)]),_:3},8,["aria-label","boundaries-padding","content","disabled","effect","enterable","fallback-placements","hide-after","gpu-acceleration","offset","persistent","popper-class","popper-style","placement","popper-options","pure","raw-content","reference-el","trigger-target-el","show-after","strategy","teleported","transition","virtual-triggering","z-index","append-to"])]),_:3},8,["role"])}var da=Q(ua,[["render",fa],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/tooltip/src/tooltip.vue"]]);const Oa=st(da),ae=new Map;let Ut;de&&(document.addEventListener("mousedown",e=>Ut=e),document.addEventListener("mouseup",e=>{for(const t of ae.values())for(const{documentHandler:o}of t)o(e,Ut)}));function Vt(e,t){let o=[];return Array.isArray(t.arg)?o=t.arg:Ie(t.arg)&&o.push(t.arg),function(n,r){const s=t.instance.popperRef,a=n.target,l=r==null?void 0:r.target,i=!t||!t.instance,u=!a||!l,c=e.contains(a)||e.contains(l),h=e===a,p=o.length&&o.some(v=>v==null?void 0:v.contains(a))||o.length&&o.includes(l),g=s&&(s.contains(a)||s.contains(l));i||u||c||h||p||g||t.value(n,r)}}const Ea={beforeMount(e,t){ae.has(e)||ae.set(e,[]),ae.get(e).push({documentHandler:Vt(e,t),bindingFn:t.value})},updated(e,t){ae.has(e)||ae.set(e,[]);const o=ae.get(e),n=o.findIndex(s=>s.bindingFn===t.oldValue),r={documentHandler:Vt(e,t),bindingFn:t.value};n>=0?o.splice(n,1,r):o.push(r)},unmounted(e){ae.delete(e)}};export{Ea as C,Oa as E,_n as O,ht as T,ya as a,ut as b,te as c,So as d,wa as e,on as g,To as u,ba as w,Dr as y};
