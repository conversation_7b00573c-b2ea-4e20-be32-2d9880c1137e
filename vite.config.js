/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-07-24 09:14:24
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-24 08:13:07
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\vite.config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineConfig } from 'vite' // 动态配置函数
// import { createVuePlugin } from 'vite-plugin-vue2'
import WindiCSS from 'vite-plugin-windicss'
// 引入vitejs
import vue from '@vitejs/plugin-vue2';
import { VueMcp } from "vite-plugin-vue-mcp";

export default () =>
	defineConfig({
		plugins: [WindiCSS(), vue(), VueMcp()],
		server: {
			host: "0.0.0.0",
			open: true, //自动打开浏览器
			port: 8080, //端口号
			proxy:{
				'/api': {
					target: 'http://*************:8086',
					changeOrigin: true,
					// rewrite: (path) => path.replace(/^\/api/, '')
					// pathRewrite: {
					// 	'^/api': '/api/v1'
					// }
				},
			}
		},
		resolve: {
            // 别名
			alias: [
				{
					find: '@',
					replacement: '/src'
				}
			]
		}
	})

