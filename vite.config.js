/*
 * @Author: flyknit <EMAIL>
 * @Date: 2023-12-02 13:54:13
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-05-20 07:57:29
 * @FilePath: \electronic-filed:\gitee\backend-demo\vite.config.js
 * @Description:
 *
 * Copyright (c) 2023 by ${<EMAIL>}, All Rights Reserved.
 */
import { defineConfig } from "vite"; // 动态配置函数
import vue from "@vitejs/plugin-vue2";

export default () =>
  defineConfig({
    base: "./", // 设置打包路径
    plugins: [vue()],
    server: {
      host: "*************",
      open: true, //自动打开浏览器
      port: 9875, //端口号
      // proxy: {
      //   "/api": {
      //     target: "http://*************:8099",
      //     changeOrigin: true,
      //     rewrite: (path) => path.replace(/^\/api/, ""),
      //   },
      // },
    },
    resolve: {
      // 别名
      alias: [
        {
          find: "@",
          replacement: "/src",
        },
      ],
    },
    build: {
      minify: "terser", // 必须启用：terserOptions配置才会有效
      terserOptions: {
        compress: {
          // 生产环境时移除console.log调试代码
          drop_console: true,
          drop_debugger: true,
        },
      },
    },
  });
