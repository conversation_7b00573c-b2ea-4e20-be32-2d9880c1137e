import{k as x,av as k,ac as H,aN as $,_ as q,C as W,i as G,a9 as J,a8 as b,n as v,S as Y,G as A,aS as z}from"./index-444b28c3.js";import{i as Q}from"./event-fe80fd0c.js";let l=[];const I=e=>{const n=e;n.key===$.esc&&l.forEach(s=>s(n))},X=e=>{x(()=>{l.length===0&&document.addEventListener("keydown",I),k&&l.push(e)}),H(()=>{l=l.filter(n=>n!==e),l.length===0&&k&&document.removeEventListener("keydown",I)})},B=e=>{const n=[],s=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const c=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||c?NodeFilter.FILTER_SKIP:o.tabIndex>=0||o===document.activeElement?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;s.nextNode();)n.push(s.currentNode);return n},P=(e,n)=>{for(const s of e)if(!Z(s,n))return s},Z=(e,n)=>{if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(n&&e===n)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1},ee=e=>{const n=B(e),s=P(n,e),o=P(n.reverse(),e);return[s,o]},te=e=>e instanceof HTMLInputElement&&"select"in e,d=(e,n)=>{if(e&&e.focus){const s=document.activeElement;e.focus({preventScroll:!0}),e!==s&&te(e)&&n&&e.select()}};function w(e,n){const s=[...e],o=e.indexOf(n);return o!==-1&&s.splice(o,1),s}const ne=()=>{let e=[];return{push:o=>{const c=e[0];c&&o!==c&&c.pause(),e=w(e,o),e.unshift(o)},remove:o=>{var c,u;e=w(e,o),(u=(c=e[0])==null?void 0:c.resume)==null||u.call(c)}}},se=(e,n=!1)=>{const s=document.activeElement;for(const o of e)if(d(o,n),document.activeElement!==s)return},K=ne(),S="focus-trap.focus-after-trapped",y="focus-trap.focus-after-released",R={cancelable:!0,bubbles:!1},D="focusAfterTrapped",U="focusAfterReleased",oe=Symbol("elFocusTrap"),re=W({name:"ElFocusTrap",inheritAttrs:!1,props:{loop:Boolean,trapped:Boolean,focusTrapEl:Object,focusStartEl:{type:[Object,String],default:"first"}},emits:[D,U,"focusin","focusout","focusout-prevented","release-requested"],setup(e,{emit:n}){const s=G();let o,c;X(t=>{e.trapped&&!u.paused&&n("release-requested",t)});const u={paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}},p=t=>{if(!e.loop&&!e.trapped||u.paused)return;const{key:r,altKey:a,ctrlKey:i,metaKey:f,currentTarget:M,shiftKey:O}=t,{loop:N}=e,j=r===$.tab&&!a&&!i&&!f,E=document.activeElement;if(j&&E){const T=M,[F,_]=ee(T);F&&_?!O&&E===_?(t.preventDefault(),N&&d(F,!0),n("focusout-prevented")):O&&[F,T].includes(E)&&(t.preventDefault(),N&&d(_,!0),n("focusout-prevented")):E===T&&(t.preventDefault(),n("focusout-prevented"))}};J(oe,{focusTrapRef:s,onKeydown:p}),b(()=>e.focusTrapEl,t=>{t&&(s.value=t)},{immediate:!0}),b([s],([t],[r])=>{t&&(t.addEventListener("keydown",p),t.addEventListener("focusin",h),t.addEventListener("focusout",g)),r&&(r.removeEventListener("keydown",p),r.removeEventListener("focusin",h),r.removeEventListener("focusout",g))});const m=t=>{n(D,t)},V=t=>n(U,t),h=t=>{const r=v(s);if(!r)return;const a=t.target,i=a&&r.contains(a);i&&n("focusin",t),!u.paused&&e.trapped&&(i?c=a:d(c,!0))},g=t=>{const r=v(s);if(!(u.paused||!r))if(e.trapped){const a=t.relatedTarget;!Q(a)&&!r.contains(a)&&setTimeout(()=>{!u.paused&&e.trapped&&d(c,!0)},0)}else{const a=t.target;a&&r.contains(a)||n("focusout",t)}};async function C(){await A();const t=v(s);if(t){K.push(u);const r=document.activeElement;if(o=r,!t.contains(r)){const i=new Event(S,R);t.addEventListener(S,m),t.dispatchEvent(i),i.defaultPrevented||A(()=>{let f=e.focusStartEl;z(f)||(d(f),document.activeElement!==f&&(f="first")),f==="first"&&se(B(t),!0),(document.activeElement===r||f==="container")&&d(t)})}}}function L(){const t=v(s);if(t){t.removeEventListener(S,m);const r=new Event(y,R);t.addEventListener(y,V),t.dispatchEvent(r),r.defaultPrevented||d(o??document.body,!0),t.removeEventListener(y,m),K.remove(u)}}return x(()=>{e.trapped&&C(),b(()=>e.trapped,t=>{t?C():L()})}),H(()=>{e.trapped&&L()}),{onKeydown:p}}});function ce(e,n,s,o,c,u){return Y(e.$slots,"default",{handleKeydown:e.onKeydown})}var fe=q(re,[["render",ce],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/focus-trap/src/focus-trap.vue"]]);export{fe as E,oe as F};
