<!--
 * @Author: flyknit <EMAIL>
 * @Date: 2023-12-09 10:22:51
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-07-13 17:00:31
 * @FilePath: \electronic-filed:\gitee\backend-demo\src\views\tasty\Tasty.vue
 * @Description: 
 * 
 * Copyright (c) 2023 by ${<EMAIL>}, All Rights Reserved. 
-->
<template>
  <div>
    <!-- 卡片式图 -->
    <el-card class="card-list" shadow="always">
      <el-form inline ref="form" class="form" size="medium" style="display: flex; flex-wrap: wrap">
        <el-form-item label="日期">
          <el-date-picker v-model="dates" type="datetimerange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="车号/Model">
          <el-input placeholder="车号、Model" v-model="keyword" clearable @clear="clearHandler">
            <!-- <el-button slot="append" icon="el-icon-search" @click="searchByKeywords"></el-button> -->
          </el-input>
        </el-form-item>
        <el-form-item label="类型筛选">
          <el-select v-model="value" placeholder="请选择类型" clearable>
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-button icon="el-icon-search" @click="searchByTime" type="primary">查询</el-button>
          <!-- 重置按钮 -->
          <el-button icon="el-icon-refresh" @click="clearHandler" type="info">重置</el-button>
          <!-- <el-button
            type="success"
            @click="exportExcel"
            icon="el-icon-notebook-2"
          >
            导出到Excel
          </el-button> -->
        </el-form-item>
        <!-- <el-form-item>
          <el-button type="danger" icon="el-icon-picture-outline-round">
            异常机台
          </el-button>
        </el-form-item> -->
      </el-form>
      <!-- 订单列表区域 -->
      <el-table ref="orderTable" :data="orderLists" resizable stripe style="width: 100%; font-size: 0.85rem"
        v-loading="loading" element-loading-text="Flyknit..." @row-click="orderRowClickHandler">
        <el-table-column type="selection" align="center"> </el-table-column>
        <el-table-column label="布票号" fixed show-overflow-tooltip prop="serial"></el-table-column>
        <el-table-column label="机台号" prop="machine_number"></el-table-column>
        <el-table-column label="Model" prop="model_name" show-overflow-tooltip></el-table-column>
        <el-table-column label="尺码" prop="size"></el-table-column>
        <el-table-column label="品名" prop="pm"></el-table-column>
        <el-table-column label="测量参数" prop="measure_params" show-overflow-tooltip>
          <template slot-scope="scope">
            <span style="margin: 0 10px" v-for="(item, index) in scope.row.measure_params" :key="index" :span="24">{{
              item.name }}:{{ item.num }}</span>
          </template>
        </el-table-column>
        <el-table-column label="调机参数" show-overflow-tooltip prop="change_params">
          <template slot-scope="scope">
            <span style="margin: 0 10px" v-for="(item, index) in scope.row.change_params" :key="index" :span="24">{{
              item.name }}:{{ item.num }}</span>
          </template>
        </el-table-column>
        <el-table-column label="长短脚" prop="is_long_short">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.is_long_short == 0" type="success" effect="dark" size="mini">否</el-tag>
            <div v-else style="display: flex; flex-direction: column">
              <el-tag type="danger" effect="dark" size="mini">是</el-tag>
              <h5>{{ scope.row.long_short_methods }}</h5>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="订单类型" prop="type">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.type == 0" type="success" effect="dark" size="mini">巡检通过</el-tag>
            <el-tag v-else type="danger" effect="dark" size="mini">保全调机</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="巡检员" prop="measure_name"></el-table-column>
        <el-table-column label="调机人员" prop="repairor">
          <template slot-scope="scope">
            <span>{{ scope.row.type === 1 && scope.row.is_complete === 1 ? scope.row.repairor : '' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="订单状态" prop="is_receive">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.type === 1 && scope.row.is_receive === 0" type="warning" effect="dark"
              size="mini">待接单</el-tag>
            <el-tag v-if="scope.row.type === 1 && scope.row.is_receive === 1 && scope.row.is_complete === 0"
              type="primary" effect="dark" size="mini">已接单</el-tag>
            <el-tag v-if="scope.row.type === 1 && scope.row.is_complete === 1" type="success" effect="dark"
              size="mini">已完成</el-tag>
            <el-tag v-if="scope.row.type === 0 && scope.row.is_receive === 0  && scope.row.is_complete === 0" type="success" effect="dark"
              size="mini">已完成</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="巡检时间" prop="create_time" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column label="备注" prop="remark" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" width="70" fixed="right" v-if="User.type === 2">
          <template slot-scope="scope">
            <template>
              <el-tooltip class="item" effect="dark" content="删除" placement="bottom">
                <el-button size="mini" type="text" style="color: #f56c6c"
                  @click.native="deleteUser(scope.row.id, scope.row.food_name)">
                  <i class="el-icon-delete"></i>
                </el-button>
              </el-tooltip>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="page" v-show="total > 0">
        <el-pagination @size-change="handleSizeChange" @current-change="currentchange"
          :current-page="queryInfo.currentnum" :page-sizes="pageSizes" :page-size="queryInfo.pageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="total">
        </el-pagination>
      </div>
    </el-card>
    <!-- 订单详情展示弹窗 -->
    <el-dialog :title="title" :visible.sync="dialogVisible" width="50%" @close="closehandler">
      <el-descriptions title="订单明细">
        <el-descriptions-item label="布票号">{{
          currentRow.serial
          }}</el-descriptions-item>
        <el-descriptions-item label="机台号">{{
          currentRow.machine_number
          }}</el-descriptions-item>
        <el-descriptions-item label="Model">{{
          currentRow.model_name
          }}</el-descriptions-item>
        <el-descriptions-item label="尺码">{{
          currentRow.size
          }}</el-descriptions-item>
        <el-descriptions-item label="品名">{{
          currentRow.pm
          }}</el-descriptions-item>
        <el-descriptions-item label="测量参数">
          <div v-for="(item, index) in currentRow.measure_params" :key="index">
            <span>{{ item.name }}:{{ item.num }}</span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="调机参数">
          <div v-for="(item, index) in currentRow.change_params" :key="index">
            {{ item.name }}:{{ item.num }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="订单类型">
          <el-tag v-if="currentRow.type == 0" type="success" effect="dark" size="mini">巡检通过</el-tag>
          <el-tag v-else type="danger" effect="dark" size="mini">保全调机</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="订单状态">
          <el-tag v-if="currentRow.type == 1 && currentRow.is_receive == 0" type="warning" effect="dark"
            size="mini">待接单</el-tag>
          <el-tag v-show="!currentRow.is_complete && currentRow.is_receive == 1" v-if="currentRow.is_receive == 1"
            type="success" effect="dark" size="mini">已接单</el-tag>
          <!-- 已完成 -->
          <el-tag v-if="
              (currentRow.is_receive == 1 &&
                currentRow.is_complete == 1 &&
                currentRow.type == 1) ||
              currentRow.type == 0
            " type="success" effect="dark" size="mini">已完成</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="调机保全"
          >{{ currentRow.type === 1 && currentRow.repairor ? currentRow.repairor : currentRow.type === 0 && currentRow.measure_name ? currentRow.measure_name : '暂未调机' }}</el-descriptions-item
        >
      </el-descriptions>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from "vuex";
export default {
  data() {
    return {
      loading: true, // 表格加载
      checked: false,
      orderLists: [], // 订单列表
      keyword: "", // 搜索框
      queryInfo: {
        pageNum: 1,
        pageSize: 12,
        currentnum: 1,
        startTime: "",
        endTime: "",
        keywords: "",
        type: "",
      },
      pageSizes: [12, 15, 20],
      total: 11,
      info: [],
      options: [
        {
          value: 0,
          label: "巡检通过",
        },
        {
          value: 1,
          label: "保全调机",
        },
      ],
      value: "", // 类型筛选
      dates: [],
      dialogVisible: false, // 详情弹窗
      currentRow: {}, // 当前选中的行数据
      title: "",
    };
  },
  computed: {
    ...mapState({
      User: (state) => state.users,
    }),
  },
  created() {
    this.getOrderList();
  },
  methods: {
    // 获取订单列表
    async getOrderList() {
      try {
        this.queryInfo.startTime = this.dates[0];
        this.queryInfo.endTime = this.dates[1];
        this.queryInfo.keywords = this.keyword;
        this.queryInfo.type = this.value;
        this.loading = true;
        const res = await this.$http.getOrderList(this.queryInfo);
        if (res.code !== 200) {
          this.loading = false;
          return this.$notify.error(res.message);
        }
        const { result, total } = res.data.list;
        this.orderLists = result;
        this.total = total;
        setTimeout(() => {
          this.loading = false;
        }, 500);
      } catch (error) {
        this.loading = false;
        return false;
      }
    },
    // 关闭弹窗
    closehandler() {
      this.dialogVisible = false;
      // 取消选中
      this.$refs.orderTable.clearSelection();
    },
    // 行点击事件
    orderRowClickHandler(row) {
      this.dialogVisible = true;
      this.title = `${row.machine_number}调机详情`;
      this.currentRow = row;
      this.$refs.orderTable.clearSelection(); // 清除所有的选中状态
      this.$refs.orderTable.toggleRowSelection(row, true); // 选中被点击的行
    },
    // 查询
    searchByTime() {
      this.getOrderList();
    },
    // 导出巡检订单
    async exportExcel() {
      console.log("导出巡检订单");
    },
    // 清空事件
    clearHandler() {
      this.keyword = "";
      this.queryInfo.keywords = "";
      this.value = "";
      this.dates = [];
      this.loading = true;
      this.getOrderList();
    },
    // 分页
    handleSizeChange(val) {
      this.loading = true;
      this.queryInfo.pageSize = val;
      this.getOrderList();
    },
    // 分页
    currentchange(val) {
      this.loading = true;
      this.queryInfo.pageNum = val;
      this.getOrderList();
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-descriptions-item {
  display: flex; /* 使 div 在一行内显示并允许换行 */
}
.el-icon-more {
  font-size: 1.2rem;
}
.el-icon-delete {
  font-size: 1rem;
}
::vue-deep.hide .el-upload--picture-card {
  display: none;
}
.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.submit-btn {
  background-color: #000;
  color: #fff;
  border: none;
}
.search-btn {
  background-color: #000;
  color: #fff;
  border: none;
}
.reset-btn {
  background-color: #fff;
  color: #000;
}
.refresh-btn {
  background-color: #fff;
  color: #000;
}

.btn-box {
  display: flex;
  padding: 50px 0 0 0;
  justify-content: flex-start;
  align-items: center;
  .el-button {
    width: 130px;
  }
}

.input-box {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  width: 400px;
}

.image-view-title {
  margin: 10px 20px 10px 10px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.image-view-title img {
  object-fit: cover;
}

.image-list {
  width: 100px;
}

.image-button {
  text-align: center;
  padding: 120px 0;
}

.status-text {
  color: #30c82b;
  font-weight: bold;
  border: 1px solid #30c82b;
  display: block;
  width: 60px;
  height: 20px;
  margin: 0 auto;
  line-height: 20px;
  text-align: center;
}

.wait-complete {
  color: #ccc;
  font-weight: bold;
  border: 1px solid #ccc;
  display: block;
  width: 60px;
  height: 20px;
  margin: 0 auto;
  line-height: 20px;
  text-align: center;
}

.page {
  padding: 20px 0 0 0;
}

.el-image__inner {
  border-radius: 50%;
}

.el-table .cell {
  text-align: center;
}
</style>