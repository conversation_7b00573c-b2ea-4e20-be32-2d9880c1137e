{"name": "maintaince", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"sass": "^1.56.1", "vite": "^3.2.0", "vite-plugin-windicss": "^1.8.8", "windicss": "^3.5.6"}, "dependencies": {"@vitejs/plugin-vue2": "^2.2.0", "axios": "^1.7.9", "clipboard": "^2.0.11", "echarts": "^5.4.3", "echarts-liquidfill": "^3.1.0", "element-ui": "^2.15.13", "koa": "^2.15.3", "koa-router": "^13.0.1", "lodash": "^4.17.21", "nprogress": "^0.2.0", "qs": "^6.11.0", "vite-plugin-vue-mcp": "^0.3.2", "vue": "^2.7.14", "vue-print-nb": "^1.7.5", "vue-router": "^3.5.2", "vue-simple-drawer": "^1.0.6", "vue-template-compiler": "^2.7.14", "vuex": "^3.6.2"}}