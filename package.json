{"name": "wetalk-admin", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"qrcode": "^1.5.3", "sass": "^1.56.1", "vite": "^3.0.0", "vite-plugin-windicss": "^1.8.8", "windicss": "^3.5.6"}, "dependencies": {"@vitejs/plugin-vue2": "^2.3.1", "axios": "^1.6.2", "echarts": "^5.4.0", "element-ui": "^2.15.14", "lodash": "^4.17.21", "nprogress": "^0.2.0", "qs": "^6.11.0", "socket.io-client": "^4.7.3", "vue": "^2.7.14", "vue-barcode": "^1.3.0", "vue-print-nb": "^1.7.5", "vue-qrcode-component": "^2.1.1", "vue-quill-editor": "^3.0.6", "vue-router": "^3.5.2", "vue-simple-drawer": "^1.0.6", "vue-socket.io": "^3.0.10", "vue-template-compiler": "^2.7.14", "vuex": "^3.6.2"}}