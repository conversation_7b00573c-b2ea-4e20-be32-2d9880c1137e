import{bQ as Kl,b4 as cl,bR as Wl,b5 as pl,bS as vl,bT as Nl,X as Ee,c as S,ab as al,bU as Ae,bV as q,a8 as z,n as oe,_ as $e,C as Me,R as se,j as De,bW as rl,ac as Hl,G as P,e as me,a3 as sl,o as w,g as $,S as re,m as D,t as J,H as T,y as Z,i as W,k as ul,bM as Ml,ad as ae,aD as ml,a$ as Gl,bX as pe,av as Ql,bY as nl,bz as gl,aN as Ul,a5 as kl,aa as bl,b9 as jl,be as Xl,a9 as Dl,B as te,r as Yl,b as ve,w as A,a as K,F as tl,h as hl,f as R,aK as Zl,a2 as V,bZ as Jl,bB as xl,L as yl,U as _l,ag as Bl}from"./index-444b28c3.js";import{a as en,E as ln,C as nn,u as tn,b as on}from"./el-scrollbar-af6196f4.js";import{i as an,E as rn}from"./el-input-6b488ec7.js";import{E as sn,t as un}from"./el-tag-29cbefd8.js";import{e as dn,i as fn}from"./validator-e4131fc3.js";import{u as dl,d as Sl}from"./index-e305bb62.js";import{u as cn}from"./index-4d7f16ce.js";import{d as pn,U as x,C as Rl}from"./event-fe80fd0c.js";import{s as vn}from"./scroll-a66dde9b.js";import{U as Cl,g as Ol,b as wl,c as Tl,S as il,d as mn}from"./_Uint8Array-55276dff.js";var gn="__lodash_hash_undefined__";function bn(e){return this.__data__.set(e,gn),this}function hn(e){return this.__data__.has(e)}function Pe(e){var l=-1,t=e==null?0:e.length;for(this.__data__=new Kl;++l<t;)this.add(e[l])}Pe.prototype.add=Pe.prototype.push=bn;Pe.prototype.has=hn;function yn(e,l){for(var t=-1,s=e==null?0:e.length;++t<s;)if(l(e[t],t,e))return!0;return!1}function Sn(e,l){return e.has(l)}var Cn=1,On=2;function Vl(e,l,t,s,v,a){var r=t&Cn,d=e.length,g=l.length;if(d!=g&&!(r&&g>d))return!1;var h=a.get(e),c=a.get(l);if(h&&c)return h==l&&c==e;var y=-1,m=!0,O=t&On?new Pe:void 0;for(a.set(e,l),a.set(l,e);++y<d;){var o=e[y],f=l[y];if(s)var b=r?s(f,o,y,l,e,a):s(o,f,y,e,l,a);if(b!==void 0){if(b)continue;m=!1;break}if(O){if(!yn(l,function(C,L){if(!Sn(O,L)&&(o===C||v(o,C,t,s,a)))return O.push(L)})){m=!1;break}}else if(!(o===f||v(o,f,t,s,a))){m=!1;break}}return a.delete(e),a.delete(l),m}function wn(e){var l=-1,t=Array(e.size);return e.forEach(function(s,v){t[++l]=[v,s]}),t}function Tn(e){var l=-1,t=Array(e.size);return e.forEach(function(s){t[++l]=s}),t}var Ln=1,In=2,En="[object Boolean]",An="[object Date]",Pn="[object Error]",$n="[object Map]",Mn="[object Number]",Dn="[object RegExp]",Bn="[object Set]",Rn="[object String]",Vn="[object Symbol]",qn="[object ArrayBuffer]",zn="[object DataView]",Ll=cl?cl.prototype:void 0,ol=Ll?Ll.valueOf:void 0;function Fn(e,l,t,s,v,a,r){switch(t){case zn:if(e.byteLength!=l.byteLength||e.byteOffset!=l.byteOffset)return!1;e=e.buffer,l=l.buffer;case qn:return!(e.byteLength!=l.byteLength||!a(new Cl(e),new Cl(l)));case En:case An:case Mn:return Wl(+e,+l);case Pn:return e.name==l.name&&e.message==l.message;case Dn:case Rn:return e==l+"";case $n:var d=wn;case Bn:var g=s&Ln;if(d||(d=Tn),e.size!=l.size&&!g)return!1;var h=r.get(e);if(h)return h==l;s|=In,r.set(e,l);var c=Vl(d(e),d(l),s,v,a,r);return r.delete(e),c;case Vn:if(ol)return ol.call(e)==ol.call(l)}return!1}var Kn=1,Wn=Object.prototype,Nn=Wn.hasOwnProperty;function Hn(e,l,t,s,v,a){var r=t&Kn,d=Ol(e),g=d.length,h=Ol(l),c=h.length;if(g!=c&&!r)return!1;for(var y=g;y--;){var m=d[y];if(!(r?m in l:Nn.call(l,m)))return!1}var O=a.get(e),o=a.get(l);if(O&&o)return O==l&&o==e;var f=!0;a.set(e,l),a.set(l,e);for(var b=r;++y<g;){m=d[y];var C=e[m],L=l[m];if(s)var G=r?s(L,C,m,l,e,a):s(C,L,m,e,l,a);if(!(G===void 0?C===L||v(C,L,t,s,a):G)){f=!1;break}b||(b=m=="constructor")}if(f&&!b){var Q=e.constructor,N=l.constructor;Q!=N&&"constructor"in e&&"constructor"in l&&!(typeof Q=="function"&&Q instanceof Q&&typeof N=="function"&&N instanceof N)&&(f=!1)}return a.delete(e),a.delete(l),f}var Gn=1,Il="[object Arguments]",El="[object Array]",Ie="[object Object]",Qn=Object.prototype,Al=Qn.hasOwnProperty;function Un(e,l,t,s,v,a){var r=pl(e),d=pl(l),g=r?El:wl(e),h=d?El:wl(l);g=g==Il?Ie:g,h=h==Il?Ie:h;var c=g==Ie,y=h==Ie,m=g==h;if(m&&Tl(e)){if(!Tl(l))return!1;r=!0,c=!1}if(m&&!c)return a||(a=new il),r||mn(e)?Vl(e,l,t,s,v,a):Fn(e,l,g,t,s,v,a);if(!(t&Gn)){var O=c&&Al.call(e,"__wrapped__"),o=y&&Al.call(l,"__wrapped__");if(O||o){var f=O?e.value():e,b=o?l.value():l;return a||(a=new il),v(f,b,t,s,a)}}return m?(a||(a=new il),Hn(e,l,t,s,v,a)):!1}function ql(e,l,t,s,v){return e===l?!0:e==null||l==null||!vl(e)&&!vl(l)?e!==e&&l!==l:Un(e,l,t,s,ql,v)}function Pl(e,l){return ql(e,l)}const kn=e=>Nl[e||"default"],jn=e=>({focus:()=>{var l,t;(t=(l=e.value)==null?void 0:l.focus)==null||t.call(l)}}),zl="ElSelectGroup",Be="ElSelect";function Xn(e,l){const t=Ee(Be),s=Ee(zl,{disabled:!1}),v=S(()=>Object.prototype.toString.call(e.value).toLowerCase()==="[object object]"),a=S(()=>t.props.multiple?y(t.props.modelValue,e.value):m(e.value,t.props.modelValue)),r=S(()=>{if(t.props.multiple){const f=t.props.modelValue||[];return!a.value&&f.length>=t.props.multipleLimit&&t.props.multipleLimit>0}else return!1}),d=S(()=>e.label||(v.value?"":e.value)),g=S(()=>e.value||e.label||""),h=S(()=>e.disabled||l.groupDisabled||r.value),c=al(),y=(f=[],b)=>{if(v.value){const C=t.props.valueKey;return f&&f.some(L=>Ae(q(L,C))===q(b,C))}else return f&&f.includes(b)},m=(f,b)=>{if(v.value){const{valueKey:C}=t.props;return q(f,C)===q(b,C)}else return f===b},O=()=>{!e.disabled&&!s.disabled&&(t.hoverIndex=t.optionsArray.indexOf(c.proxy))};z(()=>d.value,()=>{!e.created&&!t.props.remote&&t.setSelected()}),z(()=>e.value,(f,b)=>{const{remote:C,valueKey:L}=t.props;if(Object.is(f,b)||(t.onOptionDestroy(b,c.proxy),t.onOptionCreate(c.proxy)),!e.created&&!C){if(L&&typeof f=="object"&&typeof b=="object"&&f[L]===b[L])return;t.setSelected()}}),z(()=>s.disabled,()=>{l.groupDisabled=s.disabled},{immediate:!0});const{queryChange:o}=Ae(t);return z(o,f=>{const{query:b}=oe(f),C=new RegExp(dn(b),"i");l.visible=C.test(d.value)||e.created,l.visible||t.filteredOptionsCount--}),{select:t,currentLabel:d,currentValue:g,itemSelected:a,isDisabled:h,hoverItem:O}}const Yn=Me({name:"ElOption",componentName:"ElOption",props:{value:{required:!0,type:[String,Number,Boolean,Object]},label:[String,Number],created:Boolean,disabled:{type:Boolean,default:!1}},setup(e){const l=se("select"),t=De({index:-1,groupDisabled:!1,visible:!0,hitState:!1,hover:!1}),{currentLabel:s,itemSelected:v,isDisabled:a,select:r,hoverItem:d}=Xn(e,t),{visible:g,hover:h}=rl(t),c=al().proxy;r.onOptionCreate(c),Hl(()=>{const m=c.value,{selected:O}=r,f=(r.props.multiple?O:[O]).some(b=>b.value===c.value);P(()=>{r.cachedOptions.get(m)===c&&!f&&r.cachedOptions.delete(m)}),r.onOptionDestroy(m,c)});function y(){e.disabled!==!0&&t.groupDisabled!==!0&&r.handleOptionSelect(c,!0)}return{ns:l,currentLabel:s,itemSelected:v,isDisabled:a,select:r,hoverItem:d,visible:g,hover:h,selectOptionClick:y,states:t}}});function Zn(e,l,t,s,v,a){return me((w(),$("li",{class:T([e.ns.be("dropdown","item"),e.ns.is("disabled",e.isDisabled),{selected:e.itemSelected,hover:e.hover}]),onMouseenter:l[0]||(l[0]=(...r)=>e.hoverItem&&e.hoverItem(...r)),onClick:l[1]||(l[1]=Z((...r)=>e.selectOptionClick&&e.selectOptionClick(...r),["stop"]))},[re(e.$slots,"default",{},()=>[D("span",null,J(e.currentLabel),1)])],34)),[[sl,e.visible]])}var fl=$e(Yn,[["render",Zn],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/select/src/option.vue"]]);const Jn=Me({name:"ElSelectDropdown",componentName:"ElSelectDropdown",setup(){const e=Ee(Be),l=se("select"),t=S(()=>e.props.popperClass),s=S(()=>e.props.multiple),v=S(()=>e.props.fitInputWidth),a=W("");function r(){var d;a.value=`${(d=e.selectWrapper)==null?void 0:d.offsetWidth}px`}return ul(()=>{r(),Ml(e.selectWrapper,r)}),{ns:l,minWidth:a,popperClass:t,isMultiple:s,isFitInputWidth:v}}});function xn(e,l,t,s,v,a){return w(),$("div",{class:T([e.ns.b("dropdown"),e.ns.is("multiple",e.isMultiple),e.popperClass]),style:ae({[e.isFitInputWidth?"width":"minWidth"]:e.minWidth})},[re(e.$slots,"default")],6)}var _n=$e(Jn,[["render",xn],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/select/src/select-dropdown.vue"]]);function et(e){const{t:l}=dl();return De({options:new Map,cachedOptions:new Map,createdLabel:null,createdSelected:!1,selected:e.multiple?[]:{},inputLength:20,inputWidth:0,optionsCount:0,filteredOptionsCount:0,visible:!1,softFocus:!1,selectedLabel:"",hoverIndex:-1,query:"",previousQuery:null,inputHovering:!1,cachedPlaceHolder:"",currentPlaceholder:l("el.select.placeholder"),menuVisibleOnFocus:!1,isOnComposition:!1,isSilentBlur:!1,prefixWidth:11,tagInMultiLine:!1})}const lt=(e,l,t)=>{const{t:s}=dl(),v=se("select"),a=W(null),r=W(null),d=W(null),g=W(null),h=W(null),c=W(null),y=W(-1),m=ml({query:""}),O=ml(""),{form:o,formItem:f}=cn(),b=S(()=>!e.filterable||e.multiple||!l.visible),C=S(()=>e.disabled||(o==null?void 0:o.disabled)),L=S(()=>{const n=e.multiple?Array.isArray(e.modelValue)&&e.modelValue.length>0:e.modelValue!==void 0&&e.modelValue!==null&&e.modelValue!=="";return e.clearable&&!C.value&&l.inputHovering&&n}),G=S(()=>e.remote&&e.filterable&&!e.remoteShowSuffix?"":e.suffixIcon),Q=S(()=>v.is("reverse",G.value&&l.visible&&e.suffixTransition)),N=S(()=>e.remote?300:0),ue=S(()=>e.loading?e.loadingText||s("el.select.loading"):e.remote&&l.query===""&&l.options.size===0?!1:e.filterable&&l.query&&l.options.size>0&&l.filteredOptionsCount===0?e.noMatchText||s("el.select.noMatch"):l.options.size===0?e.noDataText||s("el.select.noData"):null),E=S(()=>Array.from(l.options.values())),Re=S(()=>Array.from(l.cachedOptions.values())),Ve=S(()=>{const n=E.value.filter(i=>!i.created).some(i=>i.currentLabel===l.query);return e.filterable&&e.allowCreate&&l.query!==""&&!n}),ie=Gl(),qe=S(()=>["small"].includes(ie.value)?"small":"default"),ze=S({get(){return l.visible&&ue.value!==!1},set(n){l.visible=n}});z([()=>C.value,()=>ie.value,()=>o==null?void 0:o.size],()=>{P(()=>{F()})}),z(()=>e.placeholder,n=>{l.cachedPlaceHolder=l.currentPlaceholder=n}),z(()=>e.modelValue,(n,i)=>{e.multiple&&(F(),n&&n.length>0||r.value&&l.query!==""?l.currentPlaceholder="":l.currentPlaceholder=l.cachedPlaceHolder,e.filterable&&!e.reserveKeyword&&(l.query="",U(l.query))),de(),e.filterable&&!e.multiple&&(l.inputLength=20),!Pl(n,i)&&e.validateEvent&&(f==null||f.validate("change").catch(u=>pn()))},{flush:"post",deep:!0}),z(()=>l.visible,n=>{var i,u,p;n?((u=(i=d.value)==null?void 0:i.updatePopper)==null||u.call(i),e.filterable&&(l.filteredOptionsCount=l.optionsCount,l.query=e.remote?"":l.selectedLabel,e.multiple?(p=r.value)==null||p.focus():l.selectedLabel&&(l.currentPlaceholder=`${l.selectedLabel}`,l.selectedLabel=""),U(l.query),!e.multiple&&!e.remote&&(m.value.query="",pe(m),pe(O)))):(r.value&&r.value.blur(),l.query="",l.previousQuery=null,l.selectedLabel="",l.inputLength=20,l.menuVisibleOnFocus=!1,Fe(),P(()=>{r.value&&r.value.value===""&&l.selected.length===0&&(l.currentPlaceholder=l.cachedPlaceHolder)}),e.multiple||(l.selected&&(e.filterable&&e.allowCreate&&l.createdSelected&&l.createdLabel?l.selectedLabel=l.createdLabel:l.selectedLabel=l.selected.currentLabel,e.filterable&&(l.query=l.selectedLabel)),e.filterable&&(l.currentPlaceholder=l.cachedPlaceHolder))),t.emit("visible-change",n)}),z(()=>l.options.entries(),()=>{var n,i,u;if(!Ql)return;(i=(n=d.value)==null?void 0:n.updatePopper)==null||i.call(n),e.multiple&&F();const p=((u=h.value)==null?void 0:u.querySelectorAll("input"))||[];Array.from(p).includes(document.activeElement)||de(),e.defaultFirstOption&&(e.filterable||e.remote)&&l.filteredOptionsCount&&be()},{flush:"post"}),z(()=>l.hoverIndex,n=>{typeof n=="number"&&n>-1?y.value=E.value[n]||{}:y.value={},E.value.forEach(i=>{i.hover=y.value===i})});const F=()=>{e.collapseTags&&!e.filterable||P(()=>{var n,i;if(!a.value)return;const u=a.value.$el.querySelector("input"),p=g.value,I=kn(ie.value||(o==null?void 0:o.size));u.style.height=`${(l.selected.length===0?I:Math.max(p?p.clientHeight+(p.clientHeight>I?6:0):0,I))-2}px`,l.tagInMultiLine=Number.parseFloat(u.style.height)>=I,l.visible&&ue.value!==!1&&((i=(n=d.value)==null?void 0:n.updatePopper)==null||i.call(n))})},U=async n=>{if(!(l.previousQuery===n||l.isOnComposition)){if(l.previousQuery===null&&(typeof e.filterMethod=="function"||typeof e.remoteMethod=="function")){l.previousQuery=n;return}l.previousQuery=n,P(()=>{var i,u;l.visible&&((u=(i=d.value)==null?void 0:i.updatePopper)==null||u.call(i))}),l.hoverIndex=-1,e.multiple&&e.filterable&&P(()=>{const i=r.value.value.length*15+20;l.inputLength=e.collapseTags?Math.min(50,i):i,ge(),F()}),e.remote&&typeof e.remoteMethod=="function"?(l.hoverIndex=-1,e.remoteMethod(n)):typeof e.filterMethod=="function"?(e.filterMethod(n),pe(O)):(l.filteredOptionsCount=l.optionsCount,m.value.query=n,pe(m),pe(O)),e.defaultFirstOption&&(e.filterable||e.remote)&&l.filteredOptionsCount&&(await P(),be())}},ge=()=>{l.currentPlaceholder!==""&&(l.currentPlaceholder=r.value.value?"":l.cachedPlaceHolder)},be=()=>{const n=E.value.filter(p=>p.visible&&!p.disabled&&!p.states.groupDisabled),i=n.find(p=>p.created),u=n[0];l.hoverIndex=fe(E.value,i||u)},de=()=>{var n;if(e.multiple)l.selectedLabel="";else{const u=he(e.modelValue);(n=u.props)!=null&&n.created?(l.createdLabel=u.props.value,l.createdSelected=!0):l.createdSelected=!1,l.selectedLabel=u.currentLabel,l.selected=u,e.filterable&&(l.query=l.selectedLabel);return}const i=[];Array.isArray(e.modelValue)&&e.modelValue.forEach(u=>{i.push(he(u))}),l.selected=i,P(()=>{F()})},he=n=>{let i;const u=nl(n).toLowerCase()==="object",p=nl(n).toLowerCase()==="null",I=nl(n).toLowerCase()==="undefined";for(let H=l.cachedOptions.size-1;H>=0;H--){const M=Re.value[H];if(u?q(M.value,e.valueKey)===q(n,e.valueKey):M.value===n){i={value:n,currentLabel:M.currentLabel,isDisabled:M.isDisabled};break}}if(i)return i;const X=u?n.label:!p&&!I?n:"",Y={value:n,currentLabel:X};return e.multiple&&(Y.hitState=!1),Y},Fe=()=>{setTimeout(()=>{const n=e.valueKey;e.multiple?l.selected.length>0?l.hoverIndex=Math.min.apply(null,l.selected.map(i=>E.value.findIndex(u=>q(u,n)===q(i,n)))):l.hoverIndex=-1:l.hoverIndex=E.value.findIndex(i=>le(i)===le(l.selected))},300)},Ke=()=>{var n,i;We(),(i=(n=d.value)==null?void 0:n.updatePopper)==null||i.call(n),e.multiple&&!e.filterable&&F()},We=()=>{var n;l.inputWidth=(n=a.value)==null?void 0:n.$el.getBoundingClientRect().width},Ne=()=>{e.filterable&&l.query!==l.selectedLabel&&(l.query=l.selectedLabel,U(l.query))},He=Sl(()=>{Ne()},N.value),Ge=Sl(n=>{U(n.target.value)},N.value),_=n=>{Pl(e.modelValue,n)||t.emit(Rl,n)},Qe=n=>{if(n.target.value.length<=0&&!ce()){const i=e.modelValue.slice();i.pop(),t.emit(x,i),_(i)}n.target.value.length===1&&e.modelValue.length===0&&(l.currentPlaceholder=l.cachedPlaceHolder)},Ue=(n,i)=>{const u=l.selected.indexOf(i);if(u>-1&&!C.value){const p=e.modelValue.slice();p.splice(u,1),t.emit(x,p),_(p),t.emit("remove-tag",i.value)}n.stopPropagation()},ee=n=>{n.stopPropagation();const i=e.multiple?[]:"";if(typeof i!="string")for(const u of l.selected)u.isDisabled&&i.push(u.value);t.emit(x,i),_(i),l.hoverIndex=-1,l.visible=!1,t.emit("clear")},ye=(n,i)=>{var u;if(e.multiple){const p=(e.modelValue||[]).slice(),I=fe(p,n.value);I>-1?p.splice(I,1):(e.multipleLimit<=0||p.length<e.multipleLimit)&&p.push(n.value),t.emit(x,p),_(p),n.created&&(l.query="",U(""),l.inputLength=20),e.filterable&&((u=r.value)==null||u.focus())}else t.emit(x,n.value),_(n.value),l.visible=!1;l.isSilentBlur=i,ke(),!l.visible&&P(()=>{k(n)})},fe=(n=[],i)=>{if(!gl(i))return n.indexOf(i);const u=e.valueKey;let p=-1;return n.some((I,X)=>Ae(q(I,u))===q(i,u)?(p=X,!0):!1),p},ke=()=>{l.softFocus=!0;const n=r.value||a.value;n&&(n==null||n.focus())},k=n=>{var i,u,p,I,X;const Y=Array.isArray(n)?n[0]:n;let H=null;if(Y!=null&&Y.value){const M=E.value.filter(Le=>Le.value===Y.value);M.length>0&&(H=M[0].$el)}if(d.value&&H){const M=(I=(p=(u=(i=d.value)==null?void 0:i.popperRef)==null?void 0:u.contentRef)==null?void 0:p.querySelector)==null?void 0:I.call(p,`.${v.be("dropdown","wrap")}`);M&&vn(M,H)}(X=c.value)==null||X.handleScroll()},je=n=>{l.optionsCount++,l.filteredOptionsCount++,l.options.set(n.value,n),l.cachedOptions.set(n.value,n)},Xe=(n,i)=>{l.options.get(n)===i&&(l.optionsCount--,l.filteredOptionsCount--,l.options.delete(n))},Ye=n=>{n.code!==Ul.backspace&&ce(!1),l.inputLength=r.value.value.length*15+20,F()},ce=n=>{if(!Array.isArray(l.selected))return;const i=l.selected[l.selected.length-1];if(i)return n===!0||n===!1?(i.hitState=n,n):(i.hitState=!i.hitState,i.hitState)},j=n=>{const i=n.target.value;if(n.type==="compositionend")l.isOnComposition=!1,P(()=>U(i));else{const u=i[i.length-1]||"";l.isOnComposition=!an(u)}},Se=()=>{P(()=>k(l.selected))},Ze=n=>{l.softFocus?l.softFocus=!1:((e.automaticDropdown||e.filterable)&&(e.filterable&&!l.visible&&(l.menuVisibleOnFocus=!0),l.visible=!0),t.emit("focus",n))},Ce=()=>{var n;l.visible=!1,(n=a.value)==null||n.blur()},Je=n=>{P(()=>{l.isSilentBlur?l.isSilentBlur=!1:t.emit("blur",n)}),l.softFocus=!1},xe=n=>{ee(n)},_e=()=>{l.visible=!1},Oe=n=>{l.visible&&(n.preventDefault(),n.stopPropagation(),l.visible=!1)},we=()=>{var n;C.value||(l.menuVisibleOnFocus?l.menuVisibleOnFocus=!1:l.visible=!l.visible,l.visible&&((n=r.value||a.value)==null||n.focus()))},el=()=>{l.visible?E.value[l.hoverIndex]&&ye(E.value[l.hoverIndex],void 0):we()},le=n=>gl(n.value)?q(n.value,e.valueKey):n.value,ll=S(()=>E.value.filter(n=>n.visible).every(n=>n.disabled)),Te=n=>{if(!l.visible){l.visible=!0;return}if(!(l.options.size===0||l.filteredOptionsCount===0)&&!l.isOnComposition&&!ll.value){n==="next"?(l.hoverIndex++,l.hoverIndex===l.options.size&&(l.hoverIndex=0)):n==="prev"&&(l.hoverIndex--,l.hoverIndex<0&&(l.hoverIndex=l.options.size-1));const i=E.value[l.hoverIndex];(i.disabled===!0||i.states.groupDisabled===!0||!i.visible)&&Te(n),P(()=>k(y.value))}};return{optionsArray:E,selectSize:ie,handleResize:Ke,debouncedOnInputChange:He,debouncedQueryChange:Ge,deletePrevTag:Qe,deleteTag:Ue,deleteSelected:ee,handleOptionSelect:ye,scrollToOption:k,readonly:b,resetInputHeight:F,showClose:L,iconComponent:G,iconReverse:Q,showNewOption:Ve,collapseTagSize:qe,setSelected:de,managePlaceholder:ge,selectDisabled:C,emptyText:ue,toggleLastOptionHitState:ce,resetInputState:Ye,handleComposition:j,onOptionCreate:je,onOptionDestroy:Xe,handleMenuEnter:Se,handleFocus:Ze,blur:Ce,handleBlur:Je,handleClearClick:xe,handleClose:_e,handleKeydownEscape:Oe,toggleMenu:we,selectOption:el,getValueKey:le,navigateOptions:Te,dropMenuVisible:ze,queryChange:m,groupQueryChange:O,reference:a,input:r,tooltipRef:d,tags:g,selectWrapper:h,scrollbar:c}},$l="ElSelect",nt=Me({name:$l,componentName:$l,components:{ElInput:rn,ElSelectMenu:_n,ElOption:fl,ElTag:sn,ElScrollbar:en,ElTooltip:ln,ElIcon:kl},directives:{ClickOutside:nn},props:{name:String,id:String,modelValue:{type:[Array,String,Number,Boolean,Object],default:void 0},autocomplete:{type:String,default:"off"},automaticDropdown:Boolean,size:{type:String,validator:fn},effect:{type:String,default:"light"},disabled:Boolean,clearable:Boolean,filterable:Boolean,allowCreate:Boolean,loading:Boolean,popperClass:{type:String,default:""},remote:Boolean,loadingText:String,noMatchText:String,noDataText:String,remoteMethod:Function,filterMethod:Function,multiple:Boolean,multipleLimit:{type:Number,default:0},placeholder:{type:String},defaultFirstOption:Boolean,reserveKeyword:{type:Boolean,default:!0},valueKey:{type:String,default:"value"},collapseTags:Boolean,collapseTagsTooltip:{type:Boolean,default:!1},teleported:tn.teleported,persistent:{type:Boolean,default:!0},clearIcon:{type:bl,default:jl},fitInputWidth:{type:Boolean,default:!1},suffixIcon:{type:bl,default:Xl},tagType:{...un.type,default:"info"},validateEvent:{type:Boolean,default:!0},remoteShowSuffix:{type:Boolean,default:!1},suffixTransition:{type:Boolean,default:!0},placement:{type:String,values:on,default:"bottom-start"}},emits:[x,Rl,"remove-tag","clear","visible-change","focus","blur"],setup(e,l){const t=se("select"),s=se("input"),{t:v}=dl(),a=et(e),{optionsArray:r,selectSize:d,readonly:g,handleResize:h,collapseTagSize:c,debouncedOnInputChange:y,debouncedQueryChange:m,deletePrevTag:O,deleteTag:o,deleteSelected:f,handleOptionSelect:b,scrollToOption:C,setSelected:L,resetInputHeight:G,managePlaceholder:Q,showClose:N,selectDisabled:ue,iconComponent:E,iconReverse:Re,showNewOption:Ve,emptyText:ie,toggleLastOptionHitState:qe,resetInputState:ze,handleComposition:F,onOptionCreate:U,onOptionDestroy:ge,handleMenuEnter:be,handleFocus:de,blur:he,handleBlur:Fe,handleClearClick:Ke,handleClose:We,handleKeydownEscape:Ne,toggleMenu:He,selectOption:Ge,getValueKey:_,navigateOptions:Qe,dropMenuVisible:Ue,reference:ee,input:ye,tooltipRef:fe,tags:ke,selectWrapper:k,scrollbar:je,queryChange:Xe,groupQueryChange:Ye}=lt(e,a,l),{focus:ce}=jn(ee),{inputWidth:j,selected:Se,inputLength:Ze,filteredOptionsCount:Ce,visible:Je,softFocus:xe,selectedLabel:_e,hoverIndex:Oe,query:we,inputHovering:el,currentPlaceholder:le,menuVisibleOnFocus:ll,isOnComposition:Te,isSilentBlur:n,options:i,cachedOptions:u,optionsCount:p,prefixWidth:I,tagInMultiLine:X}=rl(a),Y=S(()=>{const B=[t.b()],ne=oe(d);return ne&&B.push(t.m(ne)),e.disabled&&B.push(t.m("disabled")),B}),H=S(()=>({maxWidth:`${oe(j)-32}px`,width:"100%"})),M=S(()=>({maxWidth:`${oe(j)>123?oe(j)-123:oe(j)-75}px`}));Dl(Be,De({props:e,options:i,optionsArray:r,cachedOptions:u,optionsCount:p,filteredOptionsCount:Ce,hoverIndex:Oe,handleOptionSelect:b,onOptionCreate:U,onOptionDestroy:ge,selectWrapper:k,selected:Se,setSelected:L,queryChange:Xe,groupQueryChange:Ye})),ul(()=>{a.cachedPlaceHolder=le.value=e.placeholder||v("el.select.placeholder"),e.multiple&&Array.isArray(e.modelValue)&&e.modelValue.length>0&&(le.value=""),Ml(k,h),e.remote&&e.multiple&&G(),P(()=>{const B=ee.value&&ee.value.$el;if(B&&(j.value=B.getBoundingClientRect().width,l.slots.prefix)){const ne=B.querySelector(`.${s.e("prefix")}`);I.value=Math.max(ne.getBoundingClientRect().width+5,30)}}),L()}),e.multiple&&!Array.isArray(e.modelValue)&&l.emit(x,[]),!e.multiple&&Array.isArray(e.modelValue)&&l.emit(x,"");const Le=S(()=>{var B,ne;return(ne=(B=fe.value)==null?void 0:B.popperRef)==null?void 0:ne.contentRef});return{tagInMultiLine:X,prefixWidth:I,selectSize:d,readonly:g,handleResize:h,collapseTagSize:c,debouncedOnInputChange:y,debouncedQueryChange:m,deletePrevTag:O,deleteTag:o,deleteSelected:f,handleOptionSelect:b,scrollToOption:C,inputWidth:j,selected:Se,inputLength:Ze,filteredOptionsCount:Ce,visible:Je,softFocus:xe,selectedLabel:_e,hoverIndex:Oe,query:we,inputHovering:el,currentPlaceholder:le,menuVisibleOnFocus:ll,isOnComposition:Te,isSilentBlur:n,options:i,resetInputHeight:G,managePlaceholder:Q,showClose:N,selectDisabled:ue,iconComponent:E,iconReverse:Re,showNewOption:Ve,emptyText:ie,toggleLastOptionHitState:qe,resetInputState:ze,handleComposition:F,handleMenuEnter:be,handleFocus:de,blur:he,handleBlur:Fe,handleClearClick:Ke,handleClose:We,handleKeydownEscape:Ne,toggleMenu:He,selectOption:Ge,getValueKey:_,navigateOptions:Qe,dropMenuVisible:Ue,focus:ce,reference:ee,input:ye,tooltipRef:fe,popperPaneRef:Le,tags:ke,selectWrapper:k,scrollbar:je,wrapperKls:Y,selectTagsStyle:H,nsSelect:t,tagTextStyle:M}}}),tt=["disabled","autocomplete"],it={style:{height:"100%",display:"flex","justify-content":"center","align-items":"center"}};function ot(e,l,t,s,v,a){const r=te("el-tag"),d=te("el-tooltip"),g=te("el-icon"),h=te("el-input"),c=te("el-option"),y=te("el-scrollbar"),m=te("el-select-menu"),O=Yl("click-outside");return me((w(),$("div",{ref:"selectWrapper",class:T(e.wrapperKls),onClick:l[22]||(l[22]=Z((...o)=>e.toggleMenu&&e.toggleMenu(...o),["stop"]))},[ve(d,{ref:"tooltipRef",visible:e.dropMenuVisible,placement:e.placement,teleported:e.teleported,"popper-class":[e.nsSelect.e("popper"),e.popperClass],"fallback-placements":["bottom-start","top-start","right","left"],effect:e.effect,pure:"",trigger:"click",transition:`${e.nsSelect.namespace.value}-zoom-in-top`,"stop-popper-mouse-event":!1,"gpu-acceleration":!1,persistent:e.persistent,onShow:e.handleMenuEnter},{default:A(()=>[D("div",{class:"select-trigger",onMouseenter:l[20]||(l[20]=o=>e.inputHovering=!0),onMouseleave:l[21]||(l[21]=o=>e.inputHovering=!1)},[e.multiple?(w(),$("div",{key:0,ref:"tags",class:T(e.nsSelect.e("tags")),style:ae(e.selectTagsStyle)},[e.collapseTags&&e.selected.length?(w(),$("span",{key:0,class:T([e.nsSelect.b("tags-wrapper"),{"has-prefix":e.prefixWidth&&e.selected.length}])},[ve(r,{closable:!e.selectDisabled&&!e.selected[0].isDisabled,size:e.collapseTagSize,hit:e.selected[0].hitState,type:e.tagType,"disable-transitions":"",onClose:l[0]||(l[0]=o=>e.deleteTag(o,e.selected[0]))},{default:A(()=>[D("span",{class:T(e.nsSelect.e("tags-text")),style:ae(e.tagTextStyle)},J(e.selected[0].currentLabel),7)]),_:1},8,["closable","size","hit","type"]),e.selected.length>1?(w(),K(r,{key:0,closable:!1,size:e.collapseTagSize,type:e.tagType,"disable-transitions":""},{default:A(()=>[e.collapseTagsTooltip?(w(),K(d,{key:0,disabled:e.dropMenuVisible,"fallback-placements":["bottom","top","right","left"],effect:e.effect,placement:"bottom",teleported:e.teleported},{default:A(()=>[D("span",{class:T(e.nsSelect.e("tags-text"))},"+ "+J(e.selected.length-1),3)]),content:A(()=>[D("div",{class:T(e.nsSelect.e("collapse-tags"))},[(w(!0),$(tl,null,hl(e.selected.slice(1),(o,f)=>(w(),$("div",{key:f,class:T(e.nsSelect.e("collapse-tag"))},[(w(),K(r,{key:e.getValueKey(o),class:"in-tooltip",closable:!e.selectDisabled&&!o.isDisabled,size:e.collapseTagSize,hit:o.hitState,type:e.tagType,"disable-transitions":"",style:{margin:"2px"},onClose:b=>e.deleteTag(b,o)},{default:A(()=>[D("span",{class:T(e.nsSelect.e("tags-text")),style:ae({maxWidth:e.inputWidth-75+"px"})},J(o.currentLabel),7)]),_:2},1032,["closable","size","hit","type","onClose"]))],2))),128))],2)]),_:1},8,["disabled","effect","teleported"])):(w(),$("span",{key:1,class:T(e.nsSelect.e("tags-text"))},"+ "+J(e.selected.length-1),3))]),_:1},8,["size","type"])):R("v-if",!0)],2)):R("v-if",!0),R(" <div> "),e.collapseTags?R("v-if",!0):(w(),K(Zl,{key:1,onAfterLeave:e.resetInputHeight},{default:A(()=>[D("span",{class:T([e.nsSelect.b("tags-wrapper"),{"has-prefix":e.prefixWidth&&e.selected.length}])},[(w(!0),$(tl,null,hl(e.selected,o=>(w(),K(r,{key:e.getValueKey(o),closable:!e.selectDisabled&&!o.isDisabled,size:e.collapseTagSize,hit:o.hitState,type:e.tagType,"disable-transitions":"",onClose:f=>e.deleteTag(f,o)},{default:A(()=>[D("span",{class:T(e.nsSelect.e("tags-text")),style:ae({maxWidth:e.inputWidth-75+"px"})},J(o.currentLabel),7)]),_:2},1032,["closable","size","hit","type","onClose"]))),128))],2)]),_:1},8,["onAfterLeave"])),R(" </div> "),e.filterable?me((w(),$("input",{key:2,ref:"input","onUpdate:modelValue":l[1]||(l[1]=o=>e.query=o),type:"text",class:T([e.nsSelect.e("input"),e.nsSelect.is(e.selectSize)]),disabled:e.selectDisabled,autocomplete:e.autocomplete,style:ae({marginLeft:e.prefixWidth&&!e.selected.length||e.tagInMultiLine?`${e.prefixWidth}px`:"",flexGrow:1,width:`${e.inputLength/(e.inputWidth-32)}%`,maxWidth:`${e.inputWidth-42}px`}),onFocus:l[2]||(l[2]=(...o)=>e.handleFocus&&e.handleFocus(...o)),onBlur:l[3]||(l[3]=(...o)=>e.handleBlur&&e.handleBlur(...o)),onKeyup:l[4]||(l[4]=(...o)=>e.managePlaceholder&&e.managePlaceholder(...o)),onKeydown:[l[5]||(l[5]=(...o)=>e.resetInputState&&e.resetInputState(...o)),l[6]||(l[6]=V(Z(o=>e.navigateOptions("next"),["prevent"]),["down"])),l[7]||(l[7]=V(Z(o=>e.navigateOptions("prev"),["prevent"]),["up"])),l[8]||(l[8]=V((...o)=>e.handleKeydownEscape&&e.handleKeydownEscape(...o),["esc"])),l[9]||(l[9]=V(Z((...o)=>e.selectOption&&e.selectOption(...o),["stop","prevent"]),["enter"])),l[10]||(l[10]=V((...o)=>e.deletePrevTag&&e.deletePrevTag(...o),["delete"])),l[11]||(l[11]=V(o=>e.visible=!1,["tab"]))],onCompositionstart:l[12]||(l[12]=(...o)=>e.handleComposition&&e.handleComposition(...o)),onCompositionupdate:l[13]||(l[13]=(...o)=>e.handleComposition&&e.handleComposition(...o)),onCompositionend:l[14]||(l[14]=(...o)=>e.handleComposition&&e.handleComposition(...o)),onInput:l[15]||(l[15]=(...o)=>e.debouncedQueryChange&&e.debouncedQueryChange(...o))},null,46,tt)),[[Jl,e.query]]):R("v-if",!0)],6)):R("v-if",!0),ve(h,{id:e.id,ref:"reference",modelValue:e.selectedLabel,"onUpdate:modelValue":l[16]||(l[16]=o=>e.selectedLabel=o),type:"text",placeholder:e.currentPlaceholder,name:e.name,autocomplete:e.autocomplete,size:e.selectSize,disabled:e.selectDisabled,readonly:e.readonly,"validate-event":!1,class:T([e.nsSelect.is("focus",e.visible)]),tabindex:e.multiple&&e.filterable?-1:void 0,onFocus:e.handleFocus,onBlur:e.handleBlur,onInput:e.debouncedOnInputChange,onPaste:e.debouncedOnInputChange,onCompositionstart:e.handleComposition,onCompositionupdate:e.handleComposition,onCompositionend:e.handleComposition,onKeydown:[l[17]||(l[17]=V(Z(o=>e.navigateOptions("next"),["stop","prevent"]),["down"])),l[18]||(l[18]=V(Z(o=>e.navigateOptions("prev"),["stop","prevent"]),["up"])),V(Z(e.selectOption,["stop","prevent"]),["enter"]),V(e.handleKeydownEscape,["esc"]),l[19]||(l[19]=V(o=>e.visible=!1,["tab"]))]},xl({suffix:A(()=>[e.iconComponent&&!e.showClose?(w(),K(g,{key:0,class:T([e.nsSelect.e("caret"),e.nsSelect.e("icon"),e.iconReverse])},{default:A(()=>[(w(),K(yl(e.iconComponent)))]),_:1},8,["class"])):R("v-if",!0),e.showClose&&e.clearIcon?(w(),K(g,{key:1,class:T([e.nsSelect.e("caret"),e.nsSelect.e("icon")]),onClick:e.handleClearClick},{default:A(()=>[(w(),K(yl(e.clearIcon)))]),_:1},8,["class","onClick"])):R("v-if",!0)]),_:2},[e.$slots.prefix?{name:"prefix",fn:A(()=>[D("div",it,[re(e.$slots,"prefix")])])}:void 0]),1032,["id","modelValue","placeholder","name","autocomplete","size","disabled","readonly","class","tabindex","onFocus","onBlur","onInput","onPaste","onCompositionstart","onCompositionupdate","onCompositionend","onKeydown"])],32)]),content:A(()=>[ve(m,null,{default:A(()=>[me(ve(y,{ref:"scrollbar",tag:"ul","wrap-class":e.nsSelect.be("dropdown","wrap"),"view-class":e.nsSelect.be("dropdown","list"),class:T([e.nsSelect.is("empty",!e.allowCreate&&!!e.query&&e.filteredOptionsCount===0)])},{default:A(()=>[e.showNewOption?(w(),K(c,{key:0,value:e.query,created:!0},null,8,["value"])):R("v-if",!0),re(e.$slots,"default")]),_:3},8,["wrap-class","view-class","class"]),[[sl,e.options.size>0&&!e.loading]]),e.emptyText&&(!e.allowCreate||e.loading||e.allowCreate&&e.options.size===0)?(w(),$(tl,{key:0},[e.$slots.empty?re(e.$slots,"empty",{key:0}):(w(),$("p",{key:1,class:T(e.nsSelect.be("dropdown","empty"))},J(e.emptyText),3))],64)):R("v-if",!0)]),_:3})]),_:3},8,["visible","placement","teleported","popper-class","effect","transition","persistent","onShow"])],2)),[[O,e.handleClose,e.popperPaneRef]])}var at=$e(nt,[["render",ot],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/select/src/select.vue"]]);const rt=Me({name:"ElOptionGroup",componentName:"ElOptionGroup",props:{label:String,disabled:{type:Boolean,default:!1}},setup(e){const l=se("select"),t=W(!0),s=al(),v=W([]);Dl(zl,De({...rl(e)}));const a=Ee(Be);ul(()=>{v.value=r(s.subTree)});const r=g=>{const h=[];return Array.isArray(g.children)&&g.children.forEach(c=>{var y;c.type&&c.type.name==="ElOption"&&c.component&&c.component.proxy?h.push(c.component.proxy):(y=c.children)!=null&&y.length&&h.push(...r(c))}),h},{groupQueryChange:d}=Ae(a);return z(d,()=>{t.value=v.value.some(g=>g.visible===!0)},{flush:"post"}),{visible:t,ns:l}}});function st(e,l,t,s,v,a){return me((w(),$("ul",{class:T(e.ns.be("group","wrap"))},[D("li",{class:T(e.ns.be("group","title"))},J(e.label),3),D("li",null,[D("ul",{class:T(e.ns.b("group"))},[re(e.$slots,"default")],2)])],2)),[[sl,e.visible]])}var Fl=$e(rt,[["render",st],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/select/src/option-group.vue"]]);const yt=_l(at,{Option:fl,OptionGroup:Fl}),St=Bl(fl);Bl(Fl);export{St as E,yt as a,Pl as i};
