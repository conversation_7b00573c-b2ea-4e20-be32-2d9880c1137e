<template>
    <el-container>
        <el-row :gutter="20" class="page-title">
            <el-col :span="2">
                <el-divider direction="vertical"></el-divider><span class="title">部门KPI考核</span>
            </el-col>
            <el-col :span="5.5">
                <!-- 日期选择 -->
                <el-date-picker v-model="formInline.date" type="daterange" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期" align="right" value-format="yyyy-MM-dd" style="float: right" @change="onSubmit">
                </el-date-picker>
            </el-col>
            <el-col :span="3.5">
                <!-- 部门选择 -->
                <el-select v-model="formInline.shift" placeholder="请选择班次" style="width: 200px;">
                    <el-option label="A" value="A"></el-option>
                    <el-option label="B" value="B"></el-option>
                    <el-option label="C" value="C"></el-option>
                    <el-option label="D" value="D"></el-option>
                    <el-option label="E" value="E"></el-option>
                    <el-option label="E1" value="E1"></el-option>
                    <el-option label="E2" value="E2"></el-option>
                </el-select>
            </el-col>
            <!-- 搜索按钮 -->
            <el-col :span="2.5">
                <el-button type="primary" @click="onSubmit" icon="el-icon-search">查询</el-button>
            </el-col>
            <el-col :span="3">
                <el-button type="primary" plain @click="refresh" icon="el-icon-refresh-right"></el-button>
            </el-col>
        </el-row>
        <el-row :gutter="10" class="containner">
            <el-col :span="24">
                <!-- 选项卡 -->
                <el-tabs v-model="activeName" @tab-click="handleClick">
                    <el-tab-pane :label="item.name" :name="item.key" v-for="(item, index) in tabbars"
                        :key="index"></el-tab-pane>
                </el-tabs>
            </el-col>
        </el-row>
        <!-- 表格 -->
        <el-row :gutter="10">
            <el-col :span="24">
                <el-col :span="24" style="padding: 10px 0;font-weight:bold;">部门保全月工作量统计表</el-col>
                <el-table :data="tableData" border stripe v-loading="loading">
                    <el-table-column prop="uuid" label="保全工号">
                    </el-table-column>
                    <el-table-column v-for="header in tableHeaders" :key="header" :prop="header" :label="header"
                        :summary-method="calculateSummary">
                        <template slot-scope="{ row }">
                            {{ findDetailValue(row.details, header) || 0 }}
                        </template>
                    </el-table-column>
                </el-table>
                <!-- 分页 -->
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="currentPage" :page-sizes="[4, 8, 12, 16]" :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper" :total="total">
                </el-pagination>
            </el-col>
            <el-col :span="12" style="margin-top: 30px">
                <!-- 图表 -->
                <div style="border: 1px solid #eee; height: 400px" id="main"></div>
            </el-col>
            <el-col :span="12" style="margin-top: 30px">
                <!-- 图表 -->
                <div style="border: 1px solid #eee; height: 400px" id="mains"></div>
            </el-col>
            <!-- <el-col :span="24" style="margin-top: 20px;padding-top:20px;">
                <div style="border: 1px solid #eee; height: 400px" id="maintaince"></div>
            </el-col> -->
        </el-row>
    </el-container>
</template>
<script>
import * as echarts from "echarts";
export default {
    data() {
        return {
            activeName: "first",
            tableData: [],
            tabbars: [
                // 选项卡
                {
                    key: "first",
                    name: "织造车间",
                },
                {
                    key: "second",
                    name: "整理车间",
                },
                {
                    key: "third",
                    name: "打样车间",
                },
                {
                    key: "fourth",
                    name: "综合维修",
                },
            ],
            formInline: {
                date: "",
                shift: "",
                department: "织造车间",
            },
            currentPage: 1,
            pageSize: 12,
            total: 0,
            tableHeaders: [],
            datas: {}, // 表一数据
            datas2: {}, // 表二数据
            datas3: {}, // 表三数据
            loading: true,
        };
    },
    mounted() {
        this.getAnalyseData();
    },
    methods: {
        // 获取数据接口
        async getAnalyseData() {
            try {
                this.formInline.page = this.currentPage;
                this.formInline.pageSize = this.pageSize;
                const res = await this.$http.getAnalyseDatas(this.formInline);
                if (res.status !== 200) {
                    return this.$message.error(res.message);
                } else {
                    const { list, total, data, data2 } = res.data
                    this.tableData = list;
                    this.total = total
                    this.datas = data
                    this.datas2 = data2
                    this.buildTableHeaders();
                    setTimeout(() => {
                        this.loading = false;
                        this.initChart();
                    }, 1000);
                }
            } catch (error) {
                this.loading = false;
                return this.$message.error(error);
            }
        },
        buildTableHeaders() {
            for (let i = 0; i < this.tableData.length; i++) {
                const details = this.tableData[i].details;
                for (let j = 0; j < details.length; j++) {
                    const key = Object.keys(details[j])[0];
                    if (!this.tableHeaders.includes(key)) {
                        this.tableHeaders.push(key);
                    }
                }
            }
        },
        findDetailValue(details, header) {
            for (let i = 0; i < details.length; i++) {
                if (details[i].hasOwnProperty(header)) {
                    return details[i][header];
                }
            }
            return null;
        },
        calculateSummary({ columns, data }) {
            const results = {};
            for (let i = 0; i < columns.length; i++) {
                const column = columns[i];
                const prop = column.property;
                if (column.type === 'default') {
                    const values = data.map(item => Number(this.findDetailValue(item.details, prop) || 0));
                    results[prop] = values.reduce((acc, cur) => acc + cur, 0);
                }
            }
            return results;
        },
        // 查询
        onSubmit() {
            this.loading = true
            this.getAnalyseData();
        },
        // 分页
        handleSizeChange(val) {
            this.loading = true
            this.pageSize = val;
            this.getAnalyseData();
        },
        handleCurrentChange(val) {
            this.loading = true
            this.currentPage = val;
            this.getAnalyseData();
        },
        indexMethod(index) {
            return index * 2;
        },
        // 初始化图表
        initChart() {
            let chartDom = document.querySelector("#main");
            let chartDoms = document.querySelector("#mains");
            let myChart = echarts.init(chartDom);
            let myChart1 = echarts.init(chartDoms);
           
            let option, option1;
            myChart.showLoading({
                text: 'loading',
                color: '#c23531',
                textColor: '#000',
                maskColor: 'rgba(255, 255, 255, 0.2)',
                zlevel: 0,
            });
            myChart1.showLoading({
                text: 'loading',
                color: '#c23531',
                textColor: '#000',
                maskColor: 'rgba(255, 255, 255, 0.2)',
                zlevel: 0,
            });

            option = {
                title: {
                    // 标题设置
                    text: "月度工作总量排名", // 标题文字
                    textStyle: {
                        // 标题文字样式设置
                        color: "#000",
                        fontSize: 14,
                    },
                    borderWidth: 0, // 标题边框宽度
                    borderColor: "", // 标题边框颜色
                    borderRadius: 5, // 标题边框圆角
                    left: 20, // 标题距离左边的距离
                    top: 10, // 标题距离顶部的距离
                },
                color: ["#6D9ADE"],
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "shadow",
                    },
                },
                toolbox: {
                    show: true,
                    feature: {
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ['line', 'bar'] },
                        restore: { show: true },
                        saveAsImage: { show: true }
                    }
                },
                calculable: true,
                grid: {
                    left: "3%",
                    right: "4%",
                    bottom: "3%",
                    containLabel: true,
                },
                xAxis: [
                    {
                        type: "category",
                        data: this.datas.uuidList,
                        axisTick: {
                            alignWithLabel: true,
                        },
                    },
                ],
                yAxis: [
                    {
                        type: "value",
                        axisLabel: {
                            //y轴文字的配置
                            textStyle: {
                                color: "#ccc",
                                margin: 15,
                            },
                            formatter: "{value}", //y轴文字的配置
                        },
                    },
                ],
                series: [
                    {
                        name: "月度工量总数",
                        type: "bar",
                        barWidth: "30%",
                        label: {
                            // 柱图头部显示值
                            show: true,
                            position: "top",
                            color: "#67C23A",
                            fontSize: "14px",
                            formatter: (params) => {
                                return params.value[params.encode.x[0]];
                            },
                        },
                        itemStyle: {
                            barBorderRadius: [50] // 设置圆角弧度，依次为左上角、右上角、右下角、左下角
                        },
                        data: this.datas.totalList,
                        markPoint: {
                            itemStyle: {
                                color: 'red', // 设置标记点的颜色
                            },
                            label: {
                                color: '#feffff', // 设置标记点的字体颜色
                            },
                            data: [
                                { type: 'max', name: 'Max' },
                                { type: 'min', name: 'Min' }
                            ]
                        },
                        markLine: {
                            data: [{ type: 'average', name: 'Avg' }]
                        }
                    },
                ],
            };
            option1 = {
                title: {
                    // 标题设置
                    text: "月度工效排名(%)", // 标题文字
                    textStyle: {
                        // 标题文字样式设置
                        color: "#000",
                        fontSize: 14,
                    },
                    borderWidth: 0, // 标题边框宽度
                    borderColor: "", // 标题边框颜色
                    borderRadius: 5, // 标题边框圆角
                    left: 10, // 标题距离左边的距离
                    top: 10, // 标题距离顶部的距离
                },
                color: ["#647EF7"],
                toolbox: {
                    show: true,
                    feature: {
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ['line', 'bar'] },
                        restore: { show: true },
                        saveAsImage: { show: true }
                    }
                },
                calculable: true,
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: this.datas2.uuidList1,
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                    },
                },
                yAxis: {
                    type: 'value'
                },
                grid: {
                    left: "3%",
                    right: "4%",
                    bottom: "3%",
                    containLabel: true,
                },
                series: [
                    {
                        name: "月度工量总数",
                        label: {
                            // 柱图头部显示值
                            show: true,
                            position: "top",
                            color: "#67C23A",
                            fontSize: "14px",
                            formatter: (params) => {
                                return params.value[params.encode.x[0]];
                            },
                        },
                        data: this.datas2.totalList1,
                        type: 'line',
                        markPoint: {
                            itemStyle: {
                                color: 'red', // 设置标记点的颜色
                            },
                            label: {
                                color: '#feffff', // 设置标记点的字体颜色
                            },
                            data: [
                                { type: 'max', name: 'Max' },
                                { type: 'min', name: 'Min' }
                            ]
                        },
                        markLine: {
                            data: [{ type: 'average', name: 'Avg' }, [
                                {
                                    symbol: 'none',
                                    x: '90%',
                                    yAxis: 'max'
                                },
                                {
                                    symbol: 'circle',
                                    label: {
                                        position: 'start',
                                        formatter: 'Max'
                                    },
                                    type: 'max',
                                    name: '最高点'
                                }
                            ]]
                        }
                    }
                ]
            };
           
            myChart.hideLoading();
            myChart1.hideLoading();
            option && myChart.setOption(option);
            option1 && myChart1.setOption(option1);
           
        },
        // 选项卡点击事件
        handleClick(tab, event) {
            this.loading = true
            this.formInline.department = tab.label;
            this.getAnalyseData();
        },
        // 刷新
        refresh() {
            this.formInline.date = '';
            this.formInline.department = '';
            this.formInline.shift = '';
            this.loading = true
            this.getAnalyseData();
        },
    },
};
</script>
<style lang="scss" scoped>
.el-container {

    width: 100%;
    background-color: #fff;
    padding: 20px;
    display: flex;
    flex-direction: column;

    .page-title {
        width: 100%;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .el-col .back-text {
            margin: 0 10px;
            cursor: pointer;
        }
    }

    .containner {
        padding: 20px 0;
    }

    .el-pagination {
        padding: 15px 0 0 0;
    }
}
</style>
  