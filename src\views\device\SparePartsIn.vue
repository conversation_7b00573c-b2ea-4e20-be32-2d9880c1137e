<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-08-16 10:53:32
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-25 15:17:27
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\device\SparePartsIn.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-card style="display: flex; justify-content: space-between">
      <el-row>
        <el-col class="header">
          <i class="el-icon-present"></i>
          <el-divider direction="vertical"></el-divider
          ><span
            >上一单号: <span class="order-number">{{ orderNumber }}</span
            ><el-tag
              :type="isCopy ? 'success' : 'info'"
              size="mini"
              @click="copyHandler"
              class="m-1"
            >
              <i v-show="isCopy" class="el-icon-check"></i>
              {{ copyText }}</el-tag
            >
          </span>
        </el-col>
        <el-col :span="12">
          <el-form ref="form" :model="form" :rules="rules" label-width="auto">
            <el-form-item label="备件名称" prop="part_name">
              <el-input
                v-model="form.part_name"
                placeholder="请输入备件名称"
                clearable
                maxlength="30"
              ></el-input>
            </el-form-item>
            <el-form-item label="备件编号" prop="part_number">
              <el-input
                v-model="form.part_number"
                maxlength="30"
                placeholder="请输入备件编号"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="备件分类" prop="category">
              <el-cascader
                v-model="form.category"
                :options="options"
                :props="{ expandTrigger: 'hover' }"
                @change="handleChange"
              ></el-cascader>
            </el-form-item>
            <el-form-item label="备件图片" prop="part_img">
              <!-- 上传图片 -->
              <el-upload
                v-if="!form.part_img"
                class="upload-demo"
                drag
                name="files"
                :action="action"
                :headers="headers"
                accept=".jpg,.png"
                :limit="1"
                :on-remove="logoRemove"
                :on-success="logoSuccess"
                :on-preview="handlepreview"
                :multiple="false"
                :on-error="onErr"
                :before-upload="project"
                :file-list="logo"
              >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
                <div class="el-upload__tip" slot="tip">
                  只能上传jpg/png文件，且不超过2M
                </div>
              </el-upload>
              <el-image
                v-else
                :src="form.part_img"
                fit="fill"
                style="width: 100px; height: 100px"
                lazy
              ></el-image>
              <!-- 大图展开 -->
              <el-dialog :visible.sync="dialogVisibles" :modal="false">
                <img width="100%" :src="dialogImageUrl" alt="上传失败" />
              </el-dialog>
            </el-form-item>
            <el-form-item label="入库类型" prop="stock_specific">
              <el-select
                v-model="form.stock_specific"
                placeholder="请选择入库类型"
              >
                <el-option label="随车配件" value="随车配件"></el-option>
                <el-option label="最新购买" value="最新购买"></el-option>
                <el-option label="维修返还" value="维修返还"></el-option>
                <el-option label="其他" value="其他"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="关联设备" prop="relation_device">
              <el-select
                v-model="form.relation_device"
                filterable
                placeholder="请选择关联设备"
                @change="handleEquipmentChange"
              >
                <el-option
                  v-for="item in deviceList"
                  :key="item.id"
                  :label="item.device_name"
                  :value="item.device_name"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="规格型号" prop="specific">
              <el-input
                v-model="form.specific"
                placeholder="请输入规格型号"
                maxlength="30"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="入库数量" prop="quantity">
              <el-input
                v-model="form.quantity"
                type="number"
                placeholder="请输入入库数量"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="单位" prop="unit">
              <el-select v-model="form.unit" placeholder="请选择单位">
                <el-option
                  v-for="item in unitList"
                  :key="item.id"
                  :label="item.unit"
                  :value="item.unit"
                ></el-option>
              </el-select>
              <span style="color: #517df7; cursor: pointer" @click="showAddBtn">
                {{ showText }}
              </span>
              <span v-show="isShow">
                <span>
                  <el-input
                    v-model="units"
                    placeholder="请输入单位"
                    style="width: 200px; margin-right: 10px"
                    maxlength="10"
                  ></el-input>
                  <el-button
                    type="primary"
                    icon="el-icon-plus"
                    size="small"
                    @click="addUnit"
                    >添加</el-button
                  >
                </span>
              </span>
            </el-form-item>
            <el-form-item label="备件金额" prop="amount">
              <el-input
                placeholder="请输入备件金额"
                v-model="form.amount"
                style="width: 100%"
                @blur="handlePriceChange"
                maxlength="30"
                clearable
                @input="handlePriceInput"
              >
                <template slot="prepend">
                  <el-select
                    v-model="form.currency"
                    placeholder="请选择"
                    @change="handleCurrencyChange"
                  >
                    <el-option label="人民币" value="CNY"></el-option>
                    <el-option label="美元" value="USD"></el-option>
                    <el-option label="越南盾" value="VND"></el-option>
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="供应商" prop="supplier">
              <el-input
                v-model="form.supplier"
                maxlength="40"
                placeholder="请输入供应商名称"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="摆放货架" prop="shelf_name">
              <el-cascader
                v-model="form.shelf_name"
                :options="shellLists"
                :props="{ expandTrigger: 'hover' }"
                @change="chooseRegion"
              ></el-cascader>
            </el-form-item>
            <el-form-item label="库位选择" prop="storage_number">
              <el-select v-model="form.storage_number" placeholder="请选择库位">
                <el-option
                  v-for="item in storageList"
                  :key="item.id"
                  :label="item.storage_number"
                  :value="item.storage_number"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="安全库存" prop="safe_stock">
              <el-input
                v-model="form.safe_stock"
                type="number"
                maxlength="10"
                placeholder="请输入安全库存，该配件必须达到多少库存量"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="预警库存" prop="warn_stock">
              <el-input
                v-model="form.warn_stock"
                type="number"
                maxlength="10"
                placeholder="请输入预警库存，库存到达指定库存时邮件通知"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="开启邮件预警通知" prop="notification">
              <el-switch v-model="form.notification"></el-switch>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input
                type="textarea"
                v-model="form.remark"
                placeholder="备注"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSubmit">立即入库</el-button>
              <el-button icon="el-icon-refresh" @click="resetForm('form')"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="10" class="right-content" v-if="showEquipment">
          <!-- 关联设备图片展示 -->
          <div class="equipment-image">
            <img :src="form.equipment_image || defaultImage" alt="加载中..." />
            <div class="equipment-name">{{ form.equipment }}</div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>
<script>
import defaultImage from "../../assets/image/device.png";
import config from "../../common/config.js";
export default {
  data() {
    return {
      copyText: "复制",
      orderNumber: "TZML202108160001", // 入库单号
      dialogVisibles: false, // 大图展示
      action: config.uploadURL, // 上传地址
      dialogImageUrl: "", // 大图展示链接
      defaultImage, // 默认图片
      // 上传logoing
      loadmen: false,
      logo: [], // 上传的图片
      headers: {
        Authorization: `Bearer ${localStorage.getItem("accessToken")}`,
      },
      baseURL: config.imgPrefix, // 图片地址
      showText: "添加单位",
      isShow: false, // 展示添加单位开关
      units: "", // 单位
      form: {
        part_name: "",
        part_number: "",
        category: "",
        stock_specific: "",
        relation_device: "",
        specific: "",
        quantity: "",
        unit: "",
        amount: "",
        currency: "",
        supplier: "",
        shelf_name: "",
        storage_number: "",
        safe_stock: "",
        warn_stock: "",
        notification: true,
        remarks: "",
        part_img: "",
        relation_device: "", // 关联设备
        equipment_image: "", // 关联设备图片
        currency: "CNY", // 备件金额货币
      },
      rules: {
        part_name: [
          { required: true, message: "请输入备件名称", trigger: "blur" },
        ],
        part_number: [
          { required: true, message: "请输入备件编号", trigger: "blur" },
        ],
        category: [
          { required: true, message: "请选择备件分类", trigger: "change" },
        ],
        part_img: [
          { required: true, message: "请上传备件图片", trigger: "change" },
        ],
        stock_specific: [
          { required: true, message: "请选择入库类型", trigger: "change" },
        ],
        quantity: [
          { required: true, message: "请输入入库数量", trigger: "blur" },
        ],
        unit: [{ required: true, message: "请选择单位", trigger: "change" }],
        shelf_name: [
          { required: true, message: "请选择摆放货架", trigger: "change" },
        ],
        storage_number: [
          { required: true, message: "请选择库位", trigger: "change" },
        ],
        safe_stock: [
          { required: true, message: "请输入安全库存", trigger: "blur" },
        ],
        warn_stock: [
          { required: true, message: "请输入预警库存", trigger: "blur" },
        ],
        relation_device: [
          { required: true, message: "请选择关联设备", trigger: "change" },
        ],
      },
      isCopy: false, // 复制成功
      options: [], // 备件分类
      shellLists: [], // 各部门货架列表
      storageList: [], // 库位列表
      unitList: [], // 单位列表
      deviceList: [], // 关联设备列表
      showEquipment: false, // 展示关联设备
      oldPrice: 0, // 初始输入金额值
    };
  },
  created() {
    this.getPartsClassify();
    this.getSheltList();
    this.getUnitList();
    this.getEquipmentList();
  },
  methods: {
    // 输入金额
    handlePriceInput(value) {
      this.form.amount = value || ""; // Ensure price is an empty string if value is falsy
      this.oldPrice = value || "";
    },
    // 失去焦点的时候格式化货币
    handlePriceChange() {
      if (this.form.amount !== "") {
        this.form.amount = this.formatCurrency(
          this.form.amount,
          this.form.currency
        );
      }
    },
    // 选择货币类型
    handleCurrencyChange(value) {
      const currency = value;
      if (this.form.amount !== "") {
        this.form.amount = this.formatCurrency(this.oldPrice, currency);
      }
    },
    /**
     * 格式化货币
     * @param {string|number} amount - 数值
     * @param {string} currency - 货币类型 (USD, CNY, VND)
     * @returns {string} 格式化后的货币表示
     */
    formatCurrency(amount, currency) {
      console.log(amount, currency);
      let minimumFractionDigits;

      switch (currency) {
        case "USD": // 美金
        case "CNY": // 人民币
          minimumFractionDigits = 2; // 保留两位小数
          break;

        case "VND": // 越南盾
          minimumFractionDigits = 0; // 不需要小数
          break;

        default:
          throw new Error("Unsupported currency type");
      }

      // 确保amount是一个有效的数值字符串或数字
      const numericAmount = parseFloat(amount);
      if (isNaN(numericAmount)) {
        console.error("Invalid amount, cannot format currency");
        return "NaN";
      }

      // 使用国际化 API 格式化数值，只保留金额部分
      return new Intl.NumberFormat("en-US", {
        minimumFractionDigits: minimumFractionDigits,
        maximumFractionDigits: minimumFractionDigits,
        useGrouping: true,
      }).format(numericAmount);
    },
    // 关联设备选择
    handleEquipmentChange(value) {
      // 展示关联设备图片
      this.showEquipment = true;
      // 根据value去获取设备图片
      this.deviceList.forEach((item) => {
        if (item.device_name === value) {
          this.form.equipment_image = item.device_image;
        }
      });
    },
    // 获取关联设备信息列表
    async getEquipmentList() {
      try {
        const res = await this.$http.getEquipmentList();
        if (res.status !== 200) {
          return this.$message.error(res.message);
        } else {
          this.deviceList = res.data;
        }
      } catch (error) {
        return this.$message.error(error);
      }
    },
    // 获取单位列表
    async getUnitList() {
      try {
        const res = await this.$http.getUnitList();
        if (res.status !== 200) {
          return this.$message.error(res.message);
        } else {
          this.unitList = res.data;
        }
      } catch (error) {
        return this.$message.error(error);
      }
    },
    // 选择货架
    chooseRegion(value) {
      const department = value[0];
      const shelf = value[1];
      this.form.department = department;
      this.form.shelf_name = shelf;
      this.getStorageListByShelf(department, shelf);
    },
    // 获取各部门货架列表
    async getSheltList() {
      try {
        const res = await this.$http.getShelfList();
        if (res.status !== 200) {
          return this.$message.error(res.message);
        } else {
          this.shellLists = res.data;
        }
      } catch (error) {
        return this.$message.error(error);
      }
    },
    // 获取库位列表
    async getStorageListByShelf(department, shelf) {
      try {
        const res = await this.$http.getStorageListByShelf({
          department,
          shelf,
        });
        if (res.status !== 200) {
          return this.$message.error(res.message);
        } else {
          this.storageList = res.data;
        }
      } catch (error) {
        return this.$message.error(error);
      }
    },
    // 获取备件分类
    async getPartsClassify() {
      try {
        const res = await this.$http.getPartsClassifyList();
        if (res.status !== 200) {
          return this.$message.error(res.message);
        }
        this.options = res.data;
      } catch (error) {
        return false;
      }
    },
    // 备件分类选择
    handleChange(value) {
      this.form.category = value[value.length - 1];
    },
    // 复制文本方法，获取入库单号
    copyHandler() {
      const textToCopy = this.orderNumber;
      const clipboard = new this.$Clipboard(".btn", {
        text: () => textToCopy,
      });

      clipboard.on("success", () => {
        this.copyText = "复制成功";
        this.isCopy = true;
        clipboard.destroy();
      });

      clipboard.on("error", () => {
        this.copyText = "复制失败";
        this.isCopy = false;
        clipboard.destroy();
      });

      clipboard.onClick(event);
    },
    // 添加单位
    async addUnit() {
      if (!this.units) {
        return this.$message.error("请输入单位");
      }
      // 验证units 是否是文字
      let reg = /^[\u4e00-\u9fa5]+$/;
      if (!reg.test(this.units)) {
        return this.$message.error("不是有效的单位!");
      }
      const res = await this.$http.initUnit({
        unit: this.units,
      });
      if (res.status !== 200) {
        return this.$message.error(res.message);
      } else {
        this.$message.success(res.message);
        this.units = "";
        this.isShow = false;
        this.showText = "添加单位";
        this.getUnitList();
      }
    },
    // 打开添加单位开关
    showAddBtn() {
      this.isShow = !this.isShow;
      if (this.isShow) {
        this.showText = "取消添加";
      } else {
        this.showText = "添加单位";
      }
    },
    // 入库
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          console.log(this.form);
          this.sparePartsIn();
        } else {
          return false;
        }
      });
    },
    // 备件入库
    async sparePartsIn() {
      const res = await this.$http.sparePartsIn({ data: this.form });
      if (res.status !== 200) {
        return this.$message.error(res.message);
      } else {
        this.$message.success(res.message);
        // 重置表单
        this.resetForm("form");
        this.form.equipment_image = "";
        this.showEquipment = false;
        this.logo = [];
      }
    },
    // 展开大图
    handlepreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisibles = true;
    },
    // 上传失败
    onErr(e) {
      this.loadmen = false;
      this.$message.error("上传失败,尝试重新上传");
    },
    // 上传时
    project(file) {
      this.loadmen = true;
    },
    // logo移除文件时的钩子
    logoRemove(file, fileList) {
      this.logo = [];
      this.form.part_img = "";
    },
    // 上传成功：logo
    logoSuccess(res, file, fileList) {
      const { url } = res.data;
      this.logo.push({ url: this.baseURL + url, uid: file.uid }); //element展示图片时需要数组类型的才能展示
      this.form.part_img = this.logo[0].url;
      console.log(this.form.part_img);
      this.loadmen = false;
    },
    // 重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.form.part_img = "";
    },
  },
};
</script>
<style lang="scss" scoped>
.el-select .el-input {
  width: 130px;
}
.input-with-select .el-input-group__prepend {
  background-color: #fff;
}
.header {
  padding: 10px;
  margin-bottom: 20px;

  .el-icon-present {
    font-size: 20px;
    color: #517df7;
  }

  .order-number {
    font-weight: bold;
  }

  .el-tag {
    cursor: pointer;
  }
}

.right-content {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem 0;
  background-color: #f5f5f5;
  margin-top: 10rem;
  margin-left: 5rem;
  .equipment-image {
    width: 50%;
    height: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    img {
      width: 100%;
      height: 100%;
    }
    .equipment-name {
      margin-top: 10px;
      font-size: 16px;
      font-weight: bold;
    }
  }
}
</style>
