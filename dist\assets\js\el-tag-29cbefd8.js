import{Q as z,bP as T,_ as w,C as N,a$ as $,R as M,c as P,o as n,g as V,m as t,S as m,H as o,n as a,a as c,w as i,b as g,af as k,y as C,a5 as y,f as b,ad as h,aK as I,U as H}from"./index-444b28c3.js";const K=z({closable:Boolean,type:{type:String,values:["success","info","warning","danger",""],default:""},hit:Boolean,disableTransitions:Boolean,color:{type:String,default:""},size:{type:String,values:T,default:""},effect:{type:String,values:["dark","light","plain"],default:"light"},round:Boolean}),Q={close:l=>l instanceof MouseEvent,click:l=>l instanceof MouseEvent},R={name:"ElTag"},U=N({...R,props:K,emits:Q,setup(l,{emit:r}){const v=l,S=$(),s=M("tag"),u=P(()=>{const{type:e,hit:f,effect:_,closable:B,round:E}=v;return[s.b(),s.is("closable",B),s.m(e),s.m(S.value),s.m(_),s.is("hit",f),s.is("round",E)]}),p=e=>{r("close",e)},d=e=>{r("click",e)};return(e,f)=>e.disableTransitions?(n(),V("span",{key:0,class:o(a(u)),style:h({backgroundColor:e.color}),onClick:d},[t("span",{class:o(a(s).e("content"))},[m(e.$slots,"default")],2),e.closable?(n(),c(a(y),{key:0,class:o(a(s).e("close")),onClick:C(p,["stop"])},{default:i(()=>[g(a(k))]),_:1},8,["class","onClick"])):b("v-if",!0)],6)):(n(),c(I,{key:1,name:`${a(s).namespace.value}-zoom-in-center`,appear:""},{default:i(()=>[t("span",{class:o(a(u)),style:h({backgroundColor:e.color}),onClick:d},[t("span",{class:o(a(s).e("content"))},[m(e.$slots,"default")],2),e.closable?(n(),c(a(y),{key:0,class:o(a(s).e("close")),onClick:C(p,["stop"])},{default:i(()=>[g(a(k))]),_:1},8,["class","onClick"])):b("v-if",!0)],6)]),_:3},8,["name"]))}});var j=w(U,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/tag/src/tag.vue"]]);const A=H(j);export{A as E,K as t};
