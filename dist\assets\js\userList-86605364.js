import{i,j as z,k as Ue,cR as ze,E as p,c$ as Fe,o as v,a as F,w as o,b as l,a2 as Se,g as B,h as I,F as M,n as g,s as Le,d as b,p as Te,cM as J,e as Be,d0 as Re,cN as $e,ar as Q,m as E,f as qe,d1 as Ne,d2 as Ae,d3 as De,a5 as Ie}from"./index-444b28c3.js";/* empty css                   */import{E as Me}from"./el-dialog-e35c112f.js";import"./el-overlay-9f4b42b1.js";import{E as je,a as Ke}from"./el-form-10dec954.js";import{E as Oe,a as Pe}from"./el-radio-f870a4f5.js";import"./el-form-item-4ed993c7.js";import{E as He}from"./el-progress-9334dbae.js";/* empty css                */import{E as Ge}from"./el-card-6f02be36.js";import{E as Je}from"./el-pagination-6fc73be7.js";import{E as Qe}from"./el-input-6b488ec7.js";import"./el-tag-29cbefd8.js";import{a as We,E as Xe}from"./el-select-980e5896.js";import"./el-scrollbar-af6196f4.js";import{E as Ye,a as Ze}from"./el-table-column-fa1764a8.js";import"./el-checkbox-f3df62fa.js";import"./el-tooltip-4ed993c7.js";import{E as el}from"./el-switch-8443b491.js";import{E as ll,a as al}from"./el-col-bd5e5418.js";import{E as tl}from"./el-button-9bbdfcf9.js";import{E as S}from"./index-df5d5edc.js";import{E as ol}from"./index-be3c1320.js";import{v as rl}from"./directive-ce1b251f.js";import"./index-eba6e623.js";import"./event-fe80fd0c.js";import"./scroll-a66dde9b.js";import"./vnode-b9ec7db4.js";import"./index-e305bb62.js";import"./focus-trap-6de7266c.js";import"./index-11a84590.js";import"./index-4d7f16ce.js";import"./_Uint8Array-55276dff.js";import"./validator-e4131fc3.js";const sl={class:"demo-pagination-block"},nl=["src"],il=["src"],ul={class:"el-upload-list__item-actions"},dl=["onClick"],cl={class:"dialog-footer"},Gl={__name:"userList",setup(ml){const j="http://*************:7001",W=j+"/api/v1/uploadFile",X={Authorization:"Bearer "+localStorage.getItem("ACCESS-TOKEN")},w=i(""),V=i(""),u=i(!0),f=i(!1),R=i("添加用户"),$=i(null);let x=z([]);const q=i(),C=i(""),d=i(!1),L=i(!1),Y=[{value:"1",label:"禁用"},{value:"0",label:"启用"}],Z=[{value:"A",label:"A"},{value:"B",label:"B"},{value:"C",label:"C"},{value:"D",label:"D"},{value:"E",label:"E"}],N=z([]),ee=z({avatar:[{required:!0,message:"请上传头像",trigger:"change"}],nickname:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}],idcard:[{required:!0,message:"请输入工号",trigger:"blur"}],gender:[{required:!0,message:"请选择性别",trigger:"blur"}],shift:[{required:!0,message:"请选择班次",trigger:"blur"}],mail:[{required:!0,message:"请输入邮箱",trigger:"blur"}]}),t=z({avatar:"",nickname:"",password:"",idcard:"",gender:"",shift:"",mail:"",role:""}),k=i(1),U=i(14),K=i(0),le=i("small"),ae=i(!1),T=z([]),te=r=>{t.rid=r};Ue(()=>{y(),oe()});const oe=async()=>{try{const r=await ze({keywords:w.value,page:k.value,pageSize:U.value});if(r.code!==200)return u.value=!1,p({title:"Error",message:r.msg,type:"error",duration:2e3});setTimeout(()=>{u.value=!1},500),N.splice(0,N.length,...r.data.list)}catch{return u.value=!1,!1}},re=r=>{U.value=r,u.value=!0,y()},se=r=>{k.value=r,u.value=!0,y()},O=()=>{(w.value||V.value)&&(u.value=!0,k.value=1,y())},ne=()=>{w.value="",V.value="",u.value=!0,k.value=1,y()},ie=()=>{t.avatar="",f.value=!0},ue=()=>{d.value=!1,L.value=!1,f.value=!1,q.value.resetFields(),t.nickname="",t.password="",t.idcard="",t.gender="",t.shift="",t.mail="",C.value="",x=[]},de=({data:{url:r}},{uid:e})=>{const s=j+r;t.avatar=s,x.push({url:s,uid:e})},ce=r=>{const e=r.size/1024/1024<5;return e||(p({title:"提示",message:"上传头像图片大小不能超过 5MB!",type:"warning"}),!1)},me=r=>{const e=x.findIndex(s=>s.uid===r.uid);e!==-1&&x.splice(e,1),t.avatar="",$.value.handleRemove(r)},pe=async r=>{r&&await r.validate(async e=>{const s=S.service({lock:!0,text:"Flyknit...",background:"rgba(0, 0, 0, 0.7)"});if(e)try{const n=await Ne(t);n.code!==200&&p({title:"",message:n.msg,type:"error",duration:2e3}),r.resetFields(),t.avatar="",$.value.clearFiles(),f.value=!1,y(),setTimeout(()=>{s.close(),p({title:"",message:n.msg,type:"success",duration:2e3})},1e3)}catch{return s.close(),!1}else return s.close(),!1})},ve=(r,e)=>{d.value=!0;const s=S.service({lock:!0,text:"Flyknit...",background:"rgba(0, 0, 0, 0.7)"});setTimeout(()=>{s.close(),f.value=!0,R.value="用户详情",t.avatar=e.avatar_url,C.value=e.avatar_url,t.nickname=e.nickname,t.password=e.password,t.idcard=e.idcard,t.gender=e.gender,t.shift=e.shift,t.mail=e.mail,t.role=e.role_name},500)},ge=(r,e)=>{L.value=!0;const s=S.service({lock:!0,text:"Flyknit...",background:"rgba(0, 0, 0, 0.7)"});setTimeout(()=>{s.close(),f.value=!0,R.value="编辑用户",t.avatar=e.avatar_url,C.value=e.avatar_url,t.nickname=e.nickname,t.password=e.password,t.idcard=e.idcard,t.gender=e.gender,t.shift=e.shift,t.mail=e.mail,t.role=e.role_name,t.rid=e.rid},500)},fe=async(r,e)=>{const s=S.service({lock:!0,text:"Flyknit...",background:"rgba(0, 0, 0, 0.7)"});try{const n=await Ae({idcard:e.idcard,status:r});n.code!==200&&p({title:"",message:n.msg,type:"error",duration:2e3}),p({title:"",message:n.msg,type:"success",duration:2e3}),setTimeout(()=>{s.close(),y()},500)}catch{return s.close(),!1}},_e=async(r,e)=>{ol.confirm("此操作将永久删除该用户, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const s=S.service({lock:!0,text:"Flyknit...",background:"rgba(0, 0, 0, 0.7)"});try{const n=await De({uid:e});if(n.code!==200)return p({title:"",message:n.msg,type:"error",duration:2e3});setTimeout(()=>{s.close(),T.splice(r,1),p({title:"",message:n.msg,type:"success",duration:2e3})},1e3)}catch{return s.close(),!1}}).catch(()=>!1)},y=async()=>{try{const r=await Fe({keywords:w.value,page:k.value,pageSize:U.value,status:V.value});if(r.code!==200)return u.value=!1,p({title:"Error",message:r.msg,type:"error",duration:2e3});setTimeout(()=>{u.value=!1},500),T.splice(0,T.length,...r.data.list),K.value=r.data.total}catch{return u.value=!1,!1}};return(r,e)=>{const s=Qe,n=ll,A=Xe,D=We,_=tl,P=al,c=Ye,be=el,ke=Ze,ye=Je,he=Ge,H=Ie,we=He,m=je,G=Oe,Ee=Pe,Ve=Ke,xe=Me,Ce=rl;return v(),F(P,{gutter:20,class:"content-row"},{default:o(()=>[l(n,{xs:24,sm:24,md:24,lg:24},{default:o(()=>[l(he,{shadow:"hover"},{default:o(()=>[l(P,{gutter:20},{default:o(()=>[l(n,{span:6},{default:o(()=>[l(s,{modelValue:w.value,"onUpdate:modelValue":e[0]||(e[0]=a=>w.value=a),placeholder:"请输入用户名/工号/邮箱查询",clearable:"",onKeyup:Se(O,["enter"])},null,8,["modelValue"])]),_:1}),l(n,{span:4},{default:o(()=>[l(D,{modelValue:V.value,"onUpdate:modelValue":e[1]||(e[1]=a=>V.value=a),placeholder:"请选择用户状态",clearable:""},{default:o(()=>[(v(),B(M,null,I(Y,a=>l(A,{key:a.value,label:a.label,value:a.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),l(n,{span:14},{default:o(()=>[l(_,{type:"primary",icon:g(Le),onClick:O},{default:o(()=>[b("搜 索")]),_:1},8,["icon"]),l(_,{type:"info",icon:g(Te),onClick:ne},{default:o(()=>[b("重 置")]),_:1},8,["icon"]),l(_,{type:"success",icon:g(J),onClick:ie},{default:o(()=>[b("添 加")]),_:1},8,["icon"])]),_:1})]),_:1}),Be((v(),F(ke,{data:T,stripe:"",style:{"margin-top":"2rem"},"element-loading-text":"Flyknit..."},{default:o(()=>[l(c,{type:"selection",width:"55"}),l(c,{prop:"nickname",label:"用户名",width:"150"}),l(c,{prop:"idcard",label:"工号",width:"100"}),l(c,{prop:"gender",label:"性别"}),l(c,{prop:"shift",label:"班次"}),l(c,{prop:"mail",label:"邮箱",width:"260"}),l(c,{prop:"is_ban",label:"状态",width:"150"},{default:o(a=>[l(be,{modelValue:a.row.is_ban,"onUpdate:modelValue":h=>a.row.is_ban=h,"active-value":0,"active-text":"启用","inactive-text":"禁用","inactive-value":1,onChange:h=>fe(h,a.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),l(c,{prop:"role_name",label:"用户角色",width:"150"}),l(c,{prop:"create_time",label:"创建时间",width:"200"}),l(c,{label:"操作",width:"260",fixed:"right"},{default:o(a=>[l(_,{type:"primary",icon:g(Re),size:"small",onClick:h=>ve(a.$index,a.row)},{default:o(()=>[b("查 看")]),_:2},1032,["icon","onClick"]),l(_,{type:"warning",icon:g($e),size:"small",onClick:h=>ge(a.$index,a.row)},{default:o(()=>[b("编 辑")]),_:2},1032,["icon","onClick"]),l(_,{type:"danger",icon:g(Q),size:"small",onClick:h=>_e(a.$index,a.row.id)},{default:o(()=>[b("删 除")]),_:2},1032,["icon","onClick"])]),_:1})]),_:1},8,["data"])),[[Ce,u.value]]),E("div",sl,[l(ye,{style:{"margin-top":"1rem"},"current-page":k.value,"onUpdate:currentPage":e[2]||(e[2]=a=>k.value=a),"page-size":U.value,"onUpdate:pageSize":e[3]||(e[3]=a=>U.value=a),"page-sizes":[14,16,18,20],size:le.value,background:ae.value,layout:"total, sizes, prev, pager, next, jumper",total:K.value,onSizeChange:re,onCurrentChange:se},null,8,["current-page","page-size","size","background","total"])])]),_:1})]),_:1}),l(xe,{title:R.value,modelValue:f.value,"onUpdate:modelValue":e[13]||(e[13]=a=>f.value=a),width:"40%","close-on-click-modal":!1,"before-close":ue},{default:o(()=>[l(Ve,{ref_key:"ruleFormRef",ref:q,model:t,rules:ee,"label-width":"80px"},{default:o(()=>[l(m,{label:"头像",prop:"avatar"},{default:o(()=>[l(we,{class:"avatar-uploader",ref_key:"upload",ref:$,accept:"image/png,image/gif,image/jpeg,image/jpg","list-type":"picture-card",multiple:!1,"file-list":g(x),action:W,headers:X,"on-success":de,"before-upload":ce,"auto-upload":!0,limit:1,disabled:!!t.avatar,name:"file"},{file:o(({file:a})=>[E("div",null,[E("img",{class:"el-upload-list__item-thumbnail",src:a.url,alt:""},null,8,il),E("span",ul,[E("span",{class:"el-upload-list__item-delete",onClick:h=>me(a)},[l(H,null,{default:o(()=>[l(g(Q))]),_:1})],8,dl)])])]),default:o(()=>[C.value?(v(),B("img",{key:0,src:C.value,class:"avatar"},null,8,nl)):(v(),F(H,{key:1,class:"avatar-uploader-icon"},{default:o(()=>[l(g(J))]),_:1}))]),_:1},8,["file-list","disabled"])]),_:1}),l(m,{label:"用户名",prop:"nickname"},{default:o(()=>[l(s,{modelValue:t.nickname,"onUpdate:modelValue":e[4]||(e[4]=a=>t.nickname=a),disabled:d.value&&!L.value,clearable:"",maxlength:"30",placeholder:"请输入用户名"},null,8,["modelValue","disabled"])]),_:1}),l(m,{label:"密码",prop:"password"},{default:o(()=>[l(s,{type:"password",modelValue:t.password,"onUpdate:modelValue":e[5]||(e[5]=a=>t.password=a),disabled:d.value,clearable:"",maxlength:"20",placeholder:"请输入密码"},null,8,["modelValue","disabled"])]),_:1}),l(m,{label:"工号",prop:"idcard"},{default:o(()=>[l(s,{modelValue:t.idcard,"onUpdate:modelValue":e[6]||(e[6]=a=>t.idcard=a),maxlength:"10",disabled:d.value||L.value,clearable:"",placeholder:"请输入工号"},null,8,["modelValue","disabled"])]),_:1}),l(m,{label:"性别",prop:"gender"},{default:o(()=>[l(Ee,{modelValue:t.gender,"onUpdate:modelValue":e[7]||(e[7]=a=>t.gender=a),disabled:d.value},{default:o(()=>[l(G,{label:"男"}),l(G,{label:"女"})]),_:1},8,["modelValue","disabled"])]),_:1}),l(m,{label:"班次",prop:"shift"},{default:o(()=>[l(D,{modelValue:t.shift,"onUpdate:modelValue":e[8]||(e[8]=a=>t.shift=a),"value-key":"",placeholder:"请选择班次",disabled:d.value,clearable:"",filterable:""},{default:o(()=>[(v(),B(M,null,I(Z,a=>l(A,{key:a.value,label:a.label,value:a.value},null,8,["label","value"])),64))]),_:1},8,["modelValue","disabled"])]),_:1}),l(m,{label:"邮箱",prop:"mail"},{default:o(()=>[l(s,{modelValue:t.mail,"onUpdate:modelValue":e[9]||(e[9]=a=>t.mail=a),maxlength:"50",disabled:d.value,clearable:"",placeholder:"请输入邮箱"},null,8,["modelValue","disabled"])]),_:1}),l(m,{label:"角色",prop:"role"},{default:o(()=>[l(D,{modelValue:t.role,"onUpdate:modelValue":e[10]||(e[10]=a=>t.role=a),"value-key":"id",placeholder:"请选择角色",onChange:te,disabled:d.value,clearable:"",filterable:""},{default:o(()=>[(v(!0),B(M,null,I(N,a=>(v(),F(A,{key:a._id,label:a.name,value:a._id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),d.value?qe("",!0):(v(),F(m,{key:0},{default:o(()=>[E("span",cl,[l(_,{onClick:e[11]||(e[11]=a=>f.value=!1)},{default:o(()=>[b("取 消")]),_:1}),l(_,{type:"primary",onClick:e[12]||(e[12]=a=>pe(q.value))},{default:o(()=>[b("确 定")]),_:1})])]),_:1}))]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])]),_:1})}}};export{Gl as default};
