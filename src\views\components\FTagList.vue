<template>
  <div class="f-tag-list" :style="{ left:$store.state.asideWidth }">
    <div class="scroll-container">
      <div class="scroll-btn left" @click="scrollLeft" v-show="showLeftScroll">
        <i class="el-icon-arrow-left"></i>
      </div>
      <div class="tab-wrapper" ref="tabWrapper">
        <div class="tabs-content" ref="tabContent">
          <div v-for="(item, index) in tabLists" :key="index" style="display: flex">
            <span
              :style="{
                'background-color': index == activeTab ? '#feffff' : '#FDFDFD',
              }"
              :class="['tab', index === activeTab ? 'active' : '']"
              @click="changeTab(item.path, index, item.id, item.class_id)"
              >{{ item.title }}
              <i
                v-if="item.path !== 'console'"
                class="el-icon-close"
                @click="removeTab(item.path)"
              />
            </span>
          </div>
        </div>
      </div>
      <div class="scroll-btn right" @click="scrollRight" v-show="showRightScroll">
        <i class="el-icon-arrow-right"></i>
      </div>
      <span class="tag-btn" v-if="tabLists.length >= 15">
        <el-dropdown @command="handleClose">
          <span class="el-dropdown-link">
            <i class="el-icon-arrow-down"></i>
          </span>
          <template slot="dropdown">
            <el-dropdown-menu>
              <el-dropdown-item command="clearOther">关闭其他</el-dropdown-item>
              <el-dropdown-item command="clearAll">全部关闭</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </span>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    tabList: {
      type: Array,
      default() {
        return [];
      },
    },
    activeItem: {
      type: Number,
      default() {
        return 0
      }
    }
  },
  data() {
    return {
      activeTab: 0,
      tabId: 0,
      tabLists: [
        {
          id: "20",
          title: "控制台",
          path: "console",
        },
      ],
      showLeftScroll: false,
      showRightScroll: false,
      isScrolling: false
    };
  },
  watch: {
    tabList(newVal) { 
      let obj = {
        "class_id": 1,
        "id": "20",
        "title": "控制台",
        "path": "console",
      };
      if (newVal) {
        this.tabLists = newVal;
        Array.prototype.copyUnshift = function () {
          var argLen = arguments.length,
            len = argLen + this.length;
          for (var i = len - 1; i >= 0; i--) {
            this[i] = i > argLen - 1 ? this[i - argLen] : arguments[i];
          }
          return len;
        };
        if (this.tabLists[0].title !== "控制台") {
          this.tabLists.copyUnshift(obj);
        }
        this.activeTab = this.tabLists.length - 1;
        this.$nextTick(() => {
          this.checkScroll();
        });
      }
    },
    activeItem(nVal) {
      if(nVal) {
        this.activeTab = nVal
      }
    }
  },
  mounted() {
    this.checkScroll();
    window.addEventListener('resize', this.checkScroll);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.checkScroll);
  },
  methods: {
    checkScroll() {
      if(this.$refs.tabWrapper && this.$refs.tabContent) {
        const wrapper = this.$refs.tabWrapper;
        const content = this.$refs.tabContent;
        
        // 计算最大可滚动距离
        const maxScroll = content.offsetWidth - wrapper.offsetWidth;
        
        // 显示左箭头条件:滚动位置大于0
        this.showLeftScroll = wrapper.scrollLeft > 0;
        
        // 显示右箭头条件:还有未显示的内容且未滚动到最右侧
        this.showRightScroll = maxScroll > 0 && 
                              Math.ceil(wrapper.scrollLeft) < maxScroll;
      }
    },
    scrollLeft() {
      if(this.$refs.tabWrapper && !this.isScrolling) {
        this.isScrolling = true;
        this.$refs.tabWrapper.scrollLeft -= 100;
        setTimeout(() => {
          this.checkScroll();
          this.isScrolling = false;
        }, 300);
      }
    },
    scrollRight() {
      if(this.$refs.tabWrapper && !this.isScrolling) {
        this.isScrolling = true;
        this.$refs.tabWrapper.scrollLeft += 100;
        setTimeout(() => {
          this.checkScroll();
          this.isScrolling = false;
        }, 300);
      }
    },
    handleClose(c){
        if (c == "clearAll") {
            this.tabLists = [{
               class_id: 1,
               id: "20",
               title: "控制台",
               path: "console",
            }]
            this.$emit('clearAll', this.tabLists);
            this.activeTab = 0
            this.changeTab(this.tabLists[0].path, this.activeTab, this.tabLists[0].id, this.tabLists[0].class_id);
        } else if (c == "clearOther") {
            this.tabLists = this.tabLists.filter(tab => tab.path == "console" || tab.path == this.tabLists[this.activeTab].path)
            this.$emit('clearAll',  this.tabLists); 
       }
        this.activeTab = this.tabLists.length - 1
        this.$nextTick(() => {
          this.checkScroll();
        });
    },
    removeTab(e) {
      this.$emit("handleTabsEdit", {
        router: e,
      });
      this.$nextTick(() => {
        this.checkScroll();
      });
    },
    changeTab(url, index, id, class_id) {
      this.tabId = ''
      this.tabId = class_id
      this.$router.push({name: url, query: {pid: id}});
      this.activeTab = index;
      this.$emit('changeTabs', {
        idx : index,
        cid: this.tabId,
        fid: id
      })
    },
  },
};
</script>
<style scoped>
.scroll-container {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
}

.scroll-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  z-index: 1;
}

.scroll-btn.left {
  position: absolute;
  left: 0;
}

.scroll-btn.right {
  position: absolute;
  right: 40px;
}

.tab-wrapper {
  flex: 1;
  overflow-x: hidden;
  white-space: nowrap;
  margin: 0 25px;
  scroll-behavior: smooth;
}

.tabs-content {
  display: inline-flex;
}

.tab {
  margin: 3px 5px;
  padding: 6px 10px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.88rem;
  white-space: nowrap;
}


.active {
  color: #517DF7 !important;
  margin: 3px 5px;
  padding: 6px 10px;
  border-radius: 5px;
  font-size: 0.85rem;
}

.f-tag-list {
  display: flex;
  background-color: #fff;
  position: absolute;
  align-items: center;
  top: 60px;
  padding: 3px 0 10px 5px;
  right: 10px;
  height: 44px;
  z-index: 100;
  color: rgba(107,114,128);
  box-sizing: border-box;
}

.tag-btn {
    height: 31px;
    background-color: #fff;
    border-radius: 5px;
    margin-left:auto;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 20px;
    margin-top: 2px;
    cursor: pointer;
    position: fixed;
    right: 10px;
}
</style>
