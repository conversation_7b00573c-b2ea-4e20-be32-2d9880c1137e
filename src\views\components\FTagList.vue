<template>
  <div class="f-tag-list" :style="{ left: $store.state.asideWidth }">
    <div class="tab-wrapper" v-if="tabLists.length > 0">
      <div v-for="(item, index) in tabLists" :key="index" style="display: flex">
        <span
          :style="{
            'background-color': index == activeTab ? '#feffff' : '#FDFDFD',
          }"
          :class="['tab', index === activeTab ? 'active' : '']"
          @click="changeTab(item.path, index, item.id, item.class_id)"
          >{{ item.title }}
          <i
            v-if="item.path !== 'console'"
            class="el-icon-close"
            @click="removeTab(item.path)"
          />
        </span>
      </div>
      <span class="tag-btn" v-if="tabLists.length >= 10">
        <el-dropdown @command="handleClose">
          <span class="el-dropdown-link">
            <i class="el-icon-arrow-down"></i>
          </span>
          <template slot="dropdown">
            <el-dropdown-menu>
              <el-dropdown-item command="clearOther">关闭其他</el-dropdown-item>
              <el-dropdown-item command="clearAll">全部关闭</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </span>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    tabList: {
      type: Array,
      default() {
        return [];
      },
    },
    activeItem: {
      type: Number,
      default() {
        return 0;
      },
    },
  },
  data() {
    return {
      activeTab: 0,
      tabId: 0,
      tabLists: [
        {
          id: "20",
          title: "控制台",
          path: "console",
        },
      ],
    };
  },
  watch: {
    tabList(newVal) {
      let obj = {
        class_id: 1,
        id: "20",
        title: "控制台",
        path: "console",
      };
      if (newVal) {
        this.tabLists = newVal;
        Array.prototype.copyUnshift = function () {
          var argLen = arguments.length,
            len = argLen + this.length;
          for (var i = len - 1; i >= 0; i--) {
            this[i] = i > argLen - 1 ? this[i - argLen] : arguments[i];
          }
          return len;
        };
        if (this.tabLists[0].title !== "控制台") {
          this.tabLists.copyUnshift(obj);
        }
        this.activeTab = this.tabLists.length - 1;
      }
    },
    activeItem(nVal) {
      if (nVal) {
        // console.log('监听到', nVal);
        this.activeTab = nVal;
      }
    },
  },
  methods: {
    // 操作el-dropdown
    handleClose(c) {
      if (c == "clearAll") {
        // 切换回首页,发送通知给首页
        this.tabLists = [
          {
            class_id: 1,
            id: "20",
            title: "控制台",
            path: "console",
          },
        ];
        this.$emit("clearAll", this.tabLists);
        this.activeTab = 0;
        this.changeTab(
          this.tabLists[0].path,
          this.activeTab,
          this.tabLists[0].id,
          this.tabLists[0].class_id
        );
      } else if (c == "clearOther") {
        // console.log('清除前', this.tabLists);
        // 过滤只剩下首页和当前激活
        this.tabLists = this.tabLists.filter(
          (tab) =>
            tab.path == "console" ||
            tab.path == this.tabLists[this.activeTab].path
        );
        this.$emit("clearAll", this.tabLists);
      }
      // 当前高亮的tab的index
      this.activeTab = this.tabLists.length - 1;
    },
    // 删除tab
    removeTab(e) {
      // console.log('删除的tab',e);

      this.$emit("handleTabsEdit", {
        router: e,
      });
    },
    // 切换tab
    changeTab(url, index, id, class_id) {
      // console.log('传进来的url',url);
      // console.log('传进来的index',index);
      // console.log('传进来的id',id);
      // console.log('传进来的class_id',class_id);
      this.tabId = "";
      this.tabId = class_id;
      // console.log('当前数组', this.tabLists);
      // console.log('当前大分类', this.tabId);
      // console.log('分类id', class_id);
      this.$router.push({ name: url, query: { pid: id } });
      this.activeTab = index;
      this.$emit("changeTabs", {
        idx: index,
        cid: this.tabId,
        fid: id,
      });
    },
  },
};
</script>
<style scoped>
.tab-wrapper {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  padding-left: 10px;
}
.tab {
  margin: 3px 5px;
  padding: 6px 10px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
}
.active {
  background-color: #fff !important;
  color: #409EFF !important;
  margin: 3px 5px;
  padding: 6px 10px;
  border-radius: 5px;
  font-size: 14px;
}
.f-tag-list {
  display: flex;
  background-color: #f3f4f5;
  position: absolute;
  align-items: center;
  top: 60px;
  padding: 3px 0 10px 5px;
  flex-wrap: wrap;
  right: 10px;
  height: 44px;
  z-index: 100;
  color: rgba(107, 114, 128);
  box-sizing: border-box;
}
.tag-btn {
  height: 31px;
  background-color: #fff;
  border-radius: 5px;
  margin-left: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 20px;
  margin-top: 2px;
  cursor: pointer;
  position: fixed;
  right: 10px;
}
</style>
