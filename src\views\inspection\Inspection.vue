<template>
  <div>
    <el-row :gutter="5" v-loading="loading" element-loading-text="Flyknit...">
      <el-col :span="24">
        <!-- 左侧布局 -->
        <el-col :span="10">
          <el-card shadow="hover">
            <el-row :gutter="5">
              <!-- 搜索区域 -->
              <el-col :span="24">
                <el-col :span="20">
                  <el-input type="text" ref="searchBox" v-model="formInline.code" placeholder="请扫描布票条码" clearable
                    @change="changeInputHandler" @input="inputUpperCase" @clear="clearHandler"
                    @keyup.enter.native="searchHandler" resize="both" :autofocus="true"></el-input>
                </el-col>
                <el-col :span="3">
                  <el-button type="primary" icon="el-icon-search" @click="searchHandler">搜索</el-button>
                </el-col>
              </el-col>
              <!-- 布票信息展示 -->
              <el-col :span="24" style="margin-top: 1rem">
                <el-col :span="24" style="
                    display: flex;
                    flex-direction: column;
                    border: 1px solid #eee;
                    padding: 1rem;
                  ">
                  <el-row>
                    <el-col :span="24" style="display: flex; align-items: center">
                      <i class="el-icon-s-grid" style="font-size: 1.5rem; margin-right: 10px"></i>
                      <h4 style="font-size: 1rem; letter-spacing: 1px">
                        布票信息
                      </h4>
                    </el-col>
                  </el-row>
                  <el-row :gutter="10" class="info-content" v-if="scanInfo && scanInfo.length > 0">
                    <el-col :span="8" class="info-item">
                      <span>布票号：{{ scanInfo[0].serial }}</span>
                    </el-col>
                    <el-col :span="6" class="info-item">
                      <span>机台：{{ scanInfo[0].ch }}</span>
                    </el-col>
                    <el-col :span="8" class="info-item">
                      <span>Model：{{ scanInfo[0].model }}</span>
                    </el-col>
                    <el-col :span="6" class="info-item">
                      <span>尺码：{{ scanInfo[0].cm }}</span>
                    </el-col>
                    <el-col :span="7" class="info-item">
                      <span>品名：{{ scanInfo[0].pm }}</span>
                    </el-col>
                    <el-col :span="8" class="info-item">
                      <span>排单日期：{{ scanInfo[0].sc_rq }}</span>
                    </el-col>
                  </el-row>
                  <el-empty v-else :image-size="210" :image="require('@/assets/image/info.png')"
                    description="请使用扫描枪扫描布票或输入布票号"></el-empty>
                </el-col>
                <!-- 图片区域 -->
                <el-col :span="24" style="
                    margin-top: 0.5rem;
                    padding: 20px 13px;
                    border: 1px solid #eee;
                  ">
                  <el-row :gutter="10">
                    <el-col :span="24" style="display: flex; align-items: center">
                      <i class="el-icon-picture" style="font-size: 1.5rem; margin-right: 10px"></i>
                      <h4 style="font-size: 1rem; letter-spacing: 1px">
                        产品图片
                      </h4>
                    </el-col>
                    <el-col :span="24" style="padding: 2rem 0">
                      <div style="margin: 0 auto; width: 24rem; height: auto">
                        <el-image v-if="scanInfo.length > 0 && scanInfo[0].model_img" style="width: 100%; height: 100%"
                          class="model-image" :src="scanInfo[0].model_img" :fit="cover" @click="
                            handlepreview(
                              scanInfo[0].model_img,
                              scanInfo[0].model
                            )
                            " lazy id="rotateImage">
                        </el-image>
                        <el-empty :image-size="210" :image="require('@/assets/image/info.png')" v-else
                          description="暂无相关Model图片信息,请联系管理员上传"></el-empty>
                      </div>
                    </el-col>
                    <!-- 图片操作按钮,上、右、下、左旋转-->
                    <el-col v-if="scanInfo.length > 0 && scanInfo[0].model_img" :span="24" style="
                        display: flex;
                        justify-content: center;
                        padding: 2rem 0 0 0;
                      ">
                      <el-button type="info" icon="el-icon-refresh" @click="chongzhi('rotateImage')"></el-button>
                      <el-button type="primary" icon="el-icon-refresh-right"
                        @click="xuanzhuan('rotateImage')"></el-button>
                      <el-button type="danger" icon="el-icon-right" @click="zuoyou('rotateImage')"></el-button>
                      <el-button type="warning" icon="el-icon-top" @click="shangxia('rotateImage')"></el-button>
                    </el-col>
                  </el-row>
                </el-col>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
        <!-- 右侧布局 -->
        <el-col :span="14" v-if="scanInfo && scanInfo.length > 0">
          <el-card shadow="hover">
            <el-row :gutter="10">
              <el-col :span="24" class="inspection-content">
                <div style="display: flex">
                  <i class="el-icon-s-order" style="font-size: 1.4rem; margin-right: 10px"></i>
                  <h4 style="font-size: 1rem; letter-spacing: 1px">
                    当前巡检标准：
                  </h4>
                </div>
                <el-col :span="18">
                  <el-radio v-for="(item, index) in scanInfo[0].standard.slice(
                    0,
                    scanInfo[0].has_standard
                  )" :key="index" :label="`标准${convertNumberToChinese(index + 1)}`" v-model="formInline.type"
                    @input="changeHandler($event, index)">
                  </el-radio>
                </el-col>
                <!-- 打开草稿箱 -->
                <el-col :span="4">
                  <el-button type="warning" icon="el-icon-folder-checked" size="medium" @click="openDrawer"
                    v-show="draftBox.length > 0">草稿箱</el-button>
                </el-col>
              </el-col>
              <div>
                <el-form :model="ruleForm" ref="ruleForm">
                  <div v-for="(item, index) in scanInfo" :key="index" style="
                      display: flex;
                      flex-wrap: wrap;
                      border: 1px solid #eee;
                      padding: 1rem;
                    ">
                    <el-form-item label="" v-for="(item1, index1) in item.standard" :key="index1"
                      style="width: calc(100% / 2.1); margin-right: 1.1rem">
                      <el-input type="number" v-model="item1.value" :placeholder="`请输入测量点${item1.name}的数值`"
                        @input="inputHandler(item1, index1)" @change="inputHandler(item1, index1)" clearable
                        @clear="clearData(item1, index1)"></el-input>
                      <el-col :span="24" style="height: 20px">
                        <el-col :span="1" class="location-content">
                          <span>{{ item1.name }}</span>
                        </el-col>
                        <el-col :span="12" class="standard-content">
                          <span v-for="(item2, index2) in item1.data" :key="index2">{{
                            index2 === 0
                              ? "Min"
                              : index2 === 1
                                ? "Target"
                                : "Max"
                          }}
                            : {{ parseInt(item2) }}</span>
                        </el-col>
                        <el-col :span="10" style="
                            display: flex;
                            justify-content: flex-end;
                            padding-top: 0.8rem;
                          ">
                          <el-tag v-show="item1.st" size="mini" effect="dark"
                            :type="item1.st === 'Pass' ? 'success' : 'danger'">{{ item1.st }}</el-tag>
                        </el-col>
                      </el-col>
                    </el-form-item>
                  </div>
                  <!-- 长短脚区域 -->
                  <el-col :span="24" style="
                      display: flex;
                      justify-content: space-around;
                      align-items: center;
                      padding: 0.2rem 0;
                      border: 1px solid #eee;
                    ">
                    <el-col :span="10" style="display: flex; align-items: center">
                      <span class="el-icon-question" style="font-size: 1.1rem; margin-right: 0.2rem"></span>
                      <span style="
                          font-size: 1rem;
                          font-weight: bold;
                          letter-spacing: 1px;
                        ">是否长短脚</span>
                      <el-radio-group v-model="formInline.status" style="margin-left: 20px">
                        <el-radio label="是"></el-radio>
                        <el-radio label="否"></el-radio>
                      </el-radio-group>
                    </el-col>
                    <el-col :span="14" style="
                        padding: 20px 0px;
                        display: flex;
                        align-items: center;
                      ">
                      <span class="el-icon-s-tools" style="font-size: 1.1rem; margin-right: 0.2rem"></span>
                      <span style="
                          font-size: 1rem;
                          font-weight: bold;
                          letter-spacing: 1px;
                        ">调机方式</span>
                      <el-radio-group v-model="formInline.way" style="margin-left: 10px">
                        <el-radio label="BM: +5 BL: -5"></el-radio>
                        <el-radio label="BM: -5 BL: +5"></el-radio>
                      </el-radio-group>
                    </el-col>
                  </el-col>
                  <!-- 提交按钮区域 -->
                  <el-col :span="24" style="padding: 1.5rem 0 1.5rem 0; border: 1px solid #eee">
                    <el-col :span="24">
                      <div style="display: flex; align-items: center">
                        <span class="el-icon-s-order" style="font-size: 1.2rem; margin-right: 0.2rem"></span>
                        <span style="
                            font-size: 1rem;
                            font-weight: bold;
                            letter-spacing: 1px;
                          ">巡检员备注</span>
                      </div>
                      <div style="padding: 1rem 0">
                        <el-input type="textarea" v-model="formInline.remarks" rows="2" placeholder="备注信息"></el-input>
                      </div>
                    </el-col>
                    <!-- 输入机台号 -->
                    <el-col :span="24">
                      <div style="display: flex; align-items: center">
                        <span class="el-icon-s-platform" style="font-size: 1.2rem; margin-right: 0.2rem"></span>
                        <span style="
                            font-size: 1rem;
                            font-weight: bold;
                            letter-spacing: 1px;
                          ">机台编号</span>
                      </div>
                      <div style="padding: 1rem 0">
                        <el-input type="textarea" v-model="formInline.ch" rows="2" maxlength="4" placeholder="请输入机台号"
                          @input="inputChangeHandler"></el-input>
                      </div>
                    </el-col>
                    <!-- <el-col :span="24">
                      <div style="display: flex; align-items: center">
                        <span class="el-icon-s-promotion" style="font-size: 1.2rem; margin-right: 0.2rem"></span>
                        <span style="
                            font-size: 1rem;
                            font-weight: bold;
                            letter-spacing: 1px;
                          ">推送类型</span>
                      </div>
                      <el-radio-group v-model="formInline.sendType" style="margin: 1rem">
                        <el-radio label="调机参数"></el-radio>
                        <el-radio label="推送备注"></el-radio>
                        <el-radio label="推送全部"></el-radio>
                      </el-radio-group>
                    </el-col> -->
                    <el-col :span="24" style="padding: 1rem 0">
                      <!-- 保存到草稿箱 -->
                      <el-col :span="8">
                        <el-button :disabled="listCode.length > 0 ? false : true" type="warning"
                          style="width: 100%; padding: 1rem 0" icon="el-icon-folder-checked" size="medium"
                          @click="saveHandler">保存到草稿箱</el-button>
                      </el-col>
                      <el-col :span="8">
                        <el-button type="danger" style="width: 100%; padding: 1rem 0" @click="noticehandler"
                          icon="el-icon-message-solid" size="medium" :disabled="filterList.length > 0 ||
                            formInline.remarks ||
                            formInline.status == '是'
                            ? false
                            : true
                            ">推送保全</el-button>
                      </el-col>
                      <el-col :span="8">
                        <el-button :disabled="listCode.length > 0 ? false : true" type="primary"
                          style="width: 100%; padding: 1rem 0" icon="el-icon-right" size="medium"
                          @click="onSubmit">通过</el-button>
                      </el-col>
                    </el-col>
                  </el-col>
                </el-form>
              </div>
            </el-row>
          </el-card>
        </el-col>
        <!-- 没有扫描之前 -->
        <el-col :span="14" v-else>
          <el-card shadow="hover" style="
              display: flex;
              flex-direction: column;
              height: calc(100vh - 120px);
            ">
            <el-col :span="24" style="display: flex; align-items: center">
              <i class="el-icon-s-platform" style="font-size: 1.5rem; margin-right: 10px"></i>
              <h4 style="font-size: 1rem; letter-spacing: 1px">巡检员操作区</h4>
            </el-col>
            <el-col :span="24" style="margin-top: 25%">
              <el-empty description="请使用扫描枪连接电脑扫描布票或输入布票号" :image="require('@/assets/image/5D1O.gif')"
                :image-size="250"></el-empty>
            </el-col>
          </el-card>
        </el-col>
      </el-col>
    </el-row>
    <!-- 大图展开 -->
    <el-dialog :title="title" center :modal="false" :visible.sync="dialogVisible">
      <div class="image-box">
        <img width="100%" :src="dialogImageUrl" alt="加载失败" />
      </div>
    </el-dialog>
    <!-- 草稿箱 -->
    <el-drawer :title="drawerTitle" :visible.sync="drawer" :direction="direction" :before-close="handleClose">
      <div style="height: calc(100vh - 75px); overflow-y: scroll">
        <div style="padding: 1rem; background-color: #f3f4f5" v-for="(item, index) in draftBox" :key="index">
          <el-descriptions :title="item.serial" :column="columns" style="
              background-color: #feffff;
              padding: 10px 15px;
              border-radius: 7px;
              box-sizing: border-box;
              overflow: hidden;
            ">
            <template slot="extra">
              <el-button type="primary" icon="el-icon-refresh-right" size="small"
                @click="chooseHandler(item)">再次巡检</el-button>
              <el-button type="danger" icon="el-icon-delete" size="small" @click="deleteStorage(index)">删除</el-button>
            </template>
            <el-descriptions-item label="Model">{{
              item.model
            }}</el-descriptions-item>
            <el-descriptions-item label="尺码">{{
              item.cm
            }}</el-descriptions-item>
            <el-descriptions-item label="品名">{{
              item.pm
            }}</el-descriptions-item>
            <el-descriptions-item label="机台">
              {{ item.formInline.ch }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import { mapState } from "vuex";
//节流防抖方法封装
let debounce = (invoke, delay = 1000) => {
  let timeout = null;
  return function (...rest) {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      invoke.apply(this, rest);
    }, delay);
  };
};
export default {
  name: "Inspection",
  data() {
    return {
      columns: 2, // 展示的行数
      drawer: false, // 是否打开抽屉
      direction: "rtl", // 打开方向
      loading: true, // 加载
      xuanzhuanNumCar: 0, // 旋转
      dialogVisible: false, // 展示大图
      dialogImageUrl: "", // 大图地址
      formInline: {
        way: "", // 调机方式
        type: "标准一",
        status: "否", // 是否长短脚
        code: "", // 布票号
        remarks: "", // 备注
        ch: "", // 机台号
      },
      currentStandard: 0,
      autofocus: true, // 自动聚焦
      totalStandard: 1,
      scanInfo: [], // 扫描信息
      listCode: [], // 测量值
      sortList: [], // 排序后的测量值
      list: [], // 测量点
      ruleForm: {
        name: "",
        code: "",
      },
      cover: "fit",
      title: "",
      filterList: [], // 推送保全过略掉Pass的项
      src: "@/assets/image/yes.png",
      draftBox: [], // 草稿
      drawerTitle: "", // 抽屉标题
    };
  },
  computed: {
    ...mapState({
      User: (state) => state.users,
    }),
  },
  created() {
    // input框聚焦
    this.$nextTick(() => {
      this.$refs.searchBox.focus();
    });
    // 获取草稿箱数据
    this.getDraftBoxList();
  },
  mounted() {
    setTimeout(() => {
      this.loading = false;
    }, 500);
  },
  methods: {
    // 监听输入，将布票号转为大写
    inputUpperCase() {
      this.formInline.code = this.formInline.code.toUpperCase();
    },
    // 监听输入，将车号转为大写
    inputChangeHandler() {
      this.formInline.ch = this.formInline.ch.toUpperCase();
    },
    // 根据index删除草稿
    deleteStorage(idx) {
      this.draftBox.forEach((item, index) => {
        if (index === idx) {
          this.draftBox.splice(idx, 1);
        }
      });
      // 更改标题
      this.drawerTitle = `草稿箱(${this.draftBox.length})条`;
      localStorage.setItem("sepc-info", JSON.stringify(this.draftBox));
    },
    // 获取草稿箱内容
    getDraftBoxList() {
      const data = localStorage.getItem("sepc-info");
      if (data && data.length > 0) {
        this.draftBox = JSON.parse(data);
      } else {
        this.draftBox = []; // 如果localStorage中没有数据，初始化this.draftBox为一个空数组
      }
    },
    // 保存草稿事件
    saveHandler() {
      // 数据验证
      if (!this.scanInfo || !this.scanInfo[0]) {
        this.$notify({
          title: "错误",
          message: "没有可保存的数据",
          type: "error",
        });
        return;
      }

      // 如果没有填写机台
      if (!this.formInline.ch) {
        return this.$notify({
          title: "错误",
          message: "请填写机台号",
          type: "error",
        });
      }

      const obj = {
        serial: this.scanInfo[0].serial,
        model: this.scanInfo[0].model,
        cm: this.scanInfo[0].cm,
        pm: this.scanInfo[0].pm,
        formInline: this.formInline,
        filterList: this.filterList,
        listCode: this.listCode,
      };

      // 保存到草稿箱
      this.draftBox.push(obj);
      // 保存到本地存储
      localStorage.setItem("sepc-info", JSON.stringify(this.draftBox));
      // 清除布票信息
      this.formInline = {
        way: "", // 调机方式
        type: "标准一",
        status: "否", // 是否长短脚
        code: "", // 布票号
        remarks: "", // 备注
        ch: "", // 机台号
      };
      this.scanInfo = [];
      this.list = [];
      this.listCode = [];
      this.sortList = [];
      this.filterList = [];
      this.$notify({
        title: "成功",
        message: "保存到草稿箱成功",
        type: "success",
      });
    },
    // 选择处理器
    chooseHandler(e) {
      // 从事件对象提取相关信息
      const { formInline, filterList, listCode } = e;
      // 更新当前对象的状态
      this.formInline = formInline;
      this.filterList = filterList;
      this.listCode = listCode;
      // 遍历并更新scanInfo的标准值
      this.scanInfo.forEach((item, index1) => {
        item.standard.forEach((stdItem, index2) => {
          this.listCode.forEach((codeItem) => {
            if (stdItem.name === codeItem.name) {
              this.$set(
                this.scanInfo[index1].standard[index2],
                "value",
                codeItem.num
              );
            }
          });
          // 再次计算值
          setTimeout(() => {
            this.calculator(stdItem);
          }, 500);
        });
      });
      // 关闭抽屉
      this.drawer = false;
    },
    // 巡检计算
    calculator(e) {
      if (!e) {
        return false;
      }
      this.list.push(e);
      this.code = e.value.replace(/[, ]/g, "");
      // 如果不为整数则提示输入错误
      if (!/^\d+$/.test(this.code)) {
        this.$notify({
          title: "错误",
          message: "请输入正确的测量值",
          type: "error",
        });
        e.value = "";
        return;
      }
      this.name = e.name;
      if (!this.code) {
        return;
      }

      const object = {};
      object.name = this.name;
      object.num = this.code;
      // 找出在this.listCode中名字相同的项目
      let sameNameItem = this.listCode.find((i) => i.name === object.name);
      // 如果找到了就更新
      if (sameNameItem) {
        sameNameItem.num = this.code;
      } else {
        // 否则添加新的项目
        this.listCode.push(object);
      }

      const data = e.data;
      data.sort((a, b) => a - b);
      // console.log("排序后", data);
      const obj = {
        name: this.name,
      };

      let st = "";
      if (
        this.check(
          parseInt(this.code),
          parseInt(data[0]),
          parseInt(data[data.length - 1])
        )
      ) {
        // console.log("在标准区间");
        st = "Pass";
      } else if (this.code < data[0] || this.code > data[data.length - 1]) {
        // console.log(this.code < data[0] ? "小于最小值" : "大于最大值");
        st = parseInt(data[1] - this.code);
      }

      this.handleItemList(this.name, st, obj);
      // 过略掉this.sortList中num为Pass的项
      this.filterList = this.sortList.filter((item) => item.num !== "Pass");
      // console.log("原始测量值:", this.listCode);
      // console.log("计算结果:", this.sortList);
      // console.log("通知保全:", this.filterList);
    },
    // 关闭草稿箱
    handleClose(done) {
      done();
    },
    // 打开草稿箱
    openDrawer() {
      this.drawer = !this.drawer;
      // 获取草稿箱内容
      this.getDraftBoxList();
      // 更改标题
      this.drawerTitle = `草稿箱(${this.draftBox.length})条`;
    },
    // 清除按钮事件
    clearHandler() {
      this.scanInfo = [];
      this.formInline.code = "";
      this.formInline.ch = "";
      this.formInline.remarks = "";
      this.list = [];
      this.listCode = [];
      this.sortList = [];
      this.filterList = [];
    },
    // 搜索按钮事件
    searchHandler() {
      if (!this.formInline.code) {
        return this.$notify({
          title: "提示",
          message: "请扫描或输入布票号",
          type: "info",
        });
      }
      // 如果布票号长度不足11位则提示
      if (this.formInline.code.length !== 11) {
        this.formInline.code = "";
        return this.$notify({
          title: "错误",
          message: "不是有效的布票号，请重新扫描或输入",
          type: "error",
        });
      }
      // 获取布票信息
      this.getScanInfo();
    },
    // 扫描输入框事件
    changeInputHandler: debounce(function (e) {
      if (!e) {
        return false;
      }
      // 判断是否是布票号S开头
      if (!e.startsWith("S") && e.length !== 11) {
        this.formInline.code = "";
        return this.$notify({
          title: "错误",
          message: "不是有效的布票号，请重新扫描或输入",
          type: "error",
        });
      }
      // 如果布票号长度不足11位则提示
      if (e.length !== 11) {
        this.formInline.code = "";
        return this.$notify({
          title: "错误",
          message: "不是有效的布票号，请重新扫描或输入",
          type: "error",
        });
      }
      this.formInline.code = e;
      this.getScanInfo();
      this.loading = true;
      this.autofocus = false;
    }, 500),
    // 根据serial获取布票信息
    async getScanInfo() {
      try {
        const res = await this.$http.getSerialInfo({
          serial: this.formInline.code,
        });
        if (res.code !== 200) {
          return this.$notify({
            title: "错误",
            message: res.message,
            type: "error",
          });
        }
        const { list } = res.data;
        this.scanInfo = list;
        // 给this.scanInfo中的standard下面的data进行排序
        const order = [
          "A",
          "H",
          "A+H",
          "BM",
          "BM1",
          "BL",
          "CM",
          "CL",
          "DM",
          "DL",
          "EM",
          "EL",
          "FM",
          "FL",
          "B",
          "C",
          "D",
          "E",
          "F",
          "G",
          "H",
          "I",
          "J",
          "K",
          "AL",
          "AR",
          "KM",
          "KL",
          "JM",
          "JL",
          "GM",
          "GL",
          "HL",
          "EC",
          "H1",
          "H2",
          "A1",
          "FC",
          "I1",
          "N",
          "O",
          "NM",
          "NL",
          "T",
          "HR",
          "IR",
          "M1",
          "M2",
          "NM1",
          "NL1",
          "C1",
          "DM1",
          "DL1",
          "G1",
          "S",
          "EC",
          "A+F",
        ];

        this.scanInfo.forEach((item) => {
          item.standard.sort(
            (a, b) => order.indexOf(a.name) - order.indexOf(b.name)
          );
          item.standard.forEach((standardItem) => {
            standardItem.data.sort((a, b) => a - b);
          });
        });

        this.loading = false;
      } catch (error) {
        this.loading = false;
        return false;
      }
    },
    // 清除输入框的值
    clearData(item, index) {
      item.value = "";
      item.st = "";
      this.list.forEach((res) => {
        if (res.name === item.name) {
          this.list.splice(index, 1);
        }
      });
      this.listCode.forEach((res) => {
        if (res.name === item.name) {
          this.listCode.splice(index, 1);
        }
      });
      this.sortList.forEach((res) => {
        if (res.name === item.name) {
          this.sortList.splice(index, 1);
        }
      });
      this.filterList = this.sortList.filter((item) => item.num !== "Pass");
    },
    // 展开大图
    handlepreview(file, name) {
      this.dialogImageUrl = file;
      this.title = name;
      this.dialogVisible = true;
    },
    // 重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    // 切换标准事件
    changeHandler(e, index) {
      this.formInline.type = e;
      this.currentStandard = index;
      // console.log("type", this.formInline.type);
      // console.log("current", this.currentStandard);
      // console.log("scanInfo", this.scanInfo);
      // 切换标准api
      this.loading = true;
      this.changeStandard();
    },
    //  切换标准接口
    async changeStandard() {
      try {
        const res = await this.$http.getStandard({
          current: this.currentStandard,
          model: this.scanInfo[0].serials,
          pm: this.scanInfo[0].pm,
          size: this.scanInfo[0].cm,
          origin: this.scanInfo[0].origin,
        });
        if (res.code !== 200) {
          return this.$notify({
            title: "错误",
            message: res.message,
            type: "error",
          });
        }
        const { list } = res.data;
        // 替换this.scanInfo中的standard,刷新视图
        this.$set(this.scanInfo[0], "standard", list.standard);
        this.loading = false;
        this.$notify({
          title: "成功",
          message: `切换${this.formInline.type}成功`,
          type: "success",
        });
      } catch (error) {
        return false;
      }
    },
    // 输入框事件
    handleItemList(name, st, obj) {
      // 更新 list 中对应的 st 值
      this.list.forEach((res) => {
        if (res.name === name) {
          res.st = st;
        }
      });

      // 查询重复项并处理
      const checkName = this.sortList.indexOf(name);
      if (checkName) {
        // 删除之前的项
        this.sortList.some((item, i) => {
          if (item.name === name) {
            this.sortList.splice(i, 1);
          }
        });
      }
      // 无论是否重复都添加当前项
      obj.num = st;
      this.sortList.push(obj);
    },
    // 巡检计算
    inputHandler: debounce(function (e, index) {
      if (!e) {
        return false;
      }
      this.list.push(e);
      this.code = e.value.replace(/[, ]/g, "");
      // 如果不为整数则提示输入错误
      if (!/^\d+$/.test(this.code)) {
        this.$notify({
          title: "错误",
          message: "请输入正确的测量值",
          type: "error",
        });
        e.value = "";
        return;
      }
      this.name = e.name;
      if (!this.code) {
        return;
      }

      const object = {};
      object.name = this.name;
      object.num = this.code;

      // 找出在this.listCode中名字相同的项目下标
      let sameNameIndex = this.listCode.findIndex(
        (i) => i.name === object.name
      );
      // 如果找到了就移除
      if (sameNameIndex !== -1) {
        this.listCode.splice(sameNameIndex, 1);
      }
      // 再添加新的项目
      this.listCode.push(object);

      const data = e.data;
      data.sort((a, b) => a - b);
      // console.log("排序后", data);
      const obj = {
        name: this.name,
      };

      let st = "";
      if (
        this.check(
          parseInt(this.code),
          parseInt(data[0]),
          parseInt(data[data.length - 1])
        )
      ) {
        // console.log("在标准区间");
        st = "Pass";
      } else if (this.code < data[0] || this.code > data[data.length - 1]) {
        // console.log(this.code < data[0] ? "小于最小值" : "大于最大值");
        st = parseInt(data[1] - this.code);
      }

      this.handleItemList(this.name, st, obj);
      // 过略掉this.sortList中num为Pass的项
      this.filterList = this.sortList.filter((item) => item.num !== "Pass");
      // console.log("原始测量值:", this.listCode);
      // console.log("计算结果:", this.sortList);
      // console.log("通知保全:", this.filterList);
    }, 500),
    //判断一个数在区间
    check(str, m, n) {
      var re = /(\d+)/g;
      while (re.exec(str)) {
        var int = parseInt(RegExp.$1);
        if (int < m || int > n) return false;
      }
      return true;
    },
    // 小写数字转换为中文
    convertNumberToChinese(num) {
      const chineseNumbers = ["一", "二", "三", "四", "五"];
      return chineseNumbers[num - 1];
    },
    // 通过事件
    async onSubmit() {
      if (!this.listCode || this.listCode.length === 0) {
        return this.$notify({
          title: "错误",
          message: "测量值不能为空",
          type: "error",
        });
      }
      if (!this.sortList || this.sortList.length === 0) {
        return this.$notify({
          title: "错误",
          message: "计算结果不能为空",
          type: "error",
        });
      }
      // 如果选择了长短脚则调机方式不能为空
      if (this.formInline.status === "是" && !this.formInline.way) {
        return this.$notify({
          title: "错误",
          message: "请选择调机方式",
          type: "error",
        });
      }
      // 如果没有填写机台
      if (!this.formInline.ch) {
        return this.$notify({
          title: "错误",
          message: "请填写机台号",
          type: "error",
        });
      }
      this.loading = true;
      try {
        const res = await this.$http.submitOrder({
          serial: this.formInline.code,
          machine_number: this.formInline.ch,
          model_name: this.scanInfo[0].model,
          pm: this.scanInfo[0].pm,
          size: this.scanInfo[0].cm,
          measure_params: this.listCode,
          change_params: this.sortList,
          type: 0,
          measure_name: this.User.nickname,
          shift: this.User.shift,
          is_long_short: this.formInline.status === "是" ? 1 : 0,
          long_short_methods: this.formInline.way,
          remarks: this.formInline.remarks,
        });
        if (res.code !== 200) {
          return this.$notify({
            title: "错误",
            message: res.message,
            type: "error",
          });
        }
        // 关闭聚焦
        this.$nextTick(() => {
          this.$refs.searchBox.blur();
        });
        // 删除本地存储中的草稿
        this.deleteStorageById(this.scanInfo[0].serial);
        setTimeout(() => {
          this.loading = false;
          this.formInline.code = "";
          this.formInline.ch = "";
          this.scanInfo = [];
          this.sortList = [];
          this.list = [];
          this.listCode = [];
          this.formInline.status = "否";
          this.formInline.way = "";
          this.formInline.remarks = "";
          this.clearHandler();
          this.$refs.searchBox.focus();
          this.$notify({
            title: "成功",
            message: "提交成功",
            type: "success",
          });
        }, 500);
      } catch (error) {
        this.loading = false;
        return false;
      }
    },
    // 通知保全
    async noticehandler() {
      if (!this.listCode || this.listCode.length === 0) {
        return this.$notify({
          title: "错误",
          message: "测量值不能为空",
          type: "error",
        });
      }
      if (!this.sortList || this.sortList.length === 0) {
        return this.$notify({
          title: "错误",
          message: "计算结果不能为空",
          type: "error",
        });
      }
      // 如果选择了长短脚则调机方式不能为空
      if (this.formInline.status === "是" && !this.formInline.way) {
        return this.$notify({
          title: "错误",
          message: "请选择调机方式",
          type: "error",
        });
      }
      // 如果没有填写机台
      if (!this.formInline.ch) {
        return this.$notify({
          title: "错误",
          message: "请填写机台号",
          type: "error",
        });
      }
      this.loading = true;
      try {
        const res = await this.$http.submitOrder({
          serial: this.formInline.code,
          machine_number: this.formInline.ch,
          model_name: this.scanInfo[0].model,
          pm: this.scanInfo[0].pm,
          size: this.scanInfo[0].cm,
          measure_params: this.listCode,
          change_params: this.sortList,
          type: 1,
          measure_name: this.User.nickname,
          shift: this.User.shift,
          is_long_short: this.formInline.status === "是" ? 1 : 0,
          long_short_methods: this.formInline.way,
          remarks: this.formInline.remarks,
        });
        if (res.code !== 200) {
          return this.$notify({
            title: "错误",
            message: res.message,
            type: "error",
          });
        }
        // 关闭聚焦
        this.$nextTick(() => {
          this.$refs.searchBox.blur();
        });
        // 删除本地存储中的草稿
        this.deleteStorageById(this.scanInfo[0].serial);
        setTimeout(() => {
          this.loading = false;
          this.formInline.code = "";
          this.formInline.ch = "";
          this.scanInfo = [];
          this.sortList = [];
          this.list = [];
          this.listCode = [];
          this.formInline.status = "否";
          this.formInline.way = "";
          this.formInline.remarks = "";
          this.clearHandler();
          this.$refs.searchBox.focus();
          this.$notify({
            title: "成功",
            message: "提交成功",
            type: "success",
          });
        }, 500);
      } catch (error) {
        this.loading = false;
        return false;
      }
    },
    // 根据id删除草稿
    deleteStorageById(serial) {
      this.draftBox = this.draftBox.filter((item) => item.serial !== serial);
      localStorage.setItem("sepc-info", JSON.stringify(this.draftBox));
    },
    //翻转图片
    chongzhi(elename) {
      this.xuanzhuanNumCar = this.xuanzhuanNumCar + 1;
      var box = document.getElementById(elename);
      box.style.transform = "rotateZ(" + 0 + "deg)";
      box.style.transform = "rotateX(" + 0 + "deg)";
      box.style.transform = "rotateY(" + 0 + "deg)";
    },
    xuanzhuan(elename) {
      this.xuanzhuanNumCar = this.xuanzhuanNumCar + 1;
      var box = document.getElementById(elename);
      box.style.transform = "rotateZ(" + 90 * this.xuanzhuanNumCar + "deg)";
    },
    zuoyou(elename) {
      this.xuanzhuanNumCar = this.xuanzhuanNumCar + 1;
      var box = document.getElementById(elename);
      box.style.transform = "rotateY(" + 180 * this.xuanzhuanNumCar + "deg)";
    },
    shangxia(elename) {
      this.xuanzhuanNumCar = this.xuanzhuanNumCar + 1;
      var box = document.getElementById(elename);
      box.style.transform = "rotateX(" + 180 * this.xuanzhuanNumCar + "deg)";
    },
  },
};
</script>
<style scoped lang="scss">
.el-image {
  cursor: pointer;
}

.image-box {
  margin: 0 auto;
  width: 30rem;
  height: auto;
  box-sizing: border-box;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
  }
}

.model-image {
  width: 100%;
  height: 100%;
  flex-shrink: 0;
  border-radius: 10px;
}

.inspection-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 20px 0;
}

.info-content {
  padding-top: 1rem;

  .info-item {
    font-size: 0.85rem;
    padding: 10px 0;
    color: #666;
  }
}

.location-content {
  margin-left: -1rem;

  span {
    font-size: 0.9rem;
    color: #666;
    margin: 0 5px;
    font-weight: bold;
  }
}

.standard-content {
  margin-left: 1rem;

  span {
    font-size: 0.9rem;
    color: #666;
    margin-left: 10px;
  }
}
</style>