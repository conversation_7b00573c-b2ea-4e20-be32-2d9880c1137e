import{e as o,u as l}from"./useEcharts-57db09d5.js";import{i as e,k as c,o as n,g as p}from"./index-444b28c3.js";const u={__name:"progressBar",setup(f){const a=e(),s=e([100]),i=e([80]);return c(()=>{let t=o.init(a.value),r={grid:{top:0,bottom:0,left:0,right:0},xAxis:{show:!1,type:"value",boundaryGap:[0,0]},yAxis:[{type:"category",data:[""],axisLine:{show:!1},axisTick:[{show:!1}]}],series:[{name:"金额",type:"bar",zlevel:1,itemStyle:{borderRadius:300,color:new o.graphic.LinearGradient(1,0,0,1,[{offset:1,color:"rgba(123, 80, 201, 1)"},{offset:0,color:"rgba(33, 150, 243, 0.6)"}])},barWidth:20,data:i.value},{name:"背景",type:"bar",barWidth:20,barGap:"-100%",data:s.value,itemStyle:{color:"rgba(28, 128, 213, 0.19)",borderRadius:30}}]};l(t,r)}),(t,r)=>(n(),p("div",{ref_key:"echartsRef",ref:a,style:{height:"50px",width:"100%"}},null,512))}};export{u as default};
