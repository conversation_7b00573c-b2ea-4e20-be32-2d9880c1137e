/*
 * @Author: flyknit <EMAIL>
 * @Date: 2024-04-09 09:02:42
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-07-15 10:49:43
 * @FilePath: \electronic-filed:\gitee\backend-demo\src\config\config.js
 * @Description: 
 * 
 * Copyright (c) 2024 by ${<EMAIL>}, All Rights Reserved. 
 */
// 配置公共的后端api地址
const baseURL = "http://*************:3000/api/v1";
// 配置公共的请求头
const headers = {
  "Content-Type": "application/json;charset=UTF-8",
  Authorization: `Bearer ${localStorage.getItem("accessToken")}`,
};
// 配置图片上传地址接口
const uploadURL = baseURL + "/upload";
// 后台配置的excel上传地址接口
const uploadsURL = baseURL + "/uploadExcel";
// 配置图片前缀
const imgPrefix = baseURL + "/";

// 配置请求超时时间
const timeout = 10000;

// 导出配置
export default {
  baseURL,
  headers,
  uploadURL,
  uploadsURL,
  imgPrefix,
  timeout,
};
