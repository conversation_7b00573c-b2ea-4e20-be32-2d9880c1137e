<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-08-16 09:26:00
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-16 10:30:47
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\device\YearAnalyse.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-card>
            <!-- 头部，返回按钮，标题为年度巡检结论分析 -->
            <div slot="header">
                <el-row>
                    <el-col :span="10">
                        <i class="el-icon-back"></i> <el-divider direction="vertical"></el-divider> <span
                            class="card-back-text" @click="reTurn">返回上一页</span>
                    </el-col>
                    <el-col :span="14">
                        <span class="card-header-text">设备巡检年度结论分析</span>
                    </el-col>
                </el-row>
            </div>
            <!-- 折线图区域 -->
            <div id="main" style="width:100%;height:400px"></div>
            <!-- 漏斗图区域 -->
            <div id="main2" style="width:100%;height:400px;padding:40px 0"></div>
        </el-card>
    </div>
</template>
<script>
import * as echarts from 'echarts';
export default {
    data() {
        return {

        }
    },
    mounted() {
        this.initEcharts();
    },
    methods: {
        // 初始化echarts
        initEcharts() {
            // 基于准备好的dom，初始化echarts实例
            const myChart = echarts.init(document.getElementById('main'));
            const myChart2 = echarts.init(document.getElementById('main2'));
            let option = null;
            let option2 = null;
            // 绘制图表
            option = {
                title: {
                    text: ''
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['织造车间', '整理车间', '打样车间', '综合维修']
                },
                label: {
                    // 柱图头部显示值
                    show: true,
                    position: "top",
                    color: "#000",
                    fontSize: "14px",
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                toolbox: {
                    show: true,
                    feature: {
                        dataView: { show: true, readOnly: false },
                        magicType: { show: true, type: ['line', 'bar'] },
                        restore: { show: true },
                        saveAsImage: { show: true }
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '织造车间',
                        type: 'line',
                        data: [88, 87, 86, 92, 98, 98.5, 76],
                        lineStyle: {
                            width: 10, // 设置线条粗细
                        },
                        markPoint: {
                            itemStyle: {
                                color: 'red', // 设置标记点的颜色
                            },
                            label: {
                                color: '#feffff', // 设置标记点的字体颜色
                            },
                            data: [
                                { type: 'max', name: 'Max' },
                                { type: 'min', name: 'Min' }
                            ]
                        },
                        markLine: {
                            data: [{ type: 'average', name: 'Avg' }]
                        }
                    },
                    {
                        name: '整理车间',
                        type: 'line',
                        data: [92, 93, 96, 95.5, 97, 98.5, 82],
                        lineStyle: {
                            width: 10, // 设置线条粗细
                        },
                        markPoint: {
                            itemStyle: {
                                color: 'red', // 设置标记点的颜色
                            },
                            label: {
                                color: '#feffff', // 设置标记点的字体颜色
                            },
                            data: [
                                { type: 'max', name: 'Max' },
                                { type: 'min', name: 'Min' }
                            ]
                        },
                        markLine: {
                            data: [{ type: 'average', name: 'Avg' }]
                        }
                    },
                    {
                        name: '打样车间',
                        type: 'line',
                        data: [86.5, 89.5, 88, 94, 93, 96, 91],
                        lineStyle: {
                            width: 10, // 设置线条粗细
                        },
                        markPoint: {
                            itemStyle: {
                                color: 'red', // 设置标记点的颜色
                            },
                            label: {
                                color: '#feffff', // 设置标记点的字体颜色
                            },
                            data: [
                                { type: 'max', name: 'Max' },
                                { type: 'min', name: 'Min' }
                            ]
                        },
                        markLine: {
                            data: [{ type: 'average', name: 'Avg' }]
                        }
                    },
                    {
                        name: '综合维修',
                        type: 'line',
                        data: [88, 87, 86.5, 91.5, 78, 84, 91],
                        lineStyle: {
                            width: 10, // 设置线条粗细
                        },
                        markPoint: {
                            itemStyle: {
                                color: 'red', // 设置标记点的颜色
                            },
                            label: {
                                color: '#feffff', // 设置标记点的字体颜色
                            },
                            data: [
                                { type: 'max', name: 'Max' },
                                { type: 'min', name: 'Min' }
                            ]
                        },
                        markLine: {
                            data: [{ type: 'average', name: 'Avg' }]
                        }
                    }
                ]
            };
            option2 = {
                title: {
                    text: '车间设备巡检年度排名(排名规则：年度平均分)'
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b} : {c}%'
                },
                toolbox: {
                    feature: {
                        dataView: { readOnly: false },
                        restore: {},
                        saveAsImage: {}
                    }
                },
                legend: {
                    data: ['织造车间', '整理车间', '打样车间', '综合维修']
                },
                series: [
                    {
                        name: 'Funnel',
                        type: 'funnel',
                        left: '10%',
                        top: 60,
                        bottom: 60,
                        width: '80%',
                        min: 0,
                        max: 100,
                        minSize: '0%',
                        maxSize: '100%',
                        sort: 'descending',
                        gap: 2,
                        label: {
                            show: true,
                            position: 'inside'
                        },
                        labelLine: {
                            length: 10,
                            lineStyle: {
                                width: 1,
                                type: 'solid'
                            }
                        },
                        itemStyle: {
                            borderColor: '#fff',
                            borderWidth: 1
                        },
                        emphasis: {
                            label: {
                                fontSize: 20
                            }
                        },
                        data: [
                            { value: 98.5, name: '织造车间' },
                            { value: 98.5, name: '整理车间' },
                            { value: 96, name: '打样车间' },
                            { value: 91.5, name: '综合维修' },
                        ]
                    }
                ]
            };
            myChart.setOption(option);
            myChart2.setOption(option2);
        },
        // 返回上一页
        reTurn() {
            this.$router.push({ path: "/inspection" });
        },

    }
}
</script>
<style lang="scss">
.card-header-text {
    font-size: 18px;
    font-weight: bold;
    letter-spacing: 2px;
    color: #666;
}

.card-back-text {
    cursor: pointer;
}
</style>