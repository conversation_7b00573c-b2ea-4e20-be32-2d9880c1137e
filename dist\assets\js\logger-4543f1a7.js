import{i as r,j as B,k as I,cL as N,E as P,o as f,a as b,w as o,b as e,a2 as T,n as D,s as U,d as j,e as K,m as M}from"./index-444b28c3.js";/* empty css                   */import{E as F}from"./el-card-6f02be36.js";import{E as H}from"./el-pagination-6fc73be7.js";import{E as R}from"./el-input-6b488ec7.js";import"./el-tag-29cbefd8.js";import"./el-select-980e5896.js";import"./el-scrollbar-af6196f4.js";import{E as $,a as q}from"./el-table-column-fa1764a8.js";import"./el-checkbox-f3df62fa.js";import"./el-tooltip-4ed993c7.js";import{E as A,a as G}from"./el-col-bd5e5418.js";import{E as J}from"./el-button-9bbdfcf9.js";/* empty css                        */import{v as O}from"./directive-ce1b251f.js";import"./index-e305bb62.js";import"./event-fe80fd0c.js";import"./index-4d7f16ce.js";import"./validator-e4131fc3.js";import"./scroll-a66dde9b.js";import"./_Uint8Array-55276dff.js";import"./focus-trap-6de7266c.js";const Q={class:"demo-pagination-block"},Ee={__name:"logger",setup(W){const s=r(""),u=r(1),c=r(10),g=r(0),E=r("small"),y=r(!1),a=r(!0),m=B([]);I(()=>{i()});const _=()=>{s.value&&(a.value=!0,i())},h=()=>{s.value="",a.value=!0,i()},i=async()=>{try{const t=await N({page:u.value,pageSize:c.value,keywords:s.value});if(t.code!==200)return a.value=!1,P({title:"Error",message:t.msg,type:"error",duration:2e3});setTimeout(()=>{a.value=!1},500),m.splice(0,m.length,...t.data.list),g.value=t.data.total}catch{return a.value=!1,!1}},k=t=>{c.value=t,a.value=!0,i()},w=t=>{u.value=t,a.value=!0,i()};return(t,n)=>{const z=R,d=A,C=J,v=G,l=$,x=q,S=H,V=F,L=O;return f(),b(v,{gutter:20},{default:o(()=>[e(d,{xs:24,sm:24,md:24,lg:24},{default:o(()=>[e(V,{shadow:"hover"},{default:o(()=>[e(v,{gutter:20},{default:o(()=>[e(d,{span:6},{default:o(()=>[e(z,{modelValue:s.value,"onUpdate:modelValue":n[0]||(n[0]=p=>s.value=p),placeholder:"请输入查询内容",onClear:h,clearable:"",onKeyup:T(_,["enter"])},null,8,["modelValue"])]),_:1}),e(d,{span:14},{default:o(()=>[e(C,{type:"primary",icon:D(U),onClick:_},{default:o(()=>[j("搜 索")]),_:1},8,["icon"])]),_:1})]),_:1}),K((f(),b(x,{data:m,stripe:"",style:{"margin-top":"2rem"},"element-loading-text":"Flyknit..."},{default:o(()=>[e(l,{type:"selection",width:"55"}),e(l,{prop:"nickname",label:"用户信息"}),e(l,{prop:"ip",label:"IP地址"}),e(l,{prop:"deviceId",label:"设备ID"}),e(l,{prop:"deviceModel",label:"登录设备/环境",width:"500"}),e(l,{prop:"content",label:"事件内容"}),e(l,{prop:"create_time",label:"创建时间"})]),_:1},8,["data"])),[[L,a.value]]),M("div",Q,[e(S,{style:{"margin-top":"1rem"},"current-page":u.value,"onUpdate:currentPage":n[1]||(n[1]=p=>u.value=p),"page-size":c.value,"onUpdate:pageSize":n[2]||(n[2]=p=>c.value=p),"page-sizes":[14,16,18,20],size:E.value,background:y.value,layout:"total, sizes, prev, pager, next, jumper",total:g.value,onSizeChange:k,onCurrentChange:w},null,8,["current-page","page-size","size","background","total"])])]),_:1})]),_:1})]),_:1})}}};export{Ee as default};
