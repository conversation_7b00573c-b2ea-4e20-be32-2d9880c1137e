import{C as O,i as k,a8 as te,k as P,o as y,g as M,m as d,H as R,t as w,V as W,bn as oe,bo as se,c as X,u as U,j as ae,B as q,a as z,w as a,b as s,n as N,bp as ne,a2 as K,d as D,e as F,a3 as H,bq as le,br as ie,E as $,bs as re,bt as ue,bu as ce,bv as me,a5 as de}from"./index-444b28c3.js";import{T as pe,_ as ge,a as fe}from"./tabs-15514514.js";import{a as ve}from"./el-footer-ef9d7e15.js";import{E as _e,a as ye}from"./el-col-bd5e5418.js";import{E as he,a as ke}from"./el-form-10dec954.js";import{E as we}from"./el-button-9bbdfcf9.js";import{E as be}from"./el-checkbox-f3df62fa.js";import"./el-form-item-4ed993c7.js";/* empty css                */import{E as xe}from"./el-input-6b488ec7.js";import{K as Ee}from"./keepAlive-fa1c9640.js";import{_ as j}from"./_plugin-vue_export-helper-c27b6911.js";import"./event-fe80fd0c.js";import"./index-4d7f16ce.js";import"./_Uint8Array-55276dff.js";const Ve={class:"drag-verify"},Se={class:"text"},Ie=O({__name:"SliderBlock",props:{value:{type:Boolean,defalut:!1},successIcon:{type:String,default:"iconfont icon-zhengque"},successText:{type:String,default:"验证成功"},startIcon:{type:String,default:"iconfont icon-jiantou_yemian_xiangyou"},startText:{type:String,default:"拖动滑块到最右边"}},emits:["update:value"],setup(g,{emit:f}){const C=f,b=g,r=k(!1),p="ontouchstart"in document.documentElement,x=p?"touchmove":"mousemove",E=p?"touchend":"mouseup";te(()=>b.value,l=>{r.value=l});const V=l=>{let c=0;const S=40,e=document.querySelector(".drag-verify .block"),v=l.clientX||l.touches[0].pageX,n=e.offsetWidth-S;if(r.value)return!1;const I=t=>{c=(t.clientX||t.touches[0].pageX)-v,c<=0&&(c=0),c>=n-S&&(c=n),e.style.transition=".1s all",e.style.transform=`translateX(${c}px)`},_=()=>{c!==n?(e.style.transition=".5s all",e.style.transform="translateX(0)"):(r.value=!0,C("update:value",r.value),W.emit("verify",{result:r.value})),document.removeEventListener(x,I),document.removeEventListener(E,_)};document.addEventListener(x,I),document.addEventListener(E,_)};return P(()=>{b.value&&(r.value=!0)}),(l,c)=>(y(),M("div",Ve,[d("div",{class:R(["range",r.value?"success":""])},[d("div",{class:"block",onMousedown:V,onTouchstart:V},[d("i",{class:R(r.value?g.successIcon:g.startIcon)},null,2)],32),d("span",Se,w(r.value?g.successText:g.startText),1)],2)]))}});const Te=j(Ie,[["__scopeId","data-v-28ae69d9"]]);const $e={class:"login-container"},Ce={key:0,src:ge,style:{width:"100px",height:"100px"},alt:"logo"},Be={key:1,src:fe,style:{width:"100px",height:"100px"},alt:"logo"},Xe={class:"title-text"},qe={class:"right-info-notice"},ze={class:"right-info-notice"},Ne=O({name:"login"}),De=Object.assign(Ne,{setup(g){const f=k(!1),C=ie,b=oe(),r=pe(),p=se(),x=Ee(),E=X(()=>p.themeConfig.isDark),V=U(),{t:l}=U(),c=X(()=>l("login.yanzhengchenggong")),S=X(()=>l("login.tuodoghuakuai"));W.on("verify",t=>{e.isVerify=t.result});const e=ae({nickname:"",password:"",isVerify:!1}),v=k(null),h=k(!1),n=k(!1);P(()=>{const t=localStorage.getItem("is-active"),o=localStorage.getItem("userDetail"),m=localStorage.getItem("lang");V.locale.value=m,p.updateLanguage(m);try{if(t&&o){const i=JSON.parse(o);n.value=!0,e.nickname=i.nickname,e.password=i.password,e.isVerify=i.isVerify}else n.value=!1,e.nickname="",e.password=""}catch(i){console.error("Error parsing userInfo from localStorage:",i),n.value=!1,e.nickname="",e.password=""}});const I=t=>{n.value=t,t?localStorage.setItem("is-active",!0):(localStorage.removeItem("is-active"),localStorage.removeItem("userDetail"),e.nickname="",e.password="",e.isVerify=!1)},_=t=>{if(t){if(!e.nickname){$({title:"",message:l("login.qingshuruyonghuming"),type:"warning"});return}if(!e.password){$({title:"",message:l("login.qingshurumima"),type:"warning"});return}if(!e.isVerify){$({title:"",message:l("login.anquanyanzhengma"),type:"warning"});return}t.validate(async o=>{if(o){h.value=!0;try{const m=await re({nickname:e.nickname,password:e.password,remember:n.value}),{data:i}=m;n.value&&localStorage.setItem("userDetail",JSON.stringify(e)),p.setUserInfo(i),p.setToken(i.token),ue(n.value),await ce(),r.closeMultipleTab(),x.setKeepAliveName(),b.push(me),$({title:i.nickname,message:l("login.welcome"),type:"success",duration:3e3})}catch{return h.value=!1,e.password="",!1}finally{h.value=!1}}})}};return(t,o)=>{const m=_e,i=xe,A=q("CircleCheckFilled"),T=de,B=he,J=q("View"),G=q("Hide"),Q=be,Y=we,Z=ke,L=ye,ee=ve;return y(),z(ee,{class:"home"},{default:a(()=>[s(L,{gutter:10},{default:a(()=>[s(m,{span:24,xs:24,sm:24,md:14,lg:12},{default:a(()=>[d("div",$e,[s(L,{gutter:10},{default:a(()=>[s(m,{span:24,class:"span-24"},{default:a(()=>[E.value?(y(),M("img",Be)):(y(),M("img",Ce)),d("h1",Xe,w(N(C)),1)]),_:1}),s(m,{span:24,class:"flex-item"},{default:a(()=>[s(Z,{ref_key:"formRef",ref:v,model:e,"label-width":"auto",class:"demo-dynamic"},{default:a(()=>[s(B,{prop:"nickname",label:"",rules:[{required:!0,message:t.$t("login.yonghumingbunengweikong"),trigger:"blur"}]},{default:a(()=>[s(i,{"prefix-icon":N(ne),placeholder:t.$t("login.qingshuruyonghuming"),size:"large",maxlength:"20",modelValue:e.nickname,"onUpdate:modelValue":o[0]||(o[0]=u=>e.nickname=u),onKeyup:o[1]||(o[1]=K(u=>_(v.value),["enter"]))},{prepend:a(()=>[D(w(t.$t("login.yonghuming")),1)]),_:1},8,["prefix-icon","placeholder","modelValue"]),F(d("span",qe,[s(T,{color:"#2196f3",size:16},{default:a(()=>[s(A)]),_:1})],512),[[H,e.nickname]])]),_:1},8,["rules"]),s(B,{label:"",rules:[{required:!0,message:t.$t("login.mimabunengweikong"),trigger:"blur"},{min:8,message:t.$t("login.mimachangdubunengxiaoyu8wei"),trigger:"blur"}],style:{"margin-top":"1rem",position:"relative"},prop:"password"},{default:a(()=>[s(i,{"prefix-icon":N(le),type:f.value?"text":"password",placeholder:t.$t("login.qingshurumima"),size:"large",maxlength:"20",modelValue:e.password,"onUpdate:modelValue":o[2]||(o[2]=u=>e.password=u),onKeyup:o[3]||(o[3]=K(u=>_(v.value),["enter"]))},{prepend:a(()=>[D(w(t.$t("login.mima")),1)]),_:1},8,["prefix-icon","type","placeholder","modelValue"]),d("span",{class:"show-password",onClick:o[4]||(o[4]=u=>f.value=!f.value)},[f.value?(y(),z(T,{key:0},{default:a(()=>[s(J)]),_:1})):(y(),z(T,{key:1},{default:a(()=>[s(G)]),_:1}))]),F(d("span",ze,[s(T,{color:"#2196f3",size:16},{default:a(()=>[s(A)]),_:1})],512),[[H,e.password&&e.password.length>=8]])]),_:1},8,["rules"]),s(Te,{modelValue:e.isVerify,"onUpdate:modelValue":o[5]||(o[5]=u=>e.isVerify=u),value:e.isVerify,successText:c.value,startText:S.value},null,8,["modelValue","value","successText","startText"]),s(Q,{onChange:I,modelValue:n.value,"onUpdate:modelValue":o[6]||(o[6]=u=>n.value=u),label:t.$t("login.miandenglu"),size:"large"},null,8,["modelValue","label"]),s(B,{style:{"margin-top":"1rem"}},{default:a(()=>[s(Y,{type:"primary",size:"large",loading:h.value,class:"login-btn",onClick:o[7]||(o[7]=u=>_(v.value))},{default:a(()=>[D(w(t.$t("login.denglu")),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1})]),_:1})])]),_:1})]),_:1})]),_:1})}}}),Ye=j(De,[["__scopeId","data-v-1e42737f"]]);export{Ye as default};
