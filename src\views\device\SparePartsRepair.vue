<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-08-24 17:38:03
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-26 13:47:00
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\device\SparePartsRepair.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <!-- 备件的损坏、报废、退还登记记录，维修返还，对冲维修记录，总库存根据维修数量增加对应数量 -->
        <el-card class="cards">
            <!-- 表单,备件名称，备件编号搜索，退还人姓名、工号、退还日期，查询按钮、重置按钮 -->
            <el-form :inline="true" :model="form" class="demo-form-inline">
                <el-form-item label="名称/编号" prop="depart_name">
                    <el-input v-model="form.depart_name" placeholder="请输入备件名称或备件编号"></el-input>
                </el-form-item>
                <el-form-item label="条件" prop="keywords">
                    <el-input v-model="form.keywords" placeholder="请输入退还人姓名/工号"></el-input>
                </el-form-item>
                <el-form-item label="部门" prop="department">
                    <el-select placeholder="请选择部门" v-model="form.department" clearable>
                        <el-option label="IT部门" value="IT部门"></el-option>
                        <el-option label="织造车间" value="织造车间"></el-option>
                        <el-option label="整理车间" value="整理车间"></el-option>
                        <el-option label="检验车间" value="检验车间"></el-option>
                        <el-option label="打样车间" value="打样车间"></el-option>
                        <el-option label="综合维修" value="综合维修"></el-option>
                        <el-option label="化验室" value="化验室"></el-option>
                        <el-option label="行政办公室" value="行政办公室"></el-option>
                        <el-option label="食堂" value="食堂"></el-option>
                    </el-select>
                </el-form-item><el-form-item label="状态" prop="status">
                    <el-select placeholder="备件状态" v-model="form.status" clearable>
                        <el-option label="正常" value="正常"></el-option>
                        <el-option label="待维修" value="待维修"></el-option>
                        <el-option label="已报废" value="已报废"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="日期" prop="date">
                    <el-date-picker v-model="form.date" type="date" placeholder="选择日期" value-format="yyyy-MM-dd"
                        style="width: 100%;"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSubmit" icon="el-icon-search">查询</el-button>
                    <el-button @click="onReset" icon="el-icon-refresh">重置</el-button>
                </el-form-item>
            </el-form>
            </el-card>
            <el-card style="margin-top: 6px;">
            <!-- 表格,备件名称、备件编号、退还人姓名、工号、退还日期、退还数量、退还部门、备注、操作 -->
            <el-row :gutter="20">
                <el-col :span="24">
                    <!-- 添加按钮 -->
                    <el-button type="success" icon="el-icon-folder-add" size="small">退还登记</el-button>
                </el-col>
            </el-row>
            <el-table :data="tableData" style="width: 100%;margin-top:20px;" v-loading="loading" element-loading-text="Flyknit">
                <el-table-column type="selection" label="序号" width="60"></el-table-column>
                <el-table-column prop="depart_name" label="备件名称" width=""></el-table-column>
                <el-table-column prop="depart_number" label="备件编号" width=""></el-table-column>
                <el-table-column prop="username" label="退还人姓名" width=""></el-table-column>
                <el-table-column prop="uuid" label="工号" width=""></el-table-column>
                <el-table-column prop="quantity" label="退还数量" width=""></el-table-column>
                <el-table-column prop="number" label="退还部门" width=""></el-table-column>
                <el-table-column prop="status" label="备件状态" width="">
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.status == 0" type="success">正常</el-tag>
                        <el-tag v-else-if="scope.row.status == 1" type="warning">待维修</el-tag>
                        <el-tag v-else-if="scope.row.status == 2" type="danger">报废</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="date" label="退还日期" width="180"></el-table-column>
                <el-table-column prop="remark" label="备注" width="150"></el-table-column>
                <el-table-column label="操作" width="230">
                    <template slot-scope="scope">
                        <el-button type="text" size="mini" icon="el-icon-edit"
                            @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
                        <el-button type="text" style="color: #F56C6C;" size="mini" icon="el-icon-delete"
                            @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
                :page-sizes="[10, 20, 30, 40]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="total"></el-pagination>
        </el-card>
    </div>
</template>
<script>
export default {
    data() {
        return {
            loading: true,
            form: {
                depart_name: '',
                keywords: '',
                date: '',
                department: '',
                status: '',
            },
            tableData: [
                {
                    id: 1,
                    depart_name: '输纱器',
                    depart_number: '236958',
                    username: '王灿',
                    uuid: '10116',
                    quantity: 2,
                    number: '打样车间',
                    date: '2023-08-26 13:13:49',
                    remark: '损坏待修',
                    status: 1
                },
                {
                    id: 2,
                    depart_name: '牵拉马达',
                    depart_number: '236958',
                    username: '王灿',
                    uuid: '10116',
                    quantity: 1,
                    number: '打样车间',
                    date: '2023-08-26 13:13:49',
                    remark: '损坏待修',
                    status: 1
                },
                {
                    id: 3,
                    depart_name: '机头轴承',
                    depart_number: '236958',
                    username: '王灿',
                    uuid: '10116',
                    quantity: 1,
                    number: '打样车间',
                    date: '2023-08-26 13:13:49',
                    remark: '损坏待修',
                    status: 1
                }
            ],
            currentPage: 1,
            pageSize: 10,
            total: 3,
        };
    },
    mounted() {
        setTimeout(() => {
            this.loading = false;
        }, 1000);
    },
    methods: {
        onSubmit() {
            console.log('submit!');
        },
        onReset() {
            console.log('reset!');
        },
    },
};
</script>
<style lang="scss">
.el-card__body {
    padding: 20px 20px 0px 20px;
}

.el-pagination {
    padding: 20px 0;
}

.demo-form-inline {
    display: flex;
    flex-wrap: wrap;
}
</style>