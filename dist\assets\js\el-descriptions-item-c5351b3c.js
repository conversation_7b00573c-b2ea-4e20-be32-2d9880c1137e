import{g as H,f as L}from"./vnode-b9ec7db4.js";import{C as z,X as W,aZ as A,R as j,D as k,Q as F,_ as K,n,o as l,g as m,m as g,F as y,h as $,a as C,b as B,a_ as O,a$ as Q,b0 as T,a9 as X,c as Z,H as S,S as P,d as R,t as V,f as q,U as G,ag as J}from"./index-444b28c3.js";const I="elDescriptions";var D=z({name:"ElDescriptionsCell",props:{cell:{type:Object},tag:{type:String},type:{type:String}},setup(){return{descriptions:W(I,{})}},render(){var v,o,t,w,s,d;const a=H(this.cell),{border:E,direction:e}=this.descriptions,c=e==="vertical",u=((t=(o=(v=this.cell)==null?void 0:v.children)==null?void 0:o.label)==null?void 0:t.call(o))||a.label,r=(d=(s=(w=this.cell)==null?void 0:w.children)==null?void 0:s.default)==null?void 0:d.call(s),p=a.span,_=a.align?`is-${a.align}`:"",f=a.labelAlign?`is-${a.labelAlign}`:_,h=a.className,N=a.labelClassName,b={width:A(a.width),minWidth:A(a.minWidth)},i=j("descriptions");switch(this.type){case"label":return k(this.tag,{style:b,class:[i.e("cell"),i.e("label"),i.is("bordered-label",E),i.is("vertical-label",c),f,N],colSpan:c?p:1},u);case"content":return k(this.tag,{style:b,class:[i.e("cell"),i.e("content"),i.is("bordered-content",E),i.is("vertical-content",c),_,h],colSpan:c?p:p*2-1},r);default:return k("td",{style:b,class:[i.e("cell"),_],colSpan:p},[k("span",{class:[i.e("label"),N]},u),k("span",{class:[i.e("content"),h]},r)])}}});const M=F({row:{type:Array,default:()=>[]}}),Y={key:1},x={name:"ElDescriptionsRow"},ee=z({...x,props:M,setup(v){const o=W(I,{});return(t,w)=>n(o).direction==="vertical"?(l(),m(y,{key:0},[g("tr",null,[(l(!0),m(y,null,$(t.row,(s,d)=>(l(),C(n(D),{key:`tr1-${d}`,cell:s,tag:"th",type:"label"},null,8,["cell"]))),128))]),g("tr",null,[(l(!0),m(y,null,$(t.row,(s,d)=>(l(),C(n(D),{key:`tr2-${d}`,cell:s,tag:"td",type:"content"},null,8,["cell"]))),128))])],64)):(l(),m("tr",Y,[(l(!0),m(y,null,$(t.row,(s,d)=>(l(),m(y,{key:`tr3-${d}`},[n(o).border?(l(),m(y,{key:0},[B(n(D),{cell:s,tag:"td",type:"label"},null,8,["cell"]),B(n(D),{cell:s,tag:"td",type:"content"},null,8,["cell"])],64)):(l(),C(n(D),{key:1,cell:s,tag:"td",type:"both"},null,8,["cell"]))],64))),128))]))}});var te=K(ee,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/descriptions/src/descriptions-row.vue"]]);const se=F({border:{type:Boolean,default:!1},column:{type:Number,default:3},direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},size:O,title:{type:String,default:""},extra:{type:String,default:""}}),le={name:"ElDescriptions"},ne=z({...le,props:se,setup(v){const o=v,t=j("descriptions"),w=Q(),s=T();X(I,o);const d=Z(()=>[t.b(),t.m(w.value)]),a=(e,c,u,r=!1)=>(e.props||(e.props={}),c>u&&(e.props.span=u),r&&(e.props.span=c),e),E=()=>{var e;const c=L((e=s.default)==null?void 0:e.call(s)).filter(f=>{var h;return((h=f==null?void 0:f.type)==null?void 0:h.name)==="ElDescriptionsItem"}),u=[];let r=[],p=o.column,_=0;return c.forEach((f,h)=>{var N;const b=((N=f.props)==null?void 0:N.span)||1;if(h<c.length-1&&(_+=b>p?p:b),h===c.length-1){const i=o.column-_%o.column;r.push(a(f,i,p,!0)),u.push(r);return}b<p?(p-=b,r.push(f)):(r.push(a(f,b,p)),u.push(r),p=o.column,r=[])}),u};return(e,c)=>(l(),m("div",{class:S(n(d))},[e.title||e.extra||e.$slots.title||e.$slots.extra?(l(),m("div",{key:0,class:S(n(t).e("header"))},[g("div",{class:S(n(t).e("title"))},[P(e.$slots,"title",{},()=>[R(V(e.title),1)])],2),g("div",{class:S(n(t).e("extra"))},[P(e.$slots,"extra",{},()=>[R(V(e.extra),1)])],2)],2)):q("v-if",!0),g("div",{class:S(n(t).e("body"))},[g("table",{class:S([n(t).e("table"),n(t).is("bordered",e.border)])},[g("tbody",null,[(l(!0),m(y,null,$(E(),(u,r)=>(l(),C(te,{key:r,row:u},null,8,["row"]))),128))])],2)],2)],2))}});var ae=K(ne,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/descriptions/src/description.vue"]]),U=z({name:"ElDescriptionsItem",props:{label:{type:String,default:""},span:{type:Number,default:1},width:{type:[String,Number],default:""},minWidth:{type:[String,Number],default:""},align:{type:String,default:"left"},labelAlign:{type:String,default:""},className:{type:String,default:""},labelClassName:{type:String,default:""}}});const oe=G(ae,{DescriptionsItem:U}),ce=J(U);export{ce as E,oe as a};
