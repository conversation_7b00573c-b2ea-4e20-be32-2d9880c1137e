/*
 * @Author: flyknit <EMAIL>
 * @Date: 2023-12-02 13:54:12
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-07-13 12:59:19
 * @FilePath: \electronic-filed:\gitee\backend-demo\src\http\home.js
 * @Description: 
 * 
 * Copyright (c) 2023 by ${<EMAIL>}, All Rights Reserved. 
 */
import { get } from "lodash";
import request from "./request";

export default {
  // params GET请求, data POST请求
  // 后台管理登录接口
  userLogin(datas) {
    return request({
      url: "/adminLogin",
      method: "POST",
      data: datas,
    });
  },
  // 获取管理员列表
  getAdminList(datas) {
    return request({
      url: "/adminList",
      method: "GET",
      params: datas,
    });
  },
  // 后台创建管理员
  createAdmin(datas) {
    return request({
      url: "/admin_create",
      method: "POST",
      data: datas,
    });
  },
  // 后台管理修改管理员
  updateAdmin(datas) {
    return request({
      url: "/admin_update",
      method: "POST",
      data: datas,
    });
  },

  // 后台管理删除用户
  deleteAdmin(datas) {
    return request({
      url: "/admin_delete",
      method: "POST",
      data: datas,
    });
  },

  // 后台管理获取model列表
  getModelLists(datas) {
    return request({
      url: "/modelList",
      method: "GET",
      params: datas,
    });
  },

  // 获取越南fcdc model列表
  getFcdcModelLists(datas) {
    return request({
      url: "/fcdcModelList",
      method: "GET",
      params: datas,
    });
  },

  // 后台管理添加打样model
  addModel(datas) {
    return request({
      url: "/model_create",
      method: "POST",
      data: datas,
    });
  },

  // 后台管理删除model
  deleteModel(datas) {
    return request({
      url: "/model_delete",
      method: "POST",
      data: datas,
    });
  },

  // 后台管理添加尺码
  addSize(datas) {
    return request({
      url: "/size_create",
      method: "POST",
      data: datas,
    });
  },

  // 后台获取尺码列表
  getSizeList(datas) {
    return request({
      url: "/sizeList",
      method: "GET",
      params: datas,
    });
  },

  // 后台管理删除尺码
  deleteSize(datas) {
    return request({
      url: "/size_delete",
      method: "POST",
      data: datas,
    });
  },

  // 后台管理添加品名
  addProductName(datas) {
    return request({
      url: "/model_detail_create",
      method: "POST",
      data: datas,
    });
  },

  // 后台管理获取品名列表
  getProductNameList(datas) {
    return request({
      url: "/modelDetailList",
      method: "GET",
      params: datas,
    });
  },

  // 删除品名
  deleteProductName(datas) {
    return request({
      url: "/modelDetail_delete",
      method: "POST",
      data: datas,
    });
  },

  // 获取品名列表
  getPmLists(datas) {
    return request({
      url: "/pmAllList",
      method: "GET",
      params: datas,
    });
  },

  // 后台管理添加测量点
  addMeasurePoint(datas) {
    return request({
      url: "/standard_create",
      method: "POST",
      data: datas,
    });
  },

  // 后台管理删除点位 
  deleteMeasurePoint(datas) {
    return request({
      url: "/standard_delete",
      method: "POST",
      data: datas,
    });
  },

  // 后台管理获取点位列表
  getMeasurePointList(datas) {
    return request({
      url: "/standardList",
      method: "GET",
      params: datas,
    });
  },

  // 后台管理获取全部尺码
  getAllSizeList(datas) {
    return request({
      url: "/sizeAllList",
      method: "GET",
      params: datas,
    });
  },

  // 后台管理获取标准名列表
  getStandardNameList(datas) {
    return request({
      url: "/measure_location",
      method: "GET",
      params: datas,
    });
  },

  // 后台管理添加规格标准类型
  addStandardName(datas) {
    return request({
      url: "/measure_location_create",
      method: "POST",
      data: datas,
    });
  },

  // 后台管理删除规格标准类型
  deleteStandardName(datas) {
    return request({
      url: "/measure_location_delete",
      method: "POST",
      data: datas,
    });
  },

  // 后台管理获取全部model
  getAllModelList(datas) {
    return request({
      url: "/modelAllList",
      method: "GET",
      params: datas,
    });
  },

  // 巡检标准初始化根据model_name获取对应pm
  getPmList(datas) {
    return request({
      url: "/pmList",
      method: "GET",
      params: datas,
    });
  },

  // 根据model_name和pm获取表model_detail中的全部数据
  getModelDetailList(datas) {
    return request({
      url: "/modelDetailAllList",
      method: "GET",
      params: datas,
    });
  },

  // 根据model_name、pm、type、size获取表csh_standard中的全部数据
  getCshStandardList(datas) {
    return request({
      url: "/standardAllList",
      method: "GET",
      params: datas,
    });
  },

  // 获取默认表头信息
  getTableHeader(datas) {
    return request({
      url: "/measure_location_default",
      method: "GET",
      params: datas,
    });
  },

  // 后台管理添加model图片信息
  addModelImage(datas) {
    return request({
      url: "/model_info_create",
      method: "POST",
      data: datas,
    });
  },

  // 获取model图片信息
  getModelImage(datas) {
    return request({
      url: "/modelInfoList",
      method: "GET",
      params: datas,
    });
  },

  // 删除图片接口
  deleteModelImage(datas) {
    return request({
      url: "/modelInfo_delete",
      method: "POST",
      data: datas,
    });
  },

  // 后台管理获取全部测量点
  getAllLocation(datas) {
    return request({
      url: "/measure_location_all",
      method: "GET",
      params: datas,
    });
  },

  // 根据布票号获取布票信息
  getSerialInfo(datas) {
    return request({
      url: "/ticket",
      method: "GET",
      params: datas,
    });
  },

  // 工艺员提交可过订单
  submitOrder(datas) {
    return request({
      url: "/order_create",
      method: "POST",
      data: datas,
    });
  },

  // 后台管理获取订单列表
  getOrderList(datas) {
    return request({
      url: "/orderList",
      method: "GET",
      params: datas,
    });
  },

  // 获取首页数据
  getHomeDatas(datas) {
    return request({
      url: "/indexData",
      method: "GET",
      params: datas,
    });
  },

  // 后台管理添加配置
  addConfig(datas) {
    return request({
      url: "/link_create",
      method: "POST",
      data: datas,
    });
  },

  // 后台管理退出登录
  logOutBackend(datas) {
    return request({
      url: "/admin_logout",
      method: "POST",
      data: datas,
    });
  },

  // 后台管理切换标准
  getStandard(datas) {
    return request({
      url: "/changeStandards",
      method: "GET",
      params: datas,
    });
  },

  // 上传点位
  uploadMeasurePoint(datas) {
    return request({
      url: "/uploadExcel",
      method: "POST",
      data: datas,
    });
  },

  // 添加品名
  addProductByName(datas) {
    return request({
      url: "/pm_create",
      method: "POST",
      data: datas,
    });
  },


  

};
