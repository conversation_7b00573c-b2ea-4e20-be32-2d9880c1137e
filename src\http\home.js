/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-07-24 09:14:24
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-23 15:25:24
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\http\home.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { reject } from 'lodash'
import request from './request'

export default {
  // 登录接口
  userLogin(datas) {
    // 封装promise返回数据
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/backend_login',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 获取用户列表
  getUserList(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getUserLists',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 删除用户
  deleteUsers(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/deleteUser',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },

  // 禁用用户
  disableUsers(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/disableUser',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },

  // 添加用户接口
  addUserInfos(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/addUser',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 分配区域接口
  submitInfo(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/areaDivision',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 获取区域接口
  getAreaLists(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getAreaLists',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 删除单个区域接口
  delOneArea(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/delOneAreas',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 删除多个区域接口
  delAreaLists(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/delMoreAreas',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 修改用户权限接口
  modifyRoleinfo(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/modifyRole',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 添加保养内容接口
  addMaintaince(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/addMaintainItem',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 获取保养内容接口
  getMaintainceList(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getMaintainceLists',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    }
    )
  },
  // 删除保养项目接口
  delMaintaince(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/delMaintainceItem',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    }
    )
  },

  // 获取工作日志接口
  getWorkLists(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getWorkLists',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    }
    )
  },
  // 获取工作项列表接口
  getWorkListsItem(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getWorkItems',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    }
    )
  },
  // 添加工作项接口
  addWorkItem(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/addWorkItem',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    }
    )
  },
  // 删除工作项接口
  delWorkItem(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/deleteWorkItem',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    }
    )
  },
  // 获取部门保全功效分析接口
  efficientData(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getSecurityEfficiency',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    }
    )
  },
  // 获取加油记录接口
  getOilRecord(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getOilRecords',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    }
    )
  },
  // 提交报修信息
  callRepairs(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/callRepair',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    }
    )
  },
  // 获取报修信息
  getRepairLists(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getRepairRecords',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    }
    )
  },
  // 获取报表接口
  getAnalyseDatas(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getReportData',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 获取意见反馈列表接口
  getFeedbackLists(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getFeedbackLists',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 修改意见反馈状态接口
  modifyStatus(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/modifyFeedbackStatus',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 删除意见反馈接口
  deleteFeedback(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/deleteFeedback',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 获取登录日志接口
  getLoginLogs(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getLoginLog',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 提交app更新接口
  uploadInfo(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/uploadInfo',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 获取app更新信息接口
  getAppInfo(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getAppInfos',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 保存通知公告接口
  saveMessage(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/saveNotice',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 获取通知公告接口
  getMessage(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getMessage',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 保存app配置接口
  saveSetting(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/saveSettings',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 获取首页配置信息接口
  getPageSetting(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getPageSettings',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 清除首页配置信息接口
  clearSettings(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/clearPageSettings',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 邮箱配置接口
  saveEmailSetting(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/saveEmailSettings',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 获取邮箱配置信息接口
  getEmailSetting(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getEmailSettings',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 获取设备列表接口
  getEquipmentLists(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getEquipmentLists',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },

  // 获取设备分类接口
  getEquipmentClassify(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getEquipmentClassify',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 获取部门设备区域接口
  getAllArea(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getAllAreas',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 添加设备分类接口
  addClassify(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/addEquipmentClassify',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 添加设备接口
  addEquipment(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/addEquipment',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 删除设备接口
  delDevice(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/deleteEquipment',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 添加车间地点接口
  addArea(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/addLocation',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 全局二维码接口
  getQrCode(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/qrcode',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 设备转移接口
  transferEquipInterFace(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/transferEquipment',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 获取设备调拨记录接口
  getTransferRecord(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getTransferRecords',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 删除调拨记录接口
  deleteTransferRecord(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/deleteTransferRecords',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 导出Excel接口
  exportExcel(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/exportExcels',
        params: datas,
        responseType: 'blob' // 设置响应类型为blob
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理设备列表导入excel接口
  importExcel(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/importExcels',
        data: datas,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 获取设备保养记录接口
  getMaintainceLists(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/deviceMaintainList',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 设备管理-采购申请单提交接口
  submitPurchase(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/submitPurchaseInfo',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 获取采购申请单列表接口
  getPurchaseList(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getPurchaseInfoList',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 获取巡检记录列表接口
  getInspectionList(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getInspectionRecordList',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理退出登录接口
  logouts(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/logout',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理添加角色接口
  addRoles(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/addRole',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理获取角色列表接口
  getRoles(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getRoleList',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理删除角色接口
  delRoles(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/deleteRole',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理添加用户接口
  addUsers(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/addAdmin',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理获取用户列表接口
  getUsers(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getAdminList',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理批量删除用户接口
  delAllUsers(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/deleteAdmins',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理用户状态修改接口
  changeStatus(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/changeAdminStatus',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理分配用户角色接口
  dispatchRole(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/dispatchAdminRole',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理添加部门信息接口
  addDepartments(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/addDepartmentInfo',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理获取部门列表接口
  getDepartments(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getDepartmentInfoList',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理部门管理删除部门接口
  delDepartments(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/deleteDepartmentInfo',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理添加菜单接口
  addMenu(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/addMenuInfo',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理获取菜单列表接口
  getMenuLists(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getMenuInfoList',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理添加二级菜单接口
  addChildMenu(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/addSubMenuInfo',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理删除一级菜单接口
  delMenu(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/deleteSubMenuInfo',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 删除子菜单接口
  delChildMenu(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/delChildMenus',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理获取权限列表接口
  getPermissionList(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getPermissionList',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理添加分类管理接口
  addClassify(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/addPartsClassify',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理获取配件管理分类列表接口
  getClassifyList(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getPartsClassifyList',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理删除配件分类管理接口
  delClassify(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/deleteOneclassify',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },

  // 后台管理备件管理创建货架接口
  createShelts(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/addShelfInfo',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  // 后台管理库位管理添加库位接口
  addStorage(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/addStorageInfo',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },

  // 获取各部门货架列表接口
  getShelfList(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getShelfInfoList',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },

  // 员工卡识别
  employeeCard(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/employeeCard',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },

  // 根据部门名称获取部门下的库位列表
  getStorageList(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getStorageInfoList',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },

  // 获取部门列表
  getDepartmentList(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getDepartmentList',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },

  // 初始化存储类型
  initStorageType(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/initStorageType',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },

  // 获取存储类型
  getStorageType(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getStorageType',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },

  // 获取备件分类列表
  getPartsClassifyList(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: "get",
        url: "/getPartsClassify",
        params: datas,
      })
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    })
  },

  // 根据部门和货架名称获取库位列表
  getStorageListByShelf(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getStorageListByShelf',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },

  // 初始化单位
  initUnit(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/initUnit',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },

  // 获取单位列表
  getUnitList(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getUnitList',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },

  // 获取设备信息列表
  getEquipmentList(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getEquipmentList',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },

  // 备件入库
  sparePartsIn(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/sparePartsIn',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },

  // 获取配件列表
  getPartsList(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getPartsList',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  
  // 获取备件类型列表
  getPartsTypeList(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getPartsTypeList',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },

  // 获取备件入库单列表
  getPartsInList(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getPartsInList',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },

  // 删除入库单
  deleteStockIn(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/deleteStockIn',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },
  
  // 根据备件类型获取备件列表
  getPartsListByType(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'get',
        url: '/getPartsListByType',
        params: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },

  // 配件出库接口
  sparePartsOut(datas) {
    return new Promise((resolve, reject) => {
      request({
        method: 'post',
        url: '/sparePartsOut',
        data: datas
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  }

}
