import{b8 as A,cq as o,c8 as N,cr as p}from"./index-444b28c3.js";var L=(E=>(E[E.TEXT=1]="TEXT",E[E.CLASS=2]="CLASS",E[E.STYLE=4]="STYLE",E[E.PROPS=8]="PROPS",E[E.FULL_PROPS=16]="FULL_PROPS",E[E.HYDRATE_EVENTS=32]="HYDRATE_EVENTS",E[E.STABLE_FRAGMENT=64]="STABLE_FRAGMENT",E[E.KEYED_FRAGMENT=128]="KEYED_FRAGMENT",E[E.UNKEYED_FRAGMENT=256]="UNKEYED_FRAGMENT",E[E.NEED_PATCH=512]="NEED_PATCH",E[E.DYNAMIC_SLOTS=1024]="DYNAMIC_SLOTS",E[E.HOISTED=-1]="HOISTED",E[E.BAIL=-2]="BAIL",E))(L||{});const R=E=>{if(!o(E))return{};const e=E.props||{},r=(o(E.type)?E.type.props:void 0)||{},T={};return Object.keys(r).forEach(S=>{N(r[S],"default")&&(T[S]=r[S].default)}),Object.keys(e).forEach(S=>{T[p(S)]=e[S]}),T},_=E=>{const e=A(E)?E:[E],r=[];return e.forEach(T=>{A(T.children)?r.push(..._(T.children)):r.push(T)}),r};export{L as P,_ as f,R as g};
