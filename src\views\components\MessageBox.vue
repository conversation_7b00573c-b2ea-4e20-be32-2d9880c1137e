<template>
    <transition name="message-box">
        <div class="message-box" :class="type" v-show="display">
            <div class="message-title">
                <p :class="{'title': true, 'success': type === 'success', 'warning': type === 'warning', 'error': type === 'error'}">
                    <span class="el-icon-bell" :style="getBellIconStyle"></span>
                    系统消息
                  </p>
                <span class="el-icon-close" style="cursor: pointer;" @click="close"></span>
            </div>
            <p class="msg-text">{{ message }}</p>
        </div>
    </transition>
</template>
  
<script>
export default {
    props: ['message', 'type'], // 添加type属性，用于控制不同类型的样式
    data() {
        return {
            display: true
        };
    },
    computed: {
    getBellIconStyle() {
      if (this.type === 'success' || this.type === 'warning' || this.type === 'error') {
        return 'font-weight: 800; font-size: 20px; color: #feffff;';
      } else {
        return 'font-weight: 800; font-size: 20px; color: #000;';
      }
    }
  },
    methods: {
        close() {
            this.display = false;
            this.$emit('close');
        }
    },
    mounted() {
        setTimeout(() => {
            this.close();
        }, 600000000); // 10分钟后自动关闭消息框
    }
};
</script>
  
<style scoped>
.message-box {
    position: absolute;
    /* 将 fixed 改为 absolute */
    bottom: 40px;
    right: 20px;
    width: 400px;
    background: rgba(0, 0, 0, 0.8);
    padding: 10px 20px;
    border-radius: 7px;
    box-shadow: 4px 5px 8px rgba(0, 0, 0, 0.5);
}

.success {
    background-color: #67c23a;
    color: #feffff;
    letter-spacing: 2px;
}

.warning {
    background-color: #e6a23c;
    color: #feffff;
    letter-spacing: 2px;
}

.error {
    background-color: #f56c6c;
    color: #feffff;
    letter-spacing: 2px;
}

.normal {
    background-color: #ccc;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    color: #000;
    letter-spacing: 2px;
}

.message-box .title {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 2px;
}

.message-box .msg-text {
    padding: 10px 0;
    margin: 0;
    font-size: 16px;
    line-height: 1.5;
}

.message-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
        cursor: pointer;
        font-size: 16px;
        color: #000;
    }
    .el-icon-close {
        color: #000;
        font-size: 20px;
    }
}

.message-box .close-button {
    margin-top: 10px;
    background-color: red;
    border: none;
    color: #000;
    cursor: pointer;
}

.button {
    margin-top: 10px;
}
</style>
  