import{M as re,N as ie,O as me,P as fe,Q as de,_ as ve,C as Z,R as pe,c as _e,o as m,g as S,m as w,S as T,n as r,a as _,H as $,L as he,f as v,t as n,U as ye,w as t,b as a,d as u,V as G,i as d,W as be,X as ge,u as ke,G as $e,k as Me,Y as we,B as Ee,Z as Ce,$ as Ie,a0 as Se,a1 as Te,a2 as j,e as D,a3 as Q,a4 as qe,E as g,a5 as ze,a6 as Ke}from"./index-444b28c3.js";/* empty css                   */import{E as W,a as X}from"./el-col-bd5e5418.js";import{E as Y}from"./el-button-9bbdfcf9.js";import{E as Ve,a as je}from"./el-descriptions-item-c5351b3c.js";import{E as De}from"./el-tag-29cbefd8.js";import{E as xe}from"./el-card-6f02be36.js";import{E as Le}from"./el-input-6b488ec7.js";/* empty css                */import{E as Ne,a as Re}from"./el-step-62a69499.js";import{K as Be}from"./keepAlive-fa1c9640.js";import{_ as Pe}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                        */import{E as He}from"./index-df5d5edc.js";import{v as Ae}from"./directive-ce1b251f.js";import"./index-4d7f16ce.js";import"./vnode-b9ec7db4.js";import"./event-fe80fd0c.js";const M={success:"icon-success",warning:"icon-warning",error:"icon-error",info:"icon-info"},U={[M.success]:re,[M.warning]:ie,[M.error]:me,[M.info]:fe},Fe=de({title:{type:String,default:""},subTitle:{type:String,default:""},icon:{type:String,values:["success","warning","info","error"],default:"info"}}),Oe={name:"ElResult"},Qe=Z({...Oe,props:Fe,setup(C){const o=C,s=pe("result"),c=_e(()=>{const e=o.icon,h=e&&M[e]?M[e]:"icon-info",y=U[h]||U["icon-info"];return{class:h,component:y}});return(e,h)=>(m(),S("div",{class:$(r(s).b())},[w("div",{class:$(r(s).e("icon"))},[T(e.$slots,"icon",{},()=>[r(c).component?(m(),_(he(r(c).component),{key:0,class:$(r(c).class)},null,8,["class"])):v("v-if",!0)])],2),e.title||e.$slots.title?(m(),S("div",{key:0,class:$(r(s).e("title"))},[T(e.$slots,"title",{},()=>[w("p",null,n(e.title),1)])],2)):v("v-if",!0),e.subTitle||e.$slots["sub-title"]?(m(),S("div",{key:1,class:$(r(s).e("subtitle"))},[T(e.$slots,"sub-title",{},()=>[w("p",null,n(e.subTitle),1)])],2)):v("v-if",!0),e.$slots.extra?(m(),S("div",{key:2,class:$(r(s).e("extra"))},[T(e.$slots,"extra")],2)):v("v-if",!0)],2))}});var Ue=ve(Qe,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/result/src/result.vue"]]);const Ze=ye(Ue);const Ge={__name:"Result",props:{msg:{type:String,default:"下机成功",required:!1}},setup(C){const o=C,s=()=>{G.emit("backHome")};return(c,e)=>{const h=Y,y=Ze,q=W,p=X;return m(),_(p,{gutter:20},{default:t(()=>[a(q,{sm:24,lg:24},{default:t(()=>[a(y,{icon:"success",title:o.msg,"sub-title":"页面将在3秒后初始化..."},{extra:t(()=>[a(h,{type:"primary",onClick:s},{default:t(()=>[u("返回")]),_:1})]),_:1},8,["title"])]),_:1})]),_:1})}}};const We={class:"card-header"},Xe={class:"font-medium"},Ye={class:"mt-4"},Je=Z({name:"webpage"}),el=Object.assign(Je,{setup(C){const o=d(0),s=d(""),c=d(""),e=d([]),h=d(null),y=d(null),q=d("Flyknit..."),p=d(!1),b=d(),x=be(),L=Be(),N=ge("refresh"),R=d(),{t:E}=ke();G.on("backHome",()=>{s.value="",c.value="",e.value=[],p.value=!1,o.value=0,b.value="",B()});const B=()=>{setTimeout(()=>{L.removeKeepLiveName(x.name),N(!1),$e(()=>{L.addKeepLiveName(x.name),N(!0)})},0)},z=l=>{if(o.value===3&&l.keyCode===13)H();else return!1};let P=(l,i=500)=>{let k=null;return function(...K){clearTimeout(k),k=setTimeout(()=>{l.apply(this,K)},i)}};const J=P(async()=>{if(!s.value)return g({title:"",message:E("offMachine.smzqdkf"),type:"error",duration:2e3}),!1;if(s.value=s.value.replace(/\s+/g,""),!/^[a-zA-Z0-9]+$/.test(s.value)&&s.value.length<32)return g({title:"",message:E("offMachine.smzqdkf"),type:"error",duration:2e3}),s.value="",!1;await ee()}),ee=async()=>{p.value=!0;try{const l=await Ke({serial:s.value});if(l.code!==200)return p.value=!1,g({title:"",message:l.msg,type:"error",duration:2e3});p.value=!1,e.value=l.data,o.value=1,setTimeout(()=>{y.value.focus()},500)}catch{return p.value=!1,!1}p.value=!1},le=P(async()=>{if(o.value=2,!c.value)return g({title:"",message:E("offMachine.bdqrcode"),type:"error",duration:2e3}),!1;if(!te(c.value))return g({title:"",message:E("offMachine.smzqdqrcode"),type:"error",duration:2e3}),c.value="",!1;b.value=c.value,s.value="",c.value="",y.value.blur(),o.value=3,g({title:"",message:E("offMachine.bdchenggong"),type:"success",duration:2e3})}),te=l=>!!/^S\d{12}$/.test(l),H=async()=>{const l=He.service({lock:!0,text:"Flyknit...",background:"rgba(0, 0, 0, 0.7)"});try{const i=await qe({serial:e.value[0].Id,model:e.value[0].yhxq?e.value[0].yhxq:"",ch:e.value[0].ch?e.value[0].ch:"",pm:e.value[0].pm?e.value[0].pm:"",size:e.value[0].cm?e.value[0].cm:"",season:e.value[0].ypjj?e.value[0].ypjj:"",version_thumb:e.value[0].cxm_ml?e.value[0].cxm_ml:"",version:e.value[0].cxm_bb?e.value[0].cxm_bb:"",type:e.value[0].yplx?e.value[0].yplx:"",qrcode:b.value,color:e.value[0].styleno_mc?e.value[0].styleno_mc:"",style_number:e.value[0].ylzc?e.value[0].ylzc:""});if(i.code!==200)return l.close(),b.value="",setTimeout(()=>{y.value.focus()},500),g({title:"",message:i.msg,type:"error",duration:2e3}),!1;R.value=i.msg,e.value=[],s.value="",c.value="",b.value="",setTimeout(()=>{l.close(),o.value=4},1e3),setTimeout(()=>{B()},3e3)}catch(i){return l.close(),g({title:"",message:i.message,type:"error",duration:2e3}),!1}};return Me(()=>{o.value=0,window.addEventListener("keydown",z),setTimeout(()=>{h.value.focus()},1e3)}),we(()=>{window.removeEventListener("keydown",z,!1)}),(l,i)=>{const k=Ne,K=Re,I=W,ae=Ee("FullScreen"),oe=ze,A=Le,F=xe,f=Ve,se=De,ne=je,O=Y,ue=X,ce=Ae;return m(),_(ue,{guitter:10,class:"mb8"},{default:t(()=>[a(F,null,{default:t(()=>[a(I,{xs:24,sm:24,md:24,lg:24,class:"steps-content"},{default:t(()=>[a(K,{active:o.value},{default:t(()=>[a(k,{title:l.$t("offMachine.scanDevelopmentOrder"),icon:r(Ce)},null,8,["title","icon"]),a(k,{title:l.$t("offMachine.scanQrCode"),icon:r(Ie)},null,8,["title","icon"]),a(k,{title:l.$t("offMachine.submitData"),icon:r(Se)},null,8,["title","icon"]),a(k,{title:l.$t("offMachine.downloadResult"),icon:r(Te)},null,8,["title","icon"])]),_:1},8,["active"])]),_:1}),o.value===0||o.value===1||o.value===2||o.value===3?(m(),_(I,{key:0,xs:24,sm:24,md:24,lg:24,class:"search-content"},{default:t(()=>[a(F,{shadow:"never"},{header:t(()=>[w("div",We,[a(oe,{size:"18"},{default:t(()=>[a(ae)]),_:1}),w("span",Xe,n(l.$t("offMachine.scanArea")),1)])]),default:t(()=>[w("div",Ye,[o.value===0?(m(),_(A,{key:0,ref_key:"serialInput",ref:h,modelValue:s.value,"onUpdate:modelValue":i[0]||(i[0]=V=>s.value=V),placeholder:l.$t("offMachine.scanDevelopmentOrder"),class:"input-with-select",maxlength:"32",clearable:"",onKeydown:j(r(J),["enter"])},null,8,["modelValue","placeholder","onKeydown"])):v("",!0),o.value===1||o.value===2||o.value===3?(m(),_(A,{key:1,ref_key:"codeInput",ref:y,modelValue:c.value,"onUpdate:modelValue":i[1]||(i[1]=V=>c.value=V),placeholder:l.$t("offMachine.scanQrCode"),class:"input-with-select",maxlength:"13",clearable:"",onKeydown:j(r(le),["enter"])},null,8,["modelValue","placeholder","onKeydown"])):v("",!0)])]),_:1})]),_:1})):v("",!0),o.value===1||o.value===2||o.value===3?(m(),_(I,{key:1,xs:24,sm:24,md:24,lg:24},{default:t(()=>[e.value&&e.value.length>0?D((m(),_(ne,{key:0,title:l.$t("offMachine.developmentInfo"),column:3,"element-loading-text":q.value,border:"",style:{"margin-top":"20px"}},{default:t(()=>[a(f,{label:"Model"},{default:t(()=>[u(n(e.value[0].yhxq),1)]),_:1}),a(f,{label:l.$t("offMachine.ch")},{default:t(()=>[u(n(e.value[0].ch),1)]),_:1},8,["label"]),a(f,{label:l.$t("offMachine.cm")},{default:t(()=>[u(n(e.value[0].cm),1)]),_:1},8,["label"]),a(f,{label:l.$t("offMachine.pm")},{default:t(()=>[u(n(e.value[0].pm),1)]),_:1},8,["label"]),a(f,{label:l.$t("offMachine.ypjj")},{default:t(()=>[u(n(e.value[0].ypjj),1)]),_:1},8,["label"]),a(f,{label:l.$t("offMachine.styleno_mc")},{default:t(()=>[u(n(e.value[0].styleno_mc),1)]),_:1},8,["label"]),a(f,{label:l.$t("offMachine.ylzc")},{default:t(()=>[u(n(e.value[0].ylzc),1)]),_:1},8,["label"]),a(f,{label:l.$t("offMachine.cxm")},{default:t(()=>[u(n(e.value[0].cxm),1)]),_:1},8,["label"]),a(f,{label:l.$t("offMachine.cxm_ml")},{default:t(()=>[u(n(e.value[0].cxm_ml),1)]),_:1},8,["label"]),a(f,{label:l.$t("offMachine.cxm_bb")},{default:t(()=>[u(n(e.value[0].cxm_bb),1)]),_:1},8,["label"]),a(f,{label:l.$t("offMachine.yplx")},{default:t(()=>[a(se,{size:"small"},{default:t(()=>[u(n(e.value[0].yplx),1)]),_:1})]),_:1},8,["label"]),D(a(f,{label:l.$t("offMachine.qrcode")},{default:t(()=>[u(n(b.value?b.value:l.$t("offMachine.scanQrCode")),1)]),_:1},8,["label"]),[[Q,o.value===1&&b.value]])]),_:1},8,["title","element-loading-text"])),[[ce,p.value]]):v("",!0)]),_:1})):v("",!0),D(a(I,{xs:24,sm:24,md:24,lg:24,class:"submit-content"},{default:t(()=>[a(O,{type:"primary",onClick:H,onKeydown:j(z,["enter"])},{default:t(()=>[u(n(l.$t("offMachine.submit")),1)]),_:1}),a(O,null,{default:t(()=>[u(n(l.$t("offMachine.reset")),1)]),_:1})]),_:1},512),[[Q,o.value===3]]),o.value===4&&e.value.length===0?(m(),_(Ge,{key:2,msg:R.value},null,8,["msg"])):v("",!0)]),_:1})]),_:1})}}}),bl=Pe(el,[["__scopeId","data-v-795dc51a"]]);export{bl as default};
