<template>
  <div class="main">
    <!-- 路由占位符 -->
    <router-view>
      <transition name="fade">
        <keep-alive :max="10"> </keep-alive>
      </transition>
    </router-view>
  </div>
</template>
<script>
export default {
  data() {
    return {
     
    };
  },
  methods: {
   
  },
};
</script>
<style>
  /* 隐藏滚动条 */
  /* ::-webkit-scrollbar {
     width: 0 !important;
   }
   ::-webkit-scrollbar {
     width: 0 !important;height: 0;
   } */
.main {
  min-height: calc(100vh - 115px);
  margin-top: 30px;
  box-sizing: border-box;
  overflow: hidden;
  white-space: nowrap;
  overflow-y: scroll;
}

  /*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  /* // 滚动条的轨道的两端按钮，允许通过点击微调小方块的位置。 */
  ::-webkit-scrollbar-button {
    display: none;
  }
  /* // 滚动条里面的小方块，能向上向下移动（或往左往右移动，取决于是垂直滚动条还是水平滚动条） */
  ::-webkit-scrollbar-thumb {
    background: rgba(144, 147, 153, 0.3);
    cursor: pointer;
    border-radius: 4px;
  }
  /* // 边角，即两个滚动条的交汇处 */
  ::-webkit-scrollbar-corner {
    display: none;
  }
  /* // 两个滚动条的交汇处上用于通过拖动调整元素大小的小控件 */
  ::-webkit-resizer {
    display: none;
  }
.el-tab-pane {
  background-color: #fff;
}
</style>
