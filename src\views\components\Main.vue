<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-07-24 09:14:24
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-26 13:32:24
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\components\Main.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="main">
    <!-- This is a placeholder for the router view -->
    <keep-alive>
      <router-view>
        <transition name="fade">
          <!-- This keeps the component alive and caches up to 30 instances -->
        </transition>
      </router-view>
    </keep-alive>
  </div>
</template>
<script>
export default {
  data() {
    return {
     
    };
  },
  methods: {
   
  },
};
</script>
<style>
  /* 隐藏滚动条 */
  /* ::-webkit-scrollbar {
     width: 0 !important;
   }
   ::-webkit-scrollbar {
     width: 0 !important;height: 0;
   } */
body
{
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
.main {
  min-height: calc(100vh - 115px);
  box-sizing: border-box;
  white-space: nowrap;
  overflow-y: scroll;
  margin-top: 0px;
  padding: 0.625rem 0;
  overflow-x: hidden;
}


  /*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  /* // 滚动条的轨道的两端按钮，允许通过点击微调小方块的位置。 */
  ::-webkit-scrollbar-button {
    display: none;
  }
  /* // 滚动条里面的小方块，能向上向下移动（或往左往右移动，取决于是垂直滚动条还是水平滚动条） */
  ::-webkit-scrollbar-thumb {
    background: rgba(248, 249, 250, 0.3);
    cursor: pointer;
    border-radius: 4px;
  }
  /* // 边角，即两个滚动条的交汇处 */
  ::-webkit-scrollbar-corner {
    display: none;
  }
  /* // 两个滚动条的交汇处上用于通过拖动调整元素大小的小控件 */
  ::-webkit-resizer {
    display: none;
  }
.el-tab-pane {
  background-color: #fff;
}
</style>
