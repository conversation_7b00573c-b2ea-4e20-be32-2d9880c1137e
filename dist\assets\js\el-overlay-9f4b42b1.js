import{Q as I,aa as L,aw as V,bC as U,ab as Z,aB as $,i as c,bD as G,bE as _,c as B,aZ as q,a8 as g,G as Q,k as j,bF as D,av as H}from"./index-444b28c3.js";import{U as E}from"./event-fe80fd0c.js";import{a as x}from"./index-4d7f16ce.js";import{a as J}from"./index-eba6e623.js";const K=I({center:{type:Boolean,default:!1},alignCenter:{type:Boolean,default:!1},closeIcon:{type:L},customClass:{type:String,default:""},draggable:{type:Boolean,default:!1},fullscreen:{type:Boolean,default:!1},showClose:{type:Boolean,default:!0},title:{type:String,default:""}}),le={close:()=>!0},oe=I({...K,appendToBody:{type:<PERSON><PERSON><PERSON>,default:!1},beforeClose:{type:V(Function)},destroyOnClose:{type:<PERSON><PERSON>an,default:!1},closeOnClickModal:{type:<PERSON>olean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},lockScroll:{type:Boolean,default:!0},modal:{type:Boolean,default:!0},openDelay:{type:Number,default:0},closeDelay:{type:Number,default:0},top:{type:String},modelValue:{type:Boolean,default:!1},modalClass:String,width:{type:[String,Number]},zIndex:{type:Number},trapFocus:{type:Boolean,default:!1}}),te={open:()=>!0,opened:()=>!0,close:()=>!0,closed:()=>!0,[E]:e=>U(e),openAutoFocus:()=>!0,closeAutoFocus:()=>!0},ae=(e,a)=>{const o=Z().emit,{nextZIndex:v}=$();let C="";const F=x(),O=x(),t=c(!1),f=c(!1),u=c(!1),d=c(e.zIndex||v());let n,s;const P=G("namespace",_),T=B(()=>{const l={},i=`--${P.value}-dialog`;return e.fullscreen||(e.top&&(l[`${i}-margin-top`]=e.top),e.width&&(l[`${i}-width`]=q(e.width))),l}),h=B(()=>e.alignCenter?{display:"flex"}:{});function k(){o("opened")}function S(){o("closed"),o(E,!1),e.destroyOnClose&&(u.value=!1)}function w(){o("close")}function p(){s==null||s(),n==null||n(),e.openDelay&&e.openDelay>0?{stop:n}=D(()=>b(),e.openDelay):b()}function r(){n==null||n(),s==null||s(),e.closeDelay&&e.closeDelay>0?{stop:s}=D(()=>m(),e.closeDelay):m()}function y(){function l(i){i||(f.value=!0,t.value=!1)}e.beforeClose?e.beforeClose(l):r()}function A(){e.closeOnClickModal&&y()}function b(){H&&(t.value=!0)}function m(){t.value=!1}function N(){o("openAutoFocus")}function M(){o("closeAutoFocus")}e.lockScroll&&J(t);function z(){e.closeOnPressEscape&&y()}return g(()=>e.modelValue,l=>{l?(f.value=!1,p(),u.value=!0,d.value=e.zIndex?d.value++:v(),Q(()=>{o("open"),a.value&&(a.value.scrollTop=0)})):t.value&&r()}),g(()=>e.fullscreen,l=>{a.value&&(l?(C=a.value.style.transform,a.value.style.transform=""):a.value.style.transform=C)}),j(()=>{e.modelValue&&(t.value=!0,u.value=!0,p())}),{afterEnter:k,afterLeave:S,beforeLeave:w,handleClose:y,onModalClick:A,close:r,doClose:m,onOpenAutoFocus:N,onCloseAutoFocus:M,onCloseRequested:z,titleId:F,bodyId:O,closed:f,style:T,overlayDialogStyle:h,rendered:u,visible:t,zIndex:d}};export{le as a,oe as b,te as c,K as d,ae as u};
