<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-07-26 08:26:19
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-15 16:16:57
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\department\dailyworkLog.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-card class="top-card">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form :inline="true" :model="searchForm" class="demo-form-inline">
                        <el-form-item label="搜索">
                            <el-input v-model="searchForm.uuid" placeholder="请输入保全工号"></el-input>
                        </el-form-item>
                        <el-form-item label="部门">
                            <el-select v-model="searchForm.depart" placeholder="请选择部门">
                                <el-option label="织造车间" value="织造车间"></el-option>
                                <el-option label="整理车间" value="整理车间"></el-option>
                                <el-option label="打样车间" value="打样车间"></el-option>
                                <el-option label="综合维修" value="综合维修"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="班次">
                            <el-select v-model="searchForm.shift" placeholder="请选择班次">
                                <el-option label="A" value="A"></el-option>
                                <el-option label="B" value="B"></el-option>
                                <el-option label="C" value="C"></el-option>
                                <el-option label="D" value="D"></el-option>
                                <el-option label="E" value="E"></el-option>
                                <el-option label="E1" value="E1"></el-option>
                                <el-option label="E2" value="E2"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="日期">
                            <el-date-picker v-model="searchForm.date" type="date" placeholder="选择日期"
                                value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="search" icon="el-icon-search">查询</el-button>
                            <el-button type="button" icon="el-icon-refresh" @click="resetData">重置</el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" plain @click="refresh" icon="el-icon-refresh-right"></el-button>
                        </el-form-item>
                    </el-form>
                </el-col>
            </el-row>
            <!-- </el-card>
        <el-card class="worklist-content"> -->
            <!-- 主体部分 -->
            <el-table :data="tableData" style="width: 100%" v-loading="loading" element-loading-text="Flyknit">
                <el-table-column type="expand">
                    <template slot-scope="props">
                        <span class="el-icon-tickets"></span> <span style="font-size: 16px;">工作明细列表</span>
                        <div class="detail-content">
                            <div v-for="(item, index) in props.row.details" :key="index">
                                <el-card class="box-card">
                                    <div class="clearfix">
                                        <span>工作内容:</span>
                                        <span>{{ item.content }}</span>
                                        <span>得分:</span>
                                        <span>{{ item.efficiency }}</span>
                                    </div>
                                    <div class="device-list">
                                        <template v-if="item.machineList.length > 0">
                                            <span v-for="(item1, index1) in item.machineList" :key="index1">
                                                <el-image :src="url" fit="fill" style="width:100px;height:50px;"></el-image>
                                                {{ item1 }}
                                            </span>
                                        </template>
                                        <template v-else>
                                            <div
                                                style="display:flex;flex-direction:column;justify-content:center;align-items:center;">
                                                <el-image :src="src" style="width:200px;height:200px;"></el-image>
                                                <h5>暂无设备信息~</h5>
                                            </div>
                                        </template>
                                    </div>
                                </el-card>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="日期" prop="date">
                </el-table-column>
                <el-table-column label="工号" prop="username">
                </el-table-column>
                <el-table-column label="班次" prop="shift">
                </el-table-column>
                <el-table-column label="所属部门" prop="department">
                </el-table-column>
                <el-table-column label="工作得分" prop="efficiency">
                    <template slot-scope="scope">
                        <span style="color: green;">{{ (scope.row.efficiency).toFixed(2) }}</span>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <div class="pagination">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="currentPage" :page-sizes="[15, 20, 25, 30]" :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper" :total="total">
                </el-pagination>
            </div>
        </el-card>
    </div>
</template>
<script>
export default {
    data() {
        return {
            loading: true,
            searchForm: {
                uuid: '',
                date: '',
                shift: '',
                depart: '',
            },
            currentPage: 1,
            page: 1,
            pageSize: 15,
            total: 0,
            tableData: [], // 工作日志列表
            url: '../src/assets/image/stoll.jpg',
            src: '../src/assets/image/data.png',
        }
    },
    mounted() {
        this.getWorkList()
    },
    methods: {
        // 获取工作日志列表
        async getWorkList() {
            try {
                const obj = {
                    page: this.page,
                    pageSize: this.pageSize,
                    uuid: this.searchForm.uuid,
                    dates: this.searchForm.date,
                    shift: this.searchForm.shift,
                    department: this.searchForm.depart,
                }
                const res = await this.$http.getWorkLists(obj)
                if (res.status !== 200) return this.$message.error(res.message)
                const { list, total } = res.data
                this.tableData = list
                this.total = total
                setTimeout(() => {
                    this.loading = false
                }, 500)
                return false
            } catch (error) {
                this.loading = false
                return this.$message.error(error)
            }
        },
        search() {
            this.loading = true
            this.getWorkList()
        },
        handleSizeChange(val) {
            this.loading = true
            this.pageSize = val
            this.getWorkList()
        },
        handleCurrentChange(val) {
            this.loading = true
            this.page = val
            this.getWorkList()
        },
        refresh() {
            this.page = 1
            this.loading = true
            this.getWorkList()
        },
        resetData() {
            this.page = 1
            this.searchForm = {
                uuid: '',
                date: '',
                shift: '',
                depart: '',
            }
            this.loading = true
            this.getWorkList()
        }
    }
}
</script>
<style lang="scss">
.clearfix {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;

    span {
        font-size: 16px;
        margin: 0 8px;
    }
}

.clearfix span:nth-child(2) {
    color: #517DF7;
    margin-left: 0px;
}

.clearfix span:nth-child(4) {
    color: #517DF7;
    margin-left: 0px;
}

.detail-content {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: 15px;
    flex-wrap: wrap;
}

.box-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 5px;
}

.device-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    padding: 20px 0;

    span {
        display: flex;
        flex-direction: column;
        padding: 5px;
        background-color: #ECF5FF;
        margin: 5px;
        text-align: center;
        color: #999;
    }
}

.demo-table-expand {
    font-size: 0;
}

.demo-table-expand label {
    width: 90px;
    color: #99a9bf;
}

.demo-table-expand .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    width: 50%;
}

.worklist-content {
    margin-top: 5px;
}

.pagination {
    margin: 20px 0;
}

.top-card {
    .el-card__body {
        padding: 20px 0 0 20px;
    }
}</style>