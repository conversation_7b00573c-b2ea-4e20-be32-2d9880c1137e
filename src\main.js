/*
 * @Author: flyknit <EMAIL>
 * @Date: 2024-04-09 09:02:42
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-05-20 16:22:40
 * @FilePath: \electronic-filed:\gitee\backend-demo\src\main.js
 * @Description: 
 * 
 * Copyright (c) 2024 by ${<EMAIL>}, All Rights Reserved. 
 */
import Vue from "vue";
Vue.config.productionTip = false;
import App from "./App.vue";
import router from "./router/index.js";
import store from "./store/index.js";
import ElementUI, { Message } from "element-ui";
import 'element-ui/lib/theme-chalk/index.css';
// import "./theme/index.css";
Vue.use(ElementUI);
Vue.prototype.$store = store;
// 富文本编辑器
import VueQuillEditor from 'vue-quill-editor'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
Vue.use(VueQuillEditor)
//引入axios请求库
import axios from "axios";
Vue.prototype.$http = axios;
// 引入打印库
import Print from "vue-print-nb";
Vue.use(Print);
//引入自定义请求库
import request from "./http/home.js";
Vue.prototype.$http = request;
import qs from "qs";
// qs转换
Vue.prototype.$qs = qs;
//全部导入
import _ from "lodash";
//只导入需要的模块
import cloneDeep from "lodash/cloneDeep";

//挂载到vue原型上
Vue.prototype._ = _; //全部导入的挂载方式
Vue.prototype.cloneDeep = cloneDeep; //导入某个功能的挂载方式
new Vue({
  router,
  store,
  render: (h) => h(App),
}).$mount("#app");
