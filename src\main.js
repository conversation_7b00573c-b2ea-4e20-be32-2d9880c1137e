/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2022-11-23 08:54:47
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-26 12:38:29
 * @FilePath: \electronic-filed:\AppServ\www\WeTalk-Admin\src\main.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'
import App from './App.vue'
import router from './router/index.js'
import store from './store/index.js'
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
// 引入windi.css
// import 'virtual:windi.css'
import Clipboard from 'clipboard';
Vue.prototype.$Clipboard = Clipboard;
Vue.use(ElementUI);
Vue.prototype.$store = store
//引入axios请求库
import axios from 'axios'
Vue.prototype.$http = axios
// 引入打印库
import Print from 'vue-print-nb'
Vue.use(Print);
//引入自定义请求库
import request from './http/home.js'
Vue.prototype.$http = request
import qs from 'qs';
// qs转换
Vue.prototype.$qs = qs;
//全部导入
import _ from 'lodash'
//只导入需要的模块
import cloneDeep from "lodash/cloneDeep";

Vue.config.productionTip = false
//挂载到vue原型上
Vue.prototype._ = _  //全部导入的挂载方式
Vue.prototype.cloneDeep = cloneDeep //导入某个功能的挂载方式
new Vue({
  router,
  store,
	render: h => h(App)
}).$mount('#app')
