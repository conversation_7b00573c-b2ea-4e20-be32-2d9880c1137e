<template>
	<div>
		<!-- 添加全屏loading -->
		<el-loading
			:text="loadingText"
			:fullscreen="true"
			v-loading.fullscreen.lock="fullscreenLoading">
		</el-loading>
		<el-card shadow="always" v-loading="loading">
			<div style="padding: 0 10px;">
				<el-form :inline="true" :model="forms" size="medium" ref="forms" class="forms">
					<el-form-item label="设备名称/编号">
						<el-input 
							placeholder="输入设备名称、编号搜索" 
							v-model="keyword" 
							clearable 
							@clear="() => clearHandle('keyword')"
							@input="debounceSearch">
						</el-input>
					</el-form-item>
					<!-- 设备选型 -->
					<el-form-item label="设备分类">
						<el-select 
							clearable 
							placeholder="请选择设备分类" 
							v-model="classify" 
							@clear="() => clearHandle('classify')"
							@change="handleClassifyChange">
							<el-option v-for="(item, index) in classifyList" :label="item.class_name"
								:value="item.class_name" :key="index"></el-option>
						</el-select>
					</el-form-item>
					<!-- 设备类型，固定资产， 纳入资产 -->
					<el-form-item label="资产类型">
						<el-select 
							placeholder="请选择资产类型" 
							v-model="device_type" 
							@clear="() => clearHandle('device_type')"
							@change="handleTypeChange"
							clearable>
							<el-option label="固定资产" value="固定资产"></el-option>
							<el-option label="纳入管理" value="纳入管理"></el-option>
							<el-option label="特殊资产" value="特殊资产"></el-option>
							<el-option label="特种设备" value="特种设备"></el-option>
						</el-select>
					</el-form-item>
					<!-- 部门选择 -->
					<el-form-item label="所属部门">
						<el-select 
							placeholder="请选择部门" 
							@clear="() => clearHandle('department')"
							@change="handleDepartmentChange"
							v-model="department" 
							clearable>
							<el-option label="IT" value="IT"></el-option>
							<el-option label="织造车间" value="织造车间"></el-option>
							<el-option label="整理车间" value="整理车间"></el-option>
							<el-option label="检验车间" value="检验车间"></el-option>
							<el-option label="打样车间" value="打样车间"></el-option>
							<el-option label="综合维修" value="综合维修"></el-option>
							<el-option label="化验室" value="化验室"></el-option>
							<el-option label="行政办公室" value="行政办公室"></el-option>
							<el-option label="办公室" value="办公室"></el-option>
							<el-option label="生产办公室" value="生产办公室"></el-option>
							<el-option label="食堂" value="食堂"></el-option>
							<el-option label="计划部门" value="计划部门"></el-option>
							<el-option label="五金仓库" value="五金仓库"></el-option>
							<el-option label="成品仓库" value="成品仓库"></el-option>
							<el-option label="原料仓库" value="原料仓库"></el-option>
						</el-select>
					</el-form-item>
					<!-- 展开/收起按钮 -->
					<el-form-item>
						<el-button type="text" @click="toggleAdvanced">
							{{ isAdvanced ? '收起' : '展开更多' }}
							<i :class="isAdvanced ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
						</el-button>
					</el-form-item>
				</el-form>

				<!-- 高级搜索区域 -->
				<el-form :inline="true" v-show="isAdvanced" size="medium" class="advanced-search">
					<!-- 设备状态 -->
					<el-form-item label="设备状态">
						<el-select 
							placeholder="请选择设备状态" 
							v-model="status" 
							@change="handleStatusChange"
							@clear="() => clearHandle('status')"
							clearable>
							<el-option label="在用" value="在用"></el-option>
							<el-option label="闲置" value="闲置"></el-option>
							<el-option label="报废" value="报废"></el-option>
							<el-option label="已调拨" value="已调拨"></el-option>
							<el-option label="维修中" value="维修中"></el-option>
							<el-option label="借用" value="借用"></el-option>
						</el-select>
					</el-form-item>
					<!-- 操作区域 -->
					<el-form-item>
						<el-button type="primary" icon="el-icon-search" @click="searchDevice">搜索</el-button>
						<el-button plain @click="resetForm" icon="el-icon-refresh">重置</el-button>
						<el-button type="primary" plain icon="el-icon-refresh-right" @click="refreshData">刷新</el-button>
					</el-form-item>
				</el-form>
			</div>
			<!-- 表格区域 -->
			<div style="margin-top: 10px;">
				<el-row :gutter="0" style="display: flex;justify-content:flex-start;align-items:center;">
					<el-col :span="6" class="table-name">设备台账 (总计：{{ total }})
						<!-- <el-link type="primary" href="https://element.eleme.io"
							target="_blank"><i class="el-icon-tickets"></i>Excel模板(导入前请务必下载此模板)</el-link> -->
						</el-col>
					<el-col :span="18" style="display: flex;justify-content:flex-end;align-items:center;">
						<el-col :span="1"><span>操作：</span></el-col>
						<el-col :span="6" class="action-area">
							<el-col :span="4">
								<template>
									<el-tooltip content="添加" effect="dark" placement="top">
										<i class="el-icon-plus lg-text" @click="showAddDialog" title="添加设备"></i>
									</el-tooltip>
								</template>
							</el-col>
							<el-col :span="4">
								<i class="el-icon-delete lg-text" @click="delItem" title="删除设备"></i>
							</el-col>
							<el-col :span="4">
								<i class="el-icon-c-scale-to-original lg-text" @click="toCode" title="生成条码"></i>
							</el-col>
							<el-col :span="4">
								<i class="el-icon-sort lg-text" @click="transferMachine" title="设备转移"></i>
							</el-col>
							<el-col :span="4">
								<el-upload class="upload-demo" ref="upload" :action="actions" :headers="headers"
									accept=".xlsx, .xls" name="files" :before-upload="beforeUpload" :show-file-list="false"
									:file-list="files" :auto-upload="true" :on-change="onChange" multiple :limit="limits">
									<i class="el-icon-upload2 lg-text" slot="trigger" title="导入Excel"></i>
								</el-upload>
							</el-col>
							<el-col :span="4">
								<i class="el-icon-download lg-text" @click="exportData" title="导出Excel"></i>
							</el-col>
						</el-col>
					</el-col>
				</el-row>
				<el-row :gutter="10" style="padding: 0px 10px">
					<el-col :span="24">
						<!-- 修改表格区域的loading -->
						<el-table 
							ref="multipleTable" 
							:data="tableData" 
							v-loading="loadings"
							:element-loading-text="loadingText"
							element-loading-spinner="el-icon-loading"
							element-loading-background="rgba(255, 255, 255, 0.8)"
							max-height="600"
							tooltip-effect="dark" 
							stripe 
							style="width: 100%"
							@selection-change="handleSelectionChange"
							:fit="false">
							<el-table-column type="selection" width="55" fixed="left"> </el-table-column>
							<el-table-column label="设备编号" prop="device_number" width="140" sortable fixed="left">
							</el-table-column>
							<el-table-column label="RFID" prop="rfid" sortable width="80">
								<template slot-scope="scope">
									<el-tag v-if="scope.row.rfid" type="success">{{ scope.row.rfid }}</el-tag>
									<el-tag v-else type="info">未绑定</el-tag>
								</template>
							</el-table-column>
							<el-table-column prop="device_name" label="设备名称" sortable width="140"> 
							</el-table-column>
							<el-table-column prop="classify" label="设备分类" sortable show-overflow-tooltip width="100">
							</el-table-column>
							<el-table-column prop="type" label="资产类型" sortable show-overflow-tooltip width="100">
							</el-table-column>
							<el-table-column prop="device_model" label="规格型号" sortable show-overflow-tooltip width="150">
								<template slot-scope="scope">
									<el-tag v-if="scope.row.device_model">{{ scope.row.device_model }}</el-tag>
									<el-tag v-else type="info">暂无</el-tag>
								</template>
							</el-table-column>
							<el-table-column prop="serial" label="序列号" sortable show-overflow-tooltip width="150">
								<template slot-scope="scope">
									<el-tag v-if="scope.row.serial">{{ scope.row.serial }}</el-tag>
									<el-tag v-else type="info">暂无</el-tag>
								</template>
							</el-table-column>
							<el-table-column prop="brand" label="品牌" sortable show-overflow-tooltip width="100">
								<template slot-scope="scope">
									<el-tag v-if="scope.row.brand">{{ scope.row.brand }}</el-tag>
									<el-tag v-else type="info">暂无</el-tag>
								</template>
							</el-table-column>
							<el-table-column prop="supplier" label="供应商" sortable show-overflow-tooltip width="200">
								<template slot-scope="scope">
									<el-tag v-if="scope.row.supplier">{{ scope.row.supplier }}</el-tag>
									<span v-else type="info">暂无</span>
								</template>
							</el-table-column>
							<el-table-column prop="unit" label="单位" sortable show-overflow-tooltip width="100">
								<template slot-scope="scope">
									<el-tag v-if="scope.row.unit" type="info">{{ scope.row.unit }}</el-tag>
									<span v-else type="info">暂无</span>
								</template>
							</el-table-column>
							<el-table-column prop="status" label="设备状态" sortable width="100" show-overflow-tooltip>
								<template slot-scope="scope">
									<el-tag
										:type="scope.row.status === 0 ? 'success' : scope.row.status === 1 ? 'info' : scope.row.status === 2 ? 'warning' : 'danger'">{{
											scope.row.status === 0 ? "在用" : scope.row.status === 1 ? '闲置' : scope.row.status === 2 ? '报废' : scope.row.status === 3 ? '已调拨' : scope.row.status === 4 ? '维修中' : scope.row.status === 5 ? '借用' : '未知'
										}}</el-tag>
								</template>
							</el-table-column>
							<el-table-column :formatter="moneyFormatter" prop="device_price" label="设备金额($)" width="100">
								<template slot-scope="scope">
									<el-tag v-if="scope.row.device_price" type="danger">{{ scope.row.device_price }}</el-tag>
									<span v-else type="info">0.00</span>
								</template>
							</el-table-column>
							<el-table-column prop="belong_user" label="使用人" sortable width="100">
								<template slot-scope="scope">
									<el-tag v-if="scope.row.belong_user" type="warning">{{ scope.row.belong_user }}</el-tag>
									<span v-else type="info">暂无</span>
								</template>
							</el-table-column>
							<el-table-column prop="date_manufacture" label="出厂日期" sortable width="100">
								<template slot-scope="scope">
									<el-tag v-if="scope.row.date_manufacture">{{ scope.row.date_manufacture }}</el-tag>
									<el-tag v-else type="info">暂无</el-tag>
								</template>
							</el-table-column>
							<el-table-column prop="date_buy" label="购入日期" sortable width="100">
							</el-table-column>
							<el-table-column prop="device_location" label="存放地点" sortable width="160">
							</el-table-column>
							<el-table-column prop="department" label="所属部门" sortable width="100">
							</el-table-column>
							<el-table-column prop="responsibile_user" label="责任部门" sortable width="100">
							</el-table-column>
							<el-table-column prop="create_time" label="入库日期" sortable width="100">
							</el-table-column>
							<el-table-column label="操作" fixed="right" width="100">
								<template slot-scope="scope">
									<el-button type="text" size="mini" icon="el-icon-more"
										@click.native="toDetail(scope.row)">设备详情</el-button>
								</template>
							</el-table-column>
						</el-table>
					</el-col>
				</el-row>
			</div>
			<!-- 分页 -->
			<el-row :gutter="10" v-if="this.tableData.length > 0" style="padding: 10px 10px">
				<el-col :span="24" class="pages">
					<el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
						:current-page="currentPage" :page-sizes="[10, 20, 30, 40]" :page-size="10"
						layout="total, sizes, prev, pager, next, jumper" :total="total">
					</el-pagination>
				</el-col>
			</el-row>
		</el-card>
		<!-- 右侧设备详情展示 -->
		<Drawer :visible.sync="dialogVisible" :loading.sync="loading" loadingColor="#409EFF" :headerShow="true"
			:closeBtnShow="true" main-background="#fff" width="980px" align="right" title="设备详情" title-color="#000"
			:modal="true" close-on-click-modal @close="close">
			<div class="drawer-container">
				<el-row :gutter="10">
					<div style="display: flex; padding: 0 10px">
						<el-col :span="6" style="padding: 0 15px;display:flex;align-items:center;">
							<el-col :span="4"><i class="el-icon-news"></i></el-col>
							<el-col :span="4" class="sb-title">{{
								detailList.device_number
							}} - 【{{ detailList.type }}】</el-col>
						</el-col>
						<el-col :span="12"></el-col>
						<el-col :span="6">
							<el-col :span="10">
								<el-button type="default" size="mini" plain icon="el-icon-postcard"
									@click="changeCode(detailList.device_number)">二维码</el-button>
							</el-col>
							<el-col :span="14">
								<el-button type="primary" size="mini" icon="el-icon-edit" @click="editInfo">编
									辑</el-button>
							</el-col>
						</el-col>
					</div>
					<!-- 详情页头部开始 -->
					<el-col :span="24" class="top-drawer">

						<div style="padding: 10px 0 0 10px">
							<!-- 头部设备明细 -->
							<el-col :span="24" class="bottom-detail__box">
								<el-col :span="4" style="box-sizing: border-box; overflow: hidden; padding: 0">
									<!-- 设备图片 -->
									<!-- <el-image :src="detailList.device_image" fit="fill" v-if="codeImg.length < 1"
										style="height: 95px">
										<div slot="placeholder" class="image-slot">
											加载中<span class="dot">...</span>
										</div>
									</el-image> -->
									<img :src="detailList.device_image ? detailList.device_image : defaultImg" style="width:95px;height: 95px" :alt="detailList.device_image ? detailList.device_image : '加载中...'"
										v-if="codeImg.length < 1">
									<!-- 设备二维码 -->
									<!-- <el-image :src="codeImg" fit="cover" style="width:95px;height: 95px" v-else>
										<div slot="placeholder" class="image-slot">
											加载中<span class="dot">...</span>
										</div>
									</el-image> -->
									<!-- <img :src="codeImg" style="width:95px;height: 95px" alt="加载失败..." v-else> -->
								</el-col>
								<el-col :span="5">
									<el-row :gutter="10">
										<el-col :span="8">设备名称:</el-col>
										<el-col :span="16" class="right-text">{{
											detailList.device_name
										}}</el-col>
									</el-row>
									<el-row :gutter="10">
										<el-col :span="8">设备类型:</el-col>
										<el-col :span="16" class="right-text">{{
											detailList.classify ? detailList.classify : "暂无"
										}}</el-col>
									</el-row>
									<el-row :gutter="10">
										<el-col :span="8">设备位置:</el-col>
										<el-col :span="16" class="right-text">{{
											detailList.device_location
										}}</el-col>
									</el-row>
								</el-col>
								<el-col :span="5">
									<el-row :gutter="10">
										<el-col :span="8">规格型号:</el-col>
										<el-col :span="16" class="right-text">{{
											detailList.device_model ? detailList.device_model : "暂无"
										}}</el-col>
									</el-row>
									<el-row :gutter="10">
										<el-col :span="8">设备金额:</el-col>
										<el-col :span="16" class="right-text">{{
											detailList.device_price > 0
											? detailList.device_price
											: "0.00"
										}}</el-col>
									</el-row>
									<el-row :gutter="10">
										<el-col :span="8">责任部门:</el-col>
										<el-col :span="16" class="right-text">{{
											detailList.department
										}}</el-col>
									</el-row>
								</el-col>
								<el-col :span="5">
									<el-row :gutter="10">
										<el-col :span="8">序列号:</el-col>
										<el-col :span="16" class="right-text">{{
											detailList.serial ? detailList.serial : "暂无"
										}}</el-col>
									</el-row>
									<el-row :gutter="10">
										<el-col :span="8">出厂日期:</el-col>
										<el-col :span="16" class="right-text">{{
											detailList.date_manufacture
											? detailList.date_manufacture
											: "暂无"
										}}</el-col>
									</el-row>
									<el-row :gutter="10">
										<el-col :span="8">入库日期:</el-col>
										<el-col :span="16" class="right-text">{{
											detailList.create_time
										}}</el-col>
									</el-row>
								</el-col>

								<el-col :span="5" class="right-status">
									<el-col :span="24">设备状态</el-col>
									<el-col :span="24">
										<el-tag
											:type="detailList.status === 0 ? 'success' : detailList.status === 1 ? 'info' : detailList.status === 2 ? 'warning' : 'danger'">
											{{
												detailList.status === 0 ? "正常运行" : detailList.status === 1 ? "闲置" :
												detailList.status === 2 ? "待维修" : "已报废"
											}}
										</el-tag>
									</el-col>
								</el-col>

							</el-col>
							<!-- 设备详情 -->
							<template>
								<div style="display: flex;flex-direction:column;justify-content: flex-start;border: 1px solid #eee;padding: 0.5rem">
									<el-row>
										<el-col :span="24" class="details-title"> <i class="el-icon-warning-outline"></i>
											<span class="title">保修详情</span> </el-col>
									</el-row>
									<el-row :gutter="20" class="bottom-details__boxs">
										<el-col :span="8" style="display: flex;">
											<el-col :span="8">开保日期:</el-col>
											<el-col :span="16" class="right-text">{{
												detailList.warranty_start ? detailList.warranty_start : "暂无"
											}}</el-col>
										</el-col>
										<el-col :span="8" style="display: flex;">
											<el-col :span="8">过保日期:</el-col>
											<el-col :span="16" class="right-text">{{
												detailList.warranty_end ? detailList.warranty_end : "暂无"
											}}</el-col>
										</el-col>
										<el-col :span="8" style="display: flex;">
											<el-col :span="8">保修状态:</el-col>
											<el-col :span="16" class="right-text">
												<el-tag
													:type="detailList.warranty_status === '保修中' ? 'success' : 'warning'">
													{{
														detailList.warranty_status ? detailList.warranty_status : "未知"
													}}
												</el-tag>
											</el-col>
										</el-col>
									</el-row>
								</div>
							</template>
						</div>
					</el-col>

					<!-- 详情页头部结束 -->
					<!-- 保养数据 -->
					<el-row>
						<el-col :span="24" class="maintaince-title"> <i class="el-icon-document"></i> <span
								class="title">设备维护保养记录</span> </el-col>
					</el-row>
					<el-col :span="24" class="bottom-drawer">
						<el-tabs v-model="activeName" @tab-click="handleClick">
							<el-tab-pane :label="item.name" :name="item.key" v-for="(item, index) in tabList"
								:key="index"></el-tab-pane>
						</el-tabs>
						<!-- 展示数据 -->
						<div style="padding: 0 20px">
							<div class="option-box">
								<el-radio v-model="radio" label="1">今日</el-radio>
								<el-radio v-model="radio" label="2">昨日</el-radio>
								<el-radio v-model="radio" label="3">本周</el-radio>
								<el-radio v-model="radio" label="4">本月</el-radio>
								<el-radio v-model="radio" label="5">季度</el-radio>
								<el-radio v-model="radio" label="6">近半年</el-radio>
							</div>
							<div class="task-box">
								<!-- 巡检记录 -->
								<div v-if="tabIndex == 0">
									<div class="task-item" v-for="item in 6" :key="item">
										<div class="item-top">
											<el-button type="primary" size="mini" icon="el-icon-finished">已完成</el-button>
											<span>TZML20254154515</span>
										</div>
										<div class="item-bottom">
											<i class="el-icon-user"></i><span class="">巡检人员:杨晓伟</span>
											<i class="el-icon-time"></i><span>开始时间: 2022-01-25 10:20:13</span>
											<i class="el-icon-alarm-clock"></i><span>结束时间: 2022-01-25 10:25:33</span>
										</div>
									</div>
								</div>
								<!-- 保养记录 -->
								<div v-if="tabIndex == 1">
									<div class="task-item" v-for="item in 6" :key="item">
										<div class="item-top">
											<el-button type="primary" size="mini" icon="el-icon-finished">已完成</el-button>
											<span>TZML20254152341</span><span>【日保养-清洗针床】</span>
										</div>
										<div class="item-bottom">
											<i class="el-icon-user"></i><span class="">保养人员:黄少锋</span>
											<i class="el-icon-time"></i><span>开始时间: 2022-01-25 10:20:13</span>
											<i class="el-icon-alarm-clock"></i><span>结束时间: 2022-01-25 10:25:33</span>
										</div>
									</div>
								</div>
								<!-- 维修记录 -->
								<div v-if="tabIndex == 2">
									<div class="task-item" v-for="item in 6" :key="item">
										<div class="item-top">
											<el-button type="primary" size="mini" icon="el-icon-finished">已完成</el-button>
											<span>TZML20254157687</span><span>【更换牵拉马达 x 1】</span>
										</div>
										<div class="item-bottom">
											<i class="el-icon-user"></i><span class="">维修人员:冷继宽</span>
											<i class="el-icon-time"></i><span>开始时间: 2022-01-25 10:20:13</span>
											<i class="el-icon-alarm-clock"></i><span>结束时间: 2022-01-25 10:25:33</span>
										</div>
									</div>
								</div>
							</div>
						</div>
					</el-col>
				</el-row>
			</div>
		</Drawer>
		<!-- 添加设备 -->
		<el-dialog :title="title" :visible.sync="dialogShow" width="45%">
			<div class="add-box">
				<el-form ref="form" :model="form" label-width="80px" :rules="rules">
					<el-form-item label="设备名称" prop="name">
						<el-input v-model="form.name" placeholder="请输入设备名称"></el-input>
					</el-form-item>
					<el-form-item label="设备编号" prop="machine_number">
						<el-input v-model="form.machine_number" placeholder="请输入设备编号"></el-input>
					</el-form-item>
					<el-form-item label="设备分类" prop="classify">
						<el-select v-model="form.classify" placeholder="请选择设备分类">
							<el-option v-for="(item, index) in classifyList" :label="item.class_name"
								:value="item.class_name" :key="index"></el-option>
						</el-select>
						<span @click="show = !show" style="color: #409eff; margin: 0 5px; cursor: pointer"><i
								class="el-icon-plus" color="#409EFF"></i> 添加分类</span>
						<span v-show="show"><el-input style="width: 180px; margin: 0 5px" placeholder="请输入分类名称"
								v-model="className">
							</el-input><el-button type="primary" @click="addClassifyName">添加</el-button>
						</span>
					</el-form-item>
					<el-form-item label="设备类型" prop="type">
						<el-radio-group v-model="form.type">
							<el-radio label="固定资产"></el-radio>
							<el-radio label="纳入资产"></el-radio>
							<el-radio label="特殊资产"></el-radio>
							<el-radio label="特种设备"></el-radio>
						</el-radio-group>
					</el-form-item>
					<el-form-item label="规格型号" prop="spec">
						<el-input v-model="form.spec" placeholder="请输入规格型号"></el-input>
					</el-form-item>
					<el-form-item label="序列号" prop="serial">
						<el-input v-model="form.serial" placeholder="请输入序列号"></el-input>
					</el-form-item>
					<el-form-item label="供应商" prop="factory">
						<el-input v-model="form.factory" placeholder="请输入供应商名称"></el-input>
					</el-form-item>
					<el-form-item label="品牌" prop="brand">
						<el-input v-model="form.brand" placeholder="请输入品牌"></el-input>
					</el-form-item>
					<!-- 美金金额 -->
					<el-form-item label="设备金额$">
						<el-input v-model="form.money" type="number" placeholder="请输入设备金额" @input="changeMoney"></el-input>
					</el-form-item>
					<!-- 越盾金额 -->
					<el-form-item label="越盾金额" prop="moneyvn">
						<el-input disabled v-model="form.moneyvn" type="text" placeholder="请输入越盾金额"></el-input>
					</el-form-item>
					<el-form-item label="出厂日期" prop="dates">
						<el-col :span="11">
							<el-date-picker type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="选择日期"
								v-model="form.dates"></el-date-picker>
						</el-col>
						<el-col :span="11"> </el-col>
					</el-form-item>
					<!-- 保修期限 -->
					<el-form-item label="保修日期">
						<el-date-picker v-model="form.warranty" type="daterange" range-separator="至"
							start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd">
						</el-date-picker>
					</el-form-item>
					<!-- <el-form-item label="是否闲置" prop="status">
						<el-switch v-model="form.status"></el-switch>
					</el-form-item> -->
					<el-form-item label="存放地点" prop="location">
						<el-select v-model="form.location" placeholder="请选择存放地点">
							<el-option v-for="(item, index) in areaList" :key="index" :label="item.name + '-' + item.sub"
								:value="item.name + '-' + item.sub"></el-option>
						</el-select>
						<span @click="isshow = !isshow" style="color: #409eff; margin: 0 5px; cursor: pointer"><i
								class="el-icon-plus" color="#409EFF"></i> 添加地点</span>
						<span v-show="isshow"><el-input style="width: 130px; margin: 0 5px" placeholder="请输入部门"
								v-model="departName">
							</el-input>
							<el-input style="width: 180px; margin: 0 5px" placeholder="请输入车间地点"
								v-model="departAddress"></el-input>
							<el-button type="primary" @click="addDepart">添加</el-button>
						</span>
					</el-form-item>
					<el-form-item label="责任部门" prop="department">
						<el-select v-model="form.department" placeholder="请选择责任部门">
							<el-option label="IT" value="IT"></el-option>
							<el-option label="织造车间" value="织造车间"></el-option>
							<el-option label="整理车间" value="整理车间"></el-option>
							<el-option label="检验车间" value="检验车间"></el-option>
							<el-option label="打样车间" value="打样车间"></el-option>
							<el-option label="综合维修" value="综合维修"></el-option>
							<el-option label="行政办公室" value="行政办公室"></el-option>
							<el-option label="食堂" value="食堂"></el-option>
							<el-option label="化验室" value="化验室"></el-option>
							<el-option label="设备部门" value="设备部门"></el-option>
						</el-select>
					</el-form-item>
					<!-- 报修电话 -->
					<el-form-item label="保修电话" prop="contact">
						<el-input maxlength="15" v-model="form.contact" type="text" placeholder="请输入商家保修电话"></el-input>
					</el-form-item>
					<!-- 设备状态 -->
					<el-form-item label="设备状态" prop="status">
						<el-radio-group v-model="form.status">
							<el-radio label="正常"></el-radio>
							<el-radio label="闲置"></el-radio>
							<el-radio label="报废"></el-radio>
							<el-radio label="已调拨"></el-radio>
							<el-radio label="维修中"></el-radio>
							<el-radio label="借用"></el-radio>
						</el-radio-group>
					</el-form-item>
					<el-form-item>
						<!-- 添加图片 -->
						<el-row>
							<div class="shift-class" style="margin-left: -70px">设备图片</div>
							<div style="">
								<el-upload :action="action" :headers="headers" list-type="picture-card" name="files"
									accept=".jpg,.png,.webp,.jfif" :limit="1" :on-remove="logoRemove"
									:on-success="logoSuccess" :on-preview="handleview" :on-error="onErr"
									:on-exceed="handleExceed" :before-upload="project" :file-list="logo">
									<i class="el-icon-plus"></i>
								</el-upload>
								<!-- 大图展开 -->
								<el-dialog :modal="false" :visible.sync="dialogVisibles">
									<img width="100%" :src="dialogImageUrl" alt="加载失败" />
									<!-- <el-image :src="dialogImageUrl" fit="fill" lazy></el-image> -->
								</el-dialog>
							</div>
						</el-row>
					</el-form-item>
				</el-form>
			</div>
			<div class="footer">
				<el-button @click="resetForm('form')">重 置</el-button>
				<el-button type="primary" @click="onSubmit('form')">{{
					btnText
				}}</el-button>
			</div>
		</el-dialog>
		<!-- 生成二维码 -->
		<Drawer :visible.sync="showVisible" :loading.sync="loading" loadingColor="#409EFF" :headerShow="true"
			:closeBtnShow="true" main-background="#fff" width="790px" align="right" title="生成二维码" title-color="#000"
			:modal="true" close-on-click-modal @close="closeRightBox">
			<div class="code-area">
				<div class="menu">
					<el-button type="" plain size="mini" icon="el-icon-delete" @click="clearCode">清空</el-button>
					<el-button type="primary" size="mini" icon="el-icon-printer" v-print="'#printMe'">打印</el-button>
				</div>
				<div class="code-box" v-if="codeImgList.length > 0" id="printMe">
					<template>
						<div class="code-list" v-for="(item, index) in codeImgList" :key="index">
							<div class="left-box">
								<img :src="item.url" alt="加载失败" style="width:90px;height:90px;">
							</div>
							<div class="right-box">
								<h5>编号: {{ item.info.device_number }}</h5>
								<h5>名称: {{ item.info.device_name }}</h5>
								<h5>部门: {{ item.info.department }}</h5>
								<h5>日期: {{ item.info.create_time }}</h5>
							</div>
						</div>
					</template>
				</div>
				<div class="code-box" style="background-color: #feffff" v-else>
					<div style="
				width: 100%;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				height: 500px;
			  ">
						<el-image src="src/assets/image/data.png"> </el-image>
						<h5 style="color: #999; font-size: 14px; letter-spacing: 1px">
							暂无任何条码
						</h5>
					</div>
				</div>
			</div>
		</Drawer>
		<!-- 设备调拨 -->
		<el-dialog title="设备调拨单" :visible.sync="showDialog" width="45%" :before-close="handleClose">
			<el-row :gutter="10" style="display: flex; align-items: center">
				<!-- 左侧设备列表 -->
				<el-col :span="12" class="equip-list">
					<h4>已选设备列表：</h4>
					<el-row class="left-list">
						<el-col :span="24" v-for="(item, index) in transferList" :key="index">
							<span>{{ item.numbers }}</span><span>{{ item.names }}</span><span>{{ item.location }}</span>
						</el-col>
					</el-row>
				</el-col>
				<el-col :span="4" class="center-list">
					<p class="part-name">{{ depart }}</p>
					<i class="el-icon-sort"></i>
					<p style="font-size: 14px;margin-top:10px;color:#409eff;">{{ departs ? departs : '未选择移交部门' }}</p>
				</el-col>
				<el-col :span="8" class="right-list">
					<el-row style="display: flex; flex-direction: column">
						<h4>转移部门：</h4>
						<el-col :span="24">
							<el-select placeholder="请选择" v-model="departs" clearable>
								<el-option label="IT" value="IT"></el-option>
								<el-option label="食堂" value="食堂"></el-option>
								<el-option label="织造车间" value="织造车间"></el-option>
								<el-option label="整理车间" value="整理车间"></el-option>
								<el-option label="检验车间" value="检验车间"></el-option>
								<el-option label="打样车间" value="打样车间"></el-option>
								<el-option label="综合维修" value="综合维修"></el-option>
								<el-option label="化验室" value="化验室"></el-option>
								<el-option label="计划部门" value="计划部门"></el-option>
								<el-option label="行政办公室" value="行政办公室"></el-option>
								<el-option label="生产办公室" value="生产办公室"></el-option>
								<el-option label="宁波华耀" value="宁波华耀"></el-option>
								<el-option label="世通制衣" value="世通制衣"></el-option>
								<el-option label="世通印绣花" value="世通印绣花"></el-option>
								<el-option label="德利制衣" value="德利制衣"></el-option>
								<el-option label="德利染整" value="德利染整"></el-option>
								<el-option label="德利织造" value="德利织造"></el-option>
							</el-select>
						</el-col>
						<h4>存放地点：</h4>
						<el-col :span="24">
							<el-select placeholder="请选择" v-model="localtions" clearable>
								<el-option v-for="(item, index) in areaList" :key="index"
									:label="item.name + '-' + item.sub" :value="item.name + '-' + item.sub"></el-option>
							</el-select>
						</el-col>
					</el-row>
				</el-col>
			</el-row>
			<span slot="footer" class="dialog-footer">
				<el-button @click="showDialog = false">取 消</el-button>
				<el-button type="primary" @click="submitTransferMachine">立 即 调 拨</el-button>
			</span>
		</el-dialog>
	</div>
</template>
<script>
import Drawer from "@/views/components/Drawer.vue";
import config from '../../common/config.js'
import { mapState } from "vuex";
export default {
	components: {
		Drawer,
	},
	data() {
		return {
			limits: 1,
			actions: config.baseURL + '/importExcels',
			files: [], // 上传的文件列表
			loadingText: "Flyknit...",
			isshow: false,
			departName: "",
			departAddress: "",
			forms: {},
			loadings: true,
			departs: "", // 要转移的部门
			localtions: "", // 新的存放地点
			showDialog: false, // 设备调拨
			status: "", // 设备状态
			title: "添加新设备",
			btnText: "立即添加",
			isEdit: false, // 是否是编辑
			show: false, // 展示添加设备分类开关
			className: "", // 要添加的分类名称
			currentPage: 1,
			dialogVisibles: false, // 查看大图开关
			dialogImageUrl: "", // 大图展示地址
			dialogVisible: false, // 右侧设备详情开关
			logo: [], // 上传的图片
			// 上传logoing
			loadmen: false,
			imgList: [], // 图片集合
			action: config.uploadURL, // 上传地址
			department: "", // 责任部门
			classify: "",  // 设备分类
			device_type: "", // 设备类型
			codeUrl:
				"https://img2.baidu.com/it/u=1340460595,2858602922&fm=253&fmt=auto&app=138&f=GIF?w=511&h=500",
			showVisible: false, // 展示生成二维码开关
			form: {
				// 添加设备表单
				name: "", // 设备名称
				machine_number: "", // 设备编号
				classify: "", // 设备分类
				spec: "", // 设备型号
				factory: "", // 供应商
				brand: "", // 品牌
				money: "", // 美金金额
				moneyvn: "", // 越盾金额
				dates: "", // 出厂日期
				status: '', // 使用状态
				location: "", // 存放地点
				department: "", // 责任部门
				serial: "", // 序列号
				contact: "", // 服务电话
				isEdit: false, // 是否是编辑
				eid: "", // 编辑的id
				type: "", // 设备类型
				warranty: "", // 保修期
			},
			dialogShow: false, // 添加设备的弹窗
			radio: "1",
			tabList: [
				{
					key: "xunjian",
					name: "巡检记录",
				},
				{
					key: "maintaince",
					name: "保养记录",
				},
				{
					key: "repair",
					name: "维修记录",
				},
			],
			activeName: "xunjian",
			url: "https://img2.baidu.com/it/u=1185948584,809941684&fm=253&app=120&size=w931&n=0&f=JPEG&fmt=auto?sec=1670086800&t=9b3a9088f45d9273f8372a9a2082afc5",
			loading: false,
			keyword: "", // 搜索内容
			tableData: [],
			tabIndex: 0,
			multipleSelection: "",
			rules: {
				name: [
					{ required: true, message: "请输入设备名称", trigger: "blur" },
					{
						min: 2,
						max: 15,
						message: "长度在 2 到 15 个字符",
						trigger: "blur",
					},
				],
				machine_number: [
					{ required: true, message: "请输入设备编号", trigger: "blur" },
					{
						min: 1,
						max: 18,
						message: "长度在 1 到 18 个字符",
						trigger: "blur",
					},
				],
				classify: [
					{ required: true, message: "请选择设备分类", trigger: "change" },
				],
				spec: [
					{ required: true, message: "请输入规格型号", trigger: "blur" },
					{
						min: 2,
						max: 15,
						message: "长度在 2 到 30 个字符",
						trigger: "blur",
					},
				],
				date: [
					{
						type: "date",
						required: true,
						message: "请选择出厂日期",
						trigger: "change",
					},
				],
				location: [
					{ required: true, message: "请选择存放地点", trigger: "change" },
				],
				department: [
					{ required: true, message: "请选择责任部门", trigger: "change" },
				],
				type: [
					{ required: true, message: "请选择设备类型", trigger: "change" },
				],
				status: [
					{ required: true, message: "请选择设备状态", trigger: "change" },
				],
			},
			deletList: [], // 要删除的id
			codeList: [], // 要生成二维码的id
			rate: "23535.00", // 汇率
			webUrl: config.imgPrefix, // 图片地址前缀
			page: 1,
			pageSize: 10,
			total: 0, // 总页数
			detailList: {}, // 设备详情参数
			codeImg: "", // 二维码图片
			codeImgList: [], // 生成的二维码列表
			isClose: false, // 右侧二维码列表开关
			infoList: [], // 二维码列表设备参数
			isSelect: false, // 判断当前是选中还是取消选中
			classifyList: [], // 分类列表
			transferList: [], // 调拨单列表
			depart: "", // 当前所选部门
			areaList: [],// 部门区域列表
			headers: {
				Authorization: `Bearer ${localStorage.getItem("accessToken")}`
			},
			defaultImg: 'src/assets/image/device.png',
			fullscreenLoading: false, // 添加全屏loading控制变量
			isAdvanced: false, // 控制高级搜索的展开/收起
			searchTimer: null, // 用于防抖的定时器
		};
	},
	computed: {
		// 获取用户信息
		...mapState({
			User: state => state.users,
		})
	},
	created() {
		// 获取设备列表
		this.getEquipmentList();
		// 获取设备分类
		this.getClassify();
		// 获取部门区域
		this.getLocations();
	},
	mounted() {

	},
	methods: {
		// 文件上传前的钩子(这里可以对文件上传时的后缀进行限制)
		beforeUpload(file) {
			const FileExt = file.name.replace(/.+\./, '');
			if (['xlsx', 'xls'].indexOf(FileExt) === -1) {
				this.$message({
					type: 'warning',
					message: '请上传后缀名为 xlsx 或 xls 的文件！',
				});
				return false;
			}
		},
		// 文件选择回调
		onChange(file, fileList) {
			this.loadings = true; // 开启全屏loading
			this.loadingText = "数据导入中，请稍后...";
			if (file.response) {
				console.log('file.response', file.response);
				if (file.response.status === 200) {
					this.getEquipmentList();
					this.$message.success(file.response.message);
				} else {
					this.$message.error(file.response.message);
				}
				this.loadings = false; // 关闭全屏loading
				this.fullscreenLoading = false; // 关闭全屏loading
				this.loadingText = 'Flyknit...';
			}

		},
		// 导出excel
		async exportData() {
			this.loadings = true;
			this.loadingText = "数据导出中，请稍后...";
			try {
				const obj = {
					keyword: this.keyword,
					classify: this.classify,
					type: this.device_type,
					department: this.department,
					status: this.status,
				}
				const res = await this.$http.exportExcel(obj);
				// 使用 Blob 创建安全的下载链接
				const blob = new Blob([res.data], {
					type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
				});
				
				// 创建 URL
				const url = window.URL.createObjectURL(blob);
				
				// 创建隐藏的下载链接
				const link = document.createElement('a');
				link.style.display = 'none';
				link.href = url;
				link.setAttribute('download', `${this.department || '全部'}-设备列表.xlsx`);
				
				// 添加到 DOM 并触发下载
				document.body.appendChild(link);
				link.click();
				
				// 清理
				document.body.removeChild(link);
				window.URL.revokeObjectURL(url);
				
				setTimeout(() => {
					this.loadingText = "导出成功";
					this.loadings = false;
					this.$message.success('导出成功');
				}, 1000);
			} catch (error) {
				console.error('导出失败:', error);
				this.$message.error('导出失败，请稍后重试');
			} finally {
				this.loadings = false;
				this.loadingText = "Flyknit...";
			}
		},
		// 打开添加设备弹窗
		showAddDialog() {
			this.dialogShow = true;
			this.btnText = "立即添加";
			this.title = "添加新设备";
			this.form = {
				name: "", // 设备名称
				machine_number: "", // 设备编号
				classify: "", // 设备分类
				type: "", // 设备类型
				spec: "", // 规格型号
				money: "", // 美金金额
				moneyvn: "", // 越盾金额
				dates: "", // 出厂日期
				warranty: "", // 保修期
				status: '', // 使用状态
				location: "", // 存放地点
				department: "", // 责任部门
				serial: "", // 序列号
				contact: "", // 服务电话
				isEdit: false, // 是否是编辑
				eid: "", // 编辑的id
			};
			this.logo = [];
			this.detailList = {};
			this.codeImg = "";
			this.codeImgList = [];
			this.infoList = [];
			this.isSelect = false;
			// this.$refs["detailList"].resetFields();
		},
		// 添加部门地点
		async addDepart() {
			try {
				if (!this.departName && !this.departAddress) {
					return this.$message.warning('请填写完整信息')
				}
				const res = await this.$http.addArea({
					name: this.departName,
					address: this.departAddress
				})
				if (res.status !== 200) {
					return this.$message.error(res.message)
				}
				this.departName = ''
				this.departAddress = ''
				this.isshow = false
				this.getLocations();
				return this.$message.success(res.message)
			} catch (error) {
				return this.$message.error('服务器错误,请稍后再试')
			}
		},
		// 刷新数据
		refreshData() {
			this.loadings = true;
			this.getEquipmentList();
			this.$message.success('刷新成功');
		},
		// 搜索
		searchDevice() {
			if (!this.keyword && !this.classify && !this.device_type && !this.department && !this.status) {
				this.$message.warning('请至少输入一个搜索条件');
				return;
			}
			this.page = 1; // 重置页码
			this.getEquipmentList();
		},
		// 获取区域
		async getLocations() {
			try {
				const res = await this.$http.getAllArea()
				if (res.status !== 200) {
					return this.$message.error(res.message)
				}
				const { list } = res.data
				this.areaList = list
			} catch (error) {
				return this.$message.error('服务器错误,请稍后再试')
			}
		},
		// 提交转移
		submitTransferMachine() {
			this.submitTransfer()
		},
		// 封装提交设备调拨api
		async submitTransfer() {
			const obj = {
				machineList: this.transferList,
				oldDepart: this.User.department,
				newDepart: this.departs,
				newLocation: this.localtions,
			}
			try {
				const res = await this.$http.transferEquipInterFace(obj)
				if (res.status !== 200) {
					return this.$message.error(res.message)
				}
				this.newDepart = ''
				this.newLocation = ''
				this.transferList = []
				this.getEquipmentList();
				this.showDialog = false
				return this.$message.success(res.message)
			} catch (error) {
				return this.$message.error('服务器错误,请稍后再试');
			}
		},
		// 关闭前
		handleClose() {
			//console.log('即将关闭');
			this.showDialog = false;
		},
		// 转移设备
		transferMachine() {
			if (this.transferList.length < 1) {
				return this.$message.warning("请选择需要转移的设备！");
			}
			this.showDialog = true;
		},
		// 设备信息编辑
		editInfo() {
			this.isEdit = true;
			this.dialogVisible = false;
			setTimeout(() => {
				this.dialogShow = true;
				this.title = "编辑设备信息";
				this.btnText = "提交编辑";
				this.isEdit = true;
			}, 200);
			this.form.name = this.detailList.device_name;
			this.form.machine_number = this.detailList.device_number;
			this.form.spec = this.detailList.device_model;
			this.form.classify = this.detailList.classify;
			this.form.type = this.detailList.type;
			this.form.factory = this.detailList.supplier;
			this.form.brand = this.detailList.brand;
			this.form.money = this.detailList.device_price;
			this.form.moneyvn = this.detailList.price_viet;
			this.form.dates = this.detailList.date_manufacture;
			this.warranty_start = this.detailList.warranty_start;
			this.warranty_end = this.detailList.warranty_end;
			this.form.status = this.detailList.status === 0 ? '正常' : this.detailList.status === 1 ? '闲置' : this.detailList.status === 2 ? '维修中' : '已报废';
			this.form.location = this.detailList.device_location;
			this.form.department = this.detailList.department;
			this.form.serial = this.detailList.serial;
			this.form.contact = this.detailList.contact_phone;
			this.form.isEdit = true;
			this.form.eid = this.detailList.id;
			this.logo.push({
				url: this.detailList.device_image,
				uid: this.detailList.id,
			}); //element展示图片时需要数组类型的才能展示
		},
		// 重置表单
		resetForm(formName) {
			this.$refs[formName].resetFields();
			this.logo = [];
		},
		// 用户清空选择部门事件
		clear(e) {
			// 根据部门获取对应的设备列表
			this.getEquipmentList();
		},
		// 清除设备分类选择的事件
		clearHandles(e) {
			// 获取设备列表
			this.getEquipmentList();
		},
		// 金额初始化
		moneyFormatter(row, column, cellValue, index) {
			if (cellValue > 0) {
				return cellValue;
			}
		},
		// 获取设备分类
		async getClassify() {
			try {
				const res = await this.$http.getEquipmentClassify();
				if (res.status !== 200) {
					return this.$message.error(res.message);
				}
				const { list } = res.data;
				this.classifyList = list;
			} catch (error) {
				return this.$message.error('服务器错误，请稍后再试！');
			}
		},
		// 添加设备分类
		async addClassifyName() {
			if (!this.className) {
				return this.$message.warning("请输入分类名称");
			}
			try {
				const res = await this.$http.addClassify({
					classify: this.className,
				});
				if (res.status !== 200) {
					return this.$message.error(res.message);
				}
				this.getClassify();
				return this.$message.success(res.message);
			} catch (error) {
				return this.$message.error(error);
			}
		},
		// 分类选择
		selectHandle(e) {
			this.classify = e;
			this.getEquipmentList();
		},

		// 清除搜索词汇
		clearHandle(field) {
			// 清除对应字段
			switch(field) {
				case 'keyword':
					this.keyword = '';
					break;
				case 'classify':
					this.classify = '';
					break;
				case 'device_type':
					this.device_type = '';
					break;
				case 'department':
					this.department = '';
					break;
				case 'status':
					this.status = '';
					break;
			}
			
			// 重置分页
			this.page = 1;
			this.pageSize = 10;
			
			// 获取数据
			this.getEquipmentList();
		},
		// 关闭右侧二维码列表
		closeRightBox() {
			this.codeImgList = [];
			this.codeList = [];
			this.$nextTick(() => {
				let table = this.tableData; // 从后台获取到的数据
				table.forEach((row) => {
					// 清除选中状态
					this.$refs.multipleTable.toggleRowSelection(row, false);
				});
			});
		},
		handleSizeChange(e) {
			this.loadings = true;
			this.pageSize = e;
			this.getEquipmentList();
		},
		// 下一页
		handleCurrentChange(e) {
			this.loadings = true;
			this.page = e;
			this.getEquipmentList();
		},
		// 获取设备列表
		async getEquipmentList() {
			try {
				this.loadings = true
				const obj = {
					keyword: this.keyword?.trim(), // 添加trim()去除空格
					classify: this.classify,
					department: this.department,
					status: this.status,
					type: this.device_type,
					page: this.page,
					pageSize: this.pageSize,
				}
				
				// 检查是否有搜索条件
				const hasSearchCondition = Object.values(obj).some(value => 
					value !== undefined && value !== null && value !== ''
				)
				
				const res = await this.$http.getEquipmentLists(obj)
				if (res.status !== 200) {
					this.loadings = false
					return this.$message.error(res.message)
				}

				const { list, total } = res.data
				this.tableData = list || [] // 确保list为数组
				this.total = total

				// 如果有搜索条件但没有数据
				if (hasSearchCondition && (!list || list.length === 0)) {
					this.$message.warning('未找到匹配的设备信息')
				}

			} catch (error) {
				console.error('获取设备列表失败:', error)
				return false;
			} finally {
				setTimeout(() => {
					this.loadings = false
				}, 500)
			}
		},
		// 选择文件数量限制
		handleExceed(files, fileList) {
			return this.$message.warning("你只能添加一设备图片!");
		},
		// 查看大图
		handleview(file) {
			this.dialogImageUrl = file.url;
			this.dialogVisibles = true;
		},
		// 上传失败
		onErr(e) {
			this.loadmen = false;
			return this.$message.error("上传失败,尝试重新上传");
		},
		// 上传时
		project(file) {
			this.loadmen = true;
		},
		// logo移除文件时的钩子
		logoRemove(file, fileList) {
			this.logo = [];
		},
		// 上传成功：logo
		logoSuccess(res, file, fileList) {
			const { url } = res.data;
			this.logo.push({ url: this.webUrl + url, uid: file.uid })//element展示图片时需要数组类型的才能展示
			this.loadmen = false;
		},
		// 美金转越盾事件
		changeMoney(e) {
			let md = e;
			setTimeout(() => {
				let price = md * this.rate;
				this.toThousands(price);
			}, 1000);
		},
		//金额小数点转化
		toThousands(num) {
			var num = (num || 0).toString(),
				result = "";
			while (num.length > 3) {
				result = "," + num.slice(-3) + result;
				num = num.slice(0, num.length - 3);
			}
			if (num) {
				result = num + result;
			}
			this.form.moneyvn = result;
		},
		// 清空二维码
		clearCode() {
			this.$confirm("您确定要清空所有二维码吗?", "提示", {
				confirmButtonText: "立即清空",
				cancelButtonText: "取消",
				type: "warning",
			})
				.then((res) => {
					this.codeImgList = [];
					this.codeList = [];
					this.$nextTick(() => {
						let table = this.tableData; // 本地表格数据
						table.forEach((row) => {
							// 清除选中状态
							this.$refs.multipleTable.toggleRowSelection(row, false);
						});
					});
					return this.$message.success("清空成功");
				})
				.catch((err) => {
					return false;
				});
		},
		// 打印二维码
		// printCode() {
		// 	this.loading = true;
		// 	this.$nextTick(() => {
		// 		window.print();
		// 		this.loading = false;
		// 	});
		// },
		// 生成二维码事件
		toCode() {
			if (this.codeList.length < 1) {
				return this.$message.warning("请选择要生成二维码的设备！");
			}
			// 调用生成二维码接口
			this.qrCode();
			this.loading = true;
			this.showVisible = true;
			setTimeout(() => {
				this.loading = false;
			}, 500);
		},
		// 全局生成二维码
		async qrCode() {
			try {
				const res = await this.$http.getQrCode({
					text: this.codeList,
				});
				if (res.status !== 200) {
					return this.$message.error(res.message);
				}
				const { data } = res
				data.forEach((item) => {
					this.codeImgList.push({ url: item });
				});
				this.codeList.map((x, index) => {
					this.tableData.find((item) => {
						if (item.device_number === x) {
							this.codeImgList.map((items, indexs) => {
								if (indexs === index) {
									items.info = item;
								}
							});
						}
					});
				});
			} catch (error) {
				return this.$message.error("服务器错误，请稍后再试！");
			}
		},
		// 删除事件
		delItem() {
			if (this.deletList.length < 1) {
				return this.$message.warning("请选择要删除的设备！");
			} else {
				// 调用删除接口
				this.deleteDevice();
			}
		},
		// 封装删除设备的方法
		async deleteDevice() {
			try {
				this.$confirm("您确定要删除该设备吗?", "温馨提示", {
					confirmButtonText: "确定",
					cancelButtonText: "取消",
					type: "warning",
				})
					.then(async () => {
						const res = await this.$http.delDevice({
							device_number: this.deletList,
						});
						if (res.status !== 200) {
							return this.$message.error(res.message);
						}
						this.getEquipmentList();
						this.deletList = [];
						this.$nextTick(() => {
							let table = this.tableData; // 本地表格数据
							table.forEach((row) => {
								// 清除选中状态
								this.$refs.multipleTable.toggleRowSelection(row, false);
							});
						});
						return this.$message.success(res.message);
					})
					.catch((err) => {
						return false;
					});
			} catch (error) {
				return this.$message.error("服务器错误，请稍后再试!");
			}
		},
		// 勾选设备事件
		handleSelectionChange(val) {
			// 每次勾选前清空
			this.codeList = [];
			this.deletList = [];
			this.transferList = [];
			// 如果有值
			if (val.length > 0) {
				this.multipleSelection = val;
				this.multipleSelection.map((item, index) => {
					this.deletList.push(item.id);
					this.codeList.push(item.device_number);
					this.depart = item.belong_to;
					this.transferList.push({
						numbers: item.device_number,
						names: item.device_name,
						location: item.device_location,
					});
				});
			}
			const s1 = new Set(this.codeList);
			const s2 = new Set(this.deletList);
			const res = [...s1];
			const res1 = [...s2];
			this.codeList = res;
			this.deletList = res1;
			// console.log('勾选设备事件', this.deletList);
			// console.log('更新后', this.codeList);
		},
		// 入库新设备
		onSubmit(form) {
			this.$refs[form].validate((valid) => {
				if (valid) {
					this.form.img = this.logo[0].url;
					this.submitForm(form);
				} else {
					return false;
				}
			});
		},
		// 保存数据
		async submitForm(formName) {
			try {
				const res = await this.$http.addEquipment(this.form);
				if (res.status !== 200) {
					return this.$message.error(res.message);
				}
				// 关闭添加设备弹窗
				this.dialogShow = false;
				this.$refs[formName].resetFields();
				this.logo = [];
				this.getEquipmentList();
				if (this.isEdit) {
					// 如果是编辑
					this.isEdit = false;
					setTimeout(() => {
						this.title = "添加设备信息";
						this.btnText = "提交添加";
					}, 200);
					return this.$message.success(res.message);
				} else {
					return this.$message.success(res.message);
				}
			} catch (error) {
				return this.$message.error(error);
			}
		},
		// 重置表单
		resetForm() {
			// 重置所有搜索条件
			this.keyword = '';
			this.classify = '';
			this.device_type = '';
			this.department = '';
			this.status = '';
			
			// 重置分页
			this.page = 1;
			this.pageSize = 10;
			
			// 重新获取数据
			this.loadings = true;
			this.getEquipmentList();
		},
		// 切换二维码
		changeCode(mid) {
			const qrCode = this.codeUrl + mid;
			this.codeImg = qrCode;
		},
		// 选项卡切换
		handleClick(tab) {
			this.tabIndex = tab.index;
		},
		// 打开设备详情页
		toDetail(data) {
			this.loading = true;
			this.dialogVisible = true;
			this.detailList = data
			setTimeout(() => {
				this.loading = false;
			}, 1000);
		},
		// 关闭设备详情页
		close() {
			this.codeImg = "";
		},
		// 切换高级搜索
		toggleAdvanced() {
			this.isAdvanced = !this.isAdvanced
		},
		// 防抖搜索
		debounceSearch() {
			if (this.searchTimer) {
				clearTimeout(this.searchTimer)
			}
			this.searchTimer = setTimeout(() => {
				this.getEquipmentList()
			}, 500)
		},
		// 处理分类变化
		handleClassifyChange() {
			this.getEquipmentList()
		},
		// 处理类型变化  
		handleTypeChange() {
			this.getEquipmentList()
		},
		// 处理部门变化
		handleDepartmentChange() {
			this.getEquipmentList()
		},
		// 处理状态变化
		handleStatusChange() {
			this.getEquipmentList()
		},
	},
};
</script>
<style lang="scss" scoped>
// 右侧设备信息展示
.drawer-container {
	font-family: Helvetica Neue, Helvetica, PingFang SC, Tahoma, Arial, sans-serif;
	height: 100%;
	box-sizing: border-box;
	overflow: hidden;
	display: flex;
	flex-direction: column;

	i {
		font-size: 23px;
		color: #007ef7;
		font-weight: bold;
	}

	.sb-title {
		font-weight: 600;
		margin-left: 0px;
		letter-spacing: 1px;
		padding: 0 20px;
	}

	.top-drawer {
		display: flex;
		background-color: #feffff;
		padding: 20px 0;
		flex-direction: column;


		.bottom-detail__box {
			padding: 10px 20px;
			display: flex;
			justify-content: space-around;
			align-items: center;

			.right-status {
				text-align: center;
				flex: 1;
			}

			.right-status .el-col:first-child {
				color: #999;
			}

			.right-status .el-col:last-child {
				font-size: 16px;
				font-weight: 600;
				letter-spacing: 2px;
			}

			.el-col {
				font-size: 14px;
				line-height: 2.3;

				.right-text {
					color: #606266;
					white-space: nowrap;
					text-overflow: ellipsis;
					width: 120px;
					overflow: hidden;
				}
			}
		}

		.details-title {
			font-weight: bold;
			padding: 0 10px;
			display: flex;
			align-items: center;

			.title {
				margin-left: 10px;
				font-size: 16px;
				letter-spacing: 2px;
				letter-spacing: 3px;
			}
		}

		.bottom-details__boxs {
			padding: 20px 0px;
			display: flex;
			justify-content: flex-start;
			align-items: center;

			.right-status {
				text-align: center;
				flex: 1;
			}

			.right-status .el-col:first-child {
				color: #999;
			}

			.right-status .el-col:last-child {
				font-size: 16px;
				font-weight: 600;

				letter-spacing: 2px;
			}

			.el-col {
				font-size: 14px;
				line-height: 2.3;

				.right-text {
					color: #606266;
					white-space: nowrap;
					text-overflow: ellipsis;
					width: 120px;
					overflow: hidden;
				}
			}
		}


	}

	.maintaince-title {
		font-weight: bold;
		padding: 0 20px;
		display: flex;
		align-items: center;

		i {
			font-size: 23px;
			color: #007ef7;
			font-weight: bold;
		}

		.title {
			margin-left: 10px;
			font-size: 16px;
			letter-spacing: 2px;
		}
	}

	.bottom-drawer {
		height: 100%;
		padding: 0px 20px;
		background: #fff;
		box-sizing: border-box;

		.el-tabs {
			padding: 10px 20px;
		}

		.option-box {
			border: 1px solid #eee;
			padding: 10px 20px;
		}

		.task-box {
			padding: 20px 0;
			display: flex;
			flex-direction: column;
			height: 480px;
			overflow-y: scroll;
			border-top: 1px solid #eee;
			font-family: Helvetica Neue, Helvetica, PingFang SC, Tahoma, Arial,
				sans-serif;

			.task-item {
				border-left: 4px solid #409eff !important;
				display: flex;
				flex-direction: column;
				margin-bottom: 10px;
				border-bottom: 1px solid #f4f4f4;

				.item-top {
					padding: 10px 10px;

					span {
						font-size: 14px;
						margin-left: 15px;
						color: #235077;
					}
				}

				.item-bottom {
					padding: 10px 10px;

					span {
						font-size: 13px;
						color: #999;
						padding: 0 10px;
					}
				}
			}

			.task-item:hover {
				background-color: #ecf5ff;
				cursor: pointer;
			}
		}
	}
}
.forms {
	display: flex;
	flex-wrap: wrap; /* 设置 el-form 的布局为自动换行 */
}
.table-name {
	font-weight: 600;
	letter-spacing: 1px;
	color: #606266;
	padding-left: 18px !important;

	.table-total {
		color: #007ef7;
	}
}

.action-area .el-col {
	cursor: pointer;
	padding: 0px;
	text-align: center;
}

.action-area .el-col i:hover {
	color: #007ef7 !important;
}

.lg-text {
	font-size: 16px;
	font-weight: 600;
	color: #909399;
}

.el-table {
	border-top: 1px solid #eee;
	margin-top: 20px;
	width: 100%;
}

.pages {
	display: flex;
	justify-content: flex-start;
	width: 100%;
}

.add-box {
	display: flex;
	flex-direction: column;
	padding: 10px;
	height: 530px;
	overflow-y: scroll;
	box-sizing: border-box;
	overflow-x: hidden;
}

.footer {
	width: 100%;
	text-align: center;
}

@media print {
	.code-page {
		page-break-after: always;
	}

	.code-list {
		display: flex;
		flex-direction: row;
		flex: 1;
		justify-content: center;
		align-items: center;
	}

	.left-box {
		flex: 0 0 auto;
	}

	.right-box {
		flex: 1 1 auto;
	}
}


.code-area {
	display: flex;
	flex-direction: column;

	.menu {
		display: flex;
		padding: 10px 0px;
	}

	.code-box {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
		// height: 750px;
		overflow-y: scroll;
		background-color: #eee;
		padding: 5px 5px;

		.code-list {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin: 5px 6px;
			background-color: #feffff;
			padding: 10px 3px;
			width: calc(100% / 3.3);

			.left-box {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				flex: 1;
				flex-shrink: 0;
			}

			.right-box {
				padding: 0 5px;
				display: flex;
				flex-direction: column;
				flex: 2;
				flex-shrink: 0;

				h5 {
					line-height: 1.8;
					font-size: 12px;
				}
			}
		}
	}
}

.equip-list {
	border: 1px solid #eee;
	padding: 10px 0px;
	box-sizing: border-box;
	overflow: hidden;

	.left-list {
		padding: 10px 0px;

		.el-col {
			padding: 10px 0px;

			span {
				margin: 0 10px;
			}
		}
	}
}

.center-list {
	display: flex;
	justify-content: center;
	flex-direction: column;
	align-items: center;

	p {
		font-size: 16px;
		font-weight: bold;
	}

	i {
		transform: rotate(-90deg);
		font-size: 40px;
		color: #409eff;
	}
}

.right-list {
	border: 1px solid #eee;
	padding: 10px 15px;

	h4 {
		padding: 10px 0px;
	}
}
.el-card {
	width: 100%;
}

.advanced-search {
	padding-top: 10px;
	border-top: 1px dashed #ebeef5;
	margin-top: 10px;
}

.el-form-item {
	margin-bottom: 10px;
}
</style>
  