<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-07-29 12:45:31
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-12 08:55:42
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\department\WorkItem.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-card>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form :inline="true" class="demo-form-inline">
                        <el-form-item label="部门">
                            <el-select v-model="form.department">
                                <el-option label="织造车间" value="织造车间"></el-option>
                                <el-option label="整理车间" value="整理车间"></el-option>
                                <el-option label="打样车间" value="打样车间"></el-option>
                                <el-option label="综合维修" value="综合维修"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="success" icon="el-icon-search" @click="searchHandler">查询</el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="el-icon-plus" @click="addItem">添加工作项</el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="button" icon="el-icon-refresh" @click="resetHandler">重置</el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" plain icon="el-icon-refresh-right" @click="refresh"></el-button>
                        </el-form-item>
                    </el-form>
                </el-col>
            </el-row>
            <!-- 内容展示区域 -->
            <el-table :data="tableData" style="width: 100%" stripe v-loading="loading" element-loading-text="Flyknit">
                <el-table-column type="index" label="序号" width="180">
                </el-table-column>
                <el-table-column prop="content" label="工作项" width="180">
                </el-table-column>
                <el-table-column prop="work_num" label="标准数量">
                </el-table-column>
                <el-table-column prop="department" label="部门">
                </el-table-column>
                <el-table-column prop="create_time" label="创建时间">
                </el-table-column>
                <el-table-column prop="username" label="创建人">
                </el-table-column>
                <el-table-column label="操作" v-show="User.level > 0">
                    <template slot-scope="scope">
                        <el-button type="primary" size="mini" @click="editHandler(scope.row)">编辑</el-button>
                        <el-button type="danger" size="mini" @click="delHandler(scope.row.id)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
                :page-sizes="[12, 16, 20, 24]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="total">
            </el-pagination>
        </el-card>
        <!-- 添加工作项弹窗 -->
        <el-dialog :title="title" :visible.sync="dialogVisible" width="30%">
            <el-form :model="form" :rules="rules" ref="form" label-width="80px">
                <el-form-item label="工作项名称" prop="workItem">
                    <el-input v-model="form.workItem" placeholder="请输入工作项名称"></el-input>
                </el-form-item>
                <el-form-item label="标准数量" prop="workNum">
                    <el-input v-model="form.workNum" placeholder="请输入标准数量"></el-input>
                </el-form-item>
                <el-form-item label="单位" prop="unit">
                    <el-select v-model="form.unit">
                        <el-option label="天" value="天"></el-option>
                        <el-option label="台" value="台"></el-option>
                        <el-option label="个" value="个"></el-option>
                        <el-option label="把" value="把"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="车间部门" prop="depart">
                    <el-select v-model="form.depart">
                        <el-option label="织造车间" value="织造车间"></el-option>
                        <el-option label="整理车间" value="整理车间"></el-option>
                        <el-option label="打样车间" value="打样车间"></el-option>
                        <el-option label="综合维修" value="综合维修"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <span class="footer">
                <el-button type="primary" @click="submitInfo('form')">{{ btnText }}</el-button>
                <el-button type="button" @click="resetTable('form')">重 置 表 单</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import { mapState } from 'vuex'
export default {
    data() {
        return {
            tableData: [], // 表格数据
            form: {
                depart: '',
                workItem: '',
                workNum: '',
                unit: '',
                department: '',
                idd: 0
            },
            rules: {
                depart: [{
                    required: true,
                    message: '请选择车间部门',
                    trigger: 'change'
                }],
                workItem: [{
                    required: true,
                    message: '请输入工作项名称',
                    trigger: 'blur'
                }],
                workNum: [{
                    required: true,
                    message: '请输入标准数量',
                    trigger: 'blur'
                }],
                unit: [{
                    required: true,
                    message: '请选择单位',
                    trigger: 'change'
                }]
            },
            currentPage: 1,
            pageSize: 12,
            total: 0,
            dialogVisible: false,
            loading: true,
            types: '',// 类型
            title: '',// 标题
            btnText: '立 即 添 加',// 按钮文字
        }
    },
    computed: {
        ...mapState({
            User: state => state.users,
        })
    },
    mounted() {
        this.getWorkList()
    },
    methods: {
        // 获取工作项列表
        async getWorkList() {
            try {
                const obj = {
                    page: this.currentPage,
                    limit: this.pageSize,
                    depart: this.form.department,
                }
                const res = await this.$http.getWorkListsItem(obj)
                const { list } = res.data
                this.total = res.data.total
                this.tableData = list
                setTimeout(() => {
                    this.loading = false
                }, 1000)
                return false
            } catch (error) {
                this.loading = false
                return this.$message.error(error)
            }
        },
        // 添加工作项
        async submitInfo(formName) {
            try {
                this.$refs[formName].validate(async (valid) => {
                    if (valid) {
                        this.form.types = this.types
                        const res = await this.$http.addWorkItem(this.form)
                        if (res.status === 200) {
                            this.$message.success(res.message)
                            this.dialogVisible = false
                            this.getWorkList()
                            this.form = {
                                depart: '',
                                workItem: '',
                                workNum: '',
                                unit: '',
                                department: '',
                                idd: 0
                            }
                        } else {
                            this.$message.error(res.message)
                        }
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            } catch (error) {
                return this.$message.error(error)
            }
        },
        editHandler(row) {
            console.log('row', row);
            this.form.idd = row.id
            this.types = 'edit'
            this.title = '编辑工作项'
            this.btnText = '立 即 修 改'
            this.dialogVisible = true
            this.form.depart = row.department
            this.form.workItem = row.content
            this.form.workNum = row.work_num
            this.form.unit = row.unit
        },
        async delHandler(id) {
            try {
                this.$confirm('此操作将永久删除该工作项, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    const res = await this.$http.delWorkItem({ id })
                    if (res.status === 200) {
                        this.$message.success(res.message)
                        this.getWorkList()
                    } else {
                        this.$message.error(res.message)
                    }
                }).catch(() => {
                    this.$message.info('已取消删除')
                })
            } catch (error) {
                return this.$message.error(error)
            }
        },
        addItem() {
            this.types = 'add'
            this.title = '添加工作项'
            this.dialogVisible = true
        },
        resetTable(formName) {
            this.$refs[formName].resetFields()
        },
        handleSizeChange(val) {
            this.loading = true
            this.pageSize = val
            this.getWorkList()
        },
        handleCurrentChange(val) {
            this.loading = true
            this.currentPage = val
            this.getWorkList()
        },
        refresh() {
            this.loading = true
            this.getWorkList()
        },
        resetHandler() {
            this.form.department = ''
        },
        searchHandler() {
            if (!this.form.department) {
                return this.$message.warning('请选择车间部门')
            }
            this.loading = true
            this.getWorkList()
        }
    }

}
</script>
<style lang="scss">
.el-table {
    width: 100%;
}

.el-pagination {
    margin-top: 20px;
}

.footer {
    display: flex;
    justify-content: flex-start;
    padding: 20px;
}
</style>