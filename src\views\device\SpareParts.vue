<template>
  <div>
    <el-card>
      <!-- 表单、搜索框，状态筛选下拉，搜索按钮，重置按钮 -->
      <div class="filter-container">
        <el-form
          :inline="true"
          :model="filterForm"
          ref="filterForm"
          class="demo-form-inline"
        >
          <el-form-item label="备件名称/编号">
            <el-input
              v-model="filterForm.name"
              placeholder="请输入备件名称或编号"
            ></el-input>
          </el-form-item>
          <el-form-item label="备件类型">
            <el-select v-model="filterForm.type" placeholder="请选择备件类型">
              <el-option
                v-for="item in partsTypeList"
                :key="item.id"
                :label="item.second_class"
                :value="item.second_class"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属部门">
            <el-select
              placeholder="请选择部门"
              v-model="filterForm.department"
              clearable
            >
              <el-option
                v-for="item in departList"
                :key="item.id"
                :label="item.depart_name"
                :value="item.depart_name"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="search" icon="el-icon-search"
              >查询</el-button
            >
            <el-button @click="resetForm('filterForm')" icon="el-icon-refresh"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <!-- 表格 -->
      <el-table
        :data="tableData"
        stripe
        style="width: 100%"
        v-loading="loading"
        element-loading-text="Flyknit"
      >
        <!-- 索引 -->
        <el-table-column
          type="selection"
          label="序号"
          width="60"
        ></el-table-column>
        <el-table-column prop="partImg" label="配件图片">
          <template slot-scope="scope">
            <el-image
              :src="scope.row.partImg"
              style="width: 45px; height: 45px"
              fit="cover"
            ></el-image>
          </template>
        </el-table-column>
        <el-table-column prop="part_name" label="备件名称"></el-table-column>
        <el-table-column prop="part_number" label="备件编号"></el-table-column>
        <el-table-column
          prop="category"
          label="备件类型"
          width="100"
        ></el-table-column>
        <el-table-column
          prop="specifics"
          label="规格型号"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="relation_device"
          label="关联设备"
        ></el-table-column>
        <el-table-column
          prop="stock_quantity"
          label="库存数量"
        ></el-table-column>
        <el-table-column prop="safety_stock" label="安全库存"></el-table-column>
        <el-table-column prop="warn_stock" label="预警库存"></el-table-column>
        <el-table-column
          prop="amount"
          label="备件金额"
          width="120"
        ></el-table-column>
        <el-table-column prop="unit" label="备件单位"></el-table-column>
        <el-table-column prop="department" label="所属部门"></el-table-column>
        <el-table-column prop="shelf_name" label="所属货架"></el-table-column>
        <el-table-column
          prop="storage_number"
          label="所属库位"
        ></el-table-column>
        <!-- <el-table-column label="操作" width="150">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleEdit(scope.$index, scope.row)"
              >编辑</el-button
            >
            <el-button
              type="text"
              size="small"
              style="color: #F16E6E !important"
              @click="handleDelete(scope.$index, scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column> -->
      </el-table>
      <!-- 分页 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </el-card>
  </div>
</template>
<script>
export default {
  data() {
    return {
      loading: true,
      // 表格数据
      tableData: [],
      // 分页数据
      currentPage: 1,
      page: 1,
      pageSize: 10,
      total: 0,
      // 表单数据
      filterForm: {
        name: "",
        type: "",
        department: "",
      },
      partsTypeList: [], // 备件类型列表
      departList: [], // 部门列表
    };
  },
  created() {
    this.getDepartList();
    this.getPartsTypeList();
    this.getPartsList();
  },
  methods: {
    // 获取备件类型列表
    async getPartsTypeList() {
      try {
        const res = await this.$http.getPartsTypeList();
        if (res.status !== 200) {
          return this.$message.error(res.message);
        } else {
          this.partsTypeList = res.data;
        }
      } catch (error) {
        return this.$message.error(error);
      }
    },
    // 获取各部门列表
    async getDepartList() {
      try {
        const res = await this.$http.getDepartmentList();
        if (res.status !== 200) {
          return this.$message.error(res.message);
        } else {
          this.departList = res.data;
        }
      } catch (error) {
        return this.$message.error(error);
      }
    },
    // 获取配件列表
    async getPartsList() {
      try {
        const res = await this.$http.getPartsList({
          page: this.page,
          pageSize: this.pageSize,
          keywords: this.filterForm.name,
          type: this.filterForm.type,
          department: this.filterForm.department,
        });
        if (res.status !== 200) {
          return this.$message.error(res.message);
        } else {
          this.tableData = res.data.list;
          this.total = res.data.total;
          setTimeout(() => {
            this.loading = false;
          }, 500);
        }
      } catch (error) {
        this.loading = false;
        return this.$message.error(error);
      }
    },
    // 分页
    handleCurrentChange(val) {
      this.currentPage = val;
      this.page = val;
      this.loading = true;
      this.getPartsList();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.page = 1;
      this.loading = true;
      this.getPartsList();
    },
    search() {
      if (
        this.filterForm.name ||
        this.filterForm.type ||
        this.filterForm.department
      ) {
        this.page = 1;
        this.loading = true;
        this.getPartsList();
      } else {
        return false;
      }
    },
    // 重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.filterForm = {
        name: "",
        type: "",
        department: "",
      };
      this.getPartsList();
    },
  },
};
</script>
<style lang="scss" scoped>
.el-pagination {
  margin-top: 20px;
}
</style>
