import{_ as l,C as c,b0 as w,R as i,c as d,o as u,g as p,S as _,H as m,n as t,ad as h,U as S,ag as f}from"./index-444b28c3.js";const b={name:"ElContainer"},C=c({...b,props:{direction:{type:String}},setup(o){const s=o,e=w(),n=i("container"),a=d(()=>s.direction==="vertical"?!0:s.direction==="horizontal"?!1:e&&e.default?e.default().some(g=>{const k=g.type.name;return k==="ElHeader"||k==="ElFooter"}):!1);return(r,g)=>(u(),p("section",{class:m([t(n).b(),t(n).is("vertical",t(a))])},[_(r.$slots,"default")],2))}});var B=l(C,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/container/src/container.vue"]]);const F={name:"ElAside"},H=c({...F,props:{width:{type:String,default:null}},setup(o){const s=o,e=i("aside"),n=d(()=>s.width?e.cssVarBlock({width:s.width}):{});return(a,r)=>(u(),p("aside",{class:m(t(e).b()),style:h(t(n))},[_(a.$slots,"default")],6))}});var v=l(H,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/container/src/aside.vue"]]);const N={name:"ElFooter"},V=c({...N,props:{height:{type:String,default:null}},setup(o){const s=o,e=i("footer"),n=d(()=>s.height?e.cssVarBlock({height:s.height}):{});return(a,r)=>(u(),p("footer",{class:m(t(e).b()),style:h(t(n))},[_(a.$slots,"default")],6))}});var E=l(V,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/container/src/footer.vue"]]);const z={name:"ElHeader"},A=c({...z,props:{height:{type:String,default:null}},setup(o){const s=o,e=i("header"),n=d(()=>s.height?e.cssVarBlock({height:s.height}):{});return(a,r)=>(u(),p("header",{class:m(t(e).b()),style:h(t(n))},[_(a.$slots,"default")],6))}});var $=l(A,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/container/src/header.vue"]]);const M={name:"ElMain"},x=c({...M,setup(o){const s=i("main");return(e,n)=>(u(),p("main",{class:m(t(s).b())},[_(e.$slots,"default")],2))}});var y=l(x,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/container/src/main.vue"]]);const R=S(B,{Aside:v,Footer:E,Header:$,Main:y}),U=f(v),j=f(E),q=f($),D=f(y);export{j as E,R as a,D as b,U as c,q as d};
