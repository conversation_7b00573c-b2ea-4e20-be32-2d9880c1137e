/*
 * @Author: flyknit <EMAIL>
 * @Date: 2024-04-09 13:44:03
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-07-16 13:51:49
 * @FilePath: \electronic-filed:\gitee\nike-backend\app.js
 * @Description: 
 * 
 * Copyright (c) 2024 by ${<EMAIL>}, All Rights Reserved. 
 */
// 引入koa
const Koa = require("koa");
const { koaBody } = require("koa-body");
const app = new Koa();
const json = require("koa-json"); //用于将http响应的数据转换为json格式
const bodyParser = require("koa-bodyparser"); //解析http请求的消息体
const staticMiddleware = require("koa-static");
const router = require("@koa/router")();
const cors = require("@koa/cors"); //允许跨域
const { addAliases } = require("module-alias");
const path = require("path");
const winston = require("winston")
// 引入集群配置
const cluster = require("node:cluster");
const http = require("node:http");
const numCPUs = require("os").cpus().length;
// 创建集群
if (cluster.isMaster) {
  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }
  cluster.on("exit", (worker, code, signal) => {
    console.log('worker %d died', worker.process.pid);
  });
} else {
  http.createServer(app.callback()).listen(3001);
  console.log('master %d is running', process.pid);
}
// 引入建表文件
const createTablesFromSQLFile = require('./config/createTable')
// 执行建表操作
async function initializeDatabase() {
  const sqlDirectory = path.join(__dirname, 'kywlck_flyknit.sql'); // 调整为你的 SQL 文件目录
  await createTablesFromSQLFile(sqlDirectory);
}
// 配置别名
addAliases({
  "@": __dirname,
});

// winston日志
const logger = winston.createLogger({
  level: "info",
  format: winston.format.json(),
  defaultMeta: { service: "user-service" },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: "error.log", level: "error" }),
    new winston.transports.File({ filename: "request.log" }),
  ],
});

// 将logger挂载到全局
global.logger = logger;

// 统一返回给前端的接口数据格式:中间件
const responseHandler = require("@/config/result");
// 捕获错误的中间件
const errorHandler = require("@/config/abnormal");

/**静态资源（服务端） */
app.use(staticMiddleware(path.join(__dirname + "uploadFile")));
// 跨域设置
app.use(
  cors({
    origin: function (ctx) {
      //设置允许来自指定域名请求
      return "*"; // 允许来自所有域名请求},
    },
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    credentials: true,
    optionsSuccessStatus: 200,
  }),
);

// 记录访问日志
app.use(async (ctx, next) => {
  await next();
  const rt = ctx.response.get("X-Response-Time");
  logger.info(`${ctx.method} ${ctx.url} - ${rt}`);
});

// 当用户访问根目录时，返回禁止访问
app.use(async (ctx, next) => {
  if (ctx.url === "/" || !ctx.url.includes("/api")) {
    // 返回客官，本服务器只提供post类型的API
    const data = {
      code: 403,
      message: "啊哦~迷路了吗☹",
      data: null,
    };
    ctx.body = data;
  } else {
    await next();
  }
});

// 解析前端数据
app.use(responseHandler);
app.use(errorHandler);
app.use(
  koaBody({
    multipart: true,
  })
);
app.use(bodyParser());
app.use(json());

// 引入前端路由
const pc = require("@/routes/admin/index");
// 引入mobile路由
const mobile = require("@/routes/mobile/index");
// 引入上传路由
const upload = require("@/routes/upload/index");
// 引入前端用户路由
// const user = require("@/routes/user/index");

// 使用路由
router.use("/api", pc);
router.use("/api", mobile);
router.use("/api", upload);
// router.use("/api", user);
app.use(router.routes()).use(router.allowedMethods());
// 监听端口
// app.listen(3000, () => {
//   console.log('server is running at http://localhost:3000');
// });
// 建表
// initializeDatabase()
//   .then(() => {
//     console.log('数据库初始化完成');
//     // 监听端口
//     app.listen(3000, () => {
//       console.log('server is running at http://localhost:3000');
//     });
//   })
//   .catch(err => {
//     console.error('数据库初始化失败', err);
//   });
