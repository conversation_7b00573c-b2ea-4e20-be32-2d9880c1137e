import{au as Ie,av as K,Q as ce,aw as se,ax as de,a7 as Ae,_ as fe,C as ve,ay as ue,az as ke,aA as be,R as me,aB as he,i as w,aC as Ee,aD as xe,c as I,a8 as le,G as pe,k as ge,o as k,a as oe,b as u,w as x,m as f,H as c,n as e,ad as re,y as Ce,f as b,a5 as z,af as Te,g as S,F as ie,aE as Le,aF as ze,aG as Se,aH as Ne,L as Oe,aI as Qe,aJ as _e,h as Be,e as Ke,a3 as He,S as J,aK as Fe,aL as De,aM as H,aN as B,aO as Me,U as ye,aP as Pe,aQ as Re,t as We,aR as Ue,aS as Ze,aT as Ye}from"./index-444b28c3.js";import{d as Ge,u as we}from"./index-e305bb62.js";import{u as Xe}from"./el-input-6b488ec7.js";import{i as je}from"./position-f84d51c4.js";import{g as qe}from"./scroll-a66dde9b.js";var Ve="Expected a function";function ne(d,h,r){var i=!0,v=!0;if(typeof d!="function")throw new TypeError(Ve);return Ie(r)&&(i="leading"in r?!!r.leading:i,v="trailing"in r?!!r.trailing:v),Ge(d,h,{leading:i,maxWait:h,trailing:v})}const Je=()=>K&&/firefox/i.test(window.navigator.userAgent),$e=ce({urlList:{type:se(Array),default:()=>de([])},zIndex:{type:Number},initialIndex:{type:Number,default:0},infinite:{type:Boolean,default:!0},hideOnClickModal:{type:Boolean,default:!1},teleported:{type:Boolean,default:!1},closeOnPressEscape:{type:Boolean,default:!0}}),ea={close:()=>!0,switch:d=>Ae(d)},aa=["src"],ta={name:"ElImageViewer"},na=ve({...ta,props:$e,emits:ea,setup(d,{expose:h,emit:r}){const i=d,v={CONTAIN:{name:"contain",icon:ue(ke)},ORIGINAL:{name:"original",icon:ue(be)}},C=Je()?"DOMMouseScroll":"mousewheel",{t:$}=we(),l=me("image-viewer"),{nextZIndex:F}=he(),m=w(),E=w([]),O=Ee(),p=w(!0),A=w(i.initialIndex),Q=xe(v.CONTAIN),o=w({scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}),D=I(()=>{const{urlList:a}=i;return a.length<=1}),W=I(()=>A.value===0),U=I(()=>A.value===i.urlList.length-1),M=I(()=>i.urlList[A.value]),ee=I(()=>{const{scale:a,deg:n,offsetX:s,offsetY:g,enableTransition:T}=o.value;let y=s/a,L=g/a;switch(n%360){case 90:case-270:[y,L]=[L,-y];break;case 180:case-180:[y,L]=[-y,-L];break;case 270:case-90:[y,L]=[-L,y];break}const _={transform:`scale(${a}) rotate(${n}deg) translate(${y}px, ${L}px)`,transition:T?"transform .3s":""};return Q.value.name===v.CONTAIN.name&&(_.maxWidth=_.maxHeight="100%"),_}),Z=I(()=>Ae(i.zIndex)?i.zIndex:F());function N(){te(),r("close")}function ae(){const a=ne(s=>{switch(s.code){case B.esc:i.closeOnPressEscape&&N();break;case B.space:j();break;case B.left:q();break;case B.up:t("zoomIn");break;case B.right:V();break;case B.down:t("zoomOut");break}}),n=ne(s=>{(s.wheelDelta?s.wheelDelta:-s.detail)>0?t("zoomIn",{zoomRate:1.2,enableTransition:!1}):t("zoomOut",{zoomRate:1.2,enableTransition:!1})});O.run(()=>{H(document,"keydown",a),H(document,C,n)})}function te(){O.stop()}function Y(){p.value=!1}function G(a){p.value=!1,a.target.alt=$("el.image.error")}function X(a){if(p.value||a.button!==0||!m.value)return;o.value.enableTransition=!1;const{offsetX:n,offsetY:s}=o.value,g=a.pageX,T=a.pageY,y=ne(_=>{o.value={...o.value,offsetX:n+_.pageX-g,offsetY:s+_.pageY-T}}),L=H(document,"mousemove",y);H(document,"mouseup",()=>{L()}),a.preventDefault()}function P(){o.value={scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}}function j(){if(p.value)return;const a=Me(v),n=Object.values(v),s=Q.value.name,T=(n.findIndex(y=>y.name===s)+1)%a.length;Q.value=v[a[T]],P()}function R(a){const n=i.urlList.length;A.value=(a+n)%n}function q(){W.value&&!i.infinite||R(A.value-1)}function V(){U.value&&!i.infinite||R(A.value+1)}function t(a,n={}){if(p.value)return;const{zoomRate:s,rotateDeg:g,enableTransition:T}={zoomRate:1.4,rotateDeg:90,enableTransition:!0,...n};switch(a){case"zoomOut":o.value.scale>.2&&(o.value.scale=Number.parseFloat((o.value.scale/s).toFixed(3)));break;case"zoomIn":o.value.scale<7&&(o.value.scale=Number.parseFloat((o.value.scale*s).toFixed(3)));break;case"clockwise":o.value.deg+=g;break;case"anticlockwise":o.value.deg-=g;break}o.value.enableTransition=T}return le(M,()=>{pe(()=>{const a=E.value[0];a!=null&&a.complete||(p.value=!0)})}),le(A,a=>{P(),r("switch",a)}),ge(()=>{var a,n;ae(),(n=(a=m.value)==null?void 0:a.focus)==null||n.call(a)}),h({setActiveItem:R}),(a,n)=>(k(),oe(De,{to:"body",disabled:!a.teleported},[u(Fe,{name:"viewer-fade",appear:""},{default:x(()=>[f("div",{ref_key:"wrapper",ref:m,tabindex:-1,class:c(e(l).e("wrapper")),style:re({zIndex:e(Z)})},[f("div",{class:c(e(l).e("mask")),onClick:n[0]||(n[0]=Ce(s=>a.hideOnClickModal&&N(),["self"]))},null,2),b(" CLOSE "),f("span",{class:c([e(l).e("btn"),e(l).e("close")]),onClick:N},[u(e(z),null,{default:x(()=>[u(e(Te))]),_:1})],2),b(" ARROW "),e(D)?b("v-if",!0):(k(),S(ie,{key:0},[f("span",{class:c([e(l).e("btn"),e(l).e("prev"),e(l).is("disabled",!a.infinite&&e(W))]),onClick:q},[u(e(z),null,{default:x(()=>[u(e(Le))]),_:1})],2),f("span",{class:c([e(l).e("btn"),e(l).e("next"),e(l).is("disabled",!a.infinite&&e(U))]),onClick:V},[u(e(z),null,{default:x(()=>[u(e(ze))]),_:1})],2)],64)),b(" ACTIONS "),f("div",{class:c([e(l).e("btn"),e(l).e("actions")])},[f("div",{class:c(e(l).e("actions__inner"))},[u(e(z),{onClick:n[1]||(n[1]=s=>t("zoomOut"))},{default:x(()=>[u(e(Se))]),_:1}),u(e(z),{onClick:n[2]||(n[2]=s=>t("zoomIn"))},{default:x(()=>[u(e(Ne))]),_:1}),f("i",{class:c(e(l).e("actions__divider"))},null,2),u(e(z),{onClick:j},{default:x(()=>[(k(),oe(Oe(e(Q).icon)))]),_:1}),f("i",{class:c(e(l).e("actions__divider"))},null,2),u(e(z),{onClick:n[3]||(n[3]=s=>t("anticlockwise"))},{default:x(()=>[u(e(Qe))]),_:1}),u(e(z),{onClick:n[4]||(n[4]=s=>t("clockwise"))},{default:x(()=>[u(e(_e))]),_:1})],2)],2),b(" CANVAS "),f("div",{class:c(e(l).e("canvas"))},[(k(!0),S(ie,null,Be(a.urlList,(s,g)=>Ke((k(),S("img",{ref_for:!0,ref:T=>E.value[g]=T,key:s,src:s,style:re(e(ee)),class:c(e(l).e("img")),onLoad:Y,onError:G,onMousedown:X},null,46,aa)),[[He,g===A.value]])),128))],2),J(a.$slots,"default")],6)]),_:3})],8,["disabled"]))}});var sa=fe(na,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/image-viewer/src/image-viewer.vue"]]);const la=ye(sa),oa=ce({hideOnClickModal:{type:Boolean,default:!1},src:{type:String,default:""},fit:{type:String,values:["","contain","cover","fill","none","scale-down"],default:""},loading:{type:String,values:["eager","lazy"]},lazy:{type:Boolean,default:!1},scrollContainer:{type:se([String,Object])},previewSrcList:{type:se(Array),default:()=>de([])},previewTeleported:{type:Boolean,default:!1},zIndex:{type:Number},initialIndex:{type:Number,default:0},infinite:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0}}),ra={load:d=>d instanceof Event,error:d=>d instanceof Event,switch:d=>Ae(d),close:()=>!0,show:()=>!0},ia=["src","loading"],Aa={key:0},ua={name:"ElImage",inheritAttrs:!1},ca=ve({...ua,props:oa,emits:ra,setup(d,{emit:h}){const r=d;let i="";const{t:v}=we(),C=me("image"),$=Pe(),l=Xe(),F=w(),m=w(!1),E=w(!0),O=w(!1),p=w(),A=w(),Q=K&&"loading"in HTMLImageElement.prototype;let o,D;const W=I(()=>$.style),U=I(()=>{const{fit:t}=r;return K&&t?{objectFit:t}:{}}),M=I(()=>{const{previewSrcList:t}=r;return Array.isArray(t)&&t.length>0}),ee=I(()=>{const{previewSrcList:t,initialIndex:a}=r;let n=a;return a>t.length-1&&(n=0),n}),Z=I(()=>r.loading==="eager"?!1:!Q&&r.loading==="lazy"||r.lazy),N=()=>{K&&(E.value=!0,m.value=!1,F.value=r.src)};function ae(t){E.value=!1,m.value=!1,h("load",t)}function te(t){E.value=!1,m.value=!0,h("error",t)}function Y(){je(p.value,A.value)&&(N(),P())}const G=Ye(Y,200);async function X(){var t;if(!K)return;await pe();const{scrollContainer:a}=r;Ue(a)?A.value=a:Ze(a)&&a!==""?A.value=(t=document.querySelector(a))!=null?t:void 0:p.value&&(A.value=qe(p.value)),A.value&&(o=H(A,"scroll",G),setTimeout(()=>Y(),100))}function P(){!K||!A.value||!G||(o==null||o(),A.value=void 0)}function j(t){if(t.ctrlKey){if(t.deltaY<0)return t.preventDefault(),!1;if(t.deltaY>0)return t.preventDefault(),!1}}function R(){M.value&&(D=H("wheel",j,{passive:!1}),i=document.body.style.overflow,document.body.style.overflow="hidden",O.value=!0,h("show"))}function q(){D==null||D(),document.body.style.overflow=i,O.value=!1,h("close")}function V(t){h("switch",t)}return le(()=>r.src,()=>{Z.value?(E.value=!0,m.value=!1,P(),X()):N()}),ge(()=>{Z.value?X():N()}),(t,a)=>(k(),S("div",{ref_key:"container",ref:p,class:c([e(C).b(),t.$attrs.class]),style:re(e(W))},[F.value!==void 0&&!m.value?(k(),S("img",Re({key:0},e(l),{src:F.value,loading:t.loading,style:e(U),class:[e(C).e("inner"),e(M)&&e(C).e("preview"),E.value&&e(C).is("loading")],onClick:R,onLoad:ae,onError:te}),null,16,ia)):b("v-if",!0),E.value||m.value?(k(),S("div",{key:1,class:c(e(C).e("wrapper"))},[E.value?J(t.$slots,"placeholder",{key:0},()=>[f("div",{class:c(e(C).e("placeholder"))},null,2)]):m.value?J(t.$slots,"error",{key:1},()=>[f("div",{class:c(e(C).e("error"))},We(e(v)("el.image.error")),3)]):b("v-if",!0)],2)):b("v-if",!0),e(M)?(k(),S(ie,{key:2},[O.value?(k(),oe(e(la),{key:0,"z-index":t.zIndex,"initial-index":e(ee),infinite:t.infinite,"url-list":t.previewSrcList,"hide-on-click-modal":t.hideOnClickModal,teleported:t.previewTeleported,"close-on-press-escape":t.closeOnPressEscape,onClose:q,onSwitch:V},{default:x(()=>[t.$slots.viewer?(k(),S("div",Aa,[J(t.$slots,"viewer")])):b("v-if",!0)]),_:3},8,["z-index","initial-index","infinite","url-list","hide-on-click-modal","teleported","close-on-press-escape"])):b("v-if",!0)],64)):b("v-if",!0)],6))}});var da=fe(ca,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/image/src/image.vue"]]);const ya=ye(da);const wa="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBxIRDQ8QEBIQDhAPDQ8PEBANEQ8NDxIPFR0WFhURFRMYHSggGBolGxUTITEhJSkrLi4uFx8zODMtNygtLjIBCgoKBQUFDgUFDisZExkrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrK//AABEIAMYA/wMBIgACEQEDEQH/xAAZAAEAAwEBAAAAAAAAAAAAAAAAAgMEAQf/xAAoEAEAAgIBBAICAgIDAAAAAAAAAQIDETETIVFxMkFhsZHBEuEEIqH/xAAUAQEAAAAAAAAAAAAAAAAAAAAA/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAwDAQACEQMRAD8A9pQvk1+ZMl9e5UAlN5lES/wnwCIl058HTnwCIl058HTnwCIl058HTnwCIl058HTnwCIl058HTnwCIl058HTnwCIl058HTnwCIl058HTnwCIl058HTnwCIl058HTnwCIl058HTnwCIl058HTnwCKUXmP9n+E+EQX0vv2mytGO24BTee8oiWPmAXUrpIAAAAAAAAAAAAAAAAcmQdFc5YOr+AWCNbxKQAAAACGSm/aYDKninujbmfcuAJY/lCKWP5QDQAAAAAAAAAAAAAAACN76UTOy07lwAX0wef4hKcEfkGZZTJrnhzJj0gDUK8NvrwsAAAABmtzPuXHbcz7lwBLH8oRSx/KAaAAAAAAAAAAAAAAEck9pSQyx2kFC3/j1778Kl3/GnvMAvABy9dxMMbbadRtiBPFPdez4+YaAAAAAZrcz7lx23M+5cASx/KEUsfygGgAAHJnQEzpTbJPpy99ognXJPtdE7ZkqW0DQORO3QAAAACYAGaYIldkpv2oBppmj77fpKckeWQBZly79KxKlN+gTwx9rSIAAAAAZrcz7lx23M+5cASx/KEUsfygGgHJnQEzpRe+y99ogJ0xTP+0sWLfeeP20Ay2xTH+kG1ny4td44/QIUtpfE7ZkqW0DQORO3QAAAAEbViUgFU4fz/LnRn8LgFdcUe1gAAAAAAAzW5n3LjtuZ9y4Alj+UIpUnuC+Z0ovfZe+0QFuLFvvPH7MWLfeeP20AAAAAozYtd44/SltUZcWu8cfoFdLaXxO2ZKltA0DkTt0AAAAAABXbLHtHJf6hWC+uSJ/CbKux5PqQWAAAAzW5n3LjtuZ9y4AAAtxYt954/Zixb7zx+2gAAAAAAAAFGbFrvHCltUZsWu8cArpbS+J2zJUtoGgcidugAAKcl/qDJf6hWAJY6blqrSI4BjGu+OJ/E+WW1dTqQW48n1KxlXY8n1ILAAZrcz7lx23M+5cASxxu0Ip4flANQAAAAAAAAAAAKM2LXeOFLaz5seu8cAhS2l1Z2zpUtoGhTkv9QZMm+FYCWOm5MdNy1VrqNQBWuo1DoAI5Kbj+0gGO1dTqXGvJTcf2y2rqdSC3Hk+pWMq7Hk+pBVbmfcuO25n3LgCeH5Qgnh+UA1AAAAAAAAAAAAOOgM+XFrvHH2qbVGbFrvHAKUsdNyiljvqQaq11Godcrbcbh0AAAABHJTcf2kAx2rqdS415Kbj+2W1dTqQcAATw/KEZdxz/wBo9g1gAAAAAAAAAS46AAAAAozYtd44UtqjNi13jgFeO+paq23G4Y0sd9SDWOVtuNw6AAAAAjkpuP1KQDHMIpWnvPsiASyx39oNF67hRMaBfizfU/yt2xANoxANoxANoxANoxANoxANoxANoxANoxALs2PXeOFIAljvqWqttxtjAbRiAbRiAbZlTly/UfyoAFmGO+/CFa7aKxqAdctXYAqtiVgAAAAAAAAAAAAAAAAAAAAAAAADsQsri8/+ACyI06AP/9k=";export{ya as E,wa as d};
