<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-07-24 11:35:41
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-16 17:09:17
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\system\Feedback.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-card>
            <!-- form表单， 搜索框，部门选择，时间选择 -->
            <el-form :inline="true" ref="form" :model="form" class="demo-form-inline">
                <el-form-item label="部门">
                    <el-select v-model="form.department" placeholder="请选择部门">
                        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <!-- 下拉选择，已处理未处理 -->
                <el-form-item label="处理状态">
                    <el-select v-model="form.status" placeholder="请选择状态">
                        <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSubmit" icon="el-icon-search">查询</el-button>
                </el-form-item>
                <!-- 重置按钮 -->
                <el-form-item>
                    <el-button @click="resetForm('form')" icon="el-icon-refresh">重置</el-button>
                </el-form-item>
            </el-form>
            <!-- 主体部分 -->
            <el-table :data="tableData" style="width: 100%" stripe v-loading="loading" element-loading-text="Flyknit">
                <!-- 编号 -->
                <el-table-column type="index" label="编号" width="80">
                </el-table-column>
                <el-table-column prop="title" label="分类" width="180">
                    <template slot-scope="scope">
                        <span>【{{ scope.row.title }}】</span>
                    </template>
                </el-table-column>
                <el-table-column prop="content" label="反馈内容">
                </el-table-column>
                <el-table-column prop="department" label="反馈部门">
                </el-table-column>
                <el-table-column prop="uuid" label="反馈人">
                </el-table-column>
                <el-table-column prop="create_time" label="反馈时间">
                </el-table-column>
                <el-table-column prop="status" label="处理状态">
                    <!-- 如果status为0，显示未处理，如果status为1，显示已处理 -->
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.status == 0" type="danger">待处理</el-tag>
                        <el-tag v-else type="success">已处理</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-button size="mini" :type="scope.row.status > 0 ? 'primary' : 'warning'"
                            v-show="scope.row.status < 1 && User.level > 0" plain @click.native="editHandler(scope.row.id)">
                            <i :class="scope.row.status > 0 ? 'el-icon-circle-check' : 'el-icon-time'"></i>
                            {{ scope.row.status < 1 ? "待处理" : "已处理" }} </el-button>
                                <el-button size="mini" type="danger"
                                v-show="User.level > 0"
                                icon="el-icon-delete"
                                    @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页组件 -->
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="form.currentPage" :page-sizes="[10, 20, 30, 40]" :page-size="form.pageSize"
                layout="total, sizes, prev, pager, next, jumper" :total="form.total">
            </el-pagination>
        </el-card>
    </div>
</template>
<script>
import { mapState } from 'vuex'
export default {
    data() {
        return {
            loading: true,
            // 表单
            form: {
                // 分页
                currentPage: 1,
                pageSize: 10,
                total: 0,
                department: '',
                status: ''
            },
            // 表单
            forms: {
                name: '',
                desc: ''
            },
            options: [{
                value: '织造车间',
                label: '织造车间'
            }, {
                value: '整理车间',
                label: '整理车间'
            }, {
                value: '打样车间',
                label: '打样车间'
            }, {
                value: '综合维修',
                label: '综合维修'
            }],
            options1: [{
                value: '已处理',
                label: '已处理'
            }, {
                value: '未处理',
                label: '未处理'
            }],
            tableData: [
                {
                    id: 1,
                    title: '织造车间',
                    content: '织造车间的机器坏了',
                    department: '织造车间',
                    uuid: '张三',
                    create_time: '2021-08-09 13:30:00',
                    status: 0
                },
                {
                    id: 1,
                    title: '织造车间',
                    content: '织造车间的机器坏了',
                    department: '织造车间',
                    uuid: '张三',
                    create_time: '2021-08-09 13:30:00',
                    status: 0
                },
                {
                    id: 1,
                    title: '织造车间',
                    content: '织造车间的机器坏了',
                    department: '织造车间',
                    uuid: '张三',
                    create_time: '2021-08-09 13:30:00',
                    status: 0
                },
                {
                    id: 1,
                    title: '织造车间',
                    content: '织造车间的机器坏了',
                    department: '织造车间',
                    uuid: '张三',
                    create_time: '2021-08-09 13:30:00',
                    status: 0
                },
                {
                    id: 1,
                    title: '织造车间',
                    content: '织造车间的机器坏了',
                    department: '织造车间',
                    uuid: '张三',
                    create_time: '2021-08-09 13:30:00',
                    status: 0
                },
                {
                    id: 1,
                    title: '织造车间',
                    content: '织造车间的机器坏了',
                    department: '织造车间',
                    uuid: '张三',
                    create_time: '2021-08-09 13:30:00',
                    status: 0
                },
                {
                    id: 1,
                    title: '织造车间',
                    content: '织造车间的机器坏了',
                    department: '织造车间',
                    uuid: '张三',
                    create_time: '2021-08-09 13:30:00',
                    status: 0
                }
            ]
        }
    },
    computed: {
        ...mapState({
            User: state => state.users
        })
    },
    created() {
        this.getFeedbackList()
    },
    methods: {
        // 获取意见反馈列表
        async getFeedbackList() {
            try {
                const res = await this.$http.getFeedbackLists(this.form)
                if (res.status !== 200) {
                    return this.$message.error(res.message)
                }
                const { list, total } = res.data
                this.tableData = list
                this.form.total = total
                setTimeout(() => {
                    this.loading = false
                }, 1000);
            } catch (error) {
                this.loading = false
                return this.$message.error('服务器错误，请稍后重试')
            }
        },
        onSubmit() {
            this.loading = true
            this.getFeedbackList()
        },
        async editHandler(uid) {
            try {
                this.$confirm('确认修改当前状态吗?', '系统提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    const res = await this.$http.modifyStatus({ uid })
                    if (res.status !== 200) {
                        return this.$message.error(res.message)
                    }
                    this.$message.success(res.message)
                    this.getFeedbackList()
                }).catch(() => {
                    return false
                })
            } catch (error) {
                return this.$message.error('服务器错误，请稍后重试')
            }
        },
        async handleDelete(index, row) {
            try {
                this.$confirm('确认删除该条数据吗?', '系统提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    const res = await this.$http.deleteFeedback({ uid: row.id })
                    if (res.status !== 200) {
                        return this.$message.error(res.message)
                    }
                    this.$message.success(res.message)
                    this.getFeedbackList()
                }).catch(() => {
                    return false
                })
            } catch (error) {
                return this.$message.error('服务器错误，请稍后重试')
            }
        },
        handleSizeChange(val) {
            this.loading = true
            this.pageSize = val
            this.getFeedbackList()
        },
        handleCurrentChange(val) {
            this.loading = true
            this.currentPage = val
            this.getFeedbackList()
        },
        resetForm(formName) {
            this.form.department = ''
            this.form.status = ''
        }
    }
}
</script>
<style lang="scss">
.el-card .el-card__body {
    padding: 20px 20px 0px 20px;
}

.el-pagination {
    padding: 20px 0;
}
</style>