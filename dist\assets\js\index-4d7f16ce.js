import{X as d,bD as f,bE as p,c as v,n as I,cj as h,c5 as m,i,k as j,a8 as y,bh as w,Y as E}from"./index-444b28c3.js";const F={prefix:Math.floor(Math.random()*1e4),current:0},K=Symbol("elIdInjection"),N=n=>{const u=d(K,F),a=f("namespace",p);return v(()=>I(n)||`${a.value}-id-${u.prefix}-${u.current++}`)},_=()=>{const n=d(h,void 0),u=d(m,void 0);return{form:n,formItem:u}},$=(n,{formItemContext:u,disableIdGeneration:a,disableIdManagement:s})=>{a||(a=i(!1)),s||(s=i(!1));const e=i();let l;const r=v(()=>{var o;return!!(!n.label&&u&&u.inputIds&&((o=u.inputIds)==null?void 0:o.length)<=1)});return j(()=>{l=y([w(n,"id"),a],([o,t])=>{const c=o??(t?void 0:N().value);c!==e.value&&(u!=null&&u.removeInputId&&(e.value&&u.removeInputId(e.value),!(s!=null&&s.value)&&!t&&c&&u.addInputId(c)),e.value=c)},{immediate:!0})}),E(()=>{l&&l(),u!=null&&u.removeInputId&&e.value&&u.removeInputId(e.value)}),{isLabeledByFormItem:r,inputId:e}};export{N as a,$ as b,_ as u};
