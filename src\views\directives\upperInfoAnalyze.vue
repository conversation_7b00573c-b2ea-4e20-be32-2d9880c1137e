<template>
	<el-row :gutter="20">
		<!-- 搜索条件表单 -->
		<el-col :xs="24" :sm="24" :md="24" :lg="24" class="search-content">
			<el-card :header="$t('upperInfoAnalyze.searchCondition')" shadow="hover">
				<el-form :model="ruleForm" ref="formRef" :rules="rules" label-width="auto" :inline="true" class="demo-form-inline">
					<el-form-item :label="$t('upperInfoAnalyze.size')" prop="size">
						<el-select v-model="ruleForm.size" :placeholder="$t('upperInfoAnalyze.xzcm')" @change="sizeChange">
							<el-option v-for="item in sizeList" :key="item" :label="item.size" :value="item.size" />
						</el-select>
					</el-form-item>
					<el-form-item :label="$t('upperInfoAnalyze.type')" prop="type">
						<el-select v-model="ruleForm.type" :placeholder="$t('upperInfoAnalyze.xzlx')" @change="typeChange">
							<el-option :label="$t('upperInfoAnalyze.ml')" value="0" />
							<el-option :label="$t('upperInfoAnalyze.gp')" value="1" />
						</el-select>
					</el-form-item>
					<!-- 品名 -->
					<!-- <el-form-item label="品名" prop="pm">
						<el-select v-model="ruleForm.pm" placeholder="请选择品名">
							<el-option label="UPPER-L" value="UPPER-L" />
							<el-option label="UPPER-R" value="UPPER-R" />
						</el-select>
					</el-form-item> -->
					<el-form-item :label="$t('upperInfoAnalyze.color')">
						<el-input v-model="ruleForm.color" :placeholder="$t('upperInfoAnalyze.srcolor')" size="default" clearable></el-input>
					</el-form-item>
					<el-form-item :label="$t('upperInfoAnalyze.styleno')" v-show="isExpand">
						<el-input
							v-model="ruleForm.styleno"
							:placeholder="$t('upperInfoAnalyze.srstyleno')"
							size="default"
							clearable
						></el-input>
					</el-form-item>
					<!-- <el-form-item label="显示条数" v-show="isExpand">
						<el-select v-model="ruleForm.pageSize" placeholder="请选择展示条数">
							<el-option label="10" value="10" />
							<el-option label="15" value="15" />
							<el-option label="20" value="20" />
						</el-select>
					</el-form-item> -->
					<el-form-item :label="$t('upperInfoAnalyze.date')" v-show="isExpand">
						<el-date-picker
							v-model="ruleForm.dates"
							type="datetimerange"
							:range-separator="$t('upperInfoAnalyze.zhi')"
							:start-placeholder="$t('upperInfoAnalyze.srstartDate')"
							:end-placeholder="$t('upperInfoAnalyze.srendDate')"
							format="YYYY-MM-DD HH:mm:ss"
							value-format="YYYY-MM-DD HH:mm:ss"
							:shortcuts="shortcuts"
						/>
					</el-form-item>
					<el-form-item style="margin-left: -3rem">
						<el-col :span="6">
							<el-button type="primary" :icon="Search" @click="submitForm(formRef)">{{
								$t("upperInfoAnalyze.search")
							}}</el-button>
						</el-col>
						<el-col :span="6">
							<el-button type="info" plain :icon="Refresh" @click="refreshHandler(formRef)">{{
								$t("upperInfoAnalyze.clear")
							}}</el-button>
						</el-col>
						<el-col :span="6">
							<el-button type="success" :icon="Download" @click="exportToExcel" :disabled="dataList.length === 0">{{
								$t("upperInfoAnalyze.export")
							}}</el-button>
						</el-col>
						<el-col :span="6">
							<el-button type="default" plain :icon="isExpand ? ArrowUp : ArrowDown" @click="isExpand = !isExpand">{{
								isExpand ? $t("upperInfoAnalyze.collapse") : $t("upperInfoAnalyze.expand")
							}}</el-button>
						</el-col>
					</el-form-item>
				</el-form>
			</el-card>
		</el-col>
		<!-- tab导航栏 -->
		<el-col :xs="24" :sm="24" :md="24" :lg="24" class="navbar-content">
			<el-tabs tab-position="left" type="border-card" class="demo-tabs" @tab-change="handleClick($event)">
				<el-tab-pane v-for="(item, index) in model" :key="index" :label="item.name">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" class="analyze-content" v-if="dataList.length > 0">
						<el-col :xs="24" :sm="24" :md="24" :lg="24" v-for="(item2, index2) in dataList" :key="index2" class="analyze-item">
							<div class="analyze-card">
								<el-table
									:data="item2.tableData"
									border
									style="width: 100%"
									v-loading="loading"
									:element-loading-text="loadingText"
								>
									<!-- 固定列 -->
									<el-table-column prop="version" label="Version" align="center">
										<template #default="scope">
											<div v-if="scope.row.version" style="cursor: pointer" @click="openPopup">
												{{ scope.row.version }}
											</div>
											<div v-else-if="scope.row.isCountData" class="stat-row">
												{{ scope.row.date }}
											</div>
											<div v-else-if="scope.row.isStandardData" class="standard-row">
												{{ scope.row.date }}
											</div>
											<div v-else>
												{{ scope.row.version }}
											</div>
										</template>
									</el-table-column>
									<el-table-column prop="panel" label="Panel#" align="center" width="100px" />
									<el-table-column prop="date" label="date" align="center" width="120px">
										<template #default="scope">
											<div v-if="scope.row.isCountData || scope.row.isStandardData">
												<!-- 统计行和标准行不显示date -->
											</div>
											<div v-else>
												{{ scope.row.date }}
											</div>
										</template>
									</el-table-column>

									<!-- 动态表头 -->
									<el-table-column align="center">
										<template #header>
											<div class="custom-header">
												<div>
													<span class="size-info">SIZE</span>
													<span class="size-info-value">{{ item2.size }}</span>
												</div>
												<span class="process-info"
													>{{ ruleForm.type === "0" ? "OFF MACHINE：" : "POST-STEAM:" }} {{ item2.postSteam }}</span
												>
											</div>
										</template>
										<!-- 动态列 -->
										<el-table-column
											v-for="(item1, index1) in item2.theaderNames"
											:key="index1"
											:prop="item1"
											:label="item1"
											align="center"
										>
											<template #default="scope">
												<!-- 样式处理 -->
												<div v-if="scope.row['panel']" :class="getDeviationClass(scope.row[item1], item2.standards[item1])">
													{{ scope.row[item1] || 0 }}
												</div>
												<div v-else-if="scope.row['date'] === 'JIG'" class="color5">
													{{ scope.row[item1] || 0 }}
												</div>
												<div v-else-if="scope.row['isCountData']" class="stat-cell">
													{{ scope.row[item1] || 0 }}
												</div>
												<div v-else-if="scope.row['isStandardData']" class="standard-cell">
													{{ scope.row[item1] || 0 }}
												</div>
											</template>
										</el-table-column>
									</el-table-column>
								</el-table>
							</div>
						</el-col>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" v-else>
						<div style="padding-top: 7rem">
							<el-empty :description="$t('upperInfoAnalyze.exportDisabled')" :image-size="150" />
						</div>
					</el-col>
				</el-tab-pane>
			</el-tabs>
		</el-col>
	</el-row>
	<!-- 统计弹窗 -->
	<el-dialog :title="$t('upperInfoAnalyze.defectStatistics')" v-model="popupVisible" width="60%">
		<div style="text-align: center; padding-bottom: 1rem">
			<el-button type="primary" size="default" @click="currentTable = 'left'">LEFT</el-button>
			<el-button type="success" size="default" @click="currentTable = 'right'">RIGHT</el-button>
		</div>
		<div
			class="dialog_content"
			v-for="(item, index) in popupData"
			:key="index"
			v-show="(currentTable === 'left' && index === 0) || (currentTable === 'right' && index === 1)"
		>
			<el-table :data="item.calculateData" style="width: 100%">
				<el-table-column :label="'SIZE ' + item.size + '   OFF MACHINE：' + item.postSteam" align="center">
					<el-table-column
						v-for="(column, colIndex) in item.theaderNames"
						:key="colIndex"
						:prop="column"
						:label="column"
						align="center"
					>
						<template #default="scope">
							<div :style="{ color: scope.row[column] === 1 ? '#f00' : '#333' }">
								{{ scope.row[column] }}
							</div>
						</template>
					</el-table-column>
				</el-table-column>
			</el-table>
		</div>
	</el-dialog>
</template>
<script setup name="upperInfoAnalyze">
import { reactive, ref, onMounted } from "vue";
import { Search, Refresh, ArrowDown, ArrowUp, Download } from "@element-plus/icons-vue";
import { getOptionList, getUpperInfoAnalyze } from "@/api/api";
import { ElNotification, ElLoading } from "element-plus";
import ExcelJS from "exceljs";
import { saveAs } from "file-saver";
const loading = ref(true); // 加载
const loadingText = ref("Flyknit..."); // 加载文字
const isExpand = ref(true); // 是否展开搜索
const currentTable = ref("left"); // 'left' 或 'right'
import { useI18n } from "vue-i18n";
const { t } = useI18n();
console.log("t", t);

// 时间快捷键
const shortcuts = [
	{
		text: "上周",
		value: () => {
			const end = new Date();
			const start = new Date();
			start.setDate(start.getDate() - 7);
			return [start, end];
		}
	},
	{
		text: "上月",
		value: () => {
			const end = new Date();
			const start = new Date();
			start.setMonth(start.getMonth() - 1);
			return [start, end];
		}
	},
	{
		text: "前三月",
		value: () => {
			const end = new Date();
			const start = new Date();
			start.setMonth(start.getMonth() - 3);
			return [start, end];
		}
	},
	{
		text: "今年",
		value: () => {
			const end = new Date();
			const start = new Date();
			start.setFullYear(start.getFullYear() - 1);
			return [start, end];
		}
	}
];
const ruleForm = ref({
	model: "", // model
	size: "", // 尺码d
	type: "", // 类型
	// pm: "UPPER-L", // 品名
	styleno: "", // 类型
	dates: "", // 日期
	color: "" // 配色
});
const formRef = ref(null); // 表单ref
const dataList = ref([]); // 表格数据
const popupData = ref([]); // 弹窗数据
const popupVisible = ref(false); // 弹窗显示
// 表单验证
const rules = reactive({
	size: [{ required: true, message: t("upperInfoAnalyze.xzcm"), trigger: "change" }],
	type: [{ required: true, message: t("upperInfoAnalyze.xzlx"), trigger: "change" }]
	// pm: [{ required: true, message: "请选择品名", trigger: "change" }]
});
// model列表
const model = ref([]);
// 尺码
const sizeList = ref([]);
// 提交表单
const submitForm = async formEl => {
	if (!formEl) return;
	await formEl.validate(valid => {
		if (valid) {
			// console.log("ruleForm", ruleForm.value);
			ruleForm.value.startTime = ruleForm.value.dates[0];
			ruleForm.value.endTime = ruleForm.value.dates[1];
			loading.value = true;
			getDataList();
		} else {
			return false;
		}
	});
};
// 切换尺码
const sizeChange = () => {
	loading.value = true;
	getDataList();
};
// 更换类型
const typeChange = () => {
	loading.value = true;
	getDataList();
};
// 计算统计数据的最小值、最大值、平均值
const getDeviationClass = (value, standard) => {
	if (!standard || standard.length < 3) return ""; // 没有标准，返回默认样式
	const [, min, max] = standard; // 忽略第一个值（用逗号跳过）
	return value < min || value > max ? "color1" : "";
};
// 获取表单数据，步骤分析，首先获取订单表中该model的serial 然后根据serial去获取版本数量，将版本数据根据大版本号分组，然后根据serial去毛坯测量表或者光坯测量表中获取测量数据
// 还要根据订单中的model信息去获取标准数据，然后合并数据
// 思路：根据model，尺码，类型获取到该model包含的品名列表的测试数据， 根据左右脚做分组，然后获取到左右脚的版本数据，再根据model获取到标准数据，然后合并数据
const getDataList = async () => {
	// 如果没有ruleForm.value.model和ruleForm.value.size，不执行
	if (!ruleForm.value.model || !ruleForm.value.size || !ruleForm.value.type) return false;
	loading.value = true;
	try {
		const res = await getUpperInfoAnalyze(ruleForm.value);
		if (res.code !== 200) {
			showNotification("error", res.msg);
			return;
		}

		const resData = res.data;

		if (!isValidData(resData)) {
			clearData();
			showNotification("info", "暂无数据");
			return;
		}

		// 处理数据
		// console.log("原始数据:", resData);
		const processedData = processDataList(resData);
		// console.log("处理后的数据:", processedData);
		dataList.value = processedData;
		popupData.value = processedData; // 同步弹窗数据
	} catch (error) {
		console.error("Error fetching data:", error);
		showNotification("warning", "当前model暂无数据");
	} finally {
		setTimeout(() => {
			loading.value = false;
		}, 500);
	}
};
// 封装loading
// const showLoading = text => ElLoading.service({ lock: true, text });
const showNotification = (type, message) => {
	ElNotification({ title: "", message, type });
};

// 数据处理逻辑模块化
const processDataList = resData =>
	resData
		.filter(dataItem => dataItem.data && Array.isArray(dataItem.data) && dataItem.data.length > 0) // 只处理有数据的项目
		.map(dataItem => {
			const { size, postSteam, standard } = dataItem;

			// 初始化表格和统计数据
			const { tableData, calculateData, theaderNames, countStats } = processTableData(dataItem);

			// 生成统计行
			const countTable = generateStatistics(countStats, theaderNames);

			// 生成标准行（增强校验）
			const standardData = standard && Object.keys(standard).length > 0 ? generateStandardRows(standard, theaderNames) : [];

			return {
				tableData: [...tableData, ...countTable, ...standardData],
				calculateData,
				size,
				postSteam,
				theaderNames,
				standards: standard || []
			};
		});

// 处理表格数据
const processTableData = dataItem => {
	const tableData = [];
	const calculateData = [];

	// 安全检查：确保有数据可以处理
	if (!dataItem.data || !Array.isArray(dataItem.data) || dataItem.data.length === 0) {
		return { tableData: [], calculateData: [], theaderNames: [], countStats: {} };
	}

	// 找到第一个有 versionData 的数据项
	const firstDataItem = dataItem.data.find(
		item => item.versionData && Array.isArray(item.versionData) && item.versionData.length > 0
	);
	if (!firstDataItem || !firstDataItem.versionData || firstDataItem.versionData.length === 0) {
		return { tableData: [], calculateData: [], theaderNames: [], countStats: {} };
	}

	const theaderNames = extractHeaderNames(firstDataItem.versionData[0]);
	const countStats = initializeStatistics(theaderNames);
	const { standard } = dataItem;

	dataItem.data.forEach((item, index) => {
		if (item.versionData && Array.isArray(item.versionData) && item.versionData.length > 0) {
			item.versionData.forEach((citem, cindex) => {
				const row = { ...citem, version: cindex === 0 ? item.version : "" };

				// 插入空行
				if (cindex === 0 && index > 0) tableData.push([]);

				// 统计数据
				calculateStatistics(row, theaderNames, countStats);
				tableData.push(row);

				// 当有标准数据时计算偏离值
				const newRow = { ...row };
				theaderNames.forEach(key => {
					const value = parseFloat(row[key]);
					if (!isNaN(value) && standard && standard[key] && standard[key].length >= 3) {
						newRow[key] = value < standard[key][1] || value > standard[key][2] ? 1 : 0;
					} else {
						newRow[key] = 0; // 默认值，表示没有偏离标准
					}
				});

				calculateData.push(newRow);
			});
		}
	});

	return { tableData, calculateData, theaderNames, countStats };
};
// 提取表头和统计初始化
const extractHeaderNames = firstVersionData => {
	return Object.keys(firstVersionData).filter(key => !["panel", "date", "version"].includes(key));
};
// 统计计算逻辑
const calculateStatistics = (row, headers, countStats) => {
	headers.forEach(key => {
		const value = parseFloat(row[key]);
		if (!isNaN(value)) {
			const stat = countStats[key];
			stat.sum += value;
			stat.count += 1;
			stat.min = Math.min(stat.min, value);
			stat.max = Math.max(stat.max, value);
		}
	});
};
// 统计初始化
const initializeStatistics = headers =>
	headers.reduce((stats, key) => {
		stats[key] = { sum: 0, count: 0, min: Infinity, max: -Infinity };
		return stats;
	}, {});

// 构造标准行（增强校验）
const generateStatistics = (countStats, headers) => {
	const countTypes = ["AVG", "MIN", "MAX", "RANGE"];
	return countTypes.map(type => {
		const row = { version: "", panel: "", date: type, isCountData: true };
		headers.forEach(key => {
			const { sum, count, min, max } = countStats[key];
			row[key] = {
				AVG: count > 0 ? Math.round(sum / count) : 0,
				MIN: count > 0 ? Math.round(min) : 0,
				MAX: count > 0 ? Math.round(max) : 0,
				RANGE: count > 0 ? Math.round(max - min) : 0
			}[type];
		});
		return row;
	});
};

const generateStandardRows = (standard, headers) => {
	const standardTypes = ["TRGT", "MIN", "MAX", "JIG"];
	return standardTypes.map(type => {
		const row = { version: "", panel: "", date: type, isStandardData: true };
		headers.forEach(key => {
			row[key] =
				standard && standard[key] && standard[key].length >= 3
					? { TRGT: standard[key][1], MIN: standard[key][0], MAX: standard[key][2], JIG: 0 }[type]
					: 0; // 当无标准时填充 0
		});
		return row;
	});
};

// 判断数据是否有效
const isValidData = data => {
	// 检查data是否存在且是数组
	if (!data || !Array.isArray(data) || data.length === 0) {
		return false;
	}

	// 检查是否有至少一个数据项包含有效数据
	return data.some(item => item && item.data && Array.isArray(item.data) && item.data.length > 0);
};
// 清空数据
const clearData = () => {
	dataList.value = [];
};

// 打开弹窗
const openPopup = () => {
	popupVisible.value = true;
};
// 页面挂载
onMounted(() => {
	// 获取下拉选项
	getOptionLists();
	// ruleForm.value.type = "0";
	// ruleForm.value.pm = "UPPER-L";
	// ruleForm.value.size = "6.5";
	// loading.value = true;
	// getDataList();
});
// 获取下拉选项
const getOptionLists = async () => {
	// 开启加载
	const loadingInstance = ElLoading.service({
		lock: true,
		text: "Flyknit..."
	});
	try {
		const res = await getOptionList();
		if (res.code !== 200) {
			return ElNotification({
				title: "",
				message: res.message,
				type: "error"
			});
		}
		const modelList = res.data.modelList;
		// 处理modelList数据
		model.value = modelList.map(item => {
			return {
				name: item.model_name
			};
		});
		// 临时插入测试用的 model
		// model.value.push({
		// 	name: "PEGASUS TURBO 4"
		// });
		sizeList.value = res.data.sizeList;
		ruleForm.value.model = model.value[0].name;
		loadingInstance.close();
		loading.value = false;
	} catch (error) {
		loadingInstance.close();
		loading.value = false;
		return false;
	}
};
// tab切换
const handleClick = e => {
	// console.log(e);
	// console.log("model", model.value[e].name);
	ruleForm.value.model = model.value[e].name;
	loading.value = true;
	getDataList();
};
const refreshHandler = formEl => {
	formEl.resetFields();
};

// 导出Excel功能
const exportToExcel = async () => {
	if (dataList.value.length === 0) {
		ElNotification({
			title: "提示",
			message: "暂无数据可导出",
			type: "warning"
		});
		return;
	}

	try {
		const workbook = new ExcelJS.Workbook();
		const worksheet = workbook.addWorksheet("UpperInfoAnalyze");
		// 获取所有动态列名（假设所有数据项的动态列相同）
		const dynamicHeaders = dataList.value[0]?.theaderNames || [];
		// 设置列
		const columns = [
			{ header: "Version", key: "version", width: 40 },
			{ header: "Panel#", key: "panel", width: 25 },
			{ header: "date", key: "date", width: 25 }
		];
		// 添加动态列
		dynamicHeaders.forEach(header => {
			columns.push({
				header: header,
				key: header.toLowerCase(),
				width: 10
			});
		});
		worksheet.columns = columns;
		let currentRow = 1;
		// 为每个数据项添加数据（左右分开）
		dataList.value.forEach((item, itemIndex) => {
			// 添加Model标题行（只在第一个数据项时添加）
			if (itemIndex === 0) {
				const modelTitleRow = worksheet.addRow([]);
				modelTitleRow.getCell(1).value = `Model: ${ruleForm.value.model}`;
				modelTitleRow.getCell(1).font = { bold: true, size: 16, color: { argb: "FF000000" } };
				modelTitleRow.getCell(1).alignment = { vertical: "middle", horizontal: "center" };
				worksheet.mergeCells(currentRow, 1, currentRow, columns.length);
				// 设置合并单元格的样式
				modelTitleRow.getCell(1).alignment = { vertical: "middle", horizontal: "center" };
				currentRow++;
				// 添加空行分隔
				worksheet.addRow([]);
				currentRow++;
			}
			// 添加标题行
			const titleRow = worksheet.addRow([]);
			titleRow.getCell(1).value = `SIZE ${item.size} ${ruleForm.value.type === "0" ? "OFF MACHINE：" : "POST-STEAM:"} ${
				item.postSteam
			}`;
			titleRow.getCell(1).font = { bold: true, size: 14, color: { argb: "FFFF0000" } };
			titleRow.getCell(1).alignment = { vertical: "middle", horizontal: "center" };
			worksheet.mergeCells(currentRow, 1, currentRow, columns.length);
			// 设置合并单元格的样式
			titleRow.getCell(1).alignment = { vertical: "middle", horizontal: "center" };
			currentRow++;
			// 添加表头
			const headerRow = worksheet.addRow([]);
			columns.forEach((col, colIndex) => {
				const cell = headerRow.getCell(colIndex + 1);
				cell.value = col.header;
				cell.font = { bold: true, color: { argb: "FFFFFFFF" } };
				cell.fill = {
					type: "pattern",
					pattern: "solid",
					fgColor: { argb: "FF4472C4" }
				};
				cell.alignment = { vertical: "middle", horizontal: "center" };
				cell.border = {
					top: { style: "thin" },
					left: { style: "thin" },
					bottom: { style: "thin" },
					right: { style: "thin" }
				};
			});
			currentRow++;
			// 添加数据行
			item.tableData.forEach(row => {
				const dataRow = worksheet.addRow([]);
				// Version列：包含AVG、MIN、MAX、RANGE、TRGT、MIN、MAX、JIG
				const versionCell = dataRow.getCell(1);
				if (row.isCountData || row.isStandardData) {
					versionCell.value = row.date; // AVG、MIN、MAX、RANGE、TRGT、MIN、MAX、JIG
				} else {
					versionCell.value = row.version || "";
				}
				// Panel#列
				dataRow.getCell(2).value = row.panel || "";
				// date列：统计行和标准行留空
				const dateCell = dataRow.getCell(3);
				if (!row.isCountData && !row.isStandardData) {
					dateCell.value = row.date || "";
				}
				// 动态列数据
				dynamicHeaders.forEach((header, headerIndex) => {
					const cell = dataRow.getCell(4 + headerIndex);
					cell.value = row[header] || "";
				});
				// 样式处理
				dataRow.eachCell((cell, colNumber) => {
					cell.alignment = { vertical: "middle", horizontal: "center" };
					cell.border = {
						top: { style: "thin" },
						left: { style: "thin" },
						bottom: { style: "thin" },
						right: { style: "thin" }
					};
					// 统计行样式（AVG, MIN, MAX, RANGE）
					if (row.isCountData) {
						cell.font = { bold: true, color: { argb: "FF409EFF" } };
					}
					// 标准行样式（TRGT, MIN, MAX, JIG）
					else if (row.isStandardData) {
						cell.font = { bold: true, color: { argb: "FF67C23A" } };
					}
					// 普通数据行样式
					else if (row.panel) {
						// 检查是否有偏离标准的数据
						const headerName = columns[colNumber - 1]?.header;
						if (headerName && dynamicHeaders.includes(headerName)) {
							const standard = item.standards[headerName];
							const value = row[headerName];
							if (standard && standard.length >= 3 && value) {
								const numValue = parseFloat(value);
								if (!isNaN(numValue) && (numValue < standard[1] || numValue > standard[2])) {
									cell.fill = {
										type: "pattern",
										pattern: "solid",
										fgColor: { argb: "FFFF6B6B" }
									};
									cell.font = { color: { argb: "FFFFFFFF" } };
								}
							}
						}
					}
				});
				currentRow++;
			});
			// 在数据项之间添加空行（除了最后一个）
			if (itemIndex < dataList.value.length - 1) {
				worksheet.addRow([]);
				currentRow++;
			}
		});
		// 生成文件名
		const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, "-");
		const fileName = `upperInfoAnalyze_${ruleForm.value.model}_${timestamp}.xlsx`;
		// 导出文件
		const buffer = await workbook.xlsx.writeBuffer();
		saveAs(new Blob([buffer]), fileName);
		ElNotification({
			title: t("upperInfoAnalyze.success"),
			message: t("upperInfoAnalyze.exportSuccess"),
			type: "success"
		});
	} catch (error) {
		console.error("Export Excel failed:", error);
		ElNotification({
			title: t("upperInfoAnalyze.error"),
			message: `${t("upperInfoAnalyze.exportFailed")}: ${error.message}`,
			type: "error"
		});
	}
};
</script>
<style lang="scss" scoped>
.el-table--border .el-table__cell:first-child .cell {
	padding: 0;
}
.el-table td.el-table__cell,
.el-table th.el-table__cell.is-leaf,
.el-table--border th.el-table__cell,
.el-table__fixed-right-patch {
	border-bottom-color: #d7d7d7;
	border-right-color: #d7d7d7;
}
.el-table--border,
.el-table--group {
	border-color: #d7d7d7;
}
.el-table .el-table__cell {
	padding: 0;
}
.el-table .cell {
	width: 100%;
	height: 100%;
	padding: 0;
}
.dialog_content .el-table .cell {
	min-width: 50px !important;
}
.color1 {
	color: var(--el-text-color-regular);
	background-color: #f56c6c;
}
.color1:hover {
	scale: 1.1;
	cursor: pointer;
	color: #d7d7d7;
}
.color2 {
	color: var(--el-text-color-regular);
	background-color: #20b2aa;
}
.color3 {
	color: var(--el-text-color-regular);
	background-color: transparent;
}
.color4 {
	color: var(--el-text-color-regular);
	background-color: transparent;
}
.color5 {
	color: var(--el-text-color-regular);
	background-color: transparent;
}

:deep(.el-card) {
	width: 100%;
	margin-bottom: 10px;
}
:deep(.el-card__header) {
	padding: 5px 10px;
	width: 100%;
}
:deep(.el-card__header)::before {
	content: "▲";
	padding-right: 8px; /* 添加一些右侧间距，使符号与文本有适当的间距 */
	font-size: 16px; /* 可以调整符号大小 */
	color: var(--el-text-color-secondary);
}

:deep(.el-card__body) {
	padding: 10px 0px 0 10px;
}
.search-content {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	box-sizing: border-box;
	overflow: hidden;
}
.navbar-content {
	margin-top: 0rem;
	height: calc(100vh - 303px);
	overflow: auto;
	.demo-tabs {
		height: 100%;
	}
}
.analyze-content {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	justify-content: space-between;
	height: calc(100vh - 310px);
	overflow: auto;
	.analyze-item {
		flex: 1;
		cursor: pointer;
		.analyze-card {
			display: flex;
			.top-card {
				display: flex;
				padding: 0.5rem 0;
				.size-text {
					color: #fb191c;
					font-weight: bold;
					display: flex;
					margin-left: -0.5rem;
					justify-content: flex-end;
					align-items: flex-end;
				}
				.title-bold {
					font-weight: bold;
					font-size: 1rem;
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}
			.card-info {
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				align-items: center;
				padding-bottom: 0.5rem;
				.card-info__title {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 0.75rem;
					font-weight: bold;
					color: var(--el-text-color-regular);
					span {
						padding: 0.1rem 0.3rem;
						color: var(--el-text-color-regular);
					}
				}
			}
			.card-content {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0.2rem;
				border-top: 0.2px dashed var(--el-border-color);
				border-bottom: 0.2px dashed var(--el-border-color);
				border-left: 0.2px dashed var(--el-border-color);
				border-right: 0.2px dashed var(--el-border-color);
				.card-info__content-text {
					font-size: 0.75rem;
					color: #909399;
				}
				.card-info__content {
					display: flex;
					flex-wrap: wrap;
					justify-content: space-between;
					align-items: center;
					color: #909399;
					span {
						padding: 0.1rem 0 0.1rem 0.3rem;
						color: #909399;
						font-size: 0.75rem;
						text-align: center;
					}
				}
			}
			.card-num {
				display: flex;
				justify-content: space-around;
				align-items: center;
				padding: 0;
				border-top: 0.2px dashed var(--el-border-color);
				border-bottom: 0.2px dashed var(--el-border-color);
				border-left: 0.2px dashed var(--el-border-color);
				border-right: 0.2px dashed var(--el-border-color);
				.card-num__content-left {
					display: flex;
					flex-wrap: wrap;
					justify-content: space-between;
					align-items: center;
					h5 {
						font-size: 0.65rem;
						padding: 0.3rem 0;
						margin: 0;
						color: #909399;
					}
				}
				.card-num__content-right {
					display: flex;
					flex-wrap: wrap;
					justify-content: space-between;
					align-items: center;
					.card-num__content-text {
						font-size: 0.75rem;
						color: #909399;
					}
					.card-num__content {
						display: flex;
						flex-wrap: wrap;
						justify-content: space-between;
						align-items: center;
						font-size: 0.85rem;
						color: #909399;
						span {
							padding: 0rem 0rem;
							color: #909399;
							font-size: 0.75rem;
						}
					}
				}
			}
		}
	}
	.analyze-item:nth-of-type(even) {
		margin-top: 5rem;
	}

	.analyze-item:hover {
		.analyze-card {
			scale: 1.02;
			border: 0;
		}
	}
	// 自定义表头样式
	.custom-header {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		gap: 20px; // 水平间距
		padding: 0.5rem 0;
		.size-info {
			flex: 1;
			font-weight: bold;
			font-size: 18px;
			margin-left: 2.5rem;
		}
		.size-info-value {
			font-size: 18px;
			font-weight: bold;
			color: #ff0000;
			margin-left: 5rem;
		}
		.process-info {
			flex: 2;
			color: var(--el-text-color-secondary); // 改为白色，在深色背景下更清晰
			font-weight: bold;
			font-size: 18px;
		}
	}
}
</style>
