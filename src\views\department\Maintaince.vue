<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-07-24 11:14:07
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-04 11:03:01
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\department\Maintaince.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
       <el-card>
            <el-form :inline="true" :model="form" class="demo-form-inline">
                <el-form-item label="保全工号">
                    <el-input v-model="form.name" placeholder="保全工号"></el-input>
                </el-form-item>
                <el-form-item label="设备编号">
                    <el-input v-model="form.number" placeholder="设备编号"></el-input>
                </el-form-item>
                <el-form-item label="区域">
                    <!-- 区域选择 -->
                    <el-select v-model="form.area" placeholder="请选择区域">
                        <el-option label="1#织造横机区域" value="1#织造横机区域"></el-option>
                        <el-option label="2#织造横机区域" value="2#织造横机区域"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="部门">
                    <!-- 区域选择 -->
                    <el-select v-model="form.depart" placeholder="请选择部门">
                        <el-option label="织造车间" value="织造车间"></el-option>
                        <el-option label="整理车间" value="整理车间"></el-option>
                        <el-option label="打样车间" value="打样车间"></el-option>
                        <el-option label="综合维修" value="综合维修"></el-option>
                    </el-select>
                </el-form-item>
                <!-- 月份选择 -->
                <el-form-item label="日期">
                    <el-date-picker
                        v-model="form.date"
                        type="month"
                        placeholder="选择月"
                        value-format="yyyy-MM"
                        style="width: 100%;"
                    ></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSubmit" icon="el-icon-search">查询</el-button>
                </el-form-item>
            </el-form>
       </el-card>
    </div>
</template>
<script>
export default {
    data() {
        return {
            form: {
                name: '',
                number: '',
                area: '',
                depart: '',
                date: ''
            }
        }
    },
    methods: {
        onSubmit() {
            console.log('submit!');
        }
    }
}
</script>