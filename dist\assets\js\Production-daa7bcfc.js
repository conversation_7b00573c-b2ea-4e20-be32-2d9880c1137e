import{c as le,r as me,o as s,a as f,w as l,b as e,d as n,t,e as te,f as c,g as H,F as ee,h as ae,u as Re,i as u,j as Ge,k as Ne,l as Be,E as de,m as $,n as G,s as Ae,p as pe,q as Fe,v as Le,x as Me,y as Oe,z as He,A as Je}from"./index-444b28c3.js";/* empty css                   */import{E as fe,a as Ke}from"./el-col-bd5e5418.js";import{E as Qe}from"./el-dialog-e35c112f.js";import"./el-overlay-9f4b42b1.js";import{E as ce,a as be}from"./el-descriptions-item-c5351b3c.js";import{E as We}from"./el-pagination-6fc73be7.js";import{E as Xe}from"./el-input-6b488ec7.js";import{E as ge}from"./el-tag-29cbefd8.js";import{E as Ye,a as Ze}from"./el-select-980e5896.js";import"./el-scrollbar-af6196f4.js";import{E as xe,a as ea}from"./el-table-column-fa1764a8.js";import"./el-checkbox-f3df62fa.js";import"./el-tooltip-4ed993c7.js";import{E as aa}from"./el-card-6f02be36.js";import{E as la,a as ta}from"./el-form-10dec954.js";import{E as oa}from"./el-button-9bbdfcf9.js";import{E as ra}from"./el-date-picker-0feb0d8c.js";import"./el-form-item-4ed993c7.js";import{E as na}from"./el-drawer-12f56ca7.js";import{E as ia,a as ua}from"./el-step-62a69499.js";import{E as sa}from"./el-divider-d33f4e37.js";import{_ as da}from"./_plugin-vue_export-helper-c27b6911.js";import{v as pa}from"./directive-ce1b251f.js";import"./index-eba6e623.js";import"./event-fe80fd0c.js";import"./scroll-a66dde9b.js";import"./vnode-b9ec7db4.js";import"./index-e305bb62.js";import"./focus-trap-6de7266c.js";import"./index-11a84590.js";import"./index-4d7f16ce.js";import"./validator-e4131fc3.js";import"./_Uint8Array-55276dff.js";import"./index-f312e047.js";const ma={__name:"Drawer",props:{modelValue:{type:Boolean,default:!1},upperInfo:{type:Object,default:()=>({})}},emits:["update:modelValue"],setup(o,{emit:N}){const _=o,B=N,E=le({get:()=>_.modelValue,set:i=>{B("update:modelValue",i)}}),D=()=>{B("update:modelValue",!1)};return(i,V)=>{const g=sa,p=ce,h=ge,I=be,w=fe,z=ia,J=ua,K=na,S=me("copy");return s(),f(K,{modelValue:E.value,"onUpdate:modelValue":V[0]||(V[0]=v=>E.value=v),"show-close":!0,size:"50%","close-on-click-modal":!0,onClose:D},{default:l(()=>[e(w,{xs:24,sm:24,md:24,lg:24},{default:l(()=>[e(g,{"content-position":"left"},{default:l(()=>[n(t(i.$t("order.jibenxinxi")),1)]),_:1}),e(I,{title:"",column:3},{default:l(()=>[e(p,{label:"Model"},{default:l(()=>[n(t(o.upperInfo.model),1)]),_:1}),e(p,{label:i.$t("order.pinming")},{default:l(()=>[n(t(o.upperInfo.pm),1)]),_:1},8,["label"]),e(p,{label:i.$t("order.chima")},{default:l(()=>[n(t(o.upperInfo.size),1)]),_:1},8,["label"]),e(p,{label:i.$t("order.jijie")},{default:l(()=>[n(t(o.upperInfo.season),1)]),_:1},8,["label"]),e(p,{label:i.$t("order.kuanhao")},{default:l(()=>[n(t(o.upperInfo.style_number),1)]),_:1},8,["label"]),e(p,{label:i.$t("order.peise")},{default:l(()=>[n(t(o.upperInfo.color),1)]),_:1},8,["label"]),e(p,{label:i.$t("order.bupiaohao")},{default:l(()=>[n(t(o.upperInfo.serial),1)]),_:1},8,["label"]),te((s(),f(p,{label:i.$t("order.chanpinbianhao")},{default:l(()=>[n(t(o.upperInfo.qrcode),1)]),_:1},8,["label"])),[[S,o.upperInfo.qrcode]]),e(p,{label:i.$t("order.jitaihao")},{default:l(()=>[n(t(o.upperInfo.ch),1)]),_:1},8,["label"]),e(p,{label:i.$t("order.banbenhao")},{default:l(()=>[n(t(o.upperInfo.version),1)]),_:1},8,["label"]),e(p,{label:i.$t("order.xiaobanbenhao")},{default:l(()=>[n(t(o.upperInfo.version_serial),1)]),_:1},8,["label"]),e(p,{label:i.$t("order.leixing")},{default:l(()=>[e(h,{size:"small"},{default:l(()=>[n(t(o.upperInfo.type),1)]),_:1})]),_:1},8,["label"]),e(p,{label:i.$t("order.chengxumulu")},{default:l(()=>[n(t(o.upperInfo.version_thumb),1)]),_:1},8,["label"]),e(p,{label:i.$t("inspection.jiajubanben")},{default:l(()=>[n(t(o.upperInfo.jig_version),1)]),_:1},8,["label"]),e(p,{label:i.$t("order.status")},{default:l(()=>[e(h,{size:"small",type:o.upperInfo.status<3?"warning":"success"},{default:l(()=>[n(t(o.upperInfo.status===0?i.$t("order.daimaopi"):o.upperInfo.status===1?i.$t("order.daidingxing"):o.upperInfo.status===2?i.$t("order.daiguangjian"):o.upperInfo.complete_time&&o.upperInfo.status===3?i.$t("order.yiwancheng"):i.$t("order.yiguangpeidaitiaoji")),1)]),_:1},8,["type"])]),_:1},8,["label"]),e(p,{label:i.$t("order.shifoutiaoji")},{default:l(()=>[e(h,{size:"small",type:o.upperInfo.is_receive===0?"success":"warning"},{default:l(()=>[n(t(o.upperInfo.is_receive===0?i.$t("order.no"):i.$t("order.yes")),1)]),_:1},8,["type"])]),_:1},8,["label"]),o.upperInfo.is_receive===1?(s(),f(p,{key:0,label:i.$t("order.tiaojibaoquan")},{default:l(()=>[e(h,{size:"small",type:"info"},{default:l(()=>[n(t(o.upperInfo.changes.repair_user),1)]),_:1})]),_:1},8,["label"])):c("",!0),o.upperInfo.is_receive===1&&o.upperInfo.changes.complete_time?(s(),f(p,{key:1,label:i.$t("order.tiaojishijian")},{default:l(()=>[e(h,{size:"small",type:"info"},{default:l(()=>[n(t(o.upperInfo.changes.complete_time),1)]),_:1})]),_:1},8,["label"])):c("",!0)]),_:1})]),_:1}),e(w,{xs:24,sm:24,md:24,lg:24,style:{padding:"1rem"}},{default:l(()=>[e(g,{"content-position":"left"},{default:l(()=>[n(t(i.$t("order.jinduliucheng")),1)]),_:1}),e(J,{active:o.upperInfo.steam_measure_time?5:o.upperInfo.dxsj?4:o.upperInfo.pre_inspection_time?3:o.upperInfo.xjsj?2:1},{default:l(()=>[o.upperInfo.xjsj?(s(),f(z,{key:0,title:i.$t("order.xiaji"),description:o.upperInfo.xjsj},null,8,["title","description"])):c("",!0),o.upperInfo.pre_inspection_time?(s(),f(z,{key:1,title:i.$t("order.maopi"),description:o.upperInfo.pre_inspection_time},null,8,["title","description"])):c("",!0),o.upperInfo.dxsj?(s(),f(z,{key:2,title:i.$t("order.dingxing"),description:o.upperInfo.dxsj},null,8,["title","description"])):c("",!0),o.upperInfo.steam_measure_time?(s(),f(z,{key:3,title:i.$t("order.guangpi"),description:o.upperInfo.steam_measure_time},null,8,["title","description"])):c("",!0),o.upperInfo.changes&&o.upperInfo.changes.complete_time?(s(),f(z,{key:4,title:i.$t("order.wancheng"),description:o.upperInfo.changes.complete_time},null,8,["title","description"])):c("",!0),!o.upperInfo.changes&&o.upperInfo.measureGps.createTime?(s(),f(z,{key:5,title:i.$t("order.wancheng"),description:o.upperInfo.measureGps.createTime},null,8,["title","description"])):c("",!0)]),_:1},8,["active"])]),_:1}),o.upperInfo.measureDetails.measure_params?(s(),f(w,{key:0,xs:24,sm:24,md:24,lg:24,style:{padding:"1rem"}},{default:l(()=>[e(g,{"content-position":"left"},{default:l(()=>[n(t(i.$t("order.maopiceliangcanshu")),1)]),_:1}),e(I,{title:"",border:""},{default:l(()=>[(s(!0),H(ee,null,ae(o.upperInfo.measureDetails.measure_params,(v,y)=>(s(),f(p,{key:y,label:v.name},{default:l(()=>[n(t(v.value),1)]),_:2},1032,["label"]))),128))]),_:1})]),_:1})):c("",!0),o.upperInfo.measureGps.measure_params?(s(),f(w,{key:1,xs:24,sm:24,md:24,lg:24,style:{padding:"1rem"}},{default:l(()=>[e(g,{"content-position":"left"},{default:l(()=>[n(t(i.$t("order.guangpiceliangcanshu")),1)]),_:1}),e(I,{title:"",border:""},{default:l(()=>[(s(!0),H(ee,null,ae(o.upperInfo.measureGps.measure_params,(v,y)=>(s(),f(p,{key:y,label:v.name},{default:l(()=>[n(t(v.num),1)]),_:2},1032,["label"]))),128))]),_:1})]),_:1})):c("",!0),o.upperInfo.measureGps.change_params&&o.upperInfo.measureGps.change_params.length>0?(s(),f(w,{key:2,xs:24,sm:24,md:24,lg:24},{default:l(()=>[e(g,{"content-position":"left"},{default:l(()=>[n(t(i.$t("order.tiaojicanshu")),1)]),_:1}),e(I,{title:"",border:""},{default:l(()=>[(s(!0),H(ee,null,ae(o.upperInfo.measureGps.change_params,(v,y)=>(s(),f(p,{key:y,label:v.name},{default:l(()=>[n(t(v.num),1)]),_:2},1032,["label"]))),128))]),_:1})]),_:1})):c("",!0)]),_:1},8,["modelValue"])}}};const fa=o=>(He("data-v-aea89789"),o=o(),Je(),o),ca=fa(()=>$("span",{class:"text-gray-500"},"-",-1)),ba={class:"button-container"},ga={key:0,style:{"font-size":"0.85rem"}},va={id:"printArea",class:"print-content"},ha={class:"print-footer"},ya={class:"dialog-footer"},$a={__name:"Production",setup(o){const{t:N}=Re(),_=u(!1),B=u(null),E=u(null),D=u(null),i=u(null),V=u(null),g=u(!0),p=u(N("order.zanwushuju")),h=u(1),I=u(10),w=u(0),z=u("default"),J=u(!1),K=u(!1),S=Ge([]),v=a=>![1,2].includes(a.id),y=u(),Q=u(0),W=u(0),X=u(0),Y=u(0),ve=u([]),M=u(),he=u(null),O=u(!1),oe=u(""),Z=u([]),A=u(),F=u(),L=u(!1),b=u({}),re=le(()=>b.value.np_params||"--"),ne=le(()=>b.value.td_params||"--");Ne(()=>{U()});const ye=a=>{var R,j,q;ve.value.some(x=>x.taskId===a.taskId)?(R=y.value)==null||R.toggleRowSelection(a,!1):((j=y.value)==null||j.clearSelection(),(q=y.value)==null||q.toggleRowSelection(a,!0),M.value=a.qrcode,Z.value=a,O.value=!0,oe.value=`${N("order.xiemian")} `+a.qrcode+` ${N("order.xiangqing")}`)},$e=()=>{if(!i.value&&!V.value&&!E.value&&!D.value&&!A.value&&!F.value)return!1;g.value=!0,h.value=1,U()},we=()=>{var a;O.value=!1,(a=y.value)==null||a.clearSelection()},_e=()=>{i.value="",V.value="",E.value="",D.value="",A.value="",F.value="",U()},Ie=()=>{g.value=!0,U()},je=a=>{g.value=!0,I.value=a,U()},ke=a=>{g.value=!0,h.value=a,U()},Ee=a=>{b.value=a,L.value=!0},ie=()=>{L.value=!1,b.value={}},Ve={id:"printArea",popTitle:"产品信息打印",beforeOpenCallback:()=>{},openCallback:()=>{},closeCallback:()=>{L.value=!1,de({title:"系统提示",message:"打印完成，请去打印机查看",type:"success"})}},U=async()=>{try{g.value=!0;const a=await Be({keywords:i.value,page:h.value,pageSize:I.value,status:V.value,startDate:E.value,endDate:D.value,styleno:A.value,color:F.value});if(a.code!==200)return de({title:"",message:a.msg||"获取数据失败",type:"error",duration:2e3});a.data&&Array.isArray(a.data.list)?(S.splice(0,S.length,...a.data.list),w.value=a.data.total||0,Q.value=a.data.countStatus0||0,W.value=a.data.countStatus1||0,X.value=a.data.countStatus2||0,Y.value=a.data.countStatus3||0):(S.splice(0,S.length),w.value=0,Q.value=0,W.value=0,X.value=0,Y.value=0)}catch(a){}finally{setTimeout(()=>{g.value=!1},500)}};return(a,m)=>{const R=Xe,j=la,q=Ye,x=Ze,ue=ra,C=fe,T=oa,ze=ta,se=aa,d=xe,P=ge,Ce=ea,De=We,k=ce,Se=be,qe=Qe,Te=Ke,Pe=pa,Ue=me("print");return s(),f(Te,{gutter:0},{default:l(()=>[e(C,{xs:24,sm:24,md:24,lg:24},{default:l(()=>[e(se,{shadow:"never",header:a.$t("order.conditiontitle")},{default:l(()=>[e(ze,{model:B.value,ref_key:"form",ref:B,"label-width":"auto",inline:!0,class:"demo-form-inline"},{default:l(()=>[e(j,{label:a.$t("order.lebel")},{default:l(()=>[e(R,{modelValue:i.value,"onUpdate:modelValue":m[0]||(m[0]=r=>i.value=r),placeholder:a.$t("order.searchCondition")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),e(j,{label:a.$t("order.status")},{default:l(()=>[e(x,{modelValue:V.value,"onUpdate:modelValue":m[1]||(m[1]=r=>V.value=r),placeholder:a.$t("order.chooseStatus")},{default:l(()=>[e(q,{label:a.$t("order.daimaopi"),value:"0"},null,8,["label"]),e(q,{label:a.$t("order.daidingxing"),value:"1"},null,8,["label"]),e(q,{label:a.$t("order.daiguangjian"),value:"2"},null,8,["label"]),e(q,{label:a.$t("order.yiwancheng"),value:"3"},null,8,["label"])]),_:1},8,["modelValue","placeholder"])]),_:1},8,["label"]),e(j,{label:a.$t("order.kuanhao")},{default:l(()=>[e(R,{modelValue:A.value,"onUpdate:modelValue":m[2]||(m[2]=r=>A.value=r),placeholder:a.$t("order.shurukuanhao"),clearable:""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),e(j,{label:a.$t("order.peise")},{default:l(()=>[e(R,{modelValue:F.value,"onUpdate:modelValue":m[3]||(m[3]=r=>F.value=r),placeholder:a.$t("order.shurupeise"),clearable:""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),e(j,{label:a.$t("order.riqi")},{default:l(()=>[e(C,{span:10},{default:l(()=>[e(ue,{modelValue:E.value,"onUpdate:modelValue":m[4]||(m[4]=r=>E.value=r),type:"date",placeholder:a.$t("order.kaishiriqi"),style:{width:"100%"}},null,8,["modelValue","placeholder"])]),_:1}),e(C,{span:1,class:"text-center"},{default:l(()=>[ca]),_:1}),e(C,{span:10},{default:l(()=>[e(ue,{modelValue:D.value,"onUpdate:modelValue":m[5]||(m[5]=r=>D.value=r),type:"date",placeholder:a.$t("order.jieshuriqi"),style:{width:"100%"}},null,8,["modelValue","placeholder"])]),_:1})]),_:1},8,["label"]),e(j,{class:"button-group"},{default:l(()=>[$("div",ba,[_.value?(s(),f(T,{key:0,type:"primary",icon:G(Ae),onClick:$e,size:"default"},{default:l(()=>[n(t(a.$t("order.searchbtn")),1)]),_:1},8,["icon"])):c("",!0),_.value?(s(),f(T,{key:1,type:"info",plain:"",icon:G(pe),onClick:_e,size:"default"},{default:l(()=>[n(t(a.$t("order.resetbtn")),1)]),_:1},8,["icon"])):c("",!0),_.value?(s(),f(T,{key:2,type:"primary",plain:"",icon:G(pe),onClick:Ie,size:"default"},{default:l(()=>[n(t(a.$t("poststeam.shuaxin")),1)]),_:1},8,["icon"])):c("",!0),e(T,{type:"text",plain:"",icon:_.value?G(Fe):G(Le),onClick:m[6]||(m[6]=r=>_.value=!_.value),size:"default"},{default:l(()=>[n(t(_.value?a.$t("order.close"):a.$t("order.open")),1)]),_:1},8,["icon"])])]),_:1})]),_:1},8,["model"])]),_:1},8,["header"])]),_:1}),e(C,{xs:24,sm:24,md:24,lg:24,style:{"margin-top":"0.5rem"}},{default:l(()=>[e(se,{shadow:"always",header:a.$t("order.dingdanliebiao")},{default:l(()=>[e(C,{span:24,class:"analyse-title"},{default:l(()=>[e(C,{span:5,style:{display:"flex","align-items":"center"}},{default:l(()=>[M.value?(s(),H("span",ga,t(a.$t("order.dangqianxuanzhong"))+": "+t(M.value?M.value:a.$t("order.wu")),1)):c("",!0)]),_:1}),e(C,{span:19,class:"analyse-content"},{default:l(()=>[$("span",null,t(a.$t("order.chaxunjieguotongji"))+"："+t(w.value)+" "+t(a.$t("order.tiao")),1),$("span",null,t(a.$t("order.daimaopi"))+"："+t(Q.value)+" "+t(a.$t("order.tiao")),1),$("span",null,t(a.$t("order.daidingxing"))+"："+t(W.value)+" "+t(a.$t("order.tiao")),1),$("span",null,t(a.$t("order.daiguangjian"))+"："+t(X.value)+" "+t(a.$t("order.tiao")),1),$("span",null,t(a.$t("order.yiwancheng"))+"："+t(Y.value)+" "+t(a.$t("order.tiao")),1)]),_:1})]),_:1}),te((s(),f(Ce,{ref_key:"multipleTableRef",ref:y,data:S,stripe:"",style:{width:"100%"},"element-loading-text":"Flyknit...","empty-text":p.value,"highlight-current-row":"",onRowClick:ye},{default:l(()=>[e(d,{type:"selection",selectable:v,width:"55"}),e(d,{prop:"qrcode",fixed:"",label:a.$t("order.chanpinbianhao"),width:"140"},null,8,["label"]),e(d,{prop:"serial",label:a.$t("order.bupiaohao"),width:"300"},null,8,["label"]),e(d,{prop:"model",fixed:"",label:"Model",width:"170"}),e(d,{prop:"ch",label:a.$t("order.jitaihao"),width:"150"},null,8,["label"]),e(d,{prop:"pm",label:a.$t("order.pinming"),width:"150"},null,8,["label"]),e(d,{prop:"size",label:a.$t("order.chima"),width:"60"},null,8,["label"]),e(d,{prop:"season",label:a.$t("order.jijie")},null,8,["label"]),e(d,{prop:"style_number",label:a.$t("order.kuanhao"),width:"120"},null,8,["label"]),e(d,{prop:"color",label:a.$t("order.peise")},null,8,["label"]),e(d,{prop:"type",label:a.$t("order.leixing"),width:"110"},null,8,["label"]),e(d,{prop:"version",label:a.$t("order.banbenhao"),width:"140"},null,8,["label"]),e(d,{prop:"version_serial",label:a.$t("order.xiaobanbenhao"),width:"120"},null,8,["label"]),e(d,{label:"NP",prop:"np_params",width:"180"},{default:l(r=>[n(t(r.row.np_params||"-"),1)]),_:1}),e(d,{label:"TD",prop:"td_params",width:"180"},{default:l(r=>[n(t(r.row.td_params||"-"),1)]),_:1}),e(d,{prop:"status",label:a.$t("order.dingdanzhuangtai"),width:"120"},{default:l(r=>[e(P,{type:r.row.status===3?"success":r.row.status===0?"warning":r.row.status===1?"danger":"warning"},{default:l(()=>[n(t(r.row.status===0?a.$t("order.daimaopi"):r.row.status===1?a.$t("order.daidingxing"):r.row.status===2?a.$t("order.daiguangjian"):a.$t("order.yiwancheng")),1)]),_:2},1032,["type"])]),_:1},8,["label"]),e(d,{prop:"status",label:a.$t("order.tiaojizhuangtai"),width:"140"},{default:l(r=>[e(P,{type:r.row.is_receive===1&&r.row.complete_time?"success":"warning"},{default:l(()=>[n(t(r.row.is_receive===1&&r.row.complete_time?a.$t("order.yitiaoji"):a.$t("order.daitiaoji")),1)]),_:2},1032,["type"])]),_:1},8,["label"]),e(d,{prop:"type",label:a.$t("order.dingdanleixing"),width:"120"},{default:l(r=>[e(P,{type:r.row.pro_type===0?"success":"danger"},{default:l(()=>[n(t(r.row.pro_type===0?a.$t("order.morenbianzhi"):a.$t("order.baoquantiaoji")),1)]),_:2},1032,["type"])]),_:1},8,["label"]),e(d,{prop:"xjsj",label:a.$t("order.xiajishijian"),"default-sort":{prop:"xjsj",order:"descending"},sortable:"",width:"200"},null,8,["label"]),e(d,{prop:"dxsj",label:a.$t("order.dingxingshijian"),"default-sort":{prop:"dxsj",order:"descending"},sortable:"",width:"200"},{default:l(r=>[e(P,{type:r.row.dxsj?"success":"warning"},{default:l(()=>[n(t(r.row.dxsj?r.row.dxsj:a.$t("order.zanweidingxing")),1)]),_:2},1032,["type"])]),_:1},8,["label"]),e(d,{prop:"pre_inspection_time",label:a.$t("order.maopiceliangshijian"),"default-sort":{prop:"pre_inspection_time",order:"descending"},sortable:"",width:"200"},{default:l(r=>[e(P,{type:r.row.pre_inspection_time?"success":"warning"},{default:l(()=>[n(t(r.row.pre_inspection_time?r.row.pre_inspection_time:a.$t("order.zanweidingxing")),1)]),_:2},1032,["type"])]),_:1},8,["label"]),e(d,{prop:"steam_measure_time",label:a.$t("order.guangpiceliangshijian"),"default-sort":{prop:"steam_measure_time",order:"descending"},sortable:"",width:"200"},{default:l(r=>[e(P,{type:r.row.steam_measure_time?"success":"warning"},{default:l(()=>[n(t(r.row.steam_measure_time?r.row.steam_measure_time:a.$t("order.zanweiceliang")),1)]),_:2},1032,["type"])]),_:1},8,["label"]),e(d,{prop:"complete_time",label:a.$t("order.tiaojishijian"),"default-sort":{prop:"complete_time",order:"descending"},sortable:"",width:"200"},{default:l(r=>[e(P,{type:r.row.complete_time?"success":"warning"},{default:l(()=>[n(t(r.row.complete_time?r.row.complete_time:a.$t("order.zanweitiaoji")),1)]),_:2},1032,["type"])]),_:1},8,["label"]),e(d,{label:a.$t("order.dayin"),width:"100",align:"center",fixed:"right"},{default:l(r=>[e(T,{type:"primary",size:"small",icon:G(Me),onClick:Oe(wa=>Ee(r.row),["stop"])},{default:l(()=>[n(t(a.$t("order.dayin")),1)]),_:2},1032,["icon","onClick"])]),_:1},8,["label"])]),_:1},8,["data","empty-text"])),[[Pe,g.value]]),e(De,{style:{"margin-top":"1rem"},"current-page":h.value,"onUpdate:currentPage":m[7]||(m[7]=r=>h.value=r),"page-size":I.value,"onUpdate:pageSize":m[8]||(m[8]=r=>I.value=r),"page-sizes":[14,16,18,20],size:z.value,disabled:J.value,background:K.value,layout:"total, sizes, prev, pager, next, jumper",total:w.value,onSizeChange:je,onCurrentChange:ke},null,8,["current-page","page-size","size","disabled","background","total"])]),_:1},8,["header"])]),_:1}),Z.value?(s(),f(ma,{key:0,ref_key:"drawerRef",ref:he,modelValue:O.value,"onUpdate:modelValue":[m[9]||(m[9]=r=>O.value=r),we],upperInfo:Z.value,title:oe.value},null,8,["modelValue","upperInfo","title"])):c("",!0),e(qe,{modelValue:L.value,"onUpdate:modelValue":m[10]||(m[10]=r=>L.value=r),title:`鞋面-${b.value.qrcode}-信息打印单`,"before-close":ie},{footer:l(()=>[$("span",ya,[e(T,{onClick:ie},{default:l(()=>[n("关闭")]),_:1}),te((s(),f(T,{type:"primary"},{default:l(()=>[n("立即打印")]),_:1})),[[Ue,Ve]])])]),default:l(()=>[$("div",va,[e(Se,{title:`Model: ${b.value.model} ${b.value.size}`,border:"",column:2},{default:l(()=>[e(k,{label:"machine #",span:1},{default:l(()=>[n(t(b.value.ch||"-"),1)]),_:1}),e(k,{label:"upper #",span:1},{default:l(()=>[n(t(b.value.pm||"-"),1)]),_:1}),e(k,{label:"Development Gate",span:1},{default:l(()=>[n(t(b.value.type||"-"),1)]),_:1}),e(k,{label:"Pro version",span:1},{default:l(()=>[n(t(b.value.version||"-"),1)]),_:1}),e(k,{label:"Date and time knitted",span:1},{default:l(()=>[n(t(b.value.xjsj||"-"),1)]),_:1}),e(k,{label:"Date and time steamed",span:1},{default:l(()=>[n(t(b.value.dxsj||"-"),1)]),_:1}),e(k,{label:"Jig version",span:3},{default:l(()=>[n(t(b.value.jig_version||"-"),1)]),_:1}),e(k,{label:"NP",rowspan:2,span:4,align:"center"},{default:l(()=>[n(t(re.value?re.value:"-"),1)]),_:1}),e(k,{label:"TD",rowspan:2,span:4,align:"center"},{default:l(()=>[n(t(ne.value?ne.value:"-"),1)]),_:1})]),_:1},8,["title"]),$("div",ha,[$("p",null,"打印时间："+t(new Date().toLocaleString()),1)])])]),_:1},8,["modelValue","title"])]),_:1})}}},tl=da($a,[["__scopeId","data-v-aea89789"]]);export{tl as default};
