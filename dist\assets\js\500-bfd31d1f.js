import{bn as c,o as r,g as p,m as e,b as i,w as d,n,C as l,d as u,bv as m,z as f,A as h}from"./index-444b28c3.js";import{E as v}from"./el-button-9bbdfcf9.js";import{_ as g}from"./_plugin-vue_export-helper-c27b6911.js";import"./index-4d7f16ce.js";const x=""+new URL("../png/500-1a6c7c8f.png",import.meta.url).href;const o=t=>(f("data-v-4caf4585"),t=t(),h(),t),b={class:"not-container"},w=o(()=>e("img",{src:x,class:"not-img",alt:"500"},null,-1)),y={class:"not-detail"},B=o(()=>e("h2",null,"500",-1)),C=o(()=>e("h4",null,"抱歉，您的网络不见了~🤦‍♂️🤦‍♀️",-1)),E=l({name:"500"}),I=Object.assign(E,{setup(t){const a=c();return(k,s)=>{const _=v;return r(),p("div",b,[w,e("div",y,[B,C,i(_,{type:"primary",onClick:s[0]||(s[0]=N=>n(a).push(n(m)))},{default:d(()=>[u("返回首页")]),_:1})])])}}}),O=g(I,[["__scopeId","data-v-4caf4585"]]);export{O as default};
