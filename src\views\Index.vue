<template>
  <el-container>
    <!-- 头部高度设为50px（默认60px） -->
    <el-header height="60px">
      <!-- logo -->
      <div class="header-logos">
        <el-image style="width: 55px; height: 55px" :src="url" :fit="fit"></el-image>
        <a class="logo" href="/">Flyknit设备管家</a>
      </div>
      <!-- 折叠菜单按钮 -->
      <div class="toggle" @click="changeHide">
        <i class="el-icon-s-unfold" v-if="isCollapse"></i>
        <i class="el-icon-s-fold" v-if="!isCollapse"></i>
      </div>
      <!-- 通知消息 -->
      <el-col :span="24" class="title-bar">
        <!-- <div class="header-message">
          <el-dropdown placement="bottom-start" :hide-on-click="false">
            <el-button type="">
              <i
                class="el-icon-message-solid"
                style="color: #fff; font-size: 20px"
              ></i>
              <el-badge :value="5" class="item">
                <span class="info">通知</span>
              </el-badge>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in 5" :key="item">
                <div class="message-item">
                  <i
                    class="el-icon-s-comment"
                    style="color: #f7863b; font-size: 16px"
                  ></i>
                  <span class="message-detail"
                    >B班已完成【机台跟踪】的交班事项</span
                  >
                  <span class="time-text">12:45:32</span>
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div> -->
        <!-- 退出菜单 -->
        <div class="logout-box">
          <span class="el-dropdown-link">
            <div style="margin-right: 20px;">
              <span class="el-icon-full-screen" style="color:#fff;font-size:24px;cursor:pointer;"
                @click="fullScreen"></span>
            </div>
            <img :src="userInfo.avatar" style="width: 40px;height:40px;border-radius:50%;">
            <el-dropdown trigger="hover">
              <span class="user-text">
                欢迎您，{{ userInfo.username }}
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="logout">
                  <i class="el-icon-switch-button"></i>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </span>
        </div>
      </el-col>
    </el-header>
    <el-container>
      <!-- 菜单栏宽度设为自动 -->
      <el-aside :width="isCollapse ? '64px' : '250px'">
        <el-menu :default-active="indexs" class="el-menu-vertical" background-color="#feffff" text-color="#000"
          active-text-color="#007EF7" unique-opened :collapse="isCollapse" :default-openeds="openeds">
          <div v-for="(item, index) in menuList" :key="index">
            <el-submenu :index="item.id">
              <template slot="title">
                <i :class="item.icon"></i>
                <span @click="initOpend">{{ item.title }}</span>
              </template>
              <template v-if="item.Subclass.length > 0">
                <el-menu-item-group v-for="(item1, index1) in item.Subclass" :key="index1">
                  <router-link :to="{ path: item1.router, query: { link: item1.router, pid: item1.id } }" class="texts">
                    <el-menu-item :index="item1.id" @click="
                      getLink(
                        item1.id,
                        item1.title,
                        item1.router,
                        item.id,
                        item
                      )
                      "><i :class="item1.icon"></i>{{ item1.title }}</el-menu-item>
                  </router-link>
                </el-menu-item-group>
              </template>
            </el-submenu>
          </div>
        </el-menu>
      </el-aside>
      <!-- 可以结合vue-router路由嵌套实现页面的跳转与显示 -->
      <el-main :style="{ 'margin-top': tabList.length >= 16 ? marginTop : '60px' }">
        <FTagList :tabList="tabList" :activeItem="activeItem" @changeTabs="changeTabs" @handleTabsEdit="handleTabsEdit"
          @clearAll="initOpend"></FTagList>
        <Main></Main>
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
import Main from "@/views/components/Main.vue";
import FTagList from "@/views/components/FTagList.vue";
import { mapState } from "vuex";
export default {
  components: {
    Main,
    FTagList,
  },
  data() {
    return {
      activeItem: 0,// 传递给tagList组件的当前激活项
      indexs: "0",
      isCollapse: false,
      url: "../image/app_logo.png",
      fit: "fill",
      openeds: ["0"],
      uniqueOpened: true,
      circleUrl:
        "https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fb-ssl.duitang.com%2Fuploads%2Fitem%2F201602%2F22%2F20160222210947_stQjR.jpeg&refer=http%3A%2F%2Fb-ssl.duitang.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1671786282&t=2b55938ce1451e0b9f857819b3001e29",
      menuList: [
        {
          id: "1",
          title: "控制台",
          router: "console",
          icon: "el-icon-s-home",
          Subclass: [
            {
              id: "1",
              title: "控制台",
              router: "console",
              icon: "el-icon-help",
            },
          ],
        },
        {
          id: "2",
          title: "设备管理",
          router: "device",
          icon: "el-icon-platform-eleme",
          Subclass: [
            {
              id: "1",
              title: "设备列表",
              router: "deviceList",
              icon: "el-icon-coin",
            },
            {
              id: "2",
              title: "设备调拨",
              router: "transferRecord",
              icon: "el-icon-sort",
            },
            {
              id: "3",
              title: "采购管理",
              router: "purchase",
              icon: "el-icon-shopping-cart-full",
            },
            {
              id: "4",
              title: "巡检管理",
              router: "inspection",
              icon: "el-icon-view",
            },
            {
              id: "5",
              title: "设备分析",
              router: "deviceAnalysis",
              icon: "el-icon-data-analysis",
            }
          ]
        },
        {
          id: "3",
          title: "数据分析",
          router: "dataAnalysis",
          icon: "el-icon-s-marketing",
          Subclass: [
            {
              id: "1",
              title: "保养分析",
              router: "maintaince",
              icon: "el-icon-mobile",
            },

            {
              id: "2",
              title: "工效分析",
              router: "efficiencyAnalysis",
              icon: "el-icon-pie-chart",
            },
            {
              id: "3",
              title: "报表分析",
              router: "dataCenter",
              icon: "el-icon-document-copy",
            }
          ],
        },
        {
          id: "4",
          title: "保养管理",
          router: "maintaince",
          icon: "el-icon-s-help",
          Subclass: [
            {
              id: "1",
              title: "标准管理",
              router: "maintainceList",
              icon: "el-icon-document",
            },
            {
              id: "2",
              title: "区域管理",
              router: "maintainceArea",
              icon: "el-icon-location-information",
            },
            {
              id: "3",
              title: "保养记录",
              router: "maintainceRecord",
              icon: "el-icon-document-checked",
            }, {
              id: "4",
              title: "加油管理",
              router: "brushOil",
              icon: "el-icon-brush",
            },
            {
              id: "5",
              title: "工作日志",
              router: "dailyworkLog",
              icon: "el-icon-date",
            },
            {
              id: "6",
              title: "保全管理",
              router: "userList",
              icon: "el-icon-user",
            }
          ]
        },
        {
          id: '5',
          title: '备件管理',
          router: 'parts',
          icon: 'el-icon-s-cooperation',
          Subclass: [
            {
              id: "1",
              title: "备件列表",
              router: "spareParts",
              icon: "el-icon-cpu",
            },
            {
              id: "2",
              title: "分类管理",
              router: "sparePartsClassify",
              icon: "el-icon-share",
            },
            {
              id: "3",
              title: "库位管理",
              router: "sparePartsLocation",
              icon: "el-icon-box",
            },
            {
              id: "4",
              title: "备件入库",
              router: "sparePartsIn",
              icon: "el-icon-sold-out",
            },
            {
              id: "5",
              title: "备件出库",
              router: "sparePartsOut",
              icon: "el-icon-sell",
            },
            {
              id: "6",
              title: "入库记录",
              router: "sparePartsCheck",
              icon: "el-icon-set-up",
            },
            {
              id: "7",
              title: "出库记录",
              router: "sparePartsScrap",
              icon: "el-icon-bank-card",
            },
            {
              id: "8",
              title: "回收管理",
              router: "sparePartsRepair",
              icon: "el-icon-document-add",
            }
          ]
        },
        {
          id: '6',
          title: '维修管理',
          router: 'repair',
          icon: 'el-icon-s-platform',
          Subclass: [
            {
              id: '1',
              title: '报修管理',
              router: 'callRepair',
              icon: 'el-icon-set-up'
            }
          ]
        },
        {
          id: "7",
          title: "系统管理",
          router: "system",
          icon: "el-icon-s-tools",
          Subclass: [
            {
              id: "1",
              title: "用户管理",
              router: "userManage",
              icon: "el-icon-user",
            },
            {
              id: "2",
              title: "部门管理",
              router: "departmentManage",
              icon: "el-icon-film",
            },
            {
              id: "3",
              title: "菜单管理",
              router: "menuManage",
              icon: "el-icon-c-scale-to-original",
            },
            {
              id: "4",
              title: "角色管理",
              router: "roleManage",
              icon: "el-icon-picture-outline-round",
            },
            {
              id: "5",
              title: "反馈管理",
              router: "feedback",
              icon: "el-icon-message",
            },
            {
              id: '6',
              title: '系统配置',
              router: 'appConfig',
              icon: 'el-icon-setting'
            },
            {
              id: '7',
              title: '日志管理',
              router: 'operateLog',
              icon: 'el-icon-document'
            }
          ],
        },

      ], // 左侧菜单列表
      tabList: [], // 点击的菜单
      marginTop: "0px",
      winHeight: 0,
      winWidth: 0,
    };
  },
  // 监听tabList的长度变化
  watch: {
    tabList: {
      handler: function (newVal, oldVal) {
        // alert(newVal.length)
        // alert(this.winWidth);
        // 如果window的宽度小于1600，marginTop为90px
        if(newVal.length >= 10 && this.winWidth == 1280) {
          this.marginTop = "90px";
          return false
        }
        // console.log('大于1600');
        if (newVal.length >= 16 && this.winWidth < 1920) {
          this.marginTop = "90px";
          // 获取当前窗口的高度和宽度
        } else if (newVal.length > 16 && this.winWidth >= 1920) {
          this.marginTop = "90px";
        } else if (newVal.length <= 16 && this.winWidth >= 1920) {
          this.marginTop = "60px";
        } else if (newVal.length < 16 && this.winWidth < 1920) {
          this.marginTop = "90px";
        }
        // else {
        //   this.marginTop = "60px";
        // }
      },
      deep: true,
    },
  },

  computed: {
    ...mapState({
      userInfo: (state) => state.users ? state.users : '',
    }),
  },
  created() {
    this.getWindowWH();
    window.onresize = () => {
      return (() => {
        this.getWindowWH();
      })();
    };
  },
  mounted() {
    const tabList = localStorage.getItem('tab')
    // console.log('打开的tab', JSON.parse(tabList));
    // console.log('保存的默认激活',localStorage.getItem('fid'));
    // console.log('打开的大分类', localStorage.getItem('opend'));
    const fid = localStorage.getItem('fid')
    const open = localStorage.getItem('opend')
    if (fid && open) {
      //console.log('有');

      JSON.parse(tabList).forEach((item, index) => {
        if (item.class_id === open && item.id === fid) {
          this.activeItem = index
          this.indexs = item.id + ""
          this.openeds.push(item.class_id + "")
        }
      })
    } else {
      this.indexs = "1"
      this.openeds = ["1"]
    }
    if (tabList && tabList.length > 0) {
      this.tabList = JSON.parse(tabList)
    }
  },
  methods: {
    // 获取当前窗口的高度和宽度
    getWindowWH() {
      if (window.innerWidth) {
        this.winWidth = window.innerWidth;
      } else if (document.body && document.body.clientWidth) {
        this.winWidth = document.body.clientWidth;
      }
      if (window.innerHeight) {
        this.winHeight = window.innerHeight;
      } else if (document.body && document.body.clientHeight) {
        this.winHeight = document.body.clientHeight;
      }
      // 通过深拷贝，将当前的窗口高度和宽度保存下来
      this.winWidth = JSON.parse(JSON.stringify(this.winWidth));
      this.winHeight = JSON.parse(JSON.stringify(this.winHeight));
    },
    // 点击进入全屏
    fullScreen() {
      let element = document.documentElement;
      if (this.fullscreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
        this.fullscreen = false;
      } else {
        if (element.requestFullscreen) {
          element.requestFullscreen();
        } else if (element.webkitRequestFullScreen) {
          element.webkitRequestFullScreen();
        } else if (element.mozRequestFullScreen) {
          element.mozRequestFullScreen();
        } else if (element.msRequestFullscreen) {
          // IE11
          element.msRequestFullscreen();
        }
        this.fullscreen = true;
      }
    },
    // 初始化点击的menu
    initOpend(obj) {
      this.indexs = "1";
      if (obj.length > 0) {
        //  console.log('传进来的obj', obj);
        //  console.log('当前tablist', this.tabList);
        // this.tabList = []
        // console.log('清空后', this.tabList);
        this.tabList = obj;
      }
    },
    // 隐藏侧边栏
    changeHide() {
      this.isCollapse = !this.isCollapse;
      this.$store.commit("handleAsideWidth");
    },
    // 退出登录
    async logout() {
      this.$confirm("您即将注销当前登录, 是否继续?", "温馨提示", {
        confirmButtonText: "退出",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          const res = await this.$http.logouts();
          if (res.status !== 200) return this.$message.error(res.message)
          localStorage.removeItem("accessToken");
          localStorage.removeItem("userInfo");
          localStorage.removeItem("tab");
          localStorage.removeItem("opend")
          localStorage.removeItem("fid");
          this.$message.success(res.message);
          this.$router.push("/login");
        } catch (error) {
          return this.$message.error("服务器异常，请稍后再试！");
        }
      }).catch(() => {
        return false;
      });
    },
    // 收集tab标签
    getLink(id, name, link, classify_id) {
      // console.log('小分类', id);

      // console.log('大分类', classify_id);
      // 存储当前激活选项
      localStorage.setItem("fid", id + "")
      // 存储当前大分类
      localStorage.setItem("opend", classify_id + "")

      const obj = {
        id: id,
        title: name,
        path: link,
        class_id: classify_id,
      };
      // 去重
      this.tabList.forEach((item, index) => {
        if (item.path === link) {
          this.tabList.splice(index, 1);
        }
      });
      this.tabList.push(obj);
      // 存储打开的tablist
      localStorage.setItem('tab', JSON.stringify(this.tabList))
    },
    // 删除tab
    handleTabsEdit(e) {
      // this.tabList.forEach((tab, index) => {
      //   if (tab.path === e.router) {
      //     this.tabList.splice(index, 1);
      //   }
      // });
      const tabs = JSON.parse(localStorage.getItem('tab'));
      tabs.forEach((tab, index) => {
        if (tab.path === e.router) {
          tabs.splice(index, 1);
        }
      });
      // console.log('上一个tab', this.tabList.length - 1);
      // console.log('上一个tab的path', this.tabList[this.tabList.length - 1].path);
      // console.log('上一个tab的分类id', this.tabList[this.tabList.length - 1].class_id);
      // console.log('上一个tab的小分类id', this.tabList[this.tabList.length - 1].id);
      if (tabs[tabs.length - 1].id == 20) {
        this.indexs = "0";
        setTimeout(() => {
          this.indexs = "1";
        }, 200);
      }
      // 跳转小分类id选中
      this.indexs = tabs[tabs.length - 1].id + "";
      // 默认打开的大分类
      this.openeds = [];
      this.openeds.push(tabs[tabs.length - 1].class_id + "");
      this.$router.push(tabs[tabs.length - 1].path);
      this.tabList = tabs
      // 重新存储删除后的tabList
      localStorage.setItem("tab", JSON.stringify(this.tabList))
    },
    // 切换tab
    changeTabs(e) {
      // console.log('大分类',e.cid);
      // console.log('index', e.idx);
      // console.log('子类id', e.fid);
      if (e.fid == 20) {
        e.fid = "1";
      }
      this.indexs = e.fid;
      this.openeds = [];
      this.openeds.push(e.cid + "");
      // console.log('当前激活', this.indexs);
      //  console.log('当前大分类', this.openeds);
      // 存储当前激活选项
      localStorage.setItem("fid", this.indexs)
      // 存储当前大分类
      localStorage.setItem("opend", this.openeds)
    },
  },
};
</script>

<style scoped lang="scss">
/*隐藏文字*/
.el-menu--collapse .el-submenu__title span {
  display: none;
  transition: all 0.2s ease 0ms;
}

/*隐藏 > */
.el-menu--collapse .el-submenu__title .el-submenu__icon-arrow {
  display: none;
  transition: all 0.2s ease 0ms;
}

.title-bar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 5px;
}

.header-message {
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
  margin-top: 5px;
}

.el-aside {
  /* transition: all 0.2s; */
  transition: all 0.2s ease 0ms;
}

.fade-enter-from {
  opacity: 0;
}

.fade-enter-to {
  opacity: 1;
}

.fade-leave-from {
  opacity: 1;
}

.fade-leave-to {
  opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s;
}

.fade-enter-active {
  transition-delay: 0.3s;
}

.message-box {
  display: flex;
  width: 100px;
  position: absolute;
  left: 25%;
  top: 2.6%;
}

.message-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.message-item .message-detail {
  font-size: 0.8rem;
  width: 200px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}

.message-item .time-text {
  font-size: 0.8rem;
  margin-left: 10px;
}

.el-button {
  background-color: #007ef7 !important;
  padding: 0;
  margin: 0;
  border: 0 !important;
  display: flex;
  justify-content: center;
  align-items: center;
}

.info {
  color: #fff;
  font-size: 1rem;
  position: relative;
  top: -3px;
  left: 5px;
}

.logout-box {
  display: flex;
  flex: 1;
  justify-content: flex-end;
  align-items: center;
  padding-right: 30px;
  color: #fff;

  img {
    width: 45px;
    height: 45px;
    border-radius: 100%;
    cursor: pointer;
  }
}

.texts {
  text-decoration: none !important;
}

/* 导航二级菜单栏点击之后的一像素边的问题 */
.el-menu {
  border-right: none;
}

/* 导航栏点击后,左边的颜色设置 */
.el-menu-item.is-active {
  border-left: solid 2px rgb(0, 126, 247) !important;
}

.el-submenu:hover {
  background-color: #ecf5ff !important;
}

.el-menu-item:hover {
  background-color: #ecf5ff !important;
}


/*占满全屏*/
.el-container {
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
  bottom: 0;
  background-color: #eee;
  box-sizing: border-box;
  overflow: hidden;
}

/*上外边距50px，防止挡住Header*/
.el-aside,
.el-main {
  margin-top: 60px;
  // height: calc(100vh - 61px);
  box-sizing: border-box;
}

.el-aside {
  background-color: #fff;
  overflow-y: scroll;
  overflow-x: hidden;
  box-sizing: border-box;
  border-right: 1px solid #ebeef5;
}

/*设置背景色，方便观察效果*/
.el-header {
  background-color: #517DF7;
  /* 上层显示，避免被Main和Aside遮挡 */
  z-index: 999;
  box-sizing: border-box;
  overflow-x: hidden;
}

.el-menu-item {
  font-size: 0.875rem;
  color: #777;
}

.header-logos {
  width: 250px;
  display: flex;
  position: absolute;
  left: 0;
  top: 1px;
  justify-content: center;
  align-items: center;
}

.el-image {
  margin: 2px 0px;
  border-radius: 50px !important;
  width: 100%;
  height: 100%;
}

.el-aside {
  background-color: #feffff;
}

.el-main {
  background-color: #F1F5F9;
}

.user-text {
  font-size: 1rem;
  padding: 0 1rem;
  color: #fff;
  cursor: pointer;
}

.user-texts {
  font-size: 0.9rem;
  padding: 0 0.3rem;
  cursor: pointer;
}

/* 去除菜单右侧边框 */
.el-menu {
  border-right: none;
}

/* 设置展开时菜单宽度 */
.el-menu-vertical:not(.el-menu--collapse) {
  width: 250px;
}

/* logo */
.logo {
  color: #fff;
  text-align: center;
  font-size: 20px;
  line-height: 50px;
  padding: 0 5px;
  text-decoration: none;
  font-weight: bold;
  letter-spacing: 1px;
  font-family: 'Times New Roman', Times, serif;
}

/* 折叠按钮 */
.toggle {
  color: #fff;
  text-align: center;
  font-size: 26px;
  line-height: 60px;
  display: inline-block;
  padding: 0 15px;
  border-left: solid 1px #ccc;
  position: absolute;
  left: 250px;
  cursor: pointer;
}

.toggle:hover {
  /* background-color: #ffd04b; */
}

/* 下拉菜单 */
.el-dropdown {
  color: #fff;
  text-align: center;
  font-size: 26px;
  line-height: 40px;
  float: right;
}

.el-submenu span {
  font-size: 0.95rem;
  font-weight: normal;
  color: #333;
}

.el-dropdown-link {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 5px;
}
</style>
