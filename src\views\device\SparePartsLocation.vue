<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-08-16 10:59:26
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-25 15:17:42
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\device\SparePartsLocation.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <!-- 表单、搜索框，查询、添加库位按钮 -->
    <div class="search-form">
      <el-form :inline="true" size="medium" class="demo-form-inline">
        <el-form-item>
          <el-button type="success" @click="create" icon="el-icon-film"
            >创建货架</el-button
          >
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="add"
            icon="el-icon-circle-plus-outline"
            >添加库位</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <!-- 主体区域，多个卡片块，有header -->
    <div class="card-container">
      <el-row :gutter="20">
        <!-- 左侧部门列表 -->
        <el-col :span="24" class="card-left-content">
          <el-tabs
            tab-position="left"
            v-model="currentDepart"
            stretch
            type="border-card"
            @tab-click="tabclickHanlder"
          >
            <el-tab-pane
              :label="item.depart_name"
              :name="item.depart_name"
              v-for="(item, index) in departList"
              :key="index"
              style="overflow-y: scroll"
            >
              <template v-if="storageList.length > 0">
                <el-col
                  :v-loading="loading"
                  :element-loading-text="elementLoadingText"
                  :xs="24"
                  :sm="24"
                  :md="12"
                  :lg="8"
                  v-for="(item1, index1) in storageList"
                  :key="index1"
                  class="card-item"
                >
                  <el-card class="box-card">
                    <div slot="header" class="clearfix">
                      <span>{{ item1.shelf_name }}</span>
                      <el-button
                        style="float: right; padding: 3px 0"
                        type="text"
                        >库位:
                        {{ item1.count }}</el-button
                      >
                    </div>
                    <div class="text item">
                      <el-row :gutter="10">
                        <el-col :span="10">
                          <el-image
                            :src="src"
                            style="width: 5.5rem; height: 5.5rem"
                          ></el-image>
                        </el-col>
                        <el-col :span="14" class="right-content">
                          <el-col :span="24">
                            <span>所属部门:</span
                            ><span>{{ item1.department }}</span>
                          </el-col>
                          <el-col :span="24">
                            <span>备件类型:</span><span>{{ item1.type }}</span>
                          </el-col>
                          <el-col :span="24">
                            <span>配件种类:</span
                            ><span>{{
                              item1.category ? item1.category : 0
                            }}</span>
                          </el-col>
                          <el-col :span="24">
                            <span>配件总数:</span
                            ><span>{{
                              item1.quantity ? item1.quantity : 0
                            }}</span>
                          </el-col>
                        </el-col>
                      </el-row>
                    </div>
                  </el-card>
                </el-col>
              </template>
              <!-- 没有数据的情况 -->
              <template v-else>
                <el-empty description="暂无数据"></el-empty>
              </template>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
    </div>
    <!-- 分页 -->
    <el-pagination
      v-if="storageList.length > 0"
      @size-change="handleSizeChange"
      @current-change="currentchange"
      :current-page="queryInfo.currentnum"
      :page-sizes="pageSizes"
      :page-size="queryInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
    <!-- 创建货架弹窗,货架名称，所属部门，货架图片 -->
    <el-dialog title="创建货架" :visible.sync="dialogVisible" width="45%">
      <el-form ref="form" :model="form" label-width="auto" :rules="rules">
        <el-form-item label="货架名称" prop="name">
          <el-input
            v-model="form.name"
            maxlength="20"
            clearable
            placeholder="请输入货架名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="所属部门" prop="department">
          <el-select
            v-model="form.department"
            clearable
            placeholder="请选择所属部门"
          >
            <el-option
              v-for="item in departList"
              :key="item.id"
              :label="item.depart_name"
              :value="item.depart_name"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- 存储物品类型 -->
        <el-form-item label="存储类型" prop="type">
          <el-col :span="8">
            <el-select
              v-model="form.type"
              clearable
              placeholder="请选择存储类型"
            >
              <el-option
                v-for="item in typeList"
                :key="item.name"
                :label="item.name"
                :value="item.name"
              ></el-option>
            </el-select>
          </el-col>
          <el-col :span="16">
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: flex-start;
              "
            >
              <el-input
                v-model="newType"
                placeholder="请输入新存储类型"
                v-if="showInput"
                ref="newTypeInput"
                style="margin: 0 1rem"
              ></el-input>
              <el-button
                type="primary"
                size="medium"
                icon="el-icon-plus"
                @click="addType"
                v-if="!showInput"
                >添加类型</el-button
              >
              <el-button
                icon="el-icon-plus"
                type="primary"
                @click="saveType"
                v-if="showInput"
                >新增</el-button
              >
            </div>
          </el-col>
        </el-form-item>
        <!-- 图片上传 -->
        <el-form-item label="货架图片" prop="avatar">
          <div class="image-view-title">
            <el-upload
              :action="action"
              :headers="headers"
              list-type="picture-card"
              name="files"
              accept=".jpg,.png,.webp,.jfif"
              :limit="1"
              :on-remove="logoRemove"
              :on-success="logoSuccess"
              :on-preview="handlepreview"
              :multiple="false"
              :on-error="onErr"
              :before-upload="project"
              :file-list="logo"
            >
              <i class="el-icon-plus"></i>
            </el-upload>
            <!-- 大图展开 -->
            <el-dialog :visible.sync="dialogVisibles" :modal="false">
              <img width="100%" :src="dialogImageUrl" alt="上传失败" />
            </el-dialog>
          </div>
        </el-form-item>
        <el-form-item style="margin-top: 100px">
          <el-button type="primary" @click="onSubmit">立即创建</el-button>
          <el-button @click="dialogVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 添加库位抽屉组件 -->
    <el-drawer
      title="添加库位"
      :visible.sync="drawer"
      :direction="direction"
      :size="500"
      @close="closeEvent"
    >
      <el-form
        ref="forms"
        :model="forms"
        label-width="80px"
        :rules="formsRules"
        style="padding: 0 1rem"
      >
        <el-form-item label="库位编号" prop="name">
          <el-input
            v-model="forms.name"
            placeholder="请输入库位编号,建议A-001,A-002"
          ></el-input>
        </el-form-item>
        <el-form-item label="所属货架" prop="area">
          <!-- <el-select v-model="forms.area" placeholder="请选择所属货架">
            <el-option label="货架A" value="货架A"></el-option>
            <el-option label="货架B" value="货架B"></el-option>
          </el-select> -->
          <el-cascader
            v-model="forms.area"
            :options="shellLists"
            :props="{ expandTrigger: 'hover' }"
            @change="handleChange"
          ></el-cascader>
        </el-form-item>
        <el-form-item style="margin-top: 100px">
          <el-button type="primary" @click="addLocation">添加</el-button>
          <el-button @click="drawer = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-drawer>
  </div>
</template>
<script>
import { type } from "windicss/utils";
import config from "../../common/config.js";
import { Loading } from "element-ui";
import { set } from "lodash";
export default {
  data() {
    return {
      newType: "", // 新增存储类型
      showInput: false, // 显示输入框
      loading: false, // 加载
      elementLoadingText: "Flyknit...",
      drawer: false, // 抽屉
      direction: "rtl", // 抽屉方向
      dialogVisibles: false, // 大图展示
      action: config.uploadURL, // 上传地址
      dialogImageUrl: "", // 大图展示链接
      departList: [], // 部门列表
      // 上传logoing
      loadmen: false,
      logo: [], // 上传的图片
      headers: {
        Authorization: `Bearer ${localStorage.getItem("accessToken")}`,
      },
      baseURL: config.imgPrefix, // 图片地址
      dialogVisible: false, // 大图展示
      src: "../src/assets/image/strorage.png",
      form: {
        name: "",
        type: "", // 类型
        department: "",
        avatar: "",
      },
      forms: {
        name: "",
        area: "",
        depart: "",
      },
      queryInfo: {
        query: "",
        pagenum: 1,
        pagesize: 9,
        currentnum: 1,
      },
      pageSizes: [9, 12, 15, 18],
      total: 0,
      logo: [],
      rules: {
        name: [
          { required: true, message: "请输入货架名称", trigger: "blur" },
          { min: 3, max: 5, message: "长度在 3 到 5 个字符", trigger: "blur" },
        ],
        department: [
          { required: true, message: "请选择所属部门", trigger: "change" },
        ],
        type: [
          { required: true, message: "请选择货架存放类型", trigger: "change" },
        ],
        avatar: [
          { required: false, message: "请上传货架图片", trigger: "change" },
        ],
      },
      formsRules: {
        name: [
          { required: true, message: "请输入库位编号", trigger: "blur" },
          { min: 3, max: 5, message: "长度在 3 到 8 个字符", trigger: "blur" },
        ],
        area: [
          { required: true, message: "请选择所属货架", trigger: "change" },
        ],
      },
      shellLists: [], // 货架列表
      currentDepart: "织造车间", // 当前部门
      storageList: [], // 当前部门的货架列表
      typeList: [], // 存储类型列表
    };
  },
  mounted() {
    this.getDepartList();
    this.getTypeList();
    this.getSheltList();
    this.getAllSheltList();
  },
  methods: {
    // 获取存储类型列表
    async getTypeList() {
      try {
        const res = await this.$http.getStorageType();
        if (res.status !== 200) {
          return this.$message.error(res.message);
        } else {
          this.typeList = res.data;
        }
      } catch (error) {
        return this.$message.error(error);
      }
    },
    // 更改input框显示隐藏
    addType() {
      this.showInput = !this.showInput;
      setTimeout(() => {
        this.$refs.newType.focus();
      }, 500);
    },
    // 存储物品类型
    async saveType() {
      if (!this.newType) {
        return this.$message.warning("请输入存储类型");
      }
      try {
        const res = await this.$http.initStorageType({
          type: this.newType,
        });
        if (res.status !== 200) {
          return this.$message.error(res.message);
        } else {
          this.$message.success(res.message);
          // 清除表单数据
          this.newType = "";
          this.showInput = false;
          this.getTypeList();
        }
      } catch (error) {
        return this.$message.error(error);
      }
    },
    // 获取货架列表
    async getAllSheltList() {
      const loading = Loading.service({
        text: "Flyknit...",
        target: document.querySelector(".card-left-content"),
        fullscreen: false,
      });
      try {
        this.queryInfo.department = this.currentDepart;
        const res = await this.$http.getStorageList(this.queryInfo);
        if (res.status !== 200) {
          return this.$message.error(res.message);
        }
        const { list, total } = res.data;
        setTimeout(() => {
          loading.close();
          this.storageList = list;
          this.total = total;
        }, 500);
      } catch (error) {
        loading.close();
        return this.$message.error(error);
      }
    },
    // 部门选择
    tabclickHanlder(e) {
      this.currentDepart = e._props.name;
      this.getAllSheltList();
    },
    // 级联选择
    handleChange(value) {
      console.log(value);
      this.forms.area = value[value.length - 1];
      this.forms.depart = value[value.length - 2];
    },
    // 关闭抽屉前回调
    closeEvent() {
      // 重置表单
      this.$refs["forms"].resetFields();
    },
    add() {
      this.drawer = true;
    },
    handleSizeChange(val) {
      this.queryInfo.pagesize = val;
      this.getAllSheltList();
    },
    currentchange(val) {
      this.queryInfo.pagenum = val;
      this.getAllSheltList();
    },
    // 创建货架
    create() {
      this.dialogVisible = true;
    },
    // 展开大图
    handlepreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisibles = true;
    },
    // 上传失败
    onErr(e) {
      this.loadmen = false;
      this.$message.error("上传失败,尝试重新上传");
    },
    // 上传时
    project(file) {
      this.loadmen = true;
    },
    // logo移除文件时的钩子
    logoRemove(file, fileList) {
      this.logo = [];
    },
    // 上传成功：logo
    logoSuccess(res, file, fileList) {
      const { url } = res.data;
      this.logo.push({ url: this.baseURL + url, uid: file.uid }); //element展示图片时需要数组类型的才能展示
      this.form.avatar = this.logo[0].url;
      this.loadmen = false;
    },
    // 创建货架提交
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.createShelt();
        } else {
          return false;
        }
      });
    },
    // 封装创建货架方法
    async createShelt() {
      try {
        const res = await this.$http.createShelts(this.form);
        if (res.status !== 200) {
          return this.$message.error(res.data.message);
        } else {
          this.$message.success(res.message);
          this.dialogVisible = false;
          this.$refs["form"].resetFields();
          this.logo = [];
          this.getAllSheltList();
        }
      } catch (error) {
        return this.$message.error(error);
      }
    },
    // 添加库位
    addLocation() {
      this.$refs["forms"].validate((valid) => {
        if (valid) {
          this.addAreas();
        } else {
          return false;
        }
      });
    },
    // 封装统一添加库位方法
    async addAreas() {
      try {
        const res = await this.$http.addStorage(this.forms);
        if (res.status !== 200) {
          return this.$message.error(res.message);
        } else {
          this.$message.success(res.message);
          this.drawer = false;
          this.getAllSheltList();
        }
      } catch (error) {
        return this.$message.error(error);
      }
    },
    // 获取各部门货架列表
    async getSheltList() {
      try {
        const res = await this.$http.getShelfList();
        if (res.status !== 200) {
          return this.$message.error(res.message);
        } else {
          this.shellLists = res.data;
        }
      } catch (error) {
        return this.$message.error(error);
      }
    },
    // 获取各部门列表
    async getDepartList() {
      try {
        const res = await this.$http.getDepartmentList();
        if (res.status !== 200) {
          return this.$message.error(res.message);
        } else {
          this.departList = res.data;
        }
      } catch (error) {
        return this.$message.error(error);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.el-drawer {
  padding: 0 20px;
}
.box-card {
  cursor: pointer;

  .clearfix {
    span {
      font-weight: 800;
      letter-spacing: 2px;
    }
  }
}

.box-card:hover {
  box-shadow: 0 0 10px #ccc;
}

.card-item {
  margin: 8px 0;
}

.right-content {
  display: flex;
  flex-direction: column;

  .el-col {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-col span:nth-child(1) {
    color: #999;
    font-size: 14px;
  }

  .el-col span:nth-child(2) {
    color: #333;
    font-size: 14px;
  }
}

.el-pagination {
  margin-top: 10px;
}
</style>
