import{i as d,j as ee,k as Ie,aV as Me,E as y,c as te,B as Fe,o as i,a as b,w as o,b as l,g as E,h as D,F as z,n as $,s as Te,d as p,p as Le,a2 as Ue,m as N,t as h,f as K,aW as Ye,aX as $e,ad as Ne,aY as Re,a5 as He}from"./index-444b28c3.js";import{E as Se}from"./el-card-6f02be36.js";import{E as qe}from"./el-drawer-12f56ca7.js";import"./el-overlay-9f4b42b1.js";import{E as Oe}from"./el-footer-ef9d7e15.js";/* empty css                */import{E as Be,a as Pe}from"./el-col-bd5e5418.js";import{E as je,a as Ke}from"./el-descriptions-item-c5351b3c.js";import{E as Ae}from"./el-tag-29cbefd8.js";import{E as Ze}from"./el-empty-9653e355.js";import{E as We,a as Xe}from"./el-table-column-fa1764a8.js";import"./el-checkbox-f3df62fa.js";import"./el-tooltip-4ed993c7.js";import{E as Ge}from"./el-scrollbar-af6196f4.js";import{E as Je}from"./el-divider-d33f4e37.js";import{E as Qe,a as et}from"./el-form-10dec954.js";import{E as tt}from"./el-button-9bbdfcf9.js";import{E as lt}from"./el-date-picker-0feb0d8c.js";import{E as at}from"./el-input-6b488ec7.js";import"./el-form-item-4ed993c7.js";import{a as ot,E as rt}from"./el-select-980e5896.js";import{_ as nt}from"./_plugin-vue_export-helper-c27b6911.js";import{E as L}from"./index-df5d5edc.js";import"./index-eba6e623.js";import"./event-fe80fd0c.js";import"./scroll-a66dde9b.js";import"./vnode-b9ec7db4.js";import"./focus-trap-6de7266c.js";import"./index-e305bb62.js";import"./index-4d7f16ce.js";import"./_Uint8Array-55276dff.js";import"./index-f312e047.js";import"./validator-e4131fc3.js";import"./directive-ce1b251f.js";const st={style:{"padding-top":"7rem"}},ut={style:{display:"flex","justify-content":"space-between","align-items":"center",margin:"0.8rem 0",position:"relative"}},it={class:"standard-content"},ct={style:{display:"flex","justify-content":"flex-end",position:"absolute",left:"13rem"}},mt={__name:"orderRecords",setup(dt){const S=d(null),q=d(null),C=d(!1),A=d("订单产品测量"),le=d({}),c=d({}),ae=[{text:"上周",value:()=>{const t=new Date,e=new Date;return e.setDate(e.getDate()-7),[e,t]}},{text:"上月",value:()=>{const t=new Date,e=new Date;return e.setMonth(e.getMonth()-1),[e,t]}},{text:"前三月",value:()=>{const t=new Date,e=new Date;return e.setMonth(e.getMonth()-3),[e,t]}},{text:"今年",value:()=>{const t=new Date,e=new Date;return e.setFullYear(e.getFullYear()-1),[e,t]}}],I=d([]),w=d([]);let R=ee([]),H=ee([]),k=d();const Z=d([]),W=d([]),n=d({serial:"",model:"",styleno:"",color:"",size:"",type:"",time:""}),oe=d({model:[{required:!0,message:"请选择Model",trigger:"change"}],size:[{required:!0,message:"请选择尺码",trigger:"change"}],type:[{required:!0,message:"请选择品名",trigger:"change"}],styleno:[{required:!0,message:"请输入款号",trigger:"blur"}],color:[{required:!0,message:"请输入配色",trigger:"blur"}]}),X=d([]);let G=(t,e=500)=>{let s=null;return function(...u){clearTimeout(s),s=setTimeout(()=>{t.apply(this,u)},e)}};Ie(()=>{S.value.focus(),re()});const re=async()=>{try{const t=await Me();if(t.code!==200)return y({title:"",message:t.message,type:"error"});X.value=t.data.modelList,Z.value=t.data.sizeList,W.value=t.data.pmList}catch{return!1}},O=d([]),U=d([]),ne=te(()=>U.value.filter(t=>t.children)),Y=te(()=>O.value.map((t,e)=>({...t,_index:e}))),se=({rowIndex:t,columnIndex:e})=>{var r;const s=Y.value[t],u=Y.value[t-1];if(e<=3)if(t===0||s.name!==(u==null?void 0:u.name)){let m=1,v=t+1;for(;v<Y.value.length&&((r=Y.value[v])==null?void 0:r.name)===s.name;)m++,v++;return[m,1]}else return[0,0]},B=(t,e,s)=>{if(!t||!e)return!1;const u=s.find(M=>{var V;return(V=M.children)==null?void 0:V.some(F=>F.prop===e)});if(!u)return!1;const m=u.children[0].label.split("-").map(Number),v=Math.min(...m),_=Math.max(...m);return t>=v&&t<=_},ue=()=>{n.value={serial:"",model:"",styleno:"",color:"",size:"",type:"",time:""}},ie=G(async()=>{const t=n.value.serial;if(!t)return y({title:"",message:"请扫描产品二维码",type:"error",duration:2e3}),!1;if(!ce(t))return y({title:"",message:"产品二维码格式错误，请重新扫描",type:"error",duration:2e3}),n.value.serial="",!1;pe(),A.value=n.value.serial+"订单产品测量"}),ce=t=>(t=t.trim().replace(/[^a-zA-Z0-9]/g,""),/^S\d{12}$/i.test(t)),me=()=>{C.value=!C.value},de=t=>t.replace(/\s+/g,"").replace(/[^a-zA-Z0-9]/g,"").toUpperCase(),pe=async()=>{const t=L.service({text:"Flyknit..."});try{const e=await Re({serial:de(n.value.serial),type:"post"});e.code!==200&&y({title:"",message:e.data.msg,type:"error",duration:2e3});const{standard:s,ticketInfo:u}=e.data;I.value=s,c.value=u,I.value.forEach(r=>{r.data.sort((m,v)=>m-v)}),c.value&&I.value.length>0?me():y({message:"未查到相关标准信息",type:"error",duration:2e3}),setTimeout(()=>{t.close()},200)}catch{return t.close(),!1}},J=G(t=>{if(!t)return!1;const e={name:t.name,value:t.value},s=w.value.find(_=>_.name===e.name);s?s.value=e.value:w.value.push(e),k.value=t.value.replace(/[, ]/g,""),/^\d+$/.test(k.value)||(y({message:"请输入正确的测量值",type:"error",duration:1500}),k.value="");let u=R.findIndex(_=>_.name===e.name);u!==-1&&R.splice(u,1),R.push(e);const r=t.data;r.sort((_,M)=>_-M);const m={name:t.name};let v="";ve(parseInt(k.value),parseInt(r[0]),parseInt(r[r.length-1]))?v="Pass":(k.value<r[0]||k.value>r[r.length-1])&&(v=parseInt(r[1]-k.value)),fe(t.name,v,m)}),fe=(t,e,s)=>{w.value.forEach(r=>{r.name===t&&(r.st=e)}),I.value.forEach(r=>{r.name===t&&(r.st=e)}),H.indexOf(t)&&H.some((r,m)=>{r.name===t&&H.splice(m,1)}),s.num=e,H.push(s)},ve=(t,e,s)=>{let u=/(\d+)/g;for(;u.exec(t);){let r=parseInt(RegExp.$1);if(r<e||r>s)return!1}return!0},_e=(t,e)=>{w.value.splice(e,1)},ge=async()=>{if(w.value.length===0)return y({message:"测量值不能为空",type:"error",duration:1500}),!1;try{L.service({lock:!0,text:"Flyknit...",background:"rgba(0, 0, 0, 0.7)"});const t=await Ye({serial:n.value.serial,model:c.value.model,pm:c.value.pm,cm:c.value.size,styleno:c.value.style_number,color:c.value.color,params:w.value});if(t.code!==200)return L.service().close(),y({message:t.msg,type:"error",duration:1500});setTimeout(()=>{L.service().close(),y({message:t.msg,type:"success",duration:1500}),n.value.serial="",w.value=[],R.value=[],k.value="",I.value=null,c.value[0]="",C.value=!1,S.value.focus()},500)}catch(t){return L.service().close(),!1}},ye=async t=>{t.validate(async e=>{e&&be()})},be=async()=>{if(!n.value.model&&!n.value.size&&!n.value.type&&!n.value.color&&!n.value.styleno)return y({message:"请输入查询条件",type:"info",duration:1500}),!1;n.value.startTime=n.value.time[0],n.value.endTime=n.value.time[1],n.value.pm=n.value.type;const t=L.service({text:"Flyknit..."});try{const e=await $e(n.value);if(e.code!==200)return y({message:e.msg,type:"error",duration:1500});const{tableHeads:s,transformedData:u}=e.data;U.value=s,O.value=u,setTimeout(()=>{t.close()},200)}catch{return t.close(),!1}};return(t,e)=>{const s=rt,u=ot,r=Qe,m=at,v=lt,_=tt,M=et,V=Be,F=Je,T=We,he=Ge,Ee=Xe,ke=Ze,x=je,Q=Ae,we=Ke,P=Pe,Ve=Fe("CircleCheckFilled"),xe=He,De=Oe,ze=qe,Ce=Se;return i(),b(Ce,{shadow:"hover"},{default:o(()=>[l(P,{gutter:20},{default:o(()=>[l(V,{lx:24,lg:24,md:24,sm:24,xs:24},{default:o(()=>[l(M,{ref_key:"searchForm",ref:q,model:n.value,rules:oe.value,inline:!0,class:"search-form"},{default:o(()=>[l(r,{label:"Model",prop:"model"},{default:o(()=>[l(u,{modelValue:n.value.model,"onUpdate:modelValue":e[0]||(e[0]=a=>n.value.model=a),placeholder:"请选择Model",clearable:""},{default:o(()=>[(i(!0),E(z,null,D(X.value,a=>(i(),b(s,{key:a.model_name,label:a.model_name,value:a.model_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(r,{label:"款号",prop:"styleno"},{default:o(()=>[l(m,{modelValue:n.value.styleno,"onUpdate:modelValue":e[1]||(e[1]=a=>n.value.styleno=a),placeholder:"请输入款号",clearable:""},null,8,["modelValue"])]),_:1}),l(r,{label:"配色",prop:"color"},{default:o(()=>[l(m,{modelValue:n.value.color,"onUpdate:modelValue":e[2]||(e[2]=a=>n.value.color=a),placeholder:"请输入配色",clearable:""},null,8,["modelValue"])]),_:1}),l(r,{label:"尺码",prop:"size"},{default:o(()=>[l(u,{modelValue:n.value.size,"onUpdate:modelValue":e[3]||(e[3]=a=>n.value.size=a),placeholder:"请选择尺码",clearable:""},{default:o(()=>[(i(!0),E(z,null,D(Z.value,a=>(i(),b(s,{key:a.size,label:a.size,value:a.size},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(r,{label:"品名",prop:"type"},{default:o(()=>[l(u,{modelValue:n.value.type,"onUpdate:modelValue":e[4]||(e[4]=a=>n.value.type=a),placeholder:"请选择品名",clearable:""},{default:o(()=>[(i(!0),E(z,null,D(W.value,a=>(i(),b(s,{key:a.pm,label:a.pm,value:a.pm},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(r,{label:"时间"},{default:o(()=>[l(v,{modelValue:n.value.time,"onUpdate:modelValue":e[5]||(e[5]=a=>n.value.time=a),type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",shortcuts:ae,clearable:""},null,8,["modelValue"])]),_:1}),l(r,null,{default:o(()=>[l(_,{type:"primary",icon:$(Te),onClick:e[6]||(e[6]=a=>ye(q.value))},{default:o(()=>[p("查询")]),_:1},8,["icon"]),l(_,{type:"info",icon:$(Le),onClick:e[7]||(e[7]=a=>ue(q.value))},{default:o(()=>[p("重置")]),_:1},8,["icon"])]),_:1}),l(r,{label:"订单产品测量"},{default:o(()=>[l(m,{style:{width:"20rem"},ref_key:"scanInput",ref:S,modelValue:n.value.serial,"onUpdate:modelValue":e[8]||(e[8]=a=>n.value.serial=a),onKeydown:Ue($(ie),["enter"]),placeholder:"请扫描产品二维码",clearable:"",maxlength:"13"},null,8,["modelValue","onKeydown"])]),_:1})]),_:1},8,["model","rules"])]),_:1}),l(F,{"content-position":"left"},{default:o(()=>[p("订单尺寸列表")]),_:1}),O.value.length>0?(i(),b(V,{key:0,span:24,style:{height:"calc(100vh - 290px)",overflow:"auto"}},{default:o(()=>[l(Ee,{data:Y.value,border:"",style:{width:"100%"},"span-method":se},{default:o(()=>[l(T,{label:"款号",align:"center",prop:"styleno"}),l(T,{label:"配色",align:"center",prop:"color"}),l(T,{label:"尺寸",align:"center",prop:"size"}),l(T,{label:"类别",align:"center",prop:"pm"}),(i(!0),E(z,null,D(ne.value,a=>(i(),b(T,{align:"center",key:a.label,label:a.label},{default:o(()=>[(i(!0),E(z,null,D(a.children,f=>(i(),b(T,{key:f.label,prop:`measurements.${f.prop}`,label:f.label,align:"center","class-name":"small-font"},{default:o(g=>[l(he,{class:"box-item",effect:"dark",content:B(g.row.measurements[f.prop],f.prop,U.value)?"在范围内":"超出范围",placement:"top-start"},{default:o(()=>[N("span",{style:Ne({backgroundColor:(B(g.row.measurements[f.prop],f.prop,U.value),""),color:B(g.row.measurements[f.prop],f.prop,U.value)?"":"#F16E6E",padding:"0.2rem 0rem",cursor:"pointer"})},h(g.row.measurements[f.prop]),5)]),_:2},1032,["content"])]),_:2},1032,["prop","label"]))),128))]),_:2},1032,["label"]))),128))]),_:1},8,["data"])]),_:1})):(i(),b(V,{key:1,span:24,style:{height:"calc(100vh - 290px)",overflow:"auto"}},{default:o(()=>[N("div",st,[l(ke,{description:"暂无数据","image-size":150})])]),_:1})),l(ze,{title:A.value,modelValue:C.value,"onUpdate:modelValue":e[10]||(e[10]=a=>C.value=a),size:"55%"},{default:o(()=>[l(F,{"content-position":"left"},{default:o(()=>[p("产品信息")]),_:1}),c.value?(i(),b(P,{key:0,gutter:20,class:"info-container"},{default:o(()=>[l(V,{span:24},{default:o(()=>[l(we,{title:"",border:"",column:2},{default:o(()=>[l(x,{label:"布票号"},{default:o(()=>[p(h(c.value.serial),1)]),_:1}),l(x,{label:"Model"},{default:o(()=>[p(h(c.value.model),1)]),_:1}),l(x,{label:"品名"},{default:o(()=>[p(h(c.value.pm),1)]),_:1}),l(x,{label:"尺码"},{default:o(()=>[l(Q,{size:"small"},{default:o(()=>[p(h(c.value.size),1)]),_:1})]),_:1}),l(x,{label:"款号"},{default:o(()=>[p(h(c.value.style_number),1)]),_:1}),l(x,{label:"季节"},{default:o(()=>[p(h(c.value.season),1)]),_:1}),l(x,{label:"配色"},{default:o(()=>[p(h(c.value.color),1)]),_:1})]),_:1})]),_:1})]),_:1})):K("",!0),l(F,{"content-position":"left"},{default:o(()=>[p("产品测量")]),_:1}),l(M,{model:le.value,"label-width":"auto",class:"form-container"},{default:o(()=>[l(P,{gutter:20,style:{overflow:"auto",width:"100%",padding:"1rem"}},{default:o(()=>[(i(!0),E(z,null,D(I.value,(a,f)=>(i(),E("div",{style:{display:"flex","flex-direction":"column","flex-wrap":"wrap","justify-content":"space-around","align-items":"center"},key:a.name},[l(r,{style:{width:"28rem",margin:"0 1rem"},label:a.name},{default:o(()=>[l(m,{type:"number",maxlength:3,clearable:"",modelValue:a.value,"onUpdate:modelValue":g=>a.value=g,placeholder:"请输入"+a.name+"点测量值",onChange:g=>$(J)(a,f),onClear:g=>_e(a,f),onInput:g=>$(J)(a,f)},null,8,["modelValue","onUpdate:modelValue","placeholder","onChange","onClear","onInput"])]),_:2},1032,["label"]),N("div",ut,[N("div",it,[(i(!0),E(z,null,D(a.data,(g,j)=>(i(),E("span",{key:j},h(j===0?"Min":j===1?"Target":"Max")+" : "+h(parseInt(g)),1))),128))]),N("div",ct,[a.st==="Pass"?(i(),b(xe,{key:0,color:"#50C878",size:24},{default:o(()=>[l(Ve)]),_:1})):K("",!0),typeof a.st=="number"&&a.st!==0?(i(),b(Q,{key:1,size:"small",effect:"dark",type:a.st==="Pass"?"success":"danger"},{default:o(()=>[p(h(a.st),1)]),_:2},1032,["type"])):K("",!0)])])]))),128))]),_:1}),l(F,{"content-position":"center"},{default:o(()=>[p("操作区域")]),_:1}),l(De,{class:"dialog-footer"},{default:o(()=>[l(_,{size:"large",onClick:e[9]||(e[9]=a=>C.value=!1)},{default:o(()=>[p("取消")]),_:1}),l(_,{type:"primary",size:"large",onClick:ge},{default:o(()=>[p("提交")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])]),_:1})]),_:1})}}},At=nt(mt,[["__scopeId","data-v-b9f06c5e"]]);export{At as default};
