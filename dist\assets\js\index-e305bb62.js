import{co as A,au as I,ci as M,bD as N,c as S,cp as _,n as W,I as B,i as D,bV as F}from"./index-444b28c3.js";var j=/\s/;function G(n){for(var e=n.length;e--&&j.test(n.charAt(e)););return e}var H=/^\s+/;function P(n){return n&&n.slice(0,G(n)+1).replace(H,"")}var C=0/0,U=/^[-+]0x[0-9a-f]+$/i,V=/^0b[01]+$/i,X=/^0o[0-7]+$/i,q=parseInt;function L(n){if(typeof n=="number")return n;if(A(n))return C;if(I(n)){var e=typeof n.valueOf=="function"?n.valueOf():n;n=I(e)?e+"":e}if(typeof n!="string")return n===0?n:+n;n=P(n);var t=V.test(n);return t||X.test(n)?q(n.slice(2),t?2:8):U.test(n)?C:+n}var w=function(){return M.Date.now()};const b=w;var z="Expected a function",J=Math.max,K=Math.min;function en(n,e,t){var u,a,f,o,i,c,d=0,h=!1,l=!1,T=!0;if(typeof n!="function")throw new TypeError(z);e=L(e)||0,I(t)&&(h=!!t.leading,l="maxWait"in t,f=l?J(L(t.maxWait)||0,e):f,T="trailing"in t?!!t.trailing:T);function v(r){var s=u,m=a;return u=a=void 0,d=r,o=n.apply(m,s),o}function $(r){return d=r,i=setTimeout(g,e),h?v(r):o}function k(r){var s=r-c,m=r-d,y=e-s;return l?K(y,f-m):y}function p(r){var s=r-c,m=r-d;return c===void 0||s>=e||s<0||l&&m>=f}function g(){var r=b();if(p(r))return E(r);i=setTimeout(g,k(r))}function E(r){return i=void 0,T&&u?v(r):(u=a=void 0,o)}function O(){i!==void 0&&clearTimeout(i),d=0,u=c=a=i=void 0}function R(){return i===void 0?o:E(b())}function x(){var r=b(),s=p(r);if(u=arguments,a=this,c=r,s){if(i===void 0)return $(c);if(l)return clearTimeout(i),i=setTimeout(g,e),v(c)}return i===void 0&&(i=setTimeout(g,e)),o}return x.cancel=O,x.flush=R,x}const Q=n=>(e,t)=>Y(e,t,W(n)),Y=(n,e,t)=>F(t,n,n).replace(/\{(\w+)\}/g,(u,a)=>{var f;return`${(f=e==null?void 0:e[a])!=null?f:`{${a}}`}`}),Z=n=>{const e=S(()=>W(n).name),t=B(n)?n:D(n);return{lang:e,locale:t,t:Q(n)}},rn=()=>{const n=N("locale");return Z(S(()=>n.value||_))};export{en as d,rn as u};
