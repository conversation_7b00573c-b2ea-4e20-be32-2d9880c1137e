/*
 * @Author: flyknit <EMAIL>
 * @Date: 2023-11-09 15:58:11
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-06-04 13:34:02
 * @FilePath: \electronic-filed:\gitee\nike-backend\config\sqlsrv.js
 * @Description: 
 * 
 * Copyright (c) 2024 by ${<EMAIL>}, All Rights Reserved. 
 */
// 引入mssql
const mssql = require('mssql');
// 配置文件
const config = {
    user: 'useryxw',
    password: 'yxw.123',
    server: "***********",
    database: 'kywlck_hy',
    options: {
        encrypt: false, // Use this if you're on Windows Azure
        enableArithAbort: true,
        connectionTimeout: 15000,
    }
};

// 宁波华耀数据库配置文件
const config2 = {
    user: "sch_yy_ky",
    password: "szky.20230610",
    server: "************",
    database: "kywlck_hy",
    options: {
        encrypt: false,
        enableArithAbort: true,
        connectionTimeout: 15000,
    },
};

// 数据库3配置文件
const config3 = {
    user: "schhysy",
    password: "Schy.20240527",
    server: "***********",
    database: "hysygl",
    options: {
        encrypt: false,
        enableArithAbort: true,
        connectionTimeout: 15000,
    },
};
// 越南sql server
const pool = new mssql.ConnectionPool(config);
const poolConnect = pool.connect();

// 宁波sql server
const pool2 = new mssql.ConnectionPool(config2);
const poolConnect2 = pool2.connect();

// 式样系统sql server
const pool3 = new mssql.ConnectionPool(config3);
const poolConnect3 = pool3.connect();

// 检测连接是否成功
poolConnect.then(() => {
    console.log('越南sql server连接成功')
    // global.logger.info('sql server连接成功')
}).catch(err => {
    console.log('越南sql server连接失败', err)
    // global.logger.error('sql server连接失败')
})

// 华耀数据库
poolConnect2.then(() => {
    console.log('宁波sql server连接成功')
    // global.logger.info('sql server连接成功')
}).catch(err => {
    console.log('宁波sql server连接失败', err)
    // global.logger.error('sql server连接失败')
})

// 式样系统数据库
poolConnect3.then(() => {
    console.log('式样系统sql server连接成功')
    // global.logger.info('sql server连接成功')
}).catch(err => {
    console.log('式样系统sql server连接失败', err)
    // global.logger.error('sql server连接失败')
})




// 封装promise执行sql函数
const queryData = function (sql, params) {
    return new Promise((resolve, reject) => {
        poolConnect.then(() => {
            const request = new mssql.Request(pool);
            request.input('input_parameter', mssql.Int, params)
            request.query(sql, (err, result) => {
                if (err) {
                    // 执行失败
                    reject(err)
                } else {
                    // 将recordsets数组拍平
                    let data = []
                    result.recordsets.forEach(item => {
                        data = [...data, ...item]
                    })
                    // 返回数据
                    resolve(data)
                }
            })
        }).catch(err => {
            reject(err)
        })
    })
}

// 封装promise执行sql函数
const fetchData = function (sql, params) {
    return new Promise((resolve, reject) => {
        poolConnect2.then(() => {
            const request = new mssql.Request(pool2);
            request.input('input_parameter', mssql.Int, params)
            request.query(sql, (err, result) => {
                if (err) {
                    // 执行失败
                    reject(err)
                } else {
                    // 将recordsets数组拍平
                    let data = []
                    result.recordsets.forEach(item => {
                        data = [...data, ...item]
                    })
                    // 返回数据
                    resolve(data)
                }
            })
        }).catch(err => {
            reject(err)
        })
    })
}

// 封装promise执行sql函数
const knitFetch = function (sql, params) {
    return new Promise((resolve, reject) => {
        poolConnect3.then(() => {
            const request = new mssql.Request(pool3);
            request.input('input_parameter', mssql.Int, params)
            request.query(sql, (err, result) => {
                if (err) {
                    // 执行失败
                    reject(err)
                } else {
                    // 将recordsets数组拍平
                    let data = []
                    result.recordsets.forEach(item => {
                        data = [...data, ...item]
                    })
                    // 返回数据
                    resolve(data)
                }
            })
        }).catch(err => {
            reject(err)
        })
    })
}

module.exports = {
    queryData,
    fetchData,
    knitFetch
}




