/*
 * @Author: flyknit <EMAIL>
 * @Date: 2024-06-05 17:14:37
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-06-08 09:39:12
 * @FilePath: \electronic-filed:\gitee\nikesystem\src\stores\index.js
 * @Description: "文件描述"
 * Copyright (c) 2024 by ${<EMAIL>}, All Rights Reserved.
 */
import { defineStore, createPinia } from "pinia";
// import { GlobalState, ThemeConfigProps } from "./interface";
import { DEFAULT_PRIMARY } from "@/config/config";
import piniaPersistConfig from "@/config/piniaPersist";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";
import i18n from "@/languages";
// defineStore 调用后返回一个函数，调用该函数获得 Store 实体
export const GlobalStore = defineStore({
	// id: 必须的，在所有 Store 中唯一
	id: "GlobalState",
	// state: 返回对象的函数
	state: () => ({
		// token
		token: "",
		// userInfo
		userInfo: "",
		// element组件大小
		assemblySize: "default",
		// language
		language: "",
		// themeConfig
		themeConfig: {
			// 布局切换 ==>  纵向：vertical | 经典：classic | 横向：transverse | 分栏：columns
			layout: "vertical",
			// 默认 primary 主题颜色
			primary: DEFAULT_PRIMARY,
			// 黑夜模式
			isDark: true,
			// 折叠菜单
			isCollapse: false,
			// 面包屑导航
			breadcrumb: true,
			// 面包屑导航图标
			breadcrumbIcon: true,
			// 标签页
			tabs: true,
			// 标签页图标
			tabsIcon: true,
			// 页脚
			footer: true,
			// 当前页面是否全屏
			maximize: false
		}
	}),
	getters: {},
	actions: {
		// init config
		initConfig() {
			const themeConfig = this.themeConfig;
			const theme = localStorage.getItem("themeConfig");
			const token = localStorage.getItem("ACCESS-TOKEN");
			const userInfo = localStorage.getItem("userInfo");
			const lang = localStorage.getItem("lang");
			if (lang) {
				this.updateLanguage(lang);
				i18n.locale = lang;
			}
			if (token) {
				this.setToken(token);
			}
			if (userInfo) {
				this.setUserInfo(JSON.parse(userInfo));
			}
			if (theme) {
				this.setThemeConfig(JSON.parse(theme));
			} else {
				this.setThemeConfig(themeConfig);
			}
		},
		// setToken
		setToken(token) {
			this.token = token;
			// 持久化
			localStorage.setItem("ACCESS-TOKEN", token);
		},
		// setUserInfo
		setUserInfo(userInfo) {
			this.userInfo = userInfo;
			// 持久化
			localStorage.setItem("userInfo", JSON.stringify(userInfo));
		},
		// setAssemblySizeSize
		setAssemblySizeSize(assemblySize) {
			this.assemblySize = assemblySize;
		},
		// updateLanguage
		updateLanguage(language) {
			this.language = language;
			localStorage.setItem("lang", language);
			i18n.locale = language;
		},
		// setThemeConfig
		setThemeConfig(themeConfig) {
			this.themeConfig = themeConfig;
			localStorage.setItem("themeConfig", JSON.stringify(themeConfig));
		}
	},
	persist: piniaPersistConfig("GlobalState")
});

// piniaPersist(持久化)
const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

export default pinia;
