import{Q as d,aw as c,_ as p,C as u,R as v,c as m,o as s,g as o,H as i,n as r,S as f,f as y,ad as S,U as _}from"./index-444b28c3.js";const g=d({direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},contentPosition:{type:String,values:["left","center","right"],default:"center"},borderStyle:{type:c(String),default:"solid"}}),h={name:"ElDivider"},b=u({...h,props:g,setup(n){const a=n,e=v("divider"),l=m(()=>e.cssVar({"border-style":a.borderStyle}));return(t,P)=>(s(),o("div",{class:i([r(e).b(),r(e).m(t.direction)]),style:S(r(l)),role:"separator"},[t.$slots.default&&t.direction!=="vertical"?(s(),o("div",{key:0,class:i([r(e).e("text"),r(e).is(t.contentPosition)])},[f(t.$slots,"default")],2)):y("v-if",!0)],6))}});var k=p(b,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/divider/src/divider.vue"]]);const C=_(k);export{C as E};
