import{au as Oe,cg as Rt,ch as tt,b5 as rt,ci as Lt,b4 as Ie,bS as nt,Q as it,aw as pe,bP as at,b8 as Nt,aS as ge,bC as st,i as W,c as S,_ as ot,C as Ae,a$ as ft,R as qe,a8 as re,a9 as ut,j as lt,bW as dt,cj as je,o as ve,g as ct,S as k,H as z,n as E,bk as pt,X as ne,c5 as me,k as gt,ac as vt,bN as Ct,bM as Wt,b as ie,F as Bt,G as mt,b0 as Vt,ck as Dt,aZ as Me,cc as le,w as de,a as Ut,L as Gt,ad as Re,d as Kt,t as Le,f as Ne,m as Ce,aK as zt,U as Yt,ag as Zt}from"./index-444b28c3.js";import{d as Jt,t as Ht}from"./event-fe80fd0c.js";import{a as Qt}from"./index-4d7f16ce.js";import{e as yt,f as Xt,h as kt,o as er,k as ht,j as bt,s as tr,a as rr,l as nr,U as We,b as Ee,n as ae,m as wt,c as ir,S as ar,g as sr}from"./_Uint8Array-55276dff.js";var Be=Object.create,or=function(){function r(){}return function(e){if(!Oe(e))return{};if(Be)return Be(e);r.prototype=e;var t=new r;return r.prototype=void 0,t}}();const fr=or;function ur(r,e){var t=-1,n=r.length;for(e||(e=Array(n));++t<n;)e[t]=r[t];return e}function lr(r,e){for(var t=-1,n=r==null?0:r.length;++t<n&&e(r[t],t,r)!==!1;);return r}function se(r,e,t,n){var i=!t;t||(t={});for(var s=-1,a=e.length;++s<a;){var o=e[s],f=n?n(t[o],r[o],o,t,r):void 0;f===void 0&&(f=r[o]),i?Rt(t,o,f):tt(t,o,f)}return t}function dr(r){var e=[];if(r!=null)for(var t in Object(r))e.push(t);return e}var cr=Object.prototype,pr=cr.hasOwnProperty;function gr(r){if(!Oe(r))return dr(r);var e=yt(r),t=[];for(var n in r)n=="constructor"&&(e||!pr.call(r,n))||t.push(n);return t}function Se(r){return Xt(r)?kt(r,!0):gr(r)}var vr=er(Object.getPrototypeOf,Object);const Ft=vr;function ye(){if(!arguments.length)return[];var r=arguments[0];return rt(r)?r:[r]}function mr(r,e){return r&&se(e,ht(e),r)}function yr(r,e){return r&&se(e,Se(e),r)}var xt=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Ve=xt&&typeof module=="object"&&module&&!module.nodeType&&module,hr=Ve&&Ve.exports===xt,De=hr?Lt.Buffer:void 0,Ue=De?De.allocUnsafe:void 0;function br(r,e){if(e)return r.slice();var t=r.length,n=Ue?Ue(t):new r.constructor(t);return r.copy(n),n}function wr(r,e){return se(r,bt(r),e)}var Fr=Object.getOwnPropertySymbols,xr=Fr?function(r){for(var e=[];r;)rr(e,bt(r)),r=Ft(r);return e}:tr;const Ot=xr;function Or(r,e){return se(r,Ot(r),e)}function Ar(r){return nr(r,Se,Ot)}var qr=Object.prototype,jr=qr.hasOwnProperty;function Er(r){var e=r.length,t=new r.constructor(e);return e&&typeof r[0]=="string"&&jr.call(r,"index")&&(t.index=r.index,t.input=r.input),t}function Te(r){var e=new r.constructor(r.byteLength);return new We(e).set(new We(r)),e}function Sr(r,e){var t=e?Te(r.buffer):r.buffer;return new r.constructor(t,r.byteOffset,r.byteLength)}var Tr=/\w*$/;function _r(r){var e=new r.constructor(r.source,Tr.exec(r));return e.lastIndex=r.lastIndex,e}var Ge=Ie?Ie.prototype:void 0,Ke=Ge?Ge.valueOf:void 0;function Pr(r){return Ke?Object(Ke.call(r)):{}}function $r(r,e){var t=e?Te(r.buffer):r.buffer;return new r.constructor(t,r.byteOffset,r.length)}var Ir="[object Boolean]",Mr="[object Date]",Rr="[object Map]",Lr="[object Number]",Nr="[object RegExp]",Cr="[object Set]",Wr="[object String]",Br="[object Symbol]",Vr="[object ArrayBuffer]",Dr="[object DataView]",Ur="[object Float32Array]",Gr="[object Float64Array]",Kr="[object Int8Array]",zr="[object Int16Array]",Yr="[object Int32Array]",Zr="[object Uint8Array]",Jr="[object Uint8ClampedArray]",Hr="[object Uint16Array]",Qr="[object Uint32Array]";function Xr(r,e,t){var n=r.constructor;switch(e){case Vr:return Te(r);case Ir:case Mr:return new n(+r);case Dr:return Sr(r,t);case Ur:case Gr:case Kr:case zr:case Yr:case Zr:case Jr:case Hr:case Qr:return $r(r,t);case Rr:return new n;case Lr:case Wr:return new n(r);case Nr:return _r(r);case Cr:return new n;case Br:return Pr(r)}}function kr(r){return typeof r.constructor=="function"&&!yt(r)?fr(Ft(r)):{}}var en="[object Map]";function tn(r){return nt(r)&&Ee(r)==en}var ze=ae&&ae.isMap,rn=ze?wt(ze):tn;const nn=rn;var an="[object Set]";function sn(r){return nt(r)&&Ee(r)==an}var Ye=ae&&ae.isSet,on=Ye?wt(Ye):sn;const fn=on;var un=1,ln=2,dn=4,At="[object Arguments]",cn="[object Array]",pn="[object Boolean]",gn="[object Date]",vn="[object Error]",qt="[object Function]",mn="[object GeneratorFunction]",yn="[object Map]",hn="[object Number]",jt="[object Object]",bn="[object RegExp]",wn="[object Set]",Fn="[object String]",xn="[object Symbol]",On="[object WeakMap]",An="[object ArrayBuffer]",qn="[object DataView]",jn="[object Float32Array]",En="[object Float64Array]",Sn="[object Int8Array]",Tn="[object Int16Array]",_n="[object Int32Array]",Pn="[object Uint8Array]",$n="[object Uint8ClampedArray]",In="[object Uint16Array]",Mn="[object Uint32Array]",x={};x[At]=x[cn]=x[An]=x[qn]=x[pn]=x[gn]=x[jn]=x[En]=x[Sn]=x[Tn]=x[_n]=x[yn]=x[hn]=x[jt]=x[bn]=x[wn]=x[Fn]=x[xn]=x[Pn]=x[$n]=x[In]=x[Mn]=!0;x[vn]=x[qt]=x[On]=!1;function ee(r,e,t,n,i,s){var a,o=e&un,f=e&ln,w=e&dn;if(t&&(a=i?t(r,n,i,s):t(r)),a!==void 0)return a;if(!Oe(r))return r;var c=rt(r);if(c){if(a=Er(r),!o)return ur(r,a)}else{var g=Ee(r),b=g==qt||g==mn;if(ir(r))return br(r,o);if(g==jt||g==At||b&&!i){if(a=f||b?{}:kr(r),!o)return f?Or(r,yr(a,r)):wr(r,mr(a,r))}else{if(!x[g])return i?r:{};a=Xr(r,g,o)}}s||(s=new ar);var q=s.get(r);if(q)return q;s.set(r,a),fn(r)?r.forEach(function(v){a.add(ee(v,e,t,v,r,s))}):nn(r)&&r.forEach(function(v,u){a.set(u,ee(v,e,t,u,r,s))});var j=w?f?Ar:sr:f?Se:ht,d=c?void 0:j(r);return lr(d||r,function(v,u){d&&(u=v,v=r[u]),tt(a,u,ee(v,e,t,u,r,s))}),a}var Rn=4;function Ze(r){return ee(r,Rn)}const Ln=it({model:Object,rules:{type:pe(Object)},labelPosition:{type:String,values:["left","right","top"],default:"right"},requireAsteriskPosition:{type:String,values:["left","right"],default:"left"},labelWidth:{type:[String,Number],default:""},labelSuffix:{type:String,default:""},inline:Boolean,inlineMessage:Boolean,statusIcon:Boolean,showMessage:{type:Boolean,default:!0},size:{type:String,values:at},disabled:Boolean,validateOnRuleChange:{type:Boolean,default:!0},hideRequiredAsterisk:{type:Boolean,default:!1},scrollToError:Boolean}),Nn={validate:(r,e,t)=>(Nt(r)||ge(r))&&st(e)&&ge(t)};function Cn(){const r=W([]),e=S(()=>{if(!r.value.length)return"0";const s=Math.max(...r.value);return s?`${s}px`:""});function t(s){return r.value.indexOf(s)}function n(s,a){if(s&&a){const o=t(a);r.value.splice(o,1,s)}else s&&r.value.push(s)}function i(s){const a=t(s);a>-1&&r.value.splice(a,1)}return{autoLabelWidth:e,registerLabelWidth:n,deregisterLabelWidth:i}}const Q=(r,e)=>{const t=ye(e);return t.length>0?r.filter(n=>n.prop&&t.includes(n.prop)):r},Wn={name:"ElForm"},Bn=Ae({...Wn,props:Ln,emits:Nn,setup(r,{expose:e,emit:t}){const n=r,i=[],s=ft(),a=qe("form"),o=S(()=>{const{labelPosition:m,inline:l}=n;return[a.b(),a.m(s.value||"default"),{[a.m(`label-${m}`)]:m,[a.m("inline")]:l}]}),f=m=>{i.push(m)},w=m=>{m.prop&&i.splice(i.indexOf(m),1)},c=(m=[])=>{n.model&&Q(i,m).forEach(l=>l.resetField())},g=(m=[])=>{Q(i,m).forEach(l=>l.clearValidate())},b=S(()=>!!n.model),q=m=>{if(i.length===0)return[];const l=Q(i,m);return l.length?l:[]},j=async m=>v(void 0,m),d=async(m=[])=>{if(!b.value)return!1;const l=q(m);if(l.length===0)return!0;let h={};for(const O of l)try{await O.validate("")}catch(A){h={...h,...A}}return Object.keys(h).length===0?!0:Promise.reject(h)},v=async(m=[],l)=>{const h=!pt(l);try{const O=await d(m);return O===!0&&(l==null||l(O)),O}catch(O){const A=O;return n.scrollToError&&u(Object.keys(A)[0]),l==null||l(!1,A),h&&Promise.reject(A)}},u=m=>{var l;const h=Q(i,m)[0];h&&((l=h.$el)==null||l.scrollIntoView())};return re(()=>n.rules,()=>{n.validateOnRuleChange&&j().catch(m=>Jt())},{deep:!0}),ut(je,lt({...dt(n),emit:t,resetFields:c,clearValidate:g,validateField:v,addField:f,removeField:w,...Cn()})),e({validate:j,validateField:v,resetFields:c,clearValidate:g,scrollToField:u}),(m,l)=>(ve(),ct("form",{class:z(E(o))},[k(m.$slots,"default")],2))}});var Vn=ot(Bn,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/form/src/form.vue"]]);function B(){return B=Object.assign?Object.assign.bind():function(r){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(r[n]=t[n])}return r},B.apply(this,arguments)}function Dn(r,e){r.prototype=Object.create(e.prototype),r.prototype.constructor=r,J(r,e)}function he(r){return he=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},he(r)}function J(r,e){return J=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},J(r,e)}function Un(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function te(r,e,t){return Un()?te=Reflect.construct.bind():te=function(i,s,a){var o=[null];o.push.apply(o,s);var f=Function.bind.apply(i,o),w=new f;return a&&J(w,a.prototype),w},te.apply(null,arguments)}function Gn(r){return Function.toString.call(r).indexOf("[native code]")!==-1}function be(r){var e=typeof Map=="function"?new Map:void 0;return be=function(n){if(n===null||!Gn(n))return n;if(typeof n!="function")throw new TypeError("Super expression must either be null or a function");if(typeof e<"u"){if(e.has(n))return e.get(n);e.set(n,i)}function i(){return te(n,arguments,he(this).constructor)}return i.prototype=Object.create(n.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),J(i,n)},be(r)}var Kn=/%[sdj%]/g,zn=function(){};typeof process<"u"&&process.env;function we(r){if(!r||!r.length)return null;var e={};return r.forEach(function(t){var n=t.field;e[n]=e[n]||[],e[n].push(t)}),e}function I(r){for(var e=arguments.length,t=new Array(e>1?e-1:0),n=1;n<e;n++)t[n-1]=arguments[n];var i=0,s=t.length;if(typeof r=="function")return r.apply(null,t);if(typeof r=="string"){var a=r.replace(Kn,function(o){if(o==="%%")return"%";if(i>=s)return o;switch(o){case"%s":return String(t[i++]);case"%d":return Number(t[i++]);case"%j":try{return JSON.stringify(t[i++])}catch{return"[Circular]"}break;default:return o}});return a}return r}function Yn(r){return r==="string"||r==="url"||r==="hex"||r==="email"||r==="date"||r==="pattern"}function T(r,e){return!!(r==null||e==="array"&&Array.isArray(r)&&!r.length||Yn(e)&&typeof r=="string"&&!r)}function Zn(r,e,t){var n=[],i=0,s=r.length;function a(o){n.push.apply(n,o||[]),i++,i===s&&t(n)}r.forEach(function(o){e(o,a)})}function Je(r,e,t){var n=0,i=r.length;function s(a){if(a&&a.length){t(a);return}var o=n;n=n+1,o<i?e(r[o],s):t([])}s([])}function Jn(r){var e=[];return Object.keys(r).forEach(function(t){e.push.apply(e,r[t]||[])}),e}var He=function(r){Dn(e,r);function e(t,n){var i;return i=r.call(this,"Async Validation Error")||this,i.errors=t,i.fields=n,i}return e}(be(Error));function Hn(r,e,t,n,i){if(e.first){var s=new Promise(function(b,q){var j=function(u){return n(u),u.length?q(new He(u,we(u))):b(i)},d=Jn(r);Je(d,t,j)});return s.catch(function(b){return b}),s}var a=e.firstFields===!0?Object.keys(r):e.firstFields||[],o=Object.keys(r),f=o.length,w=0,c=[],g=new Promise(function(b,q){var j=function(v){if(c.push.apply(c,v),w++,w===f)return n(c),c.length?q(new He(c,we(c))):b(i)};o.length||(n(c),b(i)),o.forEach(function(d){var v=r[d];a.indexOf(d)!==-1?Je(v,t,j):Zn(v,t,j)})});return g.catch(function(b){return b}),g}function Qn(r){return!!(r&&r.message!==void 0)}function Xn(r,e){for(var t=r,n=0;n<e.length;n++){if(t==null)return t;t=t[e[n]]}return t}function Qe(r,e){return function(t){var n;return r.fullFields?n=Xn(e,r.fullFields):n=e[t.field||r.fullField],Qn(t)?(t.field=t.field||r.fullField,t.fieldValue=n,t):{message:typeof t=="function"?t():t,fieldValue:n,field:t.field||r.fullField}}}function Xe(r,e){if(e){for(var t in e)if(e.hasOwnProperty(t)){var n=e[t];typeof n=="object"&&typeof r[t]=="object"?r[t]=B({},r[t],n):r[t]=n}}return r}var Et=function(e,t,n,i,s,a){e.required&&(!n.hasOwnProperty(e.field)||T(t,a||e.type))&&i.push(I(s.messages.required,e.fullField))},kn=function(e,t,n,i,s){(/^\s+$/.test(t)||t==="")&&i.push(I(s.messages.whitespace,e.fullField))},X,ei=function(){if(X)return X;var r="[a-fA-F\\d:]",e=function(h){return h&&h.includeBoundaries?"(?:(?<=\\s|^)(?="+r+")|(?<="+r+")(?=\\s|$))":""},t="(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}",n="[a-fA-F\\d]{1,4}",i=(`
(?:
(?:`+n+":){7}(?:"+n+`|:)|                                    // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8
(?:`+n+":){6}(?:"+t+"|:"+n+`|:)|                             // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::1.2.3.4
(?:`+n+":){5}(?::"+t+"|(?::"+n+`){1,2}|:)|                   // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::7:1.2.3.4
(?:`+n+":){4}(?:(?::"+n+"){0,1}:"+t+"|(?::"+n+`){1,3}|:)| // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::6:7:1.2.3.4
(?:`+n+":){3}(?:(?::"+n+"){0,2}:"+t+"|(?::"+n+`){1,4}|:)| // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::5:6:7:1.2.3.4
(?:`+n+":){2}(?:(?::"+n+"){0,3}:"+t+"|(?::"+n+`){1,5}|:)| // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::4:5:6:7:1.2.3.4
(?:`+n+":){1}(?:(?::"+n+"){0,4}:"+t+"|(?::"+n+`){1,6}|:)| // 1::              1::3:4:5:6:7:8   1::8            1::3:4:5:6:7:1.2.3.4
(?::(?:(?::`+n+"){0,5}:"+t+"|(?::"+n+`){1,7}|:))             // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::1.2.3.4
)(?:%[0-9a-zA-Z]{1,})?                                             // %eth0            %1
`).replace(/\s*\/\/.*$/gm,"").replace(/\n/g,"").trim(),s=new RegExp("(?:^"+t+"$)|(?:^"+i+"$)"),a=new RegExp("^"+t+"$"),o=new RegExp("^"+i+"$"),f=function(h){return h&&h.exact?s:new RegExp("(?:"+e(h)+t+e(h)+")|(?:"+e(h)+i+e(h)+")","g")};f.v4=function(l){return l&&l.exact?a:new RegExp(""+e(l)+t+e(l),"g")},f.v6=function(l){return l&&l.exact?o:new RegExp(""+e(l)+i+e(l),"g")};var w="(?:(?:[a-z]+:)?//)",c="(?:\\S+(?::\\S*)?@)?",g=f.v4().source,b=f.v6().source,q="(?:(?:[a-z\\u00a1-\\uffff0-9][-_]*)*[a-z\\u00a1-\\uffff0-9]+)",j="(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*",d="(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))",v="(?::\\d{2,5})?",u='(?:[/?#][^\\s"]*)?',m="(?:"+w+"|www\\.)"+c+"(?:localhost|"+g+"|"+b+"|"+q+j+d+")"+v+u;return X=new RegExp("(?:^"+m+"$)","i"),X},ke={email:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/,hex:/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i},Y={integer:function(e){return Y.number(e)&&parseInt(e,10)===e},float:function(e){return Y.number(e)&&!Y.integer(e)},array:function(e){return Array.isArray(e)},regexp:function(e){if(e instanceof RegExp)return!0;try{return!!new RegExp(e)}catch{return!1}},date:function(e){return typeof e.getTime=="function"&&typeof e.getMonth=="function"&&typeof e.getYear=="function"&&!isNaN(e.getTime())},number:function(e){return isNaN(e)?!1:typeof e=="number"},object:function(e){return typeof e=="object"&&!Y.array(e)},method:function(e){return typeof e=="function"},email:function(e){return typeof e=="string"&&e.length<=320&&!!e.match(ke.email)},url:function(e){return typeof e=="string"&&e.length<=2048&&!!e.match(ei())},hex:function(e){return typeof e=="string"&&!!e.match(ke.hex)}},ti=function(e,t,n,i,s){if(e.required&&t===void 0){Et(e,t,n,i,s);return}var a=["integer","float","array","regexp","object","method","email","number","date","url","hex"],o=e.type;a.indexOf(o)>-1?Y[o](t)||i.push(I(s.messages.types[o],e.fullField,e.type)):o&&typeof t!==e.type&&i.push(I(s.messages.types[o],e.fullField,e.type))},ri=function(e,t,n,i,s){var a=typeof e.len=="number",o=typeof e.min=="number",f=typeof e.max=="number",w=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,c=t,g=null,b=typeof t=="number",q=typeof t=="string",j=Array.isArray(t);if(b?g="number":q?g="string":j&&(g="array"),!g)return!1;j&&(c=t.length),q&&(c=t.replace(w,"_").length),a?c!==e.len&&i.push(I(s.messages[g].len,e.fullField,e.len)):o&&!f&&c<e.min?i.push(I(s.messages[g].min,e.fullField,e.min)):f&&!o&&c>e.max?i.push(I(s.messages[g].max,e.fullField,e.max)):o&&f&&(c<e.min||c>e.max)&&i.push(I(s.messages[g].range,e.fullField,e.min,e.max))},G="enum",ni=function(e,t,n,i,s){e[G]=Array.isArray(e[G])?e[G]:[],e[G].indexOf(t)===-1&&i.push(I(s.messages[G],e.fullField,e[G].join(", ")))},ii=function(e,t,n,i,s){if(e.pattern){if(e.pattern instanceof RegExp)e.pattern.lastIndex=0,e.pattern.test(t)||i.push(I(s.messages.pattern.mismatch,e.fullField,t,e.pattern));else if(typeof e.pattern=="string"){var a=new RegExp(e.pattern);a.test(t)||i.push(I(s.messages.pattern.mismatch,e.fullField,t,e.pattern))}}},y={required:Et,whitespace:kn,type:ti,range:ri,enum:ni,pattern:ii},ai=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(T(t,"string")&&!e.required)return n();y.required(e,t,i,a,s,"string"),T(t,"string")||(y.type(e,t,i,a,s),y.range(e,t,i,a,s),y.pattern(e,t,i,a,s),e.whitespace===!0&&y.whitespace(e,t,i,a,s))}n(a)},si=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(T(t)&&!e.required)return n();y.required(e,t,i,a,s),t!==void 0&&y.type(e,t,i,a,s)}n(a)},oi=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(t===""&&(t=void 0),T(t)&&!e.required)return n();y.required(e,t,i,a,s),t!==void 0&&(y.type(e,t,i,a,s),y.range(e,t,i,a,s))}n(a)},fi=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(T(t)&&!e.required)return n();y.required(e,t,i,a,s),t!==void 0&&y.type(e,t,i,a,s)}n(a)},ui=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(T(t)&&!e.required)return n();y.required(e,t,i,a,s),T(t)||y.type(e,t,i,a,s)}n(a)},li=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(T(t)&&!e.required)return n();y.required(e,t,i,a,s),t!==void 0&&(y.type(e,t,i,a,s),y.range(e,t,i,a,s))}n(a)},di=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(T(t)&&!e.required)return n();y.required(e,t,i,a,s),t!==void 0&&(y.type(e,t,i,a,s),y.range(e,t,i,a,s))}n(a)},ci=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(t==null&&!e.required)return n();y.required(e,t,i,a,s,"array"),t!=null&&(y.type(e,t,i,a,s),y.range(e,t,i,a,s))}n(a)},pi=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(T(t)&&!e.required)return n();y.required(e,t,i,a,s),t!==void 0&&y.type(e,t,i,a,s)}n(a)},gi="enum",vi=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(T(t)&&!e.required)return n();y.required(e,t,i,a,s),t!==void 0&&y[gi](e,t,i,a,s)}n(a)},mi=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(T(t,"string")&&!e.required)return n();y.required(e,t,i,a,s),T(t,"string")||y.pattern(e,t,i,a,s)}n(a)},yi=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(T(t,"date")&&!e.required)return n();if(y.required(e,t,i,a,s),!T(t,"date")){var f;t instanceof Date?f=t:f=new Date(t),y.type(e,f,i,a,s),f&&y.range(e,f.getTime(),i,a,s)}}n(a)},hi=function(e,t,n,i,s){var a=[],o=Array.isArray(t)?"array":typeof t;y.required(e,t,i,a,s,o),n(a)},ce=function(e,t,n,i,s){var a=e.type,o=[],f=e.required||!e.required&&i.hasOwnProperty(e.field);if(f){if(T(t,a)&&!e.required)return n();y.required(e,t,i,o,s,a),T(t,a)||y.type(e,t,i,o,s)}n(o)},bi=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(T(t)&&!e.required)return n();y.required(e,t,i,a,s)}n(a)},Z={string:ai,method:si,number:oi,boolean:fi,regexp:ui,integer:li,float:di,array:ci,object:pi,enum:vi,pattern:mi,date:yi,url:ce,hex:ce,email:ce,required:hi,any:bi};function Fe(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){var e=JSON.parse(JSON.stringify(this));return e.clone=this.clone,e}}}var xe=Fe(),H=function(){function r(t){this.rules=null,this._messages=xe,this.define(t)}var e=r.prototype;return e.define=function(n){var i=this;if(!n)throw new Error("Cannot configure a schema with no rules");if(typeof n!="object"||Array.isArray(n))throw new Error("Rules must be an object");this.rules={},Object.keys(n).forEach(function(s){var a=n[s];i.rules[s]=Array.isArray(a)?a:[a]})},e.messages=function(n){return n&&(this._messages=Xe(Fe(),n)),this._messages},e.validate=function(n,i,s){var a=this;i===void 0&&(i={}),s===void 0&&(s=function(){});var o=n,f=i,w=s;if(typeof f=="function"&&(w=f,f={}),!this.rules||Object.keys(this.rules).length===0)return w&&w(null,o),Promise.resolve(o);function c(d){var v=[],u={};function m(h){if(Array.isArray(h)){var O;v=(O=v).concat.apply(O,h)}else v.push(h)}for(var l=0;l<d.length;l++)m(d[l]);v.length?(u=we(v),w(v,u)):w(null,o)}if(f.messages){var g=this.messages();g===xe&&(g=Fe()),Xe(g,f.messages),f.messages=g}else f.messages=this.messages();var b={},q=f.keys||Object.keys(this.rules);q.forEach(function(d){var v=a.rules[d],u=o[d];v.forEach(function(m){var l=m;typeof l.transform=="function"&&(o===n&&(o=B({},o)),u=o[d]=l.transform(u)),typeof l=="function"?l={validator:l}:l=B({},l),l.validator=a.getValidationMethod(l),l.validator&&(l.field=d,l.fullField=l.fullField||d,l.type=a.getType(l),b[d]=b[d]||[],b[d].push({rule:l,value:u,source:o,field:d}))})});var j={};return Hn(b,f,function(d,v){var u=d.rule,m=(u.type==="object"||u.type==="array")&&(typeof u.fields=="object"||typeof u.defaultField=="object");m=m&&(u.required||!u.required&&d.value),u.field=d.field;function l(A,R){return B({},R,{fullField:u.fullField+"."+A,fullFields:u.fullFields?[].concat(u.fullFields,[A]):[A]})}function h(A){A===void 0&&(A=[]);var R=Array.isArray(A)?A:[A];!f.suppressWarning&&R.length&&r.warning("async-validator:",R),R.length&&u.message!==void 0&&(R=[].concat(u.message));var $=R.map(Qe(u,o));if(f.first&&$.length)return j[u.field]=1,v($);if(!m)v($);else{if(u.required&&!d.value)return u.message!==void 0?$=[].concat(u.message).map(Qe(u,o)):f.error&&($=[f.error(u,I(f.messages.required,u.field))]),v($);var C={};u.defaultField&&Object.keys(d.value).map(function(L){C[L]=u.defaultField}),C=B({},C,d.rule.fields);var K={};Object.keys(C).forEach(function(L){var M=C[L],oe=Array.isArray(M)?M:[M];K[L]=oe.map(l.bind(null,L))});var V=new r(K);V.messages(f.messages),d.rule.options&&(d.rule.options.messages=f.messages,d.rule.options.error=f.error),V.validate(d.value,d.rule.options||f,function(L){var M=[];$&&$.length&&M.push.apply(M,$),L&&L.length&&M.push.apply(M,L),v(M.length?M:null)})}}var O;if(u.asyncValidator)O=u.asyncValidator(u,d.value,h,d.source,f);else if(u.validator){try{O=u.validator(u,d.value,h,d.source,f)}catch(A){console.error==null||console.error(A),f.suppressValidatorError||setTimeout(function(){throw A},0),h(A.message)}O===!0?h():O===!1?h(typeof u.message=="function"?u.message(u.fullField||u.field):u.message||(u.fullField||u.field)+" fails"):O instanceof Array?h(O):O instanceof Error&&h(O.message)}O&&O.then&&O.then(function(){return h()},function(A){return h(A)})},function(d){c(d)},o)},e.getType=function(n){if(n.type===void 0&&n.pattern instanceof RegExp&&(n.type="pattern"),typeof n.validator!="function"&&n.type&&!Z.hasOwnProperty(n.type))throw new Error(I("Unknown rule type %s",n.type));return n.type||"string"},e.getValidationMethod=function(n){if(typeof n.validator=="function")return n.validator;var i=Object.keys(n),s=i.indexOf("message");return s!==-1&&i.splice(s,1),i.length===1&&i[0]==="required"?Z.required:Z[this.getType(n)]||void 0},r}();H.register=function(e,t){if(typeof t!="function")throw new Error("Cannot register a validator by type, validator is not a function");Z[e]=t};H.warning=zn;H.messages=xe;H.validators=Z;const wi=["","error","validating","success"],Fi=it({label:String,labelWidth:{type:[String,Number],default:""},prop:{type:pe([String,Array])},required:{type:Boolean,default:void 0},rules:{type:pe([Object,Array])},error:String,validateStatus:{type:String,values:wi},for:String,inlineMessage:{type:[String,Boolean],default:""},showMessage:{type:Boolean,default:!0},size:{type:String,values:at}}),et="ElLabelWrap";var xi=Ae({name:et,props:{isAutoWidth:Boolean,updateAll:Boolean},setup(r,{slots:e}){const t=ne(je,void 0);ne(me)||Ht(et,"usage: <el-form-item><label-wrap /></el-form-item>");const i=qe("form"),s=W(),a=W(0),o=()=>{var c;if((c=s.value)!=null&&c.firstElementChild){const g=window.getComputedStyle(s.value.firstElementChild).width;return Math.ceil(Number.parseFloat(g))}else return 0},f=(c="update")=>{mt(()=>{e.default&&r.isAutoWidth&&(c==="update"?a.value=o():c==="remove"&&(t==null||t.deregisterLabelWidth(a.value)))})},w=()=>f("update");return gt(()=>{w()}),vt(()=>{f("remove")}),Ct(()=>w()),re(a,(c,g)=>{r.updateAll&&(t==null||t.registerLabelWidth(c,g))}),Wt(S(()=>{var c,g;return(g=(c=s.value)==null?void 0:c.firstElementChild)!=null?g:null}),w),()=>{var c,g;if(!e)return null;const{isAutoWidth:b}=r;if(b){const q=t==null?void 0:t.autoLabelWidth,j={};if(q&&q!=="auto"){const d=Math.max(0,Number.parseInt(q,10)-a.value),v=t.labelPosition==="left"?"marginRight":"marginLeft";d&&(j[v]=`${d}px`)}return ie("div",{ref:s,class:[i.be("item","label-wrap")],style:j},[(c=e.default)==null?void 0:c.call(e)])}else return ie(Bt,{ref:s},[(g=e.default)==null?void 0:g.call(e)])}}});const Oi=["role","aria-labelledby"],Ai={name:"ElFormItem"},qi=Ae({...Ai,props:Fi,setup(r,{expose:e}){const t=r,n=Vt(),i=ne(je,void 0),s=ne(me,void 0),a=ft(void 0,{formItem:!1}),o=qe("form-item"),f=Qt().value,w=W([]),c=W(""),g=Dt(c,100),b=W(""),q=W();let j,d=!1;const v=S(()=>{if((i==null?void 0:i.labelPosition)==="top")return{};const p=Me(t.labelWidth||(i==null?void 0:i.labelWidth)||"");return p?{width:p}:{}}),u=S(()=>{if((i==null?void 0:i.labelPosition)==="top"||i!=null&&i.inline)return{};if(!t.label&&!t.labelWidth&&C)return{};const p=Me(t.labelWidth||(i==null?void 0:i.labelWidth)||"");return!t.label&&!n.label?{marginLeft:p}:{}}),m=S(()=>[o.b(),o.m(a.value),o.is("error",c.value==="error"),o.is("validating",c.value==="validating"),o.is("success",c.value==="success"),o.is("required",oe.value||t.required),o.is("no-asterisk",i==null?void 0:i.hideRequiredAsterisk),(i==null?void 0:i.requireAsteriskPosition)==="right"?"asterisk-right":"asterisk-left",{[o.m("feedback")]:i==null?void 0:i.statusIcon}]),l=S(()=>st(t.inlineMessage)?t.inlineMessage:(i==null?void 0:i.inlineMessage)||!1),h=S(()=>[o.e("error"),{[o.em("error","inline")]:l.value}]),O=S(()=>t.prop?ge(t.prop)?t.prop:t.prop.join("."):""),A=S(()=>!!(t.label||n.label)),R=S(()=>t.for||w.value.length===1?w.value[0]:void 0),$=S(()=>!R.value&&A.value),C=!!s,K=S(()=>{const p=i==null?void 0:i.model;if(!(!p||!t.prop))return le(p,t.prop).value}),V=S(()=>{const{required:p}=t,F=[];t.rules&&F.push(...ye(t.rules));const P=i==null?void 0:i.rules;if(P&&t.prop){const _=le(P,t.prop).value;_&&F.push(...ye(_))}if(p!==void 0){const _=F.map((N,U)=>[N,U]).filter(([N])=>Object.keys(N).includes("required"));if(_.length>0)for(const[N,U]of _)N.required!==p&&(F[U]={...N,required:p});else F.push({required:p})}return F}),L=S(()=>V.value.length>0),M=p=>V.value.filter(P=>!P.trigger||!p?!0:Array.isArray(P.trigger)?P.trigger.includes(p):P.trigger===p).map(({trigger:P,..._})=>_),oe=S(()=>V.value.some(p=>p.required)),Tt=S(()=>{var p;return g.value==="error"&&t.showMessage&&((p=i==null?void 0:i.showMessage)!=null?p:!0)}),_e=S(()=>`${t.label||""}${(i==null?void 0:i.labelSuffix)||""}`),D=p=>{c.value=p},_t=p=>{var F,P;const{errors:_,fields:N}=p;(!_||!N)&&console.error(p),D("error"),b.value=_?(P=(F=_==null?void 0:_[0])==null?void 0:F.message)!=null?P:`${t.prop} is required`:"",i==null||i.emit("validate",t.prop,!1,b.value)},Pt=()=>{D("success"),i==null||i.emit("validate",t.prop,!0,"")},$t=async p=>{const F=O.value;return new H({[F]:p}).validate({[F]:K.value},{firstFields:!0}).then(()=>(Pt(),!0)).catch(_=>(_t(_),Promise.reject(_)))},Pe=async(p,F)=>{if(d)return!1;const P=pt(F);if(!L.value)return F==null||F(!1),!1;const _=M(p);return _.length===0?(F==null||F(!0),!0):(D("validating"),$t(_).then(()=>(F==null||F(!0),!0)).catch(N=>{const{fields:U}=N;return F==null||F(!1,U),P?!1:Promise.reject(U)}))},fe=()=>{D(""),b.value="",d=!1},$e=async()=>{const p=i==null?void 0:i.model;if(!p||!t.prop)return;const F=le(p,t.prop);d=!0,F.value=Ze(j),await mt(),fe(),d=!1},It=p=>{w.value.includes(p)||w.value.push(p)},Mt=p=>{w.value=w.value.filter(F=>F!==p)};re(()=>t.error,p=>{b.value=p||"",D(p?"error":"")},{immediate:!0}),re(()=>t.validateStatus,p=>D(p||""));const ue=lt({...dt(t),$el:q,size:a,validateState:c,labelId:f,inputIds:w,isGroup:$,addInputId:It,removeInputId:Mt,resetField:$e,clearValidate:fe,validate:Pe});return ut(me,ue),gt(()=>{t.prop&&(i==null||i.addField(ue),j=Ze(K.value))}),vt(()=>{i==null||i.removeField(ue)}),e({size:a,validateMessage:b,validateState:c,validate:Pe,clearValidate:fe,resetField:$e}),(p,F)=>{var P;return ve(),ct("div",{ref_key:"formItemRef",ref:q,class:z(E(m)),role:E($)?"group":void 0,"aria-labelledby":E($)?E(f):void 0},[ie(E(xi),{"is-auto-width":E(v).width==="auto","update-all":((P=E(i))==null?void 0:P.labelWidth)==="auto"},{default:de(()=>[E(A)?(ve(),Ut(Gt(E(R)?"label":"div"),{key:0,id:E(f),for:E(R),class:z(E(o).e("label")),style:Re(E(v))},{default:de(()=>[k(p.$slots,"label",{label:E(_e)},()=>[Kt(Le(E(_e)),1)])]),_:3},8,["id","for","class","style"])):Ne("v-if",!0)]),_:3},8,["is-auto-width","update-all"]),Ce("div",{class:z(E(o).e("content")),style:Re(E(u))},[k(p.$slots,"default"),ie(zt,{name:`${E(o).namespace.value}-zoom-in-top`},{default:de(()=>[E(Tt)?k(p.$slots,"error",{key:0,error:b.value},()=>[Ce("div",{class:z(E(h))},Le(b.value),3)]):Ne("v-if",!0)]),_:3},8,["name"])],6)],10,Oi)}}});var St=ot(qi,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/form/src/form-item.vue"]]);const _i=Yt(Vn,{FormItem:St}),Pi=Zt(St);export{Pi as E,_i as a};
