import{r as a,o as t,g as r,e as c,a as n,w as l,d as i,C as _,aU as p}from"./index-444b28c3.js";import{E as m}from"./el-button-9bbdfcf9.js";import"./index-4d7f16ce.js";const u=_({name:"throttle"}),x=Object.assign(u,{setup(d){const e=()=>{p({message:"我是节流指令",type:"success"})};return(f,h)=>{const s=m,o=a("throttle");return t(),r("div",null,[c((t(),n(s,{type:"primary"},{default:l(()=>[i("节流按钮 (每隔1S秒后执行)")]),_:1})),[[o,e]])])}}});export{x as default};
