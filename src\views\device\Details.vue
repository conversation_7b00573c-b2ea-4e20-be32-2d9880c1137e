<template>
    <div class="ordering max-wid">
        <div class="heading">
            <p @click="reTurn()" v-if="whether" style="font-size: 16px;font-weight:normal;cursor:pointer;">
                <i class="el-icon-back" style="margin-right:10px;"></i>返回上一页
            </p>
            <el-divider direction="vertical" v-if="whether"></el-divider>
            <p class="heading-users">{{ setup }}</p>
            <p class="el-icon-s-flag normal-text"
                :style="{ color: deviceInfo.status === 0 ? '#E6A23C;' : deviceInfo.status === 1 ? 'color:#909399' : 'color:#67C23A' }">
                【{{ deviceInfo.status === 0 ? '待维修' : deviceInfo.status === 1 ? '维修中' : '已完成' }}】</p>
        </div>
        <!-- 主体部分 -->
        <el-row :gutter="20" v-loading="loading">
            <el-col :span="24">
                <el-col :span="12">
                    <el-col :span="24" class="repair-detail">
                        <el-col :span="6">
                            <h5>设备名称：</h5>
                            <h5>{{ deviceInfo.device_name }}</h5>
                        </el-col>
                        <el-col :span="6">
                            <h5>设备编号：</h5>
                            <h5>{{ deviceInfo.device_number }}</h5>
                        </el-col>
                        <el-col :span="6">
                            <h5>报修故障：</h5>
                            <h5>{{ deviceInfo.content }}</h5>
                        </el-col>
                        <el-col :span="6">
                            <h5>报修部门：</h5>
                            <h5>{{ deviceInfo.department }}</h5>
                        </el-col>
                    </el-col>
                    <el-col :span="24" class="repair-detail">
                        <el-col :span="6">
                            <h5>维修部门：</h5>
                            <h5>{{ deviceInfo.repair_depart }}</h5>
                        </el-col>
                        <el-col :span="6">
                            <h5>报修人：</h5>
                            <h5>{{ deviceInfo.post_user }}</h5>
                        </el-col>
                        <el-col :span="6">
                            <h5>维修方式：</h5>
                            <h5>{{ deviceInfo.repair_way }}</h5>
                        </el-col>
                        <el-col :span="6">
                            <h5>是否紧急：</h5>
                            <h5>{{ deviceInfo.emergency }}</h5>
                        </el-col>
                    </el-col>
                    <el-col :span="24" class="repair-detail">
                        <el-col :span="6">
                            <h5>报修时间：</h5>
                            <h5>{{ deviceInfo.create_time }}</h5>
                        </el-col>
                        <el-col :span="6">
                            <h5>接单人：</h5>
                            <h5>{{ deviceInfo.receiver_uid ? deviceInfo.receiver_uid : '暂无' }}</h5>
                        </el-col>
                        <el-col :span="6">
                            <h5>维修耗时：</h5>
                            <h5>{{ deviceInfo.consume_time > 0 ? deviceInfo.consume_time : 0 }}</h5>
                        </el-col>
                    </el-col>
                </el-col>
                <el-col :span="12" style="margin-top: 40px;">
                    <el-col :span="24">
                        <h4 style="color: #666;letter-spacing:1px;">
                            故障图片列表
                        </h4>
                    </el-col>
                    <el-col :span="24" class="img-list">
                        <img style="width: 100px; height: 100px;margin: 0 20px;cursor:pointer;" v-for="(item, index) in deviceInfo.img_url" :key="index" :src="item" 
                            :preview-src-list="item" @click="viewHandler(item)">
                    </el-col>
                     <!-- 大图展开 -->
                     <el-dialog :visible.sync="dialogVisible" :modal="false">
                        <img width="100%" :src="dialogImageUrl" alt="上传失败" />
                    </el-dialog>
                </el-col>
            </el-col>
        </el-row>

    </div>
</template>
<script>
export default {
    data() {
        return {
            // 上个页面传来的id
            id: "",
            whether: true,
            setup: "报修单详情",
            deviceInfo: {},
            dialogVisible: false,
            dialogImageUrl: '',
            loading: true,
        };
    },
    created() {
        let res = this.$route.query.datas;
        // 将img_url转换成数组
        if (res.img_url) {
            res.img_url = res.img_url.replace(/-/g, ',').split(',');
        }
        this.deviceInfo = res;
        this.setup = res.device_name + '报修单详情'
        setTimeout(() => {
            this.loading = false;
        }, 300);
    },
    mounted() {
        
    },
    methods: {
        // 查看大图
        viewHandler(url) {
            this.dialogImageUrl = url;
            this.dialogVisible = true;
        },
        // 返回上一页
        reTurn() {
            this.$router.push({ path: "/callRepair" });
        },

    }
};
</script>
<style scoped="scoped" lang="scss">
.max-wid {
    width: 100%;
    background-color: #ffffff;
    padding: 30px 40px 50px 30px;
    text-align: left;
    box-sizing: border-box;
}

.heading {
    font-size: 20px;
    font-weight: bold;
    text-align: center;
    padding-top: 20px;
    display: flex;
}

.repair-detail {
    padding: 20px 0;

    .el-col h5 {
        padding: 20px 0;
    }

    .el-col h5:nth-child(1) {
        font-size: 16px;
        color: #333;
        font-weight: normal;
    }

    .el-col h5:nth-child(2) {
        font-size: 14px;
        color: #666;
        font-weight: normal;
    }
}

.heading-users {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #517DF7;
    font-size: 16px;
}

.normal-text {
    font-size: 14px;
}
.img-list {
    margin: 20px 0;
    padding: 30px 0;
}
h3 {
    color: #666;
}
</style>
  