<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-08-16 10:52:53
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-25 15:18:20
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\device\SparePartsClassify.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-card>
            <!-- 表单、添加按钮， -->
            <div class="filter-container">
                <el-form :inline="true" :model="filterForm" class="demo-form-inline">
                    <el-form-item label="备件分类">
                        <el-input v-model="filterForm.name" placeholder="请输入备件分类" clearable ></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="filterSubmit" icon="el-icon-search">查询</el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-plus" @click="add">添加分类</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <!-- 表格，分类名称，一级分类，二级分类，，创建时间，修改时间，操作 -->
            <el-table :data="tableData" border style="width: 100%" stripe v-loading="loading"
                element-loading-text="Flyknit">
                <!-- 索引 -->
                <el-table-column type="index" label="序号" prop="id" width="80"></el-table-column>
                <el-table-column prop="classify_name" label="分类名称"></el-table-column>
                <el-table-column prop="first_class" label="一级分类"></el-table-column>
                <el-table-column prop="second_class" label="二级分类"></el-table-column>
                <el-table-column prop="create_time" label="创建时间"></el-table-column>
                <el-table-column prop="update_time" label="修改时间"></el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-button type="primary" size="mini" icon="el-icon-edit" @click="edit(scope.row)">编辑</el-button>
                        <el-button type="danger" size="mini" icon="el-icon-delete" @click="del(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
                :page-sizes="[10, 20, 30, 40]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="total">
            </el-pagination>
        </el-card>
        <!-- 添加分类弹窗 -->
        <el-dialog :title="title" :visible.sync="dialogVisible" width="30%">
            <el-form :model="form" ref="form" label-width="80px" :rules="rules">
                <el-form-item label="分类名称" prop="classifyName">
                    <el-input v-model="form.classifyName" placeholder="请输入分类名称"></el-input>
                </el-form-item>
                <el-form-item label="一级分类" prop="firstClass">
                    <el-input v-model="form.firstClass" placeholder="请输入一级分类"></el-input>
                </el-form-item>
                <el-form-item label="二级分类" prop="secondClass">
                    <el-input v-model="form.secondClass" placeholder="请输入二级分类"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitClassify">{{btnText}}</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
export default {
    data() {
        return {
            title: '添加分类',
            btnText: '添 加',
            loading: true,
            filterForm: {
                name: '',
            },
            tableData: [{
                id: 1,
                classifyName: '配件类型',
                firstClass: '生产类型',
                secondClass: '劳保类',
                createTime: '2021-08-23 10:00:00',
                updateTime: '2021-08-23 10:00:00',
            }, {
                id: 2,
                classifyName: '配件类型',
                firstClass: '生产类型',
                secondClass: '劳保类',
                createTime: '2021-08-23 10:00:00',
                updateTime: '2021-08-23 10:00:00',
            }, {
                id: 3,
                classifyName: '配件类型',
                firstClass: '生产类型',
                secondClass: '劳保类',
                createTime: '2021-08-23 10:00:00',
                updateTime: '2021-08-23 10:00:00',
            }, {
                id: 4,
                classifyName: '配件类型',
                firstClass: '生产类型',
                secondClass: '劳保类',
                createTime: '2021-08-23 10:00:00',
                updateTime: '2021-08-23 10:00:00',
            }, {
                id: 5,
                classifyName: '配件类型',
                firstClass: '生产类型',
                secondClass: '劳保类',
                createTime: '2021-08-23 10:00:00',
                updateTime: '2021-08-23 10:00:00',
            }, {
                id: 6,
                classifyName: '配件类型',
                firstClass: '生产类型',
                secondClass: '劳保类',
                createTime: '2021-08-23 10:00:00',
                updateTime: '2021-08-23 10:00:00',
            }, {
                id: 7,
                classifyName: '配件类型',
                firstClass: '生产类型',
                secondClass: '劳保类',
                createTime: '2021-08-23 10:00:00',
                updateTime: '2021-08-23 10:00:00',
            }, {
                id: 8,
                classifyName: '配件类型',
                firstClass: '生产类型',
                secondClass: '劳保类',
                createTime: '2021-08-23 10:00:00',
                updateTime: '2021-08-23 10:00:00',
            }],
            currentPage: 1,
            pageSize: 10,
            total: 8,
            dialogVisible: false,
            form: {
                classifyName: '',
                firstClass: '',
                secondClass: '',
            },
            rules: {
                classifyName: [{
                    required: true,
                    message: '请输入分类名称',
                    trigger: 'blur'
                }],
                firstClass: [{
                    required: true,
                    message: '请输入一级分类',
                    trigger: 'blur'
                }],
                secondClass: [{
                    required: true,
                    message: '请输入二级分类',
                    trigger: 'blur'
                }],
            }
        }
    },
    mounted() {
        this.getList();
    },
    methods: {
        // 根据条件查询分类列表
        filterSubmit() {
            if (this.filterForm.name) {
                this.loading = true;
                this.currentPage = 1;
                this.getList();
            } else {
                return false
            }
        },
        // 获取分类列表
        async getList() {
            try {
                this.filterForm.page = this.currentPage;
                this.filterForm.pageSize = this.pageSize;
                const res = await this.$http.getClassifyList(this.filterForm);
                if (res.status !== 200) return this.$message.error(res.message);
                const { list, total } = res.data;
                this.tableData = list;
                this.total = total;
                setTimeout(() => {
                    this.loading = false;
                }, 500);
            } catch (error) {
                this.loading = false;
                return this.$message.error(error);
            }
        },
        // 添加分类弹窗
        add() {
            this.dialogVisible = true;
            this.title = '添加分类';
            this.btnText = '添 加';
            // 清除表单数据
            this.$refs['form'].resetFields();
        },
        // 添加分类提交
        submitClassify() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    this.addClassify();
                } else {
                    return false;
                }
            });
        },
        // 全局添加配件分类方法
        async addClassify() {
            try {
                const res = await this.$http.addClassify(this.form);
                if (res.status !== 200) return this.$message.error(res.message);
                this.dialogVisible = false;
                this.getList();
                this.$message.success(res.message);
                // 清除表单数据
                this.$refs['form'].resetFields();
            } catch (error) {
                return this.$message.error(error);
            }
        },
        handleCurrentChange(val) {
            this.loading = true;
            this.currentPage = val;
            this.getClassifyList();
        },
        handleSizeChange(val) {
            this.loading = true;
            this.pageSize = val;
            this.getClassifyList();
        },
        // 编辑
        edit(row) {
            this.title = '编辑分类';
            this.btnText = '保 存';
            this.dialogVisible = true;
            this.form.classifyName = row.classify_name;
            this.form.firstClass = row.first_class;
            this.form.secondClass = row.second_class;
            this.form.id = row.id;
        },
        // 删除分类
        del(row) {
            this.$confirm('此操作将永久删除该分类, 是否继续?', '温馨提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async() => {
                try {
                    const res = await this.$http.delClassify({ id: row.id });
                    if (res.status !== 200) return this.$message.error(res.message);
                    this.getList();
                    this.$message.success(res.message);
                } catch (error) {
                    return this.$message.error(error);
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
    }
}
</script>
<style lang="scss" scoped>
.el-pagination {
    padding: 20px 0;
}
</style>