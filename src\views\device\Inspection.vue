<template>
    <div>
        <el-card>
            <el-form :inline="true" ref="form" :model="form" class="forms">
                <el-form-item label="" prop="device_number">
                    <el-input v-model="form.device_number" placeholder="请输入设备编号"></el-input>
                </el-form-item>
                <el-form-item label="区域" prop="area">
                    <!-- 区域选择 -->
                    <el-select v-model="form.area" placeholder="请选择区域">
                        <el-option v-for="(item, index) in areaList" :key="index" :label="item.name + '-' + item.sub"
                        :value="item.name + '-' + item.sub"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="部门" prop="depart">
                    <!-- 区域选择 -->
                    <el-select v-model="form.depart" placeholder="请选择部门">
                        <el-option label="织造车间" value="织造车间"></el-option>
                        <el-option label="整理车间" value="整理车间"></el-option>
                        <el-option label="打样车间" value="打样车间"></el-option>
                        <el-option label="综合维修" value="综合维修"></el-option>
                    </el-select>
                </el-form-item>
                <!-- 月份选择 -->
                <el-form-item label="日期" prop="date">
                    <el-date-picker v-model="form.date" type="daterange" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" value-format="yyyy-MM-dd">
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSubmit" icon="el-icon-search">查询</el-button>
                </el-form-item>
                <!-- 重置按钮，刷新按钮 -->
                <el-form-item>
                    <el-button type="info" icon="el-icon-refresh" @click="resetForm('form')">重置</el-button>
                </el-form-item>
                <!-- 刷新按钮 -->
                <el-form-item>
                    <el-button type="primary" plain icon="el-icon-refresh-right" @click="refresh"></el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="success" icon="el-icon-pie-chart" @click="toAnalyse">年度分析</el-button>
                </el-form-item>
            </el-form>
            <!-- 展示检查数量和得分区域 -->
            <el-row :gutter="20" class="score-list" style="padding: 20px 0;">
                <el-col :span="24" class="socre-list-item">
                    <el-col :span="6">
                        <el-col :span="12" class="list-title">织造车间 <i class="el-icon-star-on"></i> </el-col>
                        <el-col :span="12" class="list-content">
                            <el-row>
                                <el-col :span="12">检查数量</el-col>
                                <el-col :span="12">230</el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">最终得分</el-col>
                                <el-col :span="12">94</el-col>
                            </el-row>
                            <el-row>
                                <el-col class="youxiu">优秀</el-col>
                            </el-row>
                        </el-col>
                    </el-col>
                    <el-col :span="6">
                        <el-col :span="12" class="list-title">整理车间</el-col>
                        <el-col :span="12" class="list-content">
                            <el-row>
                                <el-col :span="12">检查数量</el-col>
                                <el-col :span="12">47</el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">最终得分</el-col>
                                <el-col :span="12">96</el-col>
                            </el-row>
                            <el-row>
                                <el-col class="youxiu">良</el-col>
                            </el-row>
                        </el-col>
                    </el-col>
                    <el-col :span="6">
                        <el-col :span="12" class="list-title">打样车间</el-col>
                        <el-col :span="12" class="list-content">
                            <el-row>
                                <el-col :span="12">检查数量</el-col>
                                <el-col :span="12">112</el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">最终得分</el-col>
                                <el-col :span="12">98</el-col>
                            </el-row>
                            <el-row>
                                <el-col class="youxiu">中</el-col>
                            </el-row>
                        </el-col>
                    </el-col>
                    <el-col :span="6">
                        <el-col :span="12" class="list-title">综合维修</el-col>
                        <el-col :span="12" class="list-content">
                            <el-row>
                                <el-col :span="12">检查数量</el-col>
                                <el-col :span="12">78</el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">最终得分</el-col>
                                <el-col :span="12">91</el-col>
                            </el-row>
                            <el-row>
                                <el-col class="youxiu">差</el-col>
                            </el-row>
                        </el-col>
                    </el-col>
                </el-col>
            </el-row>
            <!-- 主体部分，表格，部门，检查区域，设备编号，设备名称，检查结果，评审扣分，评分依据，检查人，检查时间，操作，详情 -->
            <el-table :data="tableData" style="width: 100%" stripe height="550" v-loading="loading"
                element-loading-text="Flyknit">
                <!-- 索引 -->
                <el-table-column type="index" width="50" label="编号"></el-table-column>
                <el-table-column prop="number_machine" label="设备编号" width="150"></el-table-column>
                <el-table-column prop="device_name" label="设备名称" width="130"></el-table-column>
                <el-table-column prop="department" label="受检部门" width="120"></el-table-column>
                <el-table-column prop="location" label="检查区域" width="140"></el-table-column>
                <el-table-column prop="situation" label="检查结果" width="100"></el-table-column>
                <el-table-column prop="points" label="评审扣分" width="80">
                    <template slot-scope="scope">
                        <el-tag type="danger" v-if="scope.row.points > 0">-{{ scope.row.points }}分</el-tag>
                        <el-tag type="info" v-else>{{ scope.row.points }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="reason" label="评分依据" width="180"></el-table-column>
                <el-table-column prop="user_check" label="设备巡检人" width="100"></el-table-column>
                <el-table-column prop="create_time" label="巡检时间" width="180"></el-table-column>
                <el-table-column fixed="right" label="操作" width="180">
                    <template slot-scope="scope">
                        <el-button type="text" size="small">详情</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页组件 -->
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
                :page-sizes="[10, 20, 30, 40]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="total">
            </el-pagination>
        </el-card>
    </div>
</template>
<script>
import { set } from 'vue'

export default {
    data() {
        return {
            loading: true,
            form: {
                device_number: '',
                area: '',
                depart: '',
                date: ''
            },
            tableData: [],
            currentPage: 1,
            pageSize: 10,
            total: 40,
            areaList: [], // 检查区域列表
        }
    },
    created() {
        this.getInspectionData()
        this.getLocations()
    },
    methods: {
        // 获取区域
        async getLocations() {
            try {
                const res = await this.$http.getAllArea()
                if (res.status !== 200) {
                    return this.$message.error(res.message)
                }
                const { list } = res.data
                this.areaList = list
            } catch (error) {
                return this.$message.error('服务器错误,请稍后再试')
            }
        },
        // 获取巡检记录列表
        async getInspectionData() {
            try {
                this.form.page = this.currentPage
                this.form.pageSize = this.pageSize
                const res = await this.$http.getInspectionList(this.form)
                if (res.status !== 200) {
                    this.loading = false
                    return this.$message.error(res.message)
                }
                const { list, total } = res.data
                this.tableData = list
                this.total = total
                setTimeout(() => {
                    this.loading = false
                }, 500);
            } catch (error) {
                this.loading = false
                return this.$message.error('服务器错误，请稍后重试！')
            }
        },
        // 跳转年度分析页面
        toAnalyse() {
            this.$router.push('/yearAnalyse')
        },
        onSubmit() {
            if (this.form.device_number || this.form.area || this.form.depart || this.form.date) {
                this.loading = true
                this.getInspectionData()
            } else {
                return false
            }
        },
        handleSizeChange(val) {
            this.loading = true
            this.pageSize = val
            this.getInspectionData()
        },
        handleCurrentChange(val) {
            this.loading = true
            this.currentPage = val
            this.getInspectionData()
        },
        // 重置表单
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
        // 刷新页面
        refresh() {
            this.loading = true
            this.getInspectionData()
        }
    }
}
</script>
<style lang="scss">
.forms {
    display: flex;
    flex-wrap: wrap;
}
.el-icon-star-on {
    color: #409EFF !important;
    font-size: 20px !important;
}

.socre-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .el-col {
        padding: 5px;
        background-color: #f6f7f9;
        margin-right: 10px;
        border-radius: 7px;

        .list-title {
            font-weight: bold;
            font-size: 18px;
            letter-spacing: 2px;
            font-family: 'Times New Roman', Times, serif;
            color: #333;
        }

        .list-content {
            width: 100%;
            display: flex;

            .el-row {
                display: flex;
                justify-content: space-around;
                align-items: center;

                .youxiu {
                    color: #67C23A !important;
                    font-size: 20px !important;
                }
            }
        }

        .list-content .el-row .el-col:nth-child(1) {
            color: #666;
            font-weight: bold;
            font-size: 12px;
            letter-spacing: 2px;
            font-family: 'Times New Roman', Times, serif;
        }

        .list-content .el-row .el-col:nth-child(2) {
            color: #409EFF;
            font-weight: bold;
            font-size: 16px;
        }
    }
}

.el-pagination {
    padding: 15px 0;
}
</style>