/*
 * @Author: flyknit <EMAIL>
 * @Date: 2024-01-09 12:39:49
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-02-20 15:06:43
 * @FilePath: \electronic-filed:\gitee\switching\src\until\socket.js
 * @Description: 
 * 
 * Copyright (c) 2024 by ${<EMAIL>}, All Rights Reserved. 
 */
import VueSocketIO from "vue-socket.io";
import io from "socket.io-client";

export default {
  install(Vue) {
    const socket = io("http://*************:8099", {
      autoConnect: true,
      path: "/socket.io/",
      transports: ["polling"],
      extraHeaders: {},
      // 开启心跳检测
      pingTimeout: 60000, // 心跳超时时间
      pingInterval: 10000, // 心跳发送间隔时间
    });

    // 断线重连机制
    socket.on("disconnect", () => {
      console.log("socket disconnected");
      // 一旦断开连接，我们可以设置一个定时器，尝试重新连接
      setTimeout(() => {
        socket.connect();
      }, 5000);
    });

    Vue.use(
      new VueSocketIO({
        debug: true,
        connection: socket,
      })
    );

    Vue.mixin({
      sockets: {
        connecting() {
          console.log("正在连接");
        },
        disconnect() {
          console.log("Socket 断开");
          this.$message.error("socket连接成功");
        },
        connect_failed() {
          cosnole.log("连接失败");
          this.$message.error("socket连接成功");
        },
        connect() {
          console.log("socket connected");
          this.$message.success("socket连接成功");
        },
        error(data) {
          console.log("error", data);
        },
        message(data) {
          console.log("Message", data);
        },
      },
    });
  },
};
