import{R as h,i as k,j as S,cl as $,bW as N,D as f,w as T,e as V,b as E,a3 as P,aK as O,ca as b,av as R,G as _,aS as A,aB as j,cb as m,c9 as C,bz as B,I as z,cm as D}from"./index-444b28c3.js";function K(t){let e;const l=h("loading"),a=k(!1),s=S({...t,originalPosition:"",originalOverflow:"",visible:!1});function o(n){s.text=n}function c(){const n=s.parent;if(!n.vLoadingAddClassList){let u=n.getAttribute("loading-number");u=Number.parseInt(u)-1,u?n.setAttribute("loading-number",u.toString()):(b(n,l.bm("parent","relative")),n.removeAttribute("loading-number")),b(n,l.bm("parent","hidden"))}d(),v.unmount()}function d(){var n,u;(u=(n=r.$el)==null?void 0:n.parentNode)==null||u.removeChild(r.$el)}function p(){var n;t.beforeClose&&!t.beforeClose()||(a.value=!0,clearTimeout(e),e=window.setTimeout(i,400),s.visible=!1,(n=t.closed)==null||n.call(t))}function i(){if(!a.value)return;const n=s.parent;a.value=!1,n.vLoadingAddClassList=void 0,c()}const v=$({name:"ElLoading",setup(){return()=>{const n=s.spinner||s.svg,u=f("svg",{class:"circular",viewBox:s.svgViewBox?s.svgViewBox:"25 25 50 50",...n?{innerHTML:n}:{}},[f("circle",{class:"path",cx:"50",cy:"50",r:"20",fill:"none"})]),I=s.text?f("p",{class:l.b("text")},[s.text]):void 0;return f(O,{name:l.b("fade"),onAfterLeave:i},{default:T(()=>[V(E("div",{style:{backgroundColor:s.background||""},class:[l.b("mask"),s.customClass,s.fullscreen?"is-fullscreen":""]},[f("div",{class:l.b("spinner")},[u,I])]),[[P,s.visible]])])})}}}),r=v.mount(document.createElement("div"));return{...N(s),setText:o,removeElLoadingChild:d,close:p,handleAfterLeave:i,vm:r,get $el(){return r.$el}}}let g;const Z=function(t={}){if(!R)return;const e=q(t);if(e.fullscreen&&g)return g;const l=K({...e,closed:()=>{var s;(s=e.closed)==null||s.call(e),e.fullscreen&&(g=void 0)}});F(e,e.parent,l),L(e,e.parent,l),e.parent.vLoadingAddClassList=()=>L(e,e.parent,l);let a=e.parent.getAttribute("loading-number");return a?a=`${Number.parseInt(a)+1}`:a="1",e.parent.setAttribute("loading-number",a),e.parent.appendChild(l.$el),_(()=>l.visible.value=e.visible),e.fullscreen&&(g=l),l},q=t=>{var e,l,a,s;let o;return A(t.target)?o=(e=document.querySelector(t.target))!=null?e:document.body:o=t.target||document.body,{parent:o===document.body||t.body?document.body:o,background:t.background||"",svg:t.svg||"",svgViewBox:t.svgViewBox||"",spinner:t.spinner||!1,text:t.text||"",fullscreen:o===document.body&&((l=t.fullscreen)!=null?l:!0),lock:(a=t.lock)!=null?a:!1,customClass:t.customClass||"",visible:(s=t.visible)!=null?s:!0,target:o}},F=async(t,e,l)=>{const{nextZIndex:a}=j(),s={};if(t.fullscreen)l.originalPosition.value=m(document.body,"position"),l.originalOverflow.value=m(document.body,"overflow"),s.zIndex=a();else if(t.parent===document.body){l.originalPosition.value=m(document.body,"position"),await _();for(const o of["top","left"]){const c=o==="top"?"scrollTop":"scrollLeft";s[o]=`${t.target.getBoundingClientRect()[o]+document.body[c]+document.documentElement[c]-Number.parseInt(m(document.body,`margin-${o}`),10)}px`}for(const o of["height","width"])s[o]=`${t.target.getBoundingClientRect()[o]}px`}else l.originalPosition.value=m(e,"position");for(const[o,c]of Object.entries(s))l.$el.style[o]=c},L=(t,e,l)=>{const a=h("loading");["absolute","fixed","sticky"].includes(l.originalPosition.value)?b(e,a.bm("parent","relative")):C(e,a.bm("parent","relative")),t.fullscreen&&t.lock?C(e,a.bm("parent","hidden")):b(e,a.bm("parent","hidden"))},x=Symbol("ElLoading"),w=(t,e)=>{var l,a,s,o;const c=e.instance,d=r=>B(e.value)?e.value[r]:void 0,p=r=>{const n=A(r)&&(c==null?void 0:c[r])||r;return n&&k(n)},i=r=>p(d(r)||t.getAttribute(`element-loading-${D(r)}`)),y=(l=d("fullscreen"))!=null?l:e.modifiers.fullscreen,v={text:i("text"),svg:i("svg"),svgViewBox:i("svgViewBox"),spinner:i("spinner"),background:i("background"),customClass:i("customClass"),fullscreen:y,target:(a=d("target"))!=null?a:y?void 0:t,body:(s=d("body"))!=null?s:e.modifiers.body,lock:(o=d("lock"))!=null?o:e.modifiers.lock};t[x]={options:v,instance:Z(v)}},G=(t,e)=>{for(const l of Object.keys(e))z(e[l])&&(e[l].value=t[l])},M={mounted(t,e){e.value&&w(t,e)},updated(t,e){const l=t[x];e.oldValue!==e.value&&(e.value&&!e.oldValue?w(t,e):e.value&&e.oldValue?B(e.value)&&G(e.value,l.options):l==null||l.instance.close())},unmounted(t){var e;(e=t[x])==null||e.instance.close()}};export{Z as L,M as v};
