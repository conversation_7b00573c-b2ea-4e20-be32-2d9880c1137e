import{bn as a,o as r,g as p,m as e,b as d,w as i,n as c,C as l,d as u,bv as m,z as f,A as h}from"./index-444b28c3.js";import{E as b}from"./el-button-9bbdfcf9.js";import{_ as v}from"./_plugin-vue_export-helper-c27b6911.js";import"./index-4d7f16ce.js";const g=""+new URL("../png/403-6dc0b7a9.png",import.meta.url).href;const o=t=>(f("data-v-b9cc7ccf"),t=t(),h(),t),x={class:"not-container"},w=o(()=>e("img",{src:g,class:"not-img",alt:"403"},null,-1)),y={class:"not-detail"},B=o(()=>e("h2",null,"403",-1)),C=o(()=>e("h4",null,"抱歉，您无权访问该页面~🙅‍♂️🙅‍♀️",-1)),E=l({name:"403"}),I=Object.assign(E,{setup(t){const n=a();return(k,s)=>{const _=b;return r(),p("div",x,[w,e("div",y,[B,C,d(_,{type:"primary",onClick:s[0]||(s[0]=N=>c(n).push(c(m)))},{default:i(()=>[u("返回首页")]),_:1})])])}}}),O=v(I,[["__scopeId","data-v-b9cc7ccf"]]);export{O as default};
