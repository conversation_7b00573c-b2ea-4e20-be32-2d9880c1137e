<template>
    <el-card>
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="基础设置" name="first">
                <span slot="label"><i class="el-icon-date"></i> 基础设置</span>
                <el-col :span="10" class="left-col">
                    <el-col :span="24">
                        <el-col :span="12">
                            <span class="title">保养模块（标题和描述）</span><span style="color: red;">*</span>
                        </el-col>
                    </el-col>
                    <el-col :span="24">
                        <el-col :span="24">
                            <el-select v-model="value" placeholder="请选择标题">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-col>
                    </el-col>
                    <el-col :span="24" :gutter="20">
                        <el-col :span="24">
                            <span class="title">标题描述</span><span style="color: red;">*</span>
                        </el-col>
                    </el-col>
                    <el-col :span="24">
                        <el-col :span="16">
                            <el-input type="textarea" rows="4" v-model="describe" placeholder="请输入标题描述"></el-input>
                        </el-col>
                    </el-col>
                    <el-col :span="24" :gutter="20">
                        <el-col :span="24">
                            <span class="title">模块图标（支持阿里字体图标）</span><span style="color: red;">*</span>
                        </el-col>
                    </el-col>
                    <el-col :span="24" :gutter="20">
                        <el-col :span="24">
                            <el-select v-model="logo" placeholder="请选择图标">
                                <el-option v-for="item in moduleOptions" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-col>
                    </el-col>
                    <el-col :span="24" :gutter="20">
                        <el-col :span="24">
                            <span class="title">图标背景颜色</span><span style="color: red;">*</span>
                        </el-col>
                    </el-col>
                    <el-col :span="24" :gutter="20">
                        <el-col :span="24">
                            <el-select v-model="color" placeholder="请选择颜色">
                                <el-option v-for="item in colorList" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-col>
                    </el-col>
                    <el-col :span="24" :gutter="20">
                        <el-col :span="24">
                            <span class="title">页面路径</span><span style="color: red;">*</span>
                        </el-col>
                    </el-col>
                    <el-col :span="24" :gutter="20">
                        <el-col :span="24">
                            <el-select v-model="url" placeholder="请选择跳转路径">
                                <el-option v-for="item in urlOptions" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-col>
                    </el-col>
                    <el-col :span="24" class="btn-area">
                        <el-col :span="24">
                            <el-button type="primary" @click="saveSetting">保存配置</el-button>
                        </el-col>
                    </el-col>
                </el-col>
                <el-col :span="14" class="right-style">
                    <div class="right-style-title">
                        <h3> <span class="el-icon-s-opportunity"></span> 配置预览</h3> <el-button type="danger"
                            icon="el-icon-delete" @click="clearSetting" size="mini"
                            v-show="User.level > 0 && maintainList.length > 0" plain>清除配置</el-button>
                    </div>
                    <div class="iphone">
                        <div class="power-button"></div>
                        <div class="volume-buttons">
                            <div class="volume-button"></div>
                            <div class="volume-button"></div>
                        </div>
                        <div class="camera"></div>
                        <div class="speaker"></div>
                        <div class="screen">
                            <!-- 头部信号和图标 -->
                            <div class="top-area">
                                <span class="top-time">{{ getNowFormatDate }}</span>
                            </div>
                            <div>
                                <div class="content-box">
                                    <div class="header-box">
                                        <!-- 自定义导航栏 -->
                                        <div class="header-content">
                                            <span>特种面料厂</span>
                                        </div>
                                        <div class="" style="display: flex;flex-direction: column;">
                                            <div class="name-box">
                                                <span>Hi, {{ timeState }}{{ User.username }}</span>
                                            </div>
                                            <div class="notice-content">
                                                <span>今天是{{ formattedDate }}，{{ centence }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="maintaince-list" v-show="maintainList.length > 0">
                                        <template v-if="maintainList.length > 0">
                                            <div class="maintaince-list_box" v-for="(item, index) in maintainList"
                                                :key="index"
                                                :class="{ 'sink': index === 0 || index === 2 || index === 4, 'transite': index === 0 || index === 1 }">
                                                <div :style="'background-color:' + item.backColor"
                                                    class="maintaince-list_logo">
                                                    <span :class="'iconfont  ' + item.icon"></span>
                                                </div>
                                                <div class="maintaince-list_title">
                                                    <span>{{ item.title }}</span>
                                                    <span>{{ item.describe }}</span>
                                                </div>
                                            </div>
                                        </template>
                                        <template v-else>
                                            <div class="maintaince-list_box">
                                                <div class="maintaince-list_logo">
                                                    <span class="iconfont icon-yingyong"></span>
                                                </div>
                                                <div class="maintaince-list_title">
                                                    <text>暂无数据</text>
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="home-button"></div>
                    </div>
                </el-col>
            </el-tab-pane>
            <!-- 邮箱配置 -->
            <el-tab-pane label="邮件设置" name="second">
                <span slot="label"><i class="el-icon-message"></i> 邮件配置</span>
                <el-row :gutter="20" style="margin: 10px 0;">
                    <el-col :span="24">
                        <span style="font-weight:bold;font-size:14px;">邮箱服务器</span><span style="color: red;">*</span>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6">
                        <el-input type="text" v-model="emailAddress"
                            placeholder="请输入邮箱服务器地址，例如:smtp.shenzhougroup.com"></el-input>
                    </el-col>
                </el-row>
                <el-row :gutter="20" style="margin: 10px 0;">
                    <el-col :span="24">
                        <span style="font-weight:bold;font-size:14px;">邮箱服务器端口</span><span style="color: red;">*</span>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="4">
                        <el-input type="text" v-model="port" placeholder="请输入邮箱服务器端口, 例如：465"></el-input>
                    </el-col>
                </el-row>
                <el-row :gutter="20" style="margin: 10px 0;">
                    <el-col :span="24">
                        <span style="font-weight:bold;font-size:14px;">SSL协议</span><span>*</span>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="6" style="padding-left:20px;">
                        <el-switch v-model="isOpen" active-color="#517DF7" inactive-color="#C0CCDA">
                        </el-switch>
                    </el-col>
                </el-row>
                <el-row :gutter="20" style="margin: 10px 0;">
                    <el-col :span="24">
                        <span style="font-weight:bold;font-size:14px;">邮箱用户</span><span style="color: red;">*</span>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="4">
                        <el-input type="text" v-model="emailUser" placeholder="授权发送的邮箱"></el-input>
                    </el-col>
                </el-row>
                <el-row :gutter="20" style="margin: 10px 0;">
                    <el-col :span="24">
                        <span style="font-weight:bold;font-size:14px;">邮箱密码</span><span style="color: red;">*</span>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="4">
                        <el-input type="password" v-model="emailPass" placeholder="授权的邮箱密码"></el-input>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-button type="primary" @click="saveEmailInfo">保存配置</el-button>
                    </el-col>
                </el-row>
            </el-tab-pane>
            <!-- 公告页面 -->
            <el-tab-pane label="公告通知" name="third">
                <span slot="label"><i class="el-icon-copy-document"></i> 公告通知</span>
                <el-row :gutter="20" style="margin: 10px 0;">
                    <el-col :span="24">
                        <span style="font-weight:bold;font-size:14px;">App公告发放</span><span style="color: red;">*</span>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-input type="textarea" :rows="4" v-model="notice" placeholder="请输入公告内容"></el-input>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-button type="primary" @click="saveInfo">保存配置</el-button>
                    </el-col>
                </el-row>
            </el-tab-pane>
            <!-- APP 升级模块 -->
            <el-tab-pane label="App版本更新模块" name="fourth">
                <span slot="label"><i class="el-icon-mobile"></i> App版本更新</span>
                <el-row :gutter="20" style="margin: 10px 0;">
                    <el-col :span="24">
                        <span style="font-weight:bold;font-size:14px;">App更新URL地址</span><span style="color: red;">*</span>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6">
                        <el-input type="text" v-model="updateUrl"
                            placeholder="请输入App更新地址，例如:http://www.abc.com/qq.apk"></el-input>
                    </el-col>
                </el-row>
                <el-row :gutter="18" style="margin: 10px 0;">
                    <el-col :span="24">
                        <span style="font-weight:bold;font-size:14px;">App更新内容</span><span style="color: red;">*</span>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6">
                        <el-input type="textarea" :rows="4" v-model="content" placeholder="请输入更新内容"></el-input>
                    </el-col>
                </el-row>
                <el-row :gutter="20" style="margin: 10px 0;">
                    <el-col :span="24">
                        <span style="font-weight:bold;font-size:14px;">是否强制更新</span>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="6" style="padding-left:20px;">
                        <el-switch v-model="force_update" active-color="#517DF7" inactive-color="#C0CCDA">
                        </el-switch>
                    </el-col>
                </el-row>
                <el-row :gutter="20" style="margin: 10px 0;">
                    <el-col :span="24">
                        <span style="font-weight:bold;font-size:14px;">App版本号(注意，当前最新版本号要大于前一次版本号)</span><span
                            style="color: red;">*</span>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="4">
                        <el-input type="text" v-model="version" placeholder="请输入最新App版本号,例如1.0.1"></el-input>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-button type="primary" @click="submitInfo">保存配置</el-button>
                    </el-col>
                </el-row>
            </el-tab-pane>
        </el-tabs>
    </el-card>
</template>
<script>
import { mapState } from 'vuex'
export default {
    data() {
        return {
            activeName: 'first',
            options: [{
                value: '日保养',
                label: '日保养'
            }, {
                value: '周保养',
                label: '周保养'
            }, {
                value: '月保养',
                label: '月保养'
            }, {
                value: '季度保养',
                label: '季度保养'
            }, {
                value: '半年保养',
                label: '半年保养'
            }, {
                value: '年保养',
                label: '年保养'
            }, {
                value: '日工作',
                label: '日工作'
            }],
            moduleOptions: [
                {
                    value: 'icon-yuyuebaoyang',
                    label: 'icon-yuyuebaoyang'
                },
                {
                    value: 'icon-tubiaozhizuomoban-132',
                    label: 'icon-tubiaozhizuomoban-132'
                },
                {
                    value: 'icon-a-40-yingjiwuziyichangweixiu',
                    label: 'icon-a-40-yingjiwuziyichangweixiu'
                },
                {
                    value: 'icon-shebeiweihuguanli',
                    label: 'icon-shebeiweihuguanli'
                },
                {
                    value: 'icon-canshuweihu',
                    label: 'icon-canshuweihu'
                },
                {
                    value: 'icon-kaifa',
                    label: 'icon-kaifa'
                },

            ],
            urlOptions: [
                {
                    value: '../../pages/maintaince/day-maintaince/day-maintaince',
                    label: '../../pages/maintaince/day-maintaince/day-maintaince'
                },
                {
                    value: '../../pages/maintaince/dailywork/dailywork',
                    label: '../../pages/maintaince/dailywork/dailywork'
                },
                {
                    value: '../../pages/maintaince/month-maintaince/month-maintaince',
                    label: '../../pages/maintaince/month-maintaince/month-maintaince'
                },
                {
                    value: '../../pages/maintaince/season-maintaince/season-maintaince',
                    label: '../../pages/maintaince/season-maintaince/season-maintaince'
                },
                {
                    value: '../../pages/maintaince/halfyear-maintaince/halfyear-maintaince',
                    label: '../../pages/maintaince/halfyear-maintaince/halfyear-maintaince'
                },
                {
                    value: '../../pages/maintaince/year-maintaince/year-maintaince',
                    label: '../../pages/maintaince/year-maintaince/year-maintaince'
                }
            ],
            value: '',
            describe: '', // 描述
            logo: '', // 选中的图标
            url: '', // 选中的路径
            notice: '', // 公告内容
            emailAddress: '', // 邮箱地址
            port: '', // 邮箱端口
            isOpen: false, // 是否开启SSL协议
            emailUser: '', // 邮箱用户
            emailPass: '', // 邮箱密码
            content: '', // 更新内容
            force_update: false, // 是否强制更新
            version: '', // 版本号
            updateUrl: '', // 更新地址
            maintainList: [], // app配置列表
            colorList: [
                {
                    value: '#F57572',
                    label: '#F57572'
                }, {
                    value: '#5D99FB',
                    label: '#5D99FB'
                }, {
                    value: '#FACF02',
                    label: '#FACF02'
                }, {
                    value: '#5EDBBA',
                    label: '#5EDBBA'
                }, {
                    value: '#9179E9',
                    label: '#9179E9'
                }, {
                    value: '#5996FB',
                    label: '#5996FB'
                }
            ], // 颜色列表
            color: '', // 颜色
            // 心语
            centenceList: [
                '又是美好的一天，辛勤的汗水才能换来您追求的幸福!',
                '再困难，咬咬牙，总会过去的；再犯错，不孤独，一切还能够重来!',
                '新的一天遇上更好的自己。打开自己，共赴一场自我突破之旅，不必把时间都用来去爱去恨去浪费!',
                '你能走多远，取决于你对目标的决心，献给正在追梦的自己与你我他!',
                '生活像一只蝴蝶，没有破茧的勇气，哪来飞舞的美丽!',
                '有生命就会有希望，有信心就会有成功，有思索就会有思路，有努力就会有获得!',
                '生活像一只峰，没有勤劳和努力，怎能尝到花粉的甜蜜，越努力越幸运!!',
                '一朵花的凋零，荒芜不了整个春天，一次挫折也荒废不了整个人生!',
                '宁愿跑起来被拌倒无数次，也不愿规规矩矩走一辈子。就算跌倒也要豪迈的笑!',
                '你若不想做，会找一个或无数个借口；你若想做，会想一个或无数个办法!'
            ],
            centence: '',
            formattedDate: '',
            timeState: '',
        }
    },
    created() {
        this.getSettinginfo(this.activeName)
        this.initDate()
        this.initCentence()
    },
    computed: {
        ...mapState({
            User: state => state.users,
        }),
        // 获取当前时间函数
        getNowFormatDate() {
            let date = new Date()
            let seperator2 = ':'
            let h = date.getHours()
            let m = date.getMinutes()
            if (h >= 0 && h <= 9) {
                h = '0' + h
            }
            if (m >= 0 && m <= 9) {
                m = '0' + m
            }
            const currentdate = h + seperator2 + m
            return currentdate
        },
    },
    methods: {
        // 获取邮件配置
        async getEmailSettings() {
            try {
                const res = await this.$http.getEmailSetting()
                if (res.status !== 200) {
                    return this.$message.error(res.message)
                }
                const { list } = res.data
                if (list) {
                    this.emailAddress = list.host
                    this.port = list.port
                    this.isOpen = list.is_ssl === 1 ? true : false
                    this.emailUser = list.email
                    this.emailPass = list.password
                }
            } catch (error) {
                return this.$message.error('服务器异常，请稍后重试')
            }
        },
        // 保存邮箱配置
        async saveEmailInfo() {
            if(!this.emailAddress || !this.port || !this.emailUser || !this.emailPass) {
                return this.$message.warning('请填写完整信息')
            }
            try {
                const obj = {
                    host: this.emailAddress,
                    port: this.port,
                    ssl: this.isOpen,
                    email: this.emailUser,
                    password: this.emailPass
                }
                const res = await this.$http.saveEmailSetting(obj)
                if (res.status !== 200) {
                    return this.$message.error(res.message)
                }
                return this.$message.success(res.message)
            } catch (error) {
                return this.$message.error('服务器异常，请稍后重试')
            }
        },
        // 清除配置
        async clearSetting() {
            try {
                this.$confirm('此操作将清除所有配置, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    const res = await this.$http.clearSettings()
                    if (res.status !== 200) {
                        return this.$message.error(res.message)
                    }
                    this.$message.success(res.message)
                    this.maintainList = []
                    this.getSetting()
                }).catch(() => {
                    return false
                })
            } catch (error) {
                return this.$message.error('服务器异常，请稍后重试')
            }
        },
        // 获取app首页配置信息
        async getHomeSetting() {
            try {
                const res = await this.$http.getPageSetting()
                if (res.status !== 200) {
                    return this.$message.error(res.message)
                }
                const { list } = res.data
                if (list && list.length > 0) {
                    this.maintainList = list
                } else {
                    this.maintainList = []
                }
            } catch (error) {
                return this.$message.error('服务器异常，请稍后重试')
            }
        },
        // 保存app配置信息
        saveSetting() {
            // 最多只能添加6个
            if (this.maintainList && this.maintainList.length >= 6) {
                return this.$message({
                    message: '最多只能添加6个',
                    type: 'warning'
                })
            }
            if (this.value && this.describe && this.logo && this.color && this.url) {
                const obj = {
                    title: this.value,
                    describe: this.describe,
                    icon: this.logo,
                    backColor: this.color,
                    url: this.url
                }
                this.maintainList.push(obj)
                if (this.maintainList.length > 0) {
                    this.saveAppSetting(obj)
                }
            } else {
                this.$message({
                    message: '请填写完整信息',
                    type: 'warning'
                })
            }
        },
        // 封装保存app配置信息函数
        async saveAppSetting(obj) {
            try {
                const res = await this.$http.saveSetting(obj)
                if (res.status !== 200) {
                    this.maintainList = []
                    return this.$message.error(res.message)
                }
                this.$message.success(res.message)
                setTimeout(() => {
                    this.value = ''
                    this.describe = ''
                    this.logo = ''
                    this.color = ''
                    this.url = ''
                }, 1000)
            } catch (error) {
                return this.$message.error('服务器错误，请稍后重试')
            }
        },
        // 随机抽取早安心语
        initCentence() {
            return this.centence = this.centenceList[Math.floor(Math.random() * this.centenceList.length)];
        },
        // 获取当前时间中的小时
        initDate() {
            const dates = new Date();
            const h = dates.getHours()
            const d = dates.getDate()
            const m = dates.getMonth() + 1
            this.formattedDate = m + '月' + d + '日'
            if (h > 6 && h <= 11) {
                this.timeState = '早上好呀！'
            }
            if (h >= 11 && h <= 13) {
                this.timeState = '中午好呀！'
            }
            if (h >= 14 && h <= 17) {
                this.timeState = '下午好呀！'
            }
            if (h >= 18 && h <= 23) {
                this.timeState = '晚上好呀！'
            }
        },
        // 保存通知信息
        async saveInfo() {
            try {
                if (!this.notice || this.notice === '') {
                    return this.$message.warning('请输入公告内容')
                }
                const res = await this.$http.saveMessage({
                    notice: this.notice
                })
                if (res.status !== 200) {
                    return this.$message.error(res.message)
                }
                return this.$message.success(res.message)
            } catch (error) {
                return this.$message.error('服务器错误，请稍后重试')
            }
        },
        handleClick(tab, event) {
            const { name } = tab
            this.getSettinginfo(name)
        },
        // 封装公共方法
        getSettinginfo(name) {
            switch (name) {
                case 'first':
                    this.getSetting()
                    break;
                case 'second':
                    this.getEmail()
                    break;
                case 'third':
                    this.getNotice()
                    break;
                case 'fourth':
                    this.getApp()
                    break;
                default:
                    break;
            }
        },
        // 获取设置信息
        getSetting() {
            this.getHomeSetting()
        },
        // 获取公告信息
        async getNotice() {
            try {
                const res = await this.$http.getMessage()
                if (res.status !== 200) {
                    return this.$message.error(res.message)
                }
                const { list } = res.data
                this.notice = list.message
            } catch (error) {
                return this.$message.error('服务器错误,请稍后重试')
            }
        },
        // 获取邮箱配置信息
        getEmail() {
            this.getEmailSettings()
        },
        // 获取app更新信息
        async getApp() {
            try {
                const res = await this.$http.getAppInfo()
                if (res.status !== 200) {
                    return this.$message.error(res.message)
                }
                const { list } = res.data
                this.content = list.content
                if (list.force_update === 1) {
                    this.force_update = true
                } else {
                    this.force_update = false
                }
                this.version = list.version
                this.updateUrl = list.url
            } catch (error) {
                return this.$message.error('服务器错误,请稍后重试')
            }
        },
        // 提交app更新信息
        async submitInfo() {
            try {
                const obj = {
                    url: this.updateUrl,
                    content: this.content,
                    force_update: this.force_update,
                    version: this.version
                }
                const res = await this.$http.uploadInfo(obj)
                if (res.status !== 200) {
                    return this.$message.error(res.message)
                }
                return this.$message.success(res.message)
            } catch (error) {
                return this.$message.error('服务器错误,请稍后重试')
            }
        }
    }
}
</script>
<style lang="scss" scoped>
.el-card {
    width: 100%;
}

.el-row {
    margin: 20px 0;
}

.el-card__body {
    padding: 20px;
}

.left-col {
    .el-col {
        margin: 5px 10px;

        .title {
            font-weight: bold;
            font-size: 14px;
        }
    }

    .btn-area {
        margin-top: 20px;
    }
}

.right-style {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border: 1px solid #eee;
    padding: 20px 0;

    .right-style-title {
        padding: 20px 0;
        letter-spacing: 3px;
        display: flex;
        justify-content: space-around;
        align-items: center;
        width: 100%;
    }

    /* iPhone 外框样式 */
    .iphone {
        position: relative;
        width: 300px;
        height: 600px;
        border: 2px solid black;
        border-radius: 40px;
        background: linear-gradient(45deg, #000000, #444444);
    }

    .power-button {
        position: absolute;
        top: 135px;
        right: -20px;
        width: 40px;
        height: 10px;
        background: linear-gradient(45deg, #444444, #000000);
        border-radius: 10px;
        transform: rotate(90deg);
    }

    .volume-buttons {
        position: absolute;
        top: 100px;
        left: -5px;
        width: 10px;
        height: 80px;
    }

    .volume-button {
        width: 100%;
        height: 50%;
        background: linear-gradient(45deg, #444444, #000000);
    }

    .volume-button:first-child {
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
    }

    .volume-button:last-child {
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
    }

    /* 摄像头样式 */
    .camera {
        width: 12px;
        height: 12px;
        background-color: #fff;
        border-radius: 50%;
        position: absolute;
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
    }

    /* 听筒样式 */
    .speaker {
        width: 30px;
        height: 4px;
        background-color: #fff;
        position: absolute;
        top: 25px;
        left: 50%;
        transform: translateX(-50%);
        border-radius: 10px;
    }

    /* 屏幕样式 */
    .screen {
        width: 280px;
        height: 520px;
        background-color: #fff;
        position: absolute;
        top: 40px;
        left: 50%;
        transform: translateX(-50%);
        box-sizing: border-box;
        overflow: hidden;
        .top-area {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 1px 10px;
            background-color: #9C65F7;
            box-sizing: border-box;
            overflow: hidden;
            .top-time {
                font-size: 12px;
                font-weight: bold;
                color: #feffff;
            }

            .el-icon-sunny {
                margin-left: 10px;
            }
        }

        .content-box {
            display: flex;
            flex-direction: column;
            height: 52vh;
            box-sizing: border-box;

            .header-content {
                display: flex;
                height: 24px;
                align-items: center;
                justify-content: center;
                box-sizing: border-box;

                span {
                    color: #feffff;
                    font-size: 14px;
                }
            }

            .header-box {
                background-image: url('../../assets/image/index.png');
                display: flex;
                flex-direction: column;
                align-items: center;
                background-size: cover;
                background-repeat: no-repeat;
                position: relative;
                box-sizing: border-box;
                overflow: hidden;
                height: 220px;

                .name-box {
                    margin-top: 0px;
                    text-align: center;

                    span {
                        color: #feffff;
                        font-weight: bold;
                        letter-spacing: 2px;
                        font-size: 2px;
                    }
                }

                .notice-content {
                    display: flex;
                    flex-direction: column;
                    flex-wrap: wrap;
                    align-items: center;
                    padding: 10px 0px;
                    width: 200px;
                    white-space: wrap;
                    text-overflow: ellipsis;

                    span {
                        font-size: 12px;
                        letter-spacing: 1.5px;
                        color: #feffff;
                        line-height: 1.5;
                    }
                }
            }

            .maintaince-list {
                position: absolute;
                top: 23.5%;
                display: flex;
                justify-content: space-around;
                align-items: center;
                flex-wrap: wrap;
                padding: 5px 8px;

                .maintaince-list_box {
                    background-color: #feffff;
                    box-shadow: 2px 4px 16px 4.5px rgba(0, 0, 0, 0.1);
                    border-radius: 12.5px;
                    padding: 8px 12px 10px 12px;
                    width: calc(100% / 2.9);
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    margin: 5px 0;
                    cursor: pointer;

                    .maintaince-list_logo {
                        width: 45px;
                        height: 45px;
                        border: 1px solid #eee;
                        border-radius: 50%;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }

                    .maintaince-list_title {
                        padding: 2px 0;
                        display: flex;
                        flex-direction: column;
                        text-align: center;
                    }

                    .maintaince-list_title span:nth-child(1) {
                        color: #1A1A1A;
                        font-weight: bold;
                        font-size: 14px;
                        letter-spacing: 1px;
                        font-family: "微软雅黑";
                        line-height: 1.5;
                    }

                    .maintaince-list_title span:nth-child(2) {
                        color: #999;
                        font-size: 12px;
                        padding: 5px 0;
                    }
                }

                .maintaince-list_box:hover {
                    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
                }

                .sink {
                    position: relative;
                    top: 15px;
                }

                .transite {
                    opacity: 0.9;
                }

            }
        }

    }

    /* 主页按钮样式 */
    .home-button {
        width: 50px;
        height: 50px;
        background-color: #000;
        border-radius: 50%;
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        border: 1px solid #feffff;
    }

}
</style>