<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-07-24 09:14:24
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-02 13:30:57
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\404.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="home">
        <el-row :gutter="20" style="display: flex;flex-direction:column;justify-content:center;align-items:center;text-align:center;">
            <el-col :span="24">
                <el-image :src="url" style="width: 100%;height:100%;"></el-image>
            </el-col>
            <el-col :span="24">
                <h5>哎呀~您没有权限访问该页面</h5>
                <el-button type="primary" @click="$router.push('/')">回到首页</el-button>
            </el-col>
        </el-row>
    </div>
</template>
<script>
    import svg1 from '@/assets/image/401.svg';
    export default {
        name: 'NotFound',
        data() {
            return {
                url: svg1
            };
        },
    };
</script>
<style>
    .home {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
    h5 {
        margin: 20px 0;
        color: #999;
        font-size: 22px;
    }
    .el-button {
        margin-top: 20px;
    }
</style>