<template>
  <el-container>
    <el-row :gutter="10" style="width: 100%">
      <!-- 左侧布局 -->
      <el-col :span="16">
        <el-card shadow="hover">
          <!-- 角标 -->
          <el-badge :value="1" class="item" type="primary">
          </el-badge>
          <div style="padding:10px">
            <el-row :gutter="0">

              <div style="display:flex;justify-content:space-between;">
                <div>
                  <h4>保养次数统计</h4>
                </div>
                <div>
                  <el-radio-group v-model="form.depart">
                    <el-radio label="织造车间"></el-radio>
                    <el-radio label="整理车间"></el-radio>
                    <el-radio label="打样车间"></el-radio>
                    <el-radio label="综合维修"></el-radio>
                  </el-radio-group>
                </div>
              </div>

              <el-col :span="24" style="margin-top:20px;display:flex;">
                <el-col :span="16" style="display:flex;align-items:center;flex-wrap:wrap;">
                  <div class="card-box" v-for="(item, index) in maintainNumList" :key="index">
                    <div class="card-box-title">
                      <div>
                        <p class="el-icon-s-flag" style="font-size: 25px;color:#409EFF;"></p>
                        <span>{{ item.name }}</span>
                      </div>
                      <div class="num-box">
                        {{ item.value }}
                      </div>
                    </div>
                    <div class="card-box-content">
                      <!-- 图表 -->
                      <el-progress :stroke-width="10" :percentage="item.value"></el-progress>
                    </div>
                  </div>
                </el-col>
                <el-col :span="8" style="display:flex;justify-content:center;align-items:center;">
                  <!-- 饼图 -->
                  <div id="mains" style="width: 100%;height: 100%"></div>
                </el-col>
              </el-col>
            </el-row>
          </div>
        </el-card>
        <el-card shadow="hover" style="margin-top: 10px;padding:10px 0">
          <el-badge :value="2" class="item" type="primary">
          </el-badge>
          <div style="display:flex;justify-content:space-between;">
            <div>
              <h4>保养进度一览</h4>
            </div>
            <div>
              <!-- 筛选 -->
              <el-select v-model="form.department" placeholder="请筛选部门">
                <el-option v-for="item in departList" :key="item.value" :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </div>
          </div>
          <div>
            <el-col :span="10">
              <div class="process-data" style="display: flex;flex-direction:column;">
                <div class="progress-line" style="padding: 6px 0;" v-for="(item, index) in progressList" :key="index">
                  <span style="font-size: 14px;color:#999;">{{ item.name }}:</span> <el-progress :stroke-width="18"
                    :color="item.color" :percentage="item.value"></el-progress>
                </div>
              </div>
            </el-col>
            <el-col :span="14">
              <div class="maintaince-process">
                <el-col :span="24">
                  <el-col :span="8" v-for="(item, index) in progressList" :key="index"
                    style="display: flex;flex-direction:column;justify-content:center;align-items:center;margin-top:5px;padding:10px;">
                    <el-progress type="circle" :color="item.color" :width="95" :stroke-width="18"
                      :percentage="item.value"></el-progress>
                    <span style="color: #999;padding:10px 0">{{ item.name }}</span>
                  </el-col>
                </el-col>
              </div>
            </el-col>
          </div>
        </el-card>
        <el-col :span="24">
          <el-card shadow="hover" style="margin-top: 10px">
            <el-badge :value="3" class="item" type="primary">
            </el-badge>
            <div class="brush-box">
              <div style="margin-top:5px;margin-left:2px;">
                <h4>加油日报</h4>
              </div>
              <div class="brush-list">
                <div class="brush-list-item" v-for="(item, index) in oilList" :key="index">
                  <span>{{ item.area }}</span><span>{{ item.value }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="24">
          <el-card shadow="hover" style="margin-top: 10px;">
            <el-badge :value="4" class="item" type="primary">
            </el-badge>
            <div class="equipment-status-box">
              <div style="margin-top:5px;margin-left:2px;">
                <h4>日工作量趋势</h4>
              </div>
              <div style="width: 100%;height:300px;">
                <!-- 图表 -->
                <div id="equipment" style="width: 100%;height:100%;"></div>
              </div>
            </div>
          </el-card>
        </el-col>

      </el-col>
      <!-- 右侧布局 -->
      <el-col :span="8">
        <el-card shadow="hover">
          <el-badge :value="5" class="item" type="primary">
          </el-badge>
          <el-row :gutter="20">
            <div style="display:flex;justify-content:space-between;">
              <div style="margin-top:5px;margin-left:8px;">
                <h4>日工效排行榜</h4>
              </div>
              <div>
                <p class="el-icon-refresh-right" style="color: #517DF7;cursor:pointer;font-size:14px;">换一拨~</p>
              </div>
            </div>
            <el-col :span="24" style="display:flex;align-items:center;flex-wrap:wrap;margin-top:10px;padding:10px;">
              <el-col class="efficient-box" v-for="(item, index) in dailyworkList" :key="index">
                <div class="user-efficient-box">
                  <div class="left-efficient">
                    <h5>{{ item.username }}</h5>
                    <h2>{{ item.num }} <span>单</span> </h2>
                  </div>
                  <div class="right-efficient">
                    <img :src="item.avatar" style="width: 40px;height:40px;border-radius:50% !important;" />
                  </div>
                </div>
              </el-col>
            </el-col>
          </el-row>
        </el-card>

        <el-card shadow="hover" style="margin-top: 10px">
          <el-badge :value="6" class="item" type="primary">
          </el-badge>
          <div class="equipment-box">
            <div>
              <h4>部门设备总览</h4>
            </div>
            <div class="equipment-list">
              <div class="left-chart">
                <el-progress type="circle" color="#409EFF" :width="120" :stroke-width="18"
                  :percentage="88.5"></el-progress>
                <h5>设备完好率</h5>
              </div>
              <div class="right-charts-box">
                <el-col :span="24">
                  <el-col :span="6">
                    <div class="right-charts">
                      <div>
                        <p class="el-icon-s-grid" style="font-size: 50px;color:#409EFF"></p>
                      </div>
                      <div class="right-charts-num">
                        1660
                      </div>
                      <div class="right-charts-title">
                        <span>设备总数</span>
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="right-charts">
                      <div>
                        <p class="el-icon-news" style="font-size: 50px;color:#838996"></p>
                      </div>
                      <div class="right-charts-num">
                        10
                      </div>
                      <div class="right-charts-title">
                        <span>停机</span>
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="right-charts">
                      <div>
                        <p class="el-icon-coin" style="font-size: 50px;color:#F7D127"></p>
                      </div>
                      <div class="right-charts-num">
                        3
                      </div>
                      <div class="right-charts-title">
                        <span>闲置</span>
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="right-charts">
                      <div>
                        <p class="el-icon-circle-close" style="font-size: 50px;color:#F79422"></p>
                      </div>
                      <div class="right-charts-num">
                        0
                      </div>
                      <div class="right-charts-title">
                        <span>损坏待修</span>
                      </div>
                    </div>
                  </el-col>
                </el-col>
              </div>
            </div>
          </div>
        </el-card>

        <el-card shadow="hover" style="margin-top: 10px">
          <el-badge :value="7" class="item" type="primary">
          </el-badge>
          <div class="broken-box">
            <div>
              <h4>维修中心</h4>
            </div>
            <div class="repair-center">
              <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="全部" name="first">
                  <el-col :span="24" v-for="(item, index) in noticeData" :key="index" class="repair-center-list">
                    <el-col :span="4">{{ item.equipName }}</el-col>
                    <el-col :span="9">{{ item.content }}</el-col>
                    <el-col :span="4">{{ item.department }}</el-col>
                    <el-col :span="3" :style="{ color: item.emergency > 0 ? 'red' : 'green' }">{{ item.emergency > 0 ? '紧急'
                      : '正常' }}</el-col>
                    <el-col :span="4">
                      <el-button :type="item.status === 0 ? 'warning' : item.status === 1 ? 'success' : 'primary'"
                        size="mini">{{ item.status === 0 ? '待维修' : item.status === 1 ? '维修中' : '已完成' }}</el-button>
                    </el-col>
                  </el-col>
                </el-tab-pane>
                <el-tab-pane label="待维修" name="second">
                </el-tab-pane>
                <el-tab-pane label="维修中" name="third">维修中</el-tab-pane>
                <el-tab-pane label="已完成" name="fourth">已完成</el-tab-pane>
              </el-tabs>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </el-container>
</template>

<script>
import * as echarts from 'echarts';
import 'echarts-liquidfill'
export default {
  name: "Console",
  data() {
    return {
      url: '../src/assets/image/004.png',
      form: {
        depart: "织造车间",
        department: ""
      },
      activeName: 'first',
      departList: [
        {
          label: "织造车间",
          value: "织造车间"
        },
        {
          label: '整理车间',
          value: '整理车间'
        },
        {
          label: '打样车间',
          value: '打样车间'
        },
        {
          label: '综合维修',
          value: '综合维修'
        }
      ],
      progressList: [
        {
          id: 1,
          name: "日保养",
          value: 80,
          color: '#30C81F'
        },
        {
          id: 2,
          name: "周保养",
          value: 60,
          color: '#EBC633'
        },
        {
          id: 3,
          name: "月保养",
          value: 40,
          color: '#EB6E25'
        },
        {
          id: 4,
          name: "季度保养",
          value: 20,
          color: '#EB3C85'
        },
        {
          id: 5,
          name: "半年保养",
          value: 10,
          color: '#B95CEB'
        },
        {
          id: 6,
          name: "年保养",
          value: 5,
          color: '#517DF7'
        }
      ],
      maintainNumList: [{
        id: 1,
        name: "日保养",
        value: 80,
      }, {
        id: 2,
        name: "周保养",
        value: 75,
      }, {
        id: 3,
        name: "月保养",
        value: 88,
      }, {
        id: 4,
        name: "季度保养",
        value: 34,
      }, {
        id: 5,
        name: "半年保养",
        value: 25,
      }, {
        id: 6,
        name: "年保养",
        value: 12,
      }],
      oilList: [
        {
          id: 1,
          area: "1#织造横机区域",
          value: 60,
        },
        {
          id: 2,
          area: "2#织造横机区域",
          value: 58,
        },
        {
          id: 3,
          area: "3#织造横机区域",
          value: 30,
        },
        {
          id: 4,
          area: "4#织造横机区域",
          value: 56,
        },
        {
          id: 5,
          area: "5#织造横机区域",
          value: 54,
        },
        {
          id: 6,
          area: "6#织造横机区域",
          value: 60,
        },
        {
          id: 7,
          area: "7#织造横机区域",
          value: 60,
        },
        {
          id: 8,
          area: "8#织造横机区域",
          value: 50,
        },
        {
          id: 9,
          area: "9#织造横机区域",
          value: 60,
        },
        {
          id: 10,
          area: "11#织造横机区域",
          value: 60,
        },
        {
          id: 12,
          area: "12#织造横机区域",
          value: 50,
        }
      ],
      dailyworkList: [
        {
          id: 1,
          username: '张守冉',
          num: 10,
          avatar: '../src/assets/image/004.png'
        },
        {
          id: 2,
          username: '黄少锋',
          num: 12,
          avatar: '../src/assets/image/004.png'
        },
        {
          id: 3,
          username: '伍祚国',
          num: 14,
          avatar: '../src/assets/image/004.png'
        },
        {
          id: 4,
          username: '孙春光',
          num: 9,
          avatar: '../src/assets/image/004.png'
        },
        {
          id: 5,
          username: '刘爱芳',
          num: 7,
          avatar: '../src/assets/image/004.png'
        },
        {
          id: 6,
          username: '徐伟',
          num: 13,
          avatar: '../src/assets/image/004.png'
        },
        {
          id: 7,
          username: '胡友利',
          num: 8,
          avatar: '../src/assets/image/004.png'
        },
        {
          id: 8,
          username: '吴福友',
          num: 11,
          avatar: '../src/assets/image/004.png'
        }
      ],
      noticeData: [
        {
          id: 1,
          equip: 'F0001',
          equipName: '电脑编织横机',
          content: '测试无网络，水晶头不亮',
          department: '织造车间',
          username: '张守冉',
          emergency: 0,
          status: 1,
        },
        {
          id: 2,
          equip: 'TZML5415484',
          equipName: '电脑主机',
          content: '无法开机，蓝屏',
          department: '检验车间',
          username: '安冉',
          emergency: 1,
          status: 0,
        },
        {
          id: 3,
          equip: 'FCDC20211001',
          equipName: '精雕机',
          content: '无法上传程序',
          department: '打样车间',
          username: '唐婷',
          emergency: 1,
          status: 2,
        },
        {
          id: 4,
          equip: 'FCDC20211002',
          equipName: '割纸机',
          content: '程序无法启动',
          department: '业务',
          username: '贺知州',
          emergency: 0,
          status: 0,
        },
        {
          id: 5,
          equip: 'FCDC20211002',
          equipName: '割纸机',
          content: '程序无法启动',
          department: '业务',
          username: '贺知州',
          emergency: 0,
          status: 0,
        },
        {
          id: 6,
          equip: 'FCDC20211002',
          equipName: '割纸机',
          content: '程序无法启动',
          department: '业务',
          username: '贺知州',
          emergency: 0,
          status: 0,
        },
        {
          id: 7,
          equip: 'FCDC20211002',
          equipName: '割纸机',
          content: '程序无法启动',
          department: '业务',
          username: '贺知州',
          emergency: 0,
          status: 0,
        }
      ]
    };
  },
  mounted() {
    this.initChart();
    // setTimeout(() => {
    //   this.$store.dispatch('showMessage', { message: '您收到一条新的消息，织造车间王佳俊报修了编号为F0001的电脑编织横机水晶头不会闪烁的问题，请尽快帮忙处理！', type: 'success' });
    // }, 1000);
  },
  methods: {
    // 切换tab事件
    handleClick(tab, event) {
      console.log(tab, event);
    },
    // 初始化图表
    initChart() {
      let chartDom = document.querySelector("#equipment");
      let chartDom1 = document.querySelector("#mains");
      let myChart1 = echarts.init(chartDom1);
      let myChart = echarts.init(chartDom);
      let option, options;

      // 折线图
      option = {
        title: {
          text: '日工作量趋势图',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        toolbox: {
          show: true,
          feature: {
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ['line', 'bar'] },
            restore: { show: true },
            saveAsImage: { show: true }
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [150, 230, 224, 218, 135, 147, 260],
            markPoint: {
              label: {
                color: '#feffff', // 设置标记点的字体颜色
              },
              data: [
                { type: 'max', name: 'Max' },
                { type: 'min', name: 'Min' }
              ]
            },
            markLine: {
              data: [{ type: 'average', name: 'Avg' }, [
                {
                  symbol: 'none',
                  x: '90%',
                  yAxis: 'max'
                },
                {
                  symbol: 'circle',
                  label: {
                    position: 'start',
                    formatter: 'Max'
                  },
                  type: 'max',
                  name: '最高点'
                }
              ]]
            },
            type: 'line',
            smooth: true,
            lineStyle: {
              color: '#517DF7',
              width: 8
            },
            itemStyle: {
              color: '#517DF7'
            }
          }
        ]
      };
      // 圆环图
      options = {
        title: {
          text: '保养次数统计',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        toolbox: {
          show: true,
          feature: {
            mark: { show: true },
            dataView: { show: true, readOnly: false },
            restore: { show: true },
            saveAsImage: { show: true }
          }
        },
        series: [
          {
            name: '保养次数',
            type: 'pie',
            radius: [25, 80],
            center: ['55%', '60%'],
            roseType: 'area',
            itemStyle: {
              borderRadius: 5
            },
            label: {
              show: true
            },
            emphasis: {
              label: {
                show: true
              }
            },
            data: [
              { value: 40, name: '日保养' },
              { value: 33, name: '周保养' },
              { value: 28, name: '月保养' },
              { value: 22, name: '季度保养' },
              { value: 20, name: '半年保养' },
              { value: 15, name: '年保养' }
            ]
          }
        ]
      };

      // 图表初始化
      option && myChart.setOption(option);
      options && myChart1.setOption(options);
    },
  },
};
</script>

<style scoped lang="scss">
.brush-list {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  flex-wrap: wrap;
  padding: 10px;

  .brush-list-item {
    flex: 0 0 calc(50% - 20px); // 一行2列布局，减去padding和margin
    padding: 10px;
    background-color: #ECF5FF;
    margin: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;

    span {
      font-size: 14px;
    }
  }

  .brush-list-item span:nth-child(1) {
    color: #666;
  }

  .brush-list-item span:nth-child(2) {
    margin-left: 10px;
    color: #5CB1F7;
    font-weight: 600;
  }
}

.equipment-box {
  padding: 10px 0px;
}

.broken-box {
  padding: 10px 0;
}
.el-tab-pane {
  span {
    margin: 0 20px;
  }
}
.repair-center {
  padding: 10px 0;
}
.repair-center-list {
  margin: 5px 0;

  .el-col {
    font-size: 12px;
    color: #666;
  }
}

.equipment-list {
  display: flex;
  padding: 20px 0;

  .left-chart {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    h5 {
      font-size: 16px;
      color: #666;
      font-weight: 600;
      letter-spacing: 1px;
      font-family: "Microsoft YaHei";
      margin-top: 10px;
    }
  }

  .right-charts-box {
    padding: 20px;
    flex: 1;

    .right-charts {
      display: flex;
      flex-direction: column;
      flex: 1;
      justify-content: center;
      align-items: center;

      .right-charts-num {
        font-size: 18px;
        color: #333;
        font-weight: 600;
        margin-top: 15px;
      }

      .right-charts-title {
        font-size: 16px;
        color: #666;
        font-weight: 600;
        letter-spacing: 1px;
        font-family: "Microsoft YaHei";
        margin-top: 15px;
      }
    }
  }
}

.el-select {
  all: unset;
  margin-right: 10px;
  outline: none;
}

//清除el-select边框
.el-select .el-input {
  border: 0;
  margin-right: 10px;
}

.el-container {
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;

  .el-radio-group .el-radio {
    margin-right: 10px;
  }

  .logo {
    font-size: 30px;
    color: #5CB1F7;
    font-weight: 600;
  }

  h4 {
    font-size: 14px;
    color: #666;
    font-weight: 600;
    letter-spacing: 1px;
    font-family: 'Times New Roman', Times, serif;
  }

  .card-box {
    background-color: #E5E9F2;
    padding: 10px 15px;
    border-radius: 12px;
    width: 27%;
    height: 80px;
    margin: 5px;
    display: flex;

    span {
      font-size: 14px;
      margin-left: 6px;
      font-weight: 600;
      color: #999;
    }

    .card-box-title {
      flex: 1;

      .num-box {
        flex: 1;
        padding: 20px 0;
        font-weight: bold;
        font-size: 20px;
        color: #666;
      }
    }

    .card-box-content {
      flex: 1;
      margin-top: 30px;
    }
  }
  .card-box:hover {
    background-color: #E6E6E6;
    cursor: pointer;
  }

  .efficient {
    h4 {
      font-size: 14px;
      color: #666;
      font-weight: 600;
      letter-spacing: 1px;
      font-family: 'Times New Roman', Times, serif;
      margin: 6px;
    }
  }

  .efficient-box {
    border-radius: 7px;
    padding: 10px;
    margin: 5px;
    background-color: #F6F7F9;
    cursor: pointer;
    width: calc(100% / 4.5);
    transition: all 0.3s ease; // 添加过渡效果

    .user-efficient-box {
      display: flex;
      align-items: center;
      gap: 10px; // 使用gap设置间距

      .left-efficient {
        display: flex;
        flex-direction: column;
        padding: 5px 0;
        flex: 1;
        min-width: 0; // 防止文本溢出

        h5 {
          font-size: 12px;
          color: #666;
          letter-spacing: 1px;
          margin: 0;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        h2 {
          padding: 12px 0;
          font-weight: normal;
          font-size: 16px;
          margin: 0;

          span {
            color: #999;
            font-size: 12px;
          }
        }
      }

      .right-efficient {
        width: 40px; // 固定宽度
        height: 40px; // 固定高度
        flex-shrink: 0; // 防止被压缩
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
  .efficient-box:hover {
    background-color: #B3D9D9;
    box-shadow: 0 0 10px #B3D9D9;
  }

  .el-card {
    position: relative;
  }

  .el-badge {
    position: absolute;
    top: 1px;
    left: 3px;
  }
}
</style>
