import{o as n,g as m,b as t,w as a,d as c,t as _}from"./index-444b28c3.js";import{E as d,a as u}from"./el-table-column-fa1764a8.js";import"./el-checkbox-f3df62fa.js";import{E as b}from"./el-tag-29cbefd8.js";import"./el-tooltip-4ed993c7.js";import"./el-scrollbar-af6196f4.js";import{E as h}from"./el-divider-d33f4e37.js";import"./event-fe80fd0c.js";import"./index-e305bb62.js";import"./index-4d7f16ce.js";import"./focus-trap-6de7266c.js";const f={class:"card",style:{height:"400px",cursor:"pointer"}},M={__name:"HomeTable",props:{tableData:{type:Array,default:()=>[]}},setup(l){const r=l;return(e,E)=>{const s=b,p=h,o=d,i=u;return n(),m("div",f,[t(s,{type:"success"},{default:a(()=>[c("Model "+_(e.$t("home.xiajipianshu")),1)]),_:1}),t(p),t(i,{data:r.tableData,border:"",style:{width:"100%"}},{default:a(()=>[t(o,{prop:"model",label:"Model"}),t(o,{prop:"quantity",label:e.$t("home.xiajishuliang")},null,8,["label"])]),_:1},8,["data"])])}}};export{M as default};
