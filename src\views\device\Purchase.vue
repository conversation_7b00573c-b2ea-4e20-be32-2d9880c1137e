<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-08-12 15:37:22
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-26 10:01:22
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\device\PurchaseRecord.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-card>
            <el-form :model="form" ref="form" class="forms" inline>
                <el-form-item prop="username" label="经办人">
                    <el-input v-model="form.username" placeholder="请输入经办人姓名"></el-input>
                </el-form-item>
                <el-form-item prop="keywords" label="设备名称">
                    <el-input v-model="form.keywords" placeholder="请输入设备名称"></el-input>
                </el-form-item>
                <el-form-item prop="department" label="申购部门">
                    <el-select placeholder="请选择申购部门" v-model="form.department" clearable>
                        <el-option label="IT" value="IT"></el-option>
                        <el-option label="织造车间" value="织造车间"></el-option>
                        <el-option label="整理车间" value="整理车间"></el-option>
                        <el-option label="检验车间" value="检验车间"></el-option>
                        <el-option label="打样车间" value="打样车间"></el-option>
                        <el-option label="综合维修" value="综合维修"></el-option>
                        <el-option label="化验室" value="化验室"></el-option>
                        <el-option label="行政办公室" value="行政办公室"></el-option>
                        <el-option label="食堂" value="食堂"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="status" label="申购状态">
                    <el-select placeholder="请选择申购状态" v-model="form.status" clearable>
                        <el-option label="待审批" value="待审批"></el-option>
                        <el-option label="询价中" value="询价中"></el-option>
                        <el-option label="审批中" value="审批中"></el-option>
                        <el-option label="采购中" value="采购中"></el-option>
                        <el-option label="已到货" value="已到货"></el-option>
                    </el-select>
                </el-form-item>
                 <!-- 采购日期 -->
                 <el-form-item prop="date" label="申购日期">
                    <el-date-picker v-model="form.date" type="date" placeholder="请选择申购日期" value-format="yyyy-MM-dd"
                        style="width: 100%;"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSubmit" icon="el-icon-search">查询</el-button>
                    <el-button @click="onReset('form')" icon="el-icon-refresh">重置</el-button>
                    <el-button type="primary" plain @click="onRefresh" icon="el-icon-refresh-right"></el-button>
                    <el-button type="success" icon="el-icon-shopping-cart-2" @click="createPurchase">申购单</el-button>
                </el-form-item>
            </el-form>
            <!-- 主体部分，表格，设备名称，品牌型号，供应商，申购数量，设备单价预估，设备总金额预估，询价人，采购经办人，申购原因及其设备要求，申购人，联系方式，设备管理负责人，部门负责人，审批人 -->
            <el-table :data="tableData" style="width: 100%" stripe v-loading="loading" element-loading-text="Flyknit">
                <!-- 索引 -->
                <el-table-column type="index" width="50"></el-table-column>
                <el-table-column prop="order_number" label="采购单号" ></el-table-column>
                <el-table-column prop="device_name" label="设备名称" ></el-table-column>
                <el-table-column prop="brand" label="品牌型号"></el-table-column>
                <el-table-column prop="supplier" label="供应商" ></el-table-column>
                <el-table-column prop="quantity" label="申购数量" ></el-table-column>
                <el-table-column prop="operator" label="采购经办人" ></el-table-column>
                <el-table-column prop="reason" label="申购原因及其要求" width="200"></el-table-column>
                <el-table-column prop="applicant" label="申购人" ></el-table-column>
                <el-table-column prop="department" label="申购部门" ></el-table-column>
                <el-table-column prop="purchase_status" label="申购状态" >
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.purchase_status == 0" type="danger">待审批</el-tag>
                        <el-tag v-else-if="scope.row.purchase_status == 1" type="success">已批准</el-tag>
                        <el-tag v-else-if="scope.row.purchase_status == 2" type="warning">询价中</el-tag>
                        <el-tag v-else-if="scope.row.purchase_status == 3" type="info">采购中</el-tag>
                        <el-tag v-else-if="scope.row.purchase_status == 4" type="success">已完成</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="create_time" label="申购时间" width="160"></el-table-column>
                <!-- 操作 -->
                <el-table-column label="操作" width="180">
                    <template slot-scope="scope">
                        <!-- <el-button type="button" size="mini" @click="handleEdit(scope.$index, scope.row)"
                            icon="el-icon-more-outline">详情</el-button> -->
                        <el-button type="primary" size="mini" @click="handleEdit(scope.$index, scope.row)"
                            icon="el-icon-edit">编辑</el-button>
                        <el-button type="danger" size="mini" @click="handleDelete(scope.row.id)"
                            icon="el-icon-delete">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页组件 -->
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
                :page-sizes="[10, 20, 30, 40]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="total">
            </el-pagination>
        </el-card>
    </div>
</template>
<script>
import { set } from 'vue';

export default {
    data() {
        return {
            loading: true,
            form: {
                keywords: '',
                username: '',
                date: '',
                department: '',
                status: ''
            },
            tableData: [],
            currentPage: 1,
            pageSize: 10,
            total: 10,
        };
    },
    mounted() {
        this.getPurchaseList();
    },
    methods: {
        // 获取申购单列表
        async getPurchaseList() {
            try {
                this.form.page = this.currentPage;
                this.form.pageSize = this.pageSize;
                const res = await this.$http.getPurchaseList(this.form);
                if (res.status !== 200) return this.$message.error(res.message);
                const { list, total } = res.data;
                this.tableData = list;
                this.total = total;
                setTimeout(() => {
                    this.loading = false;
                }, 500);
            } catch (error) {
                this.loading = false;
                return this.$message.error('服务器错误，请稍后重试！');
            }
        },
        // 打开申购单弹窗
        createPurchase() {
            // 跳转到申购单预览页面
            this.$router.push({ path: '/purchasePreview' });
        },
        onSubmit() {
            if (this.form.username || this.form.date || this.form.department || this.form.status) {
                this.loading = true;
                this.getPurchaseList();
            } else {
                return false
            }
        },
        // 重置表单
        onReset(formName) {
            this.$refs[formName].resetFields();
            this.loading = true;
            this.getPurchaseList();
        },
        onRefresh() {
            this.loading = true;
            this.getPurchaseList();
        },
        handleSizeChange(val) {
            this.pageSize = val;
            this.loading = true;
            this.getPurchaseList();
        },
        handleCurrentChange(val) {
            this.currentPage = val;
            this.loading = true;
            this.getPurchaseList();
        },
        // 编辑申购单
        handleEdit(index, row) {
            // 跳转到申购单预览页面
            this.$router.push({ path: '/purchasePreview', query: { info: row } });
        },
    },
};
</script>
<style>
.el-card {
    width: 100%;
}
.el-card__body {
    padding: 20px 20px 0 20px;
}

.el-pagination {
    margin: 10px 0;
}

.dialog-content {
    height: 650px;
    /* 设置el-dialog的固定高度 */
    overflow: hidden;
    /* 隐藏超过固定高度的内容 */
    display: flex;
    flex-direction: column;
}

.scrollable-content {
    flex-grow: 1;
    overflow-y: auto;
    /* 允许内容滚动 */
}
.forms {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}
</style>