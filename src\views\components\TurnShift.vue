<template>
  <div>
    <el-dialog
      title="交接班"
      :visible="visible"
      width="50%"
      center
      class="showAll_dialog"
      @close="close"
    >
      <div style="height: 55vh; box-sizing: border-box; overflow: scroll">
        <div style="display: flex; flex-direction: column">
          <div class="shift-class">
            <span>交班分类标签</span>
            <div class="add-box" @click="addHandle">
              <i class="el-icon-edit-outline" style="color: #409eff"></i
              ><span class="add-text">添加标签</span>
            </div>
          </div>
          <div class="add-box-wrap" v-show="showAdd">
            <el-input
              v-model="classify"
              placeholder="请输入要添加的类型"
            ></el-input>
            <el-button type="primary" icon="el-icon-plus"
            @click="addClassify"
              >添加</el-button
            >
          </div>
          <div class="box-wrapper">
            <div class="tag-box" v-for="(tag, index) in tags" :key="index">
              <el-button
                :class="[tabIndex === index ? 'type-btns' : 'type-btn']"
                :type="tag.type"
                @click="tabHandle(index, tag.classify)"
              >
              <i class="el-icon-success" v-if="tabIndex === index"></i>
                {{ tag.classify }}
              </el-button>
            </div>
          </div>
        </div>
        <el-row>
          <div class="shift-class">接班班次</div>
          <div class="shift-list">
            <el-radio
              @input="changeShift"
              v-for="(item, index) in shiftList"
              :key="index"
              v-model="radio"
              :label="item.name"
              border
             
              >{{ item.name }}</el-radio
            >
          </div>
        </el-row>
        <el-row>
          <div class="shift-class">交接事项</div>
          <div style="margin: 20px 20px">
            <el-input
              type="textarea"
              :rows="5"
              v-model="remarks"
              placeholder="请详细描述你的问题以便接班者更好的追踪"
            ></el-input>
          </div>
        </el-row>
        <!-- 添加图片 -->
        <el-row>
          <div class="shift-class">现场图片</div>
          <div style="margin: 20px 20px">
            <el-upload
              :action="action"
              list-type="picture-card"
              name="file"
              accept=".jpg,.png,.webp,.jfif"
              :limit="6"
              :on-remove="logoRemove"
              :on-success="logoSuccess"
              :on-preview="handleview"
              :multiple="false"
              :on-error="onErr"
              :before-upload="project"
              :file-list="logo"
            >
              <i class="el-icon-plus"></i>
            </el-upload>
            <!-- 大图展开 -->
            <el-dialog :modal="false" :visible.sync="dialogVisible">
              <img width="100%" :src="dialogImageUrl" alt="" />
            </el-dialog>
          </div>
        </el-row>
        <!-- 添加视频 -->
        <el-row style="position:relative;box-sizing:border-box;">
          <div class="shift-class">现场视频</div>
          <div v-show="videoForm.showVideoPath.length > 0" class="del-icon" @click.stop="delVideo">
            <i class="el-icon-delete" style="font-size:22px;color:#ccc;font-weight:bold;"></i>
          </div>
          <div style="margin: 20px 20px">
            <el-upload
              class="avatar-uploader"
              list-type="picture-card"
              name="file"
              accept=".mp4, .avi, .wmv, .mov, .m4v"
              :action="action"
              :limit="1"
              :on-progress="uploadVideoProcess"
              :on-success="handleVideoSuccess"
              :before-upload="beforeUploadVideo"
              :show-file-list="false"
            >
              <video
                v-if="videoForm.showVideoPath != '' && !videoFlag"
                :src="videoForm.showVideoPath"
                controls="controls"
                object-fit="cover"
                autoplay
                loop
                :muted="false"
              >
                您的浏览器不支持视频播放
              </video>
              <i
                v-else-if="videoForm.showVideoPath == '' && !videoFlag"
                class="el-icon-video-camera-solid avatar-uploader-icon"
              ></i>
              <el-progress
                v-if="videoFlag == true"
                type="circle"
                :percentage="videoUploadPercent"
                style="margin-top: 7px"
              ></el-progress>
            </el-upload>
            <el-dialog :visible.sync="dialogVisible">
              <img width="100%" :src="dialogImageUrl" alt="" />
            </el-dialog>
          </div>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="reset" size="mini">重置</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="submitForm"
          icon="el-icon-position"
          >立 即 提 交</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {mapState} from 'vuex';
export default {
  data() {
    return {
      videoFlag: false,
      //是否显示进度条
      videoUploadPercent: "",
      //进度条的进度，
      isShowUploadVideo: false,
      //显示上传按钮
      videoForm: {
        showVideoPath: "",
      },
      shiftList: [
        {
          id: 1,
          name: "A",
        },
        {
          id: 1,
          name: "B",
        },
        {
          id: 1,
          name: "C",
        },
        {
          id: 1,
          name: "E",
        },
      ],
      // 班次
      radio: "",
      classify: "", // 要添加的分类
      showAdd: false, // 展示添加输入框
      tabIndex: 0,
      remarks: "", // 交班事项
      dialogImageUrl: "",
      dialogVisible: false,
      logo: [],
      // 上传logoing
      loadmen: false,
      imgList: [],
      action: "http://*************/im/upload/uploadMore?_token=" + localStorage.getItem('accessToken'),
      shift: "", // 选择的班次
      webUrl: "http://*************/uploads/", // 图片地址前缀
      imglists: [], // 上传的图片集合
      videoList: [], // 上传的视频id
      token: ""
    };
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    tags: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.users
    })
  },
  created() {
    this.token = localStorage.getItem("accessToken")
  },
  methods: {
    // 添加交班分类
    async addClassify() {
      if(!this.classify) {
        return this.$message.warning('请输入要添加的类型')
      }
      try {
        const res = await this.$http.addShiftClassify({
          _token: this.token,
          type: this.classify,
          depart: this.userInfo.doodling
        })
        //console.log('res', res);
        // 通知父页面获取新的交班分类
        this.$emit("getDatas");
        return this.$message.success(res.msg)
      } catch (error) {
        return this.$message.error(error);
      }
    },
    // 删除视频
    delVideo() {
      this.videoForm.showVideoPath = ''
      this.isShowUploadVideo = false;
      this.videoFlag = false;
      this.videoUploadPercent = 0;
    },
    //上传前回调
    beforeUploadVideo(file) {
      let fileSize = file.size / 1024 / 1024 < 50;
      if (
        [
          "video/mp4",
          "video/ogg",
          "video/flv",
          "video/avi",
          "video/wmv",
          "video/rmvb",
          "video/mov",
        ].indexOf(file.type) == -1
      ) {
        this.$message.error("请上传正确的视频格式");
        return false;
      }
      if (!fileSize) {
        this.$message.error("视频大小不能超过50MB");
        return false;
      }
      this.isShowUploadVideo = false;
    },
    //进度条
    uploadVideoProcess(event, file, fileList) {
      this.videoFlag = true;
      this.videoUploadPercent = file.percentage.toFixed(0) * 1;
    },
    //上传视频成功回调
    handleVideoSuccess(res, file) {
      // console.log('上传结果', res);
      //console.log('上传文件', file);
      this.isShowUploadVideo = true;
      this.videoFlag = false;
      this.videoUploadPercent = 0;
      const {list} = res.data
      this.videoList.push(list.id);
      //后台上传地址
      if (list) {
        this.videoForm.showVideoPath = this.webUrl + list.img_url;
      } else {
        this.$message.error(res.Message);
      }
    },
    // 提交交班
    async submitForm() {
      if (!this.remarks) {
        return this.$message.warning("交接事项不能为空");
      }
      const obj = {
					duty_sent: this.userInfo.user_type,// 交班人班次
					duty_depart: this.userInfo.doodling, // 交班人部门
					duty_receive: this.radio, // 接班班次
					duty_type: this.tabIndex,// 交班类型0,1,2,3
					duty_name: this.shift, // 交班分类
					contents: this.remarks, // 交班内容
					img_id: JSON.stringify(this.imglists),
					video_id: JSON.stringify(this.videoList),
					list_id: ""
				}
        // console.log('交班', obj);
        try {
          const res = await this.$http.sendShift({
            datas: obj,
            _token: this.token
          })
          // console.log('res', res);
          const {data} = res.data
          if(data.id) {
            setTimeout(() =>{
              this.close()
            }, 500)
            this.tabIndex = 0
            this.imglists = []
            this.videoList = []
            this.shift = ''
            this.radio = ''
            this,this.remarks = ''
            this.logo = []
            this.videoForm.showVideoPath = ''
            return this.$message.success(res.msg)
          }else {
            return this.$message.error('交班失败，请稍后再试!')
          }
        } catch (error) {
          return this.$message.error(error)
        }
      // console.table(this.shift, this.radio, this.remarks, this.imglists, this.videoList);
    },
    // 选择接班班次
    changeShift(e) {
      // console.log("接班班次", e);
      this.radio = e;
    },
    handleview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    // 上传失败
    onErr(e) {
      this.loadmen = false;
      this.$message.error("上传失败,尝试重新上传");
    },
    // 上传时
    project(file) {
      this.loadmen = true;
    },
    // logo移除文件时的钩子
    logoRemove(file, fileList) {
      this.logo = [];
    },
    // 上传成功：logo
    logoSuccess(res, file, fileList) {
      // console.log('上传结果', res);
      // console.log('返回的文件', file);
      const {list} = res.data
      this.imglists.push(list.id);
      this.logo.push({
        url: this.webUrl + res.data.list.img_url,
        uid: file.uid,
      }); //element展示图片时需要数组类型的才能展示
      this.loadmen = false;
    },
    // 重置
    reset() {
      console.log("表单重置");
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    // 交班分类点击事件
    tabHandle(index, shift) {
      this.tabIndex = index;
      this.shift = shift;
      // console.log('交班分类', this.shift)
    },
    // 关闭事件
    close() {
      this.$emit("closeShift");
    },
    // 添加分类
    addHandle() {
      this.showAdd = !this.showAdd;
    },
  },
};
</script>
<style lang="scss" scoped>
.shift-class {
  margin: 20px 0;
  padding-left: 20px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  letter-spacing: 1px;
  .add-box {
    margin: 0 20px;
    .add-text {
      color: #409eff;
      margin-left: 5px;
      font-size: 0.86rem;
      cursor: pointer;
    }
  }
}
.shift-list {
  margin: 20px 20px;
  display: flex;
  justify-content: flex-start;
}
.add-box-wrap {
  padding: 0 20px 20px 20px;
  display: flex;
  .el-input {
    width: 200px;
  }
  .el-button {
    margin-left: 20px;
  }
}
.box-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  flex: wrap;
  padding: 0 20px;
}
.tag-box {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  box-sizing: border-box;
  overflow: hidden;
  .type-btns {
    background-color: #409eff;
    color: #fff;
  }
  .el-button {
    margin-right: 10px;
  }
}
.dialog-footer {
  display: flex;
  justify-content: center;
  align-items: center;
}
.avatar-uploader {
  width: 100%;
  display: flex;
  box-sizing: border-box;
}
.el-upload video {
  height: 320px;
  background-color: #000;
}
.del-icon {
  position:absolute;right:20%;top:65px;z-index: 999999;cursor: pointer;
}
</style>
