<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-07-24 09:14:24
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-15 11:18:33
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\department\UserList.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
	<div>
		<!-- 卡片式图 -->
		<el-card class="card-list">
			<el-form :inline="true" :model="form" class="demo-form-inline">
				<el-form-item label="姓名/工号">
					<el-input placeholder="请输入机修姓名或者工号" v-model="keyword" clearable></el-input>
				</el-form-item>
				<el-form-item label="部门">
					<el-select v-model="depart" placeholder="请选择部门">
						<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="search">查询</el-button>
				</el-form-item>
				<el-form-item v-show="User.level > 0">
					<el-button type="success" @click="addUser" icon="el-icon-user">添加</el-button>
				</el-form-item>
				<el-form-item>
					<el-button icon="el-icon-refresh" @click="resetHandler">重置</el-button>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" icon="el-icon-refresh-right" plain @click="refresh"></el-button>
				</el-form-item>
			</el-form>
			<!-- </el-card>
		<el-card style="margin-top:5px;"> -->
			<!-- 订单列表区域 -->
			<el-table :data="userList" stripe style="width: 100%;" v-loading="loading" element-loading-text="Flyknit">
				<el-table-column label="#" type="index">
					<el-checkbox v-model="checked"></el-checkbox>
				</el-table-column>
				<el-table-column label="头像" prop="avatar" width="100">
					<template slot-scope="scope">
						<!-- <el-image style="width: 50px; height: 50px;border-radius:50%;" :src="scope.row.avatar" lazy :fit="fit">
							<div slot="error" class="image-slot">
								<i class="el-icon-picture-outline"></i>
							</div>
						</el-image> -->
						<img :src="scope.row.avatar" style="width: 50px; height: 50px;border-radius:50%;" alt="加载失败">
					</template>
				</el-table-column>
				<el-table-column sortable label="工号" prop="uuid" width="100"></el-table-column>
				<el-table-column label="姓名" prop="username" width="100"></el-table-column>
				<el-table-column label="部门" prop="department" width="100"></el-table-column>
				<el-table-column label="班次" prop="shift" width="100"></el-table-column>
				<el-table-column label="用户类型" prop="level" width="100">
					<template slot-scope="scope">
						<span :class="scope.row.level > 0 ? 'status-text' : 'wait-complete'">{{ scope.row.level === 0 ? '普通用户' : '管理员' }}</span>
					</template>
				</el-table-column>
				<el-table-column label="保养区域" prop="area" width="150">
					<template slot-scope="scope">
						<span>{{ scope.row.area ? scope.row.area : '暂未分配区域' }}</span>
					</template>
				</el-table-column>
				<el-table-column label="创建日期" prop="create_time" width="160"></el-table-column>
				<el-table-column label="登录状态" prop="status" width="130">
					<template slot-scope="scope">
						<span :class="scope.row.is_online > 0 ? 'status-text' : 'wait-complete'">{{ scope.row.is_online > 0
							?
							"在线"
							: "离线" }}</span>
					</template>
				</el-table-column>
				<el-table-column label="账户状态" prop="is_ban" width="120">
					<template slot-scope="scope">
						<span :class="scope.row.is_ban === 0 ? 'status-text' : 'wait-complete'">{{ scope.row.is_ban === 0 ?
							"正常"
							: "已禁用" }}</span>
					</template>
				</el-table-column>

				<el-table-column label="操作" v-show="User.level > 0">
					<template slot-scope="scope">
						<el-button size="mini" plain @click.native="modifyUser(scope.row)">
							<i class="el-icon-edit"></i>
							修改
						</el-button>
						<el-button size="mini" type="danger" plain @click.native="deleteUser(scope.row.uuid)">
							<i class="el-icon-delete"></i>
							删除
						</el-button>
						<el-button size="mini" type="warning" plain @click.native="disableUser(scope.row.uuid)">
							<i :class="scope.row.is_ban > 0 ? 'el-icon-lock' : 'el-icon-unlock'"></i>
							{{ scope.row.is_ban < 1 ? "锁定" : "解锁" }} </el-button>
						<el-button size="mini" type="success" plain @click="addRole(scope.row.uuid, scope.row.username)">
							<i class="el-icon-user"></i>
							角色分配
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<!-- 分页 -->
			<div class="page" v-show="total > 0">
				<el-pagination @size-change="handleSizeChange" @current-change="currentchange"
					:current-page="queryInfo.currentnum" :page-sizes="pageSizes" :page-size="queryInfo.pageSize"
					layout="total, sizes, prev, pager, next, jumper" :total="total">
				</el-pagination>
			</div>
		</el-card>
		<!-- 添加用户弹窗 -->
		<el-dialog :title="title" :visible.sync="dialogFormVisible" width="45%" :close-on-click-modal="false"
			:close-on-press-escape="false">
			<div class="input-box">
				<!-- 员工姓名 -->
				<div class="image-view-title">
					<div class="image-list">员工姓名</div>
					<el-input v-model="form.username" placeholder="请输入员工姓名" maxlength="20"></el-input>
				</div>
				<!-- 工号 -->
				<div class="image-view-title">
					<div class="image-list">员工工号</div>
					<el-input v-model="form.uuid" type="text" :readonly="isAdd ? false : true" placeholder="请输入员工工号"
						maxlength="5"></el-input>
				</div>
				<!-- 密码 -->
				<div class="image-view-title">
					<div class="image-list">登录密码</div>
					<el-input v-model="form.password" type="password" placeholder="请创建登录密码" maxlength="15"></el-input>
				</div>
				<!-- 性别 -->
				<div class="image-view-title">
					<div class="image-list">员工性别</div>
					<el-radio-group v-model="form.sex">
						<el-radio label="男"></el-radio>
						<el-radio label="女"></el-radio>
					</el-radio-group>
				</div>
				<!-- 部门 -->
				<div class="image-view-title">
					<div class="image-list">所属部门</div>
					<el-select v-model="form.department" placeholder="请选部门" style="margin-left:-20px;">
						<el-option label="织造车间" value="织造车间"></el-option>
						<el-option label="整理车间" value="整理车间"></el-option>
						<el-option label="检验车间" value="检验车间"></el-option>
						<el-option label="打样车间" value="打样车间"></el-option>
						<el-option label="综合维修" value="综合维修"></el-option>
					</el-select>
				</div>
				<!-- 班次 -->
				<div class="image-view-title">
					<div class="image-list">所属班次</div>
					<el-select v-model="form.shift" placeholder="请选班次" style="margin-left:-20px;">
						<el-option label="A" value="A"></el-option>
						<el-option label="B" value="B"></el-option>
						<el-option label="C" value="C"></el-option>
						<el-option label="E" value="D"></el-option>
						<el-option label="E1" value="E1"></el-option>
						<el-option label="E2" value="E2"></el-option>
					</el-select>
				</div>
			</div>
			<!-- 员工照片 -->
			<div class="image-view-title">
				<div class="image-list">员工头像</div>
				<el-upload :action="action" :headers="headers" list-type="picture-card" name="files"
					accept=".jpg,.png,.webp,.jfif" :limit="1" :on-remove="logoRemove" :on-success="logoSuccess"
					:on-preview="handlepreview" :multiple="false" :on-error="onErr" :before-upload="project"
					:file-list="logo">
					<i class="el-icon-plus"></i>
				</el-upload>
				<!-- 大图展开 -->
				<el-dialog :visible.sync="dialogVisible" :modal="false">
					<img width="100%" :src="dialogImageUrl" alt="上传失败" />
				</el-dialog>
			</div>

			<!-- 提交 -->
			<div class="btn-box">
				<el-button type="primary" size="medium" @click="addUserInfo">
					<i style="margin-right: 10px;" :class="isAdd ? 'el-icon-s-promotion' : 'el-icon-edit'"></i>{{ btnText
					}}</el-button>
			</div>
		</el-dialog>
		<!-- 角色分配弹窗 -->
		<el-dialog title="角色分配（超级管理员可以增删改）" :visible.sync="showDialog" width="25%" :close-on-click-modal="false">
			<el-form :model="form"  ref="form" label-width="80px" class="demo-ruleForm">
				<el-form-item label="当前用户">{{roleInfo.name}}</el-form-item>
				<el-form-item label="用户工号">{{roleInfo.uuid}}</el-form-item>
				<el-form-item label="角色分配" prop="role">
					<el-select v-model="form.role" placeholder="请选择角色" style="width: 100%;">
						<el-option v-for="item in roleList" :key="item.id" :label="item.name" :value="item.name">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item style="margin-top: 50px;">
					<el-button type="primary" @click="modifyRole">立即保存</el-button>
				</el-form-item>
			</el-form>
		</el-dialog>
	</div>
</template>
<script>
import { mapState } from 'vuex'
import config from '../../common/config.js'
export default {
	data() {
		return {
			showDialog: false, // 角色分配弹窗
			dialogVisible: false, // 展示大图弹窗
			dialogFormVisible: false,// 显示dialog
			loading: true, // 表格加载
			checked: false,
			// 用户数据
			userList: [{
				id: 1,
				avatar: "https://fuss10.elemecdn.com/5/4a/731a90594e4f4e6b4b0b2b2c4b5b1jpeg.jpeg",
				idcard: "12336",
				username: "张三",
				department: "织造车间",
				shift: "E",
				area: "1#织造横机区域",
				status: "正常",
				isOnline: 1
			}, {
				id: 2,
				avatar: "https://fuss10.elemecdn.com/5/4a/731a90594e4f4e6b4b0b2b2c4b5b1jpeg.jpeg",
				idcard: "12336",
				username: "张三",
				department: "织造车间",
				shift: "E",
				area: "2#织造横机区域",
				status: "正常",
				isOnline: 0
			}, {
				id: 3,
				avatar: "https://fuss10.elemecdn.com/5/4a/731a90594e4f4e6b4b0b2b2c4b5b1jpeg.jpeg",
				idcard: "12336",
				username: "张三",
				department: "织造车间",
				shift: "A",
				area: "3#织造横机区域",
				status: "正常",
				isOnline: 0
			}, {
				id: 4,
				avatar: "https://fuss10.elemecdn.com/5/4a/731a90594e4f4e6b4b0b2b2c4b5b1jpeg.jpeg",
				idcard: "12336",
				username: "张三",
				department: "打样车间",
				shift: "A",
				area: "4#织造横机区域",
				status: "正常",
				isOnline: 0
			}, {
				id: 5,
				avatar: "https://fuss10.elemecdn.com/5/4a/731a90594e4f4e6b4b0b2b2c4b5b1jpeg.jpeg",
				idcard: "12336",
				username: "张三",
				department: "打样车间",
				shift: "A",
				area: "4#织造横机区域",
				status: "正常",
				isOnline: 0
			}, {
				id: 6,
				avatar: "https://fuss10.elemecdn.com/5/4a/731a90594e4f4e6b4b0b2b2c4b5b1jpeg.jpeg",
				idcard: "12336",
				username: "张三",
				department: "打样车间",
				shift: "A",
				area: "4#织造横机区域",
				status: "正常",
				isOnline: 0
			}, {
				id: 7,
				avatar: "https://fuss10.elemecdn.com/5/4a/731a90594e4f4e6b4b0b2b2c4b5b1jpeg.jpeg",
				idcard: "12336",
				username: "张三",
				department: "打样车间",
				shift: "A",
				area: "4#织造横机区域",
				status: "正常",
				isOnline: 0
			}, {
				id: 8,
				avatar: "https://fuss10.elemecdn.com/5/4a/731a90594e4f4e6b4b0b2b2c4b5b1jpeg.jpeg",
				idcard: "12336",
				username: "张三",
				department: "打样车间",
				shift: "A",
				area: "4#织造横机区域",
				status: "正常",
				isOnline: 0
			}],
			keyword: "", // 搜索框
			queryInfo: {
				pageNum: 1,
				pageSize: 8,
				currentnum: 1,
			},
			pageSizes: [8, 16, 24],
			total: 0,
			info: [],
			depart: "", // 状态筛选
			options: [
				{
					value: "织造车间",
					label: "织造车间",
				},
				{
					value: "整理车间",
					label: "整理车间",
				},
				{
					value: "打样车间",
					label: "打样车间",
				},
				{
					value: "综合维修",
					label: "综合维修",
				}
			],
			fit: 'fill', // 图片裁剪方式
			url: 'https://img0.baidu.com/it/u=1821253856,3774998416&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500', // 默认头像
			form: { // 表单数据
				username: '',
				uuid: '',
				password: '',
				sex: '',
				department: '',
				shift: '',
				role: '',
			},
			action: config.uploadURL, // 上传地址
			dialogImageUrl: '', // 大图展示链接
			// 上传logoing
			loadmen: false,
			logo: [],// 上传的图片
			headers: {
				Authorization: `Bearer ${localStorage.getItem("accessToken")}`
			},
			baseURL: config.imgPrefix, // 图片地址
			title: '添加新员工', // 弹窗标题
			btnText: '立即添加', // 按钮文字
			isAdd: 1, // 是否是添加
			roleList: [{
				id: 1,
				name: '超级管理员',
			},{
				id:2,
				name: '普通用户',
			}], // 角色列表
			roleInfo: {}, // 角色信息
		};
	},
	computed: {
		...mapState({
			User: state => state.users,
		})
	},
	mounted() {
		this.getUserList();
	},
	methods: {
		// 保存权限
		async modifyRole() {
			try {
				this.roleInfo.roles = this.form.role;
				const res = await this.$http.modifyRoleinfo(this.roleInfo);
				if (res.status === 200) {
					this.$message.success(res.message);
					this.showDialog = false;
					this.getUserList();
				} else {
					this.$message.error(res.msg);
				}
			} catch (error) {
				return this.$message.error('服务器错误，请稍后重试！');
			}
		},
		// 分配角色
		addRole(uuid, name) {
			const obj = {
				name,
				uuid
			}
			this.roleInfo = obj;
			this.showDialog = true;
		},
		// 修改用户信息
		modifyUser(row) {
			this.dialogFormVisible = true;
			this.form = row;
			this.form.uuid = row.uuid;
			this.logo = [{ url: row.avatar, uid: 1 }];
			this.title = "修改用户信息";
			this.btnText = "立即修改";
			this.isAdd = 0;
		},
		// 展开大图
		handlepreview(file) {
			this.dialogImageUrl = file.url;
			this.dialogVisible = true;
		},
		// 上传失败
		onErr(e) {
			this.loadmen = false;
			this.$message.error("上传失败,尝试重新上传");
		},
		// 上传时
		project(file) {
			this.loadmen = true;
		},
		// logo移除文件时的钩子
		logoRemove(file, fileList) {
			this.logo = [];
		},
		// 上传成功：logo
		logoSuccess(res, file, fileList) {
			const { url } = res.data;
			this.logo.push({ url: this.baseURL + url, uid: file.uid })//element展示图片时需要数组类型的才能展示
			this.loadmen = false;
		},
		// 获取用户列表
		async getUserList() {
			try {
				this.queryInfo.keyword = this.keyword;
				this.queryInfo.department = this.depart;
				const res = await this.$http.getUserList(this.queryInfo);
				if (res.status == 200) {
					const { list } = res.data;
					const { total } = res.data;
					this.userList = list;
					this.total = total;
					setTimeout(() => {
						this.loading = false
					}, 500)
					return false
				} else {
					this.loading = false
					this.userList = [];
					this.total = 0;
					return this.$message.warning(res.message);
				}
			} catch (error) {
				this.loading = false
				return this.$message.error(error.message);
			}
		},
		// 搜索用户
		search() {
			this.getUserList();
		},
		// 开启添加用户弹窗
		addUser() {
			this.dialogFormVisible = true
			this.isAdd = 1;
		},
		// 添加用户
		async addUserInfo() {
			// 校验表单
			if(this.form.username == '' && this.form.password == '' && !this.form.uuid && !this.form.sex && !this.form.department && !this.form.shift && !this.logo.length) {
				return this.$message.warning('请完善表单信息！');
			}
			try {
				this.form.avatar = this.logo[0].url;
				this.form.type = this.isAdd
				const res = await this.$http.addUserInfos(this.form);
				if (res.status == 200) {
					// 关闭弹窗
					this.dialogFormVisible = false;
					// 获取用户列表
					this.getUserList();
					// 清空表单
					this.form.username = "";
					this.form.password = "";
					this.form.uuid = "";
					this.form.sex = "";
					this.form.department = "";
					this.form.shift = "";
					this.logo = [];
					return this.$message.success(res.message);
				} else {
					return this.$message.warning(res.message);
				}
			} catch (error) {
				return this.$message.error("内部服务器错误");
			}
		},
		// 刷新列表
		refresh() {
			this.loading = true
			this.getUserList();
		},
		// 禁用用户
		async disableUser(uid) {
			try {
				const res = await this.$http.disableUsers({ uuid: uid });
				if (res.status == 200) {
					this.getUserList();
					return this.$message.success(res.message);
				} else {
					return this.$message.warning(res.message);
				}
			} catch (error) {
				return this.$message.error("内部服务器错误");
			}
		},
		// 删除用户
		async deleteUser(uid) {
			try {
				this.$alert(`删除后不可恢复，确定删除工号${uid}的用户吗?`, '系统提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					callback: action => {
						if (action == 'confirm') {
							this.$http.deleteUsers({ uuid: uid }).then(res => {
								if (res.status == 200) {
									this.getUserList();
									return this.$message.success(res.message);
								} else {
									return this.$message.warning(res.message);
								}
							})

						} else {
							return false
						}
					}
				})
			} catch (error) {
				return this.$message.error("内部服务器错误");
			}
		},
		// 重置
		resetHandler() {
			this.keyword = "";
			this.depart = "";
			this.getUserList();
		},
		handleSizeChange(val) {
			this.loading = true
			this.queryInfo.pageSize = val;
			this.getUserList();
		},
		currentchange(val) {
			this.loading = true
			this.queryInfo.pageNum = val;
			this.getUserList();
		},
	},

}
</script>
<style>
.btn-box {
	display: flex;
	padding: 50px 0 0 0;
	justify-content: left;
	align-items: left;
}

.input-box {
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	width: 400px;
}

.image-view-title {
	margin: 10px 20px 10px 10px;
	display: flex;
	flex-direction: row;
	justify-content: flex-start;
	align-items: center;
}

.image-view-title img {
	object-fit: cover;
}

.image-list {
	width: 100px;
}

.el-textarea>>>.el-textarea__inner {
	border: 0 !important;
}

.el-button--primary {}

.image-button {
	text-align: center;
	padding: 120px 0;
}

.status-text {
	color: #30C82B;
	font-weight: bold;
	border: 1px solid #30C82B;
	display: block;
	width: 60px;
	height: 20px;
	margin: 0 auto;
	line-height: 20px;
}

.wait-complete {
	color: #ccc;
	font-weight: bold;
	border: 1px solid #ccc;
	display: block;
	width: 60px;
	height: 20px;
	margin: 0 auto;
	line-height: 20px;
}

.page {
	padding: 20px 0 0 0;
}

.el-image__inner {
	border-radius: 50%;
}

.el-table .cell {
	text-align: center;
}
</style>