<!--
 * @Author: flyknit <EMAIL>
 * @Date: 2024-05-07 10:47:35
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-06-17 08:59:01
 * @FilePath: \electronic-filed:\gitee\backend-demo\src\views\standard\Standard.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by ${<EMAIL>}, All Rights Reserved. 
-->
<template>
  <div>
    <el-card shadow="always" v-loading="loading" element-loading-text="Flyknit...">
      <el-row :gutter="10">
        <el-col :span="24">
          <!-- 搜索框，搜索按钮 -->
          <el-form inline :form="form" size="medium">
            <el-form-item label="Model名称">
              <el-input
                v-model="form.model_name"
                placeholder="请输入Model名称"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="searchModel">搜索</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="24">
          <!-- 左侧model列表 -->
          <el-col :span="4">
            <!-- 表格，serila， model_name -->
            <el-table
              :data="modelList"
              border
              ref="modelTable"
              highlight-current-row
              :header-cell-style="{
                'background-color': '#FAFAFA',
                color: '#333',
                'font-weight': 'bold',
              }"
              size="mini"
              @row-click="rowClickHandler"
              height="700px"
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                fixed
                sortable
                label="Model名称"
                prop="model_name"
                align="center"
              ></el-table-column>
            </el-table>
          </el-col>
          <!-- 中间表格 -->
          <el-col :span="8">
            <!-- 上面表格 -->
            <el-table
              ref="pmTable"
              :data="tableData"
              :header-cell-style="{
                'background-color': '#FAFAFA',
                color: '#333',
                'font-weight': 'bold',
              }"
              size="mini"
              highlight-current-row
              @row-click="pmRowClickHandler"
              border
              height="200px"
              empty-text="请选择model"
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                sortable
                property="pm"
                label="品名"
                align="center"
              >
              </el-table-column>
            </el-table>
            <!-- 下面表格 -->
            <el-col :span="24" style="margin: 1rem 0 0 0">
              <!-- 下拉筛选框,选择毛坯光坯 -->
              <el-form :form="form" inline size="medium">
                <el-form-item label="标准类型">
                  <el-select
                    v-model="form.type"
                    placeholder="请选择类型"
                    @change="changeType"
                  >
                    <el-option label="毛坯" value="0"></el-option>
                    <el-option label="光坯" value="1"></el-option>
                  </el-select>
                </el-form-item>
              </el-form>
            </el-col>
            <el-table
              :data="tableDatas"
              :header-cell-style="{
                'background-color': '#FAFAFA',
                color: '#333',
                'font-weight': 'bold',
              }"
              size="mini"
              highlight-current-row
              style="width: 100%; font-size: 0.75rem;"
              border
              height="425px"
              @row-click="typeRowClickHandler"
              ref="typeTable"
              empty-text="请选择品名"
            >
              <el-table-column type="selection" width="55" align="center">
              </el-table-column>
              <el-table-column
                sortable
                property="model_name"
                label="Model"
                align="center"
              >
              </el-table-column>
              <el-table-column
                sortable
                property="pm"
                label="品名"
                align="center"
              >
              </el-table-column>
              <el-table-column
                sortable
                property="type"
                label="类型"
                align="center"
              >
                <template slot-scope="scope">
                  <div>
                    <el-tag v-if="scope.row.type == 0" type="success"
                      >毛坯</el-tag
                    >
                    <el-tag v-else type="warning">光坯</el-tag>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <!-- 右侧表格 -->
          <el-col :span="12">
            <el-col :span="6">
              <!-- 表格，尺码 -->
              <el-table
                :data="sizeList"
                border
                size="mini"
                :header-cell-style="{
                  'background-color': '#FAFAFA',
                  color: '#333',
                  'font-weight': 'bold',
                }"
                height="700px"
                style="
                  width: 100%;
                  overflow-y: scroll;
                "
                @row-click="sizeRowClickHandler"
                ref="sizeTable"
                element-loading-text="正在加载..."
              >
                <el-table-column type="selection" width="55" align="center">
                </el-table-column>
                <el-table-column
                  fixed
                  sortable
                  label="尺码"
                  prop="size"
                  align="center"
                ></el-table-column>
              </el-table>
            </el-col>
            <!-- 右侧表格 -->
            <el-col :span="18">
              <el-table
                :data="userData"
                border
                :header-cell-style="{
                  'background-color': '#FAFAFA',
                  color: '#333',
                  'font-weight': 'bold',
                }"
                ref="table"
                v-loading="loading"
                size="mini"
              >
                <el-table-column
                  v-for="(column, index) in tableHeader"
                  :key="index"
                  :prop="column.prop"
                  :label="column.label"
                >
                </el-table-column>
              </el-table>
            </el-col>
          </el-col>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>
<script>
import { mapState } from "vuex";
export default {
  data() {
    return {
      modelList: [], // model列表
      tableData: [], // 品名列表
      tableDatas: [], // 标准类型列表
      sizeList: [], // 尺码列表
      tableHeader: [], // 表头
      userData: [], // 表格数据
      form: {
        model_name: "",
        type: "0",
      },
      currentModel: "", // 当前选中Model
      currentPm: "", // 当前选中品名
      currentType: "", // 当前选中类型
      currentSize: "", // 当前选中尺码
      loading: true,
    };
  },
  computed: {
    ...mapState({
      User: (state) => state.User,
    }),
  },
  mounted() {
    this.getSizeList();
    this.getModelList();
    this.getTableHeader();
  },
  methods: {
    // 搜索model
    searchModel() {
      if (!this.form.model_name) {
        return this.$notify({
          title: "系统提示",
          message: "请输入Model名称",
          type: "warning",
        });
      }
      this.getModelList();
    },
    // 获取默认表头
    async getTableHeader() {
      try {
        const res = await this.$http.getTableHeader({
          type: this.form.type,
        });
        if (res.code !== 200) {
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        this.tableHeader = res.data.list.names;
      } catch (error) {
        return this.$notify({
          title: "系统提示",
          message: "获取表头失败",
          type: "error",
        });
      }
    },
    // 根据类型筛选
    changeType() {
      // console.log('当前类型', this.form.type);
      this.getStandardList();
      this.getTableHeader()
    },
    // model表格行点击事件
    rowClickHandler(row, column, event) {
      // console.log("当前model行", row);
      this.currentModel = row.model_name;
      this.$refs.modelTable.clearSelection(); // 清除所有的选中状态
      this.$refs.modelTable.toggleRowSelection(row, true); // 选中被点击的行
      this.getPmList();
    },
    // 根据当前选中model获取品名列表
    async getPmList() {
      try {
        const res = await this.$http.getPmList({
          model_name: this.currentModel,
        });
        if (res.code !== 200) {
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        this.tableData = res.data.list;
        // 去除重复的品名
        this.tableData = this.tableData.filter(
          (item, index, self) =>
            self.findIndex((t) => t.pm === item.pm) === index
        );
        // console.log("品名列表", this.tableData);
      } catch (error) {
        return this.$notify({
          title: "系统提示",
          message: "获取品名列表失败",
          type: "error",
        });
      }
    },
    // 选择当前品名行
    pmRowClickHandler(row, column, event) {
      // console.log("当前品名行", row);
      this.currentPm = row.pm;
      // console.log("当前model", this.currentModel);
      this.$refs.pmTable.clearSelection(); // 清除所有的选中状态
      this.$refs.pmTable.toggleRowSelection(row, true); // 选中被点击的行
      this.getStandardList();
    },
    // 根据选中的model和品名获取标准类型列表
    async getStandardList() {
      try {
        const res = await this.$http.getModelDetailList({
          model_name: this.currentModel,
          pm: this.currentPm,
          type: this.form.type,
        });
        if (res.code !== 200) {
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        this.tableDatas = res.data.list;
        // 标准类型去重
        this.tableDatas = this.tableDatas.filter(
          (item, index, self) =>
            self.findIndex((t) => t.type === item.type) === index
        );
      } catch (error) {
        return this.$notify({
          title: "系统提示",
          message: "获取标准类型列表失败",
          type: "error",
        });
      }
    },
    // 标准类型表格行点击事件
    typeRowClickHandler(row, column, event) {
      // console.log("当前标准类型行", row);
      this.currentType = row.type;
      this.$refs.typeTable.clearSelection(); // 清除所有的选中状态
      this.$refs.typeTable.toggleRowSelection(row, true); // 选中被点击的行
    },
    // 尺码表格行点击事件
    sizeRowClickHandler(row, column, event) {
      // console.log("当前尺码行", row);
      this.currentSize = row.size;
      this.$refs.sizeTable.clearSelection(); // 清除所有的选中状态
      this.$refs.sizeTable.toggleRowSelection(row, true); // 选中被点击的行
      this.getTableData();
    },
    // 根据当前currentModel, currentPm, currentType, currentSize获取表头和表格数据
    async getTableData() {
      try {
        const res = await this.$http.getCshStandardList({
          model_name: this.currentModel,
          pm: this.currentPm,
          type: this.currentType,
          size: this.currentSize,
        });
        if (res.code !== 200) {
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        const { names, resultData } = res.data.list;
        this.tableHeader = names;
        this.userData = resultData;
        // console.log('表头', this.tableHeader);
        // console.log('表格数据', this.userData);
      } catch (error) {
        return this.$notify({
          title: "系统提示",
          message: "获取表格数据失败",
          type: "error",
        });
      }
    },
    // 获取全部model
    async getModelList() {
      try {
        const res = await this.$http.getAllModelList({
          model_name: this.form.model_name,
        });
        if (res.code !== 200) {
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        this.modelList = res.data.list;
        setTimeout(() => {
          this.loading = false;
        }, 500);
      } catch (error) {
        this.loading = false;
        return this.$notify({
          title: "系统提示",
          message: error.message,
          type: "error",
        });
      }
    },
    // 获取全部尺码
    async getSizeList() {
      try {
        const res = await this.$http.getAllSizeList();
        if (res.code !== 200) {
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        this.sizeList = res.data.list;
      } catch (error) {
        return this.$notify({
          title: "系统提示",
          message: "获取尺码列表失败",
          type: "error",
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
</style>