<template>
    <div>
        <el-card>
            <!-- 表单，出库单号搜索，备件名称搜索，领用人姓名搜索，部门筛选，日期选择 -->
            <el-form :model="form" inline :rules="rules" ref="form" class="forms">
                <el-form-item label="出库单号">
                    <el-input v-model="form.chukudanhao" placeholder="请输入出库单号"></el-input>
                </el-form-item>
                <el-form-item label="备件名称/编号">
                    <el-input v-model="form.parts_name" placeholder="请输入备件名称或备件编号"></el-input>
                </el-form-item>
                <!-- 領用人姓名/工号 -->
                <el-form-item label="领用人">
                    <el-input v-model="form.Receiver" placeholder="请输入领用人姓名或工号"></el-input>
                </el-form-item>
                <el-form-item label="部门">
                    <el-select placeholder="请选择部门" v-model="form.department" clearable>
                        <el-option label="IT部门" value="IT部门"></el-option>
                        <el-option label="织造车间" value="织造车间"></el-option>
                        <el-option label="整理车间" value="整理车间"></el-option>
                        <el-option label="检验车间" value="检验车间"></el-option>
                        <el-option label="打样车间" value="打样车间"></el-option>
                        <el-option label="综合维修" value="综合维修"></el-option>
                        <el-option label="化验室" value="化验室"></el-option>
                        <el-option label="行政办公室" value="行政办公室"></el-option>
                        <el-option label="食堂" value="食堂"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="日期">
                    <el-date-picker v-model="form.date" type="daterange" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" align="right">
                    </el-date-picker>
                </el-form-item>
                <!-- 搜索按钮 -->
                <el-form-item>
                    <el-button type="primary" @click="onSubmit" icon="el-icon-search">搜索</el-button>
                </el-form-item>
                <!-- 重置按钮 -->
                <el-form-item>
                    <el-button @click="onReset" icon="el-icon-refresh">重置</el-button>
                </el-form-item>
            </el-form>
            <!-- 主体部分，表格，出库单号，备件名称，备件编号，货架编号，库位，出库日期，出库人，出库数量，出库类型，备件分类， -->
            <el-table :data="tableData" stripe style="width: 100%" v-loading="loading" element-loading-text="Flyknit">
                <!-- 索引 -->
                <el-table-column type="selection" width="50"></el-table-column>
                <el-table-column prop="chukudanhao" label="出库单号" >
                </el-table-column>
                <el-table-column prop="parts_name" label="备件名称" width="">
                </el-table-column>
                <el-table-column prop="parts_number" label="备件编号" width="">
                </el-table-column>
                <el-table-column prop="quantity" label="出库数量" width="100">
                </el-table-column>
                <el-table-column prop="receiver" label="領用人" width="">
                </el-table-column>
                <el-table-column prop="shift" label="班次" width="">
                </el-table-column>
                <el-table-column prop="date" label="出库日期" width="200">
                </el-table-column>
                <el-table-column prop="operator" label="操作员" width="120">
                </el-table-column>
                <!-- 操作 -->
                <el-table-column label="操作" width="250">
                    <template slot-scope="scope">
                        <el-button type="primary" size="mini" icon="el-icon-edit" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
                        <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <div class="pagination">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="currentPage" :page-sizes="[10, 20, 30, 40]" :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper" :total="total">
                </el-pagination>
            </div>
        </el-card>
    </div>
</template>
<script>
export default {
    data() {
        return {
            loading: true,
            form: {
                chukudanhao: '',
                parts_name: '',
                Receiver: '',
                department: '',
                date: ''
            },
            rules: {
                chukudanhao: [
                    { required: true, message: '请输入出库单号', trigger: 'blur' }
                ],
                parts_name: [
                    { required: true, message: '请输入备件名称', trigger: 'blur' }
                ],
                Receiver: [
                    { required: true, message: '请输入领用人姓名或工号', trigger: 'blur' }
                ],
                department: [
                    { required: true, message: '请选择部门', trigger: 'blur' }
                ],
                date: [
                    { required: true, message: '请选择日期', trigger: 'blur' }
                ]
            },
            tableData: [
                {
                    id: 1,
                    chukudanhao: 'CK202108250001',
                    parts_name: '电脑',
                    parts_number: 'D202108250001',
                    quantity: '1',
                    receiver: '张三',
                    shift: 'A',
                    date: '2023-08-25 15:52:20',
                    operator: '李四'
                },
                {
                    id: 1,
                    chukudanhao: 'CK202108250001',
                    parts_name: '电脑',
                    parts_number: 'D202108250001',
                    quantity: '1',
                    receiver: '张三',
                    shift: 'A',
                    date: '2023-08-25 15:52:20',
                    operator: '李四'
                },{
                    id: 1,
                    chukudanhao: 'CK202108250001',
                    parts_name: '电脑',
                    parts_number: 'D202108250001',
                    quantity: '1',
                    receiver: '张三',
                    shift: 'A',
                    date: '2023-08-25 15:52:20',
                    operator: '李四'
                },{
                    id: 1,
                    chukudanhao: 'CK202108250001',
                    parts_name: '电脑',
                    parts_number: 'D202108250001',
                    quantity: '1',
                    receiver: '张三',
                    shift: 'A',
                    date: '2023-08-25 15:52:20',
                    operator: '李四'
                },{
                    id: 1,
                    chukudanhao: 'CK202108250001',
                    parts_name: '电脑',
                    parts_number: 'D202108250001',
                    quantity: '1',
                    receiver: '张三',
                    shift: 'A',
                    date: '2023-08-25 15:52:20',
                    operator: '李四'
                },{
                    id: 1,
                    chukudanhao: 'CK202108250001',
                    parts_name: '电脑',
                    parts_number: 'D202108250001',
                    quantity: '1',
                    receiver: '张三',
                    shift: 'A',
                    date: '2023-08-25 15:52:20',
                    operator: '李四'
                },{
                    id: 1,
                    chukudanhao: 'CK202108250001',
                    parts_name: '电脑',
                    parts_number: 'D202108250001',
                    quantity: '1',
                    receiver: '张三',
                    shift: 'A',
                    date: '2023-08-25 15:52:20',
                    operator: '李四'
                },{
                    id: 1,
                    chukudanhao: 'CK202108250001',
                    parts_name: '电脑',
                    parts_number: 'D202108250001',
                    quantity: '1',
                    receiver: '张三',
                    shift: 'A',
                    date: '2023-08-25 15:52:20',
                    operator: '李四'
                },{
                    id: 1,
                    chukudanhao: 'CK202108250001',
                    parts_name: '电脑',
                    parts_number: 'D202108250001',
                    quantity: '1',
                    receiver: '张三',
                    shift: 'A',
                    date: '2023-08-25 15:52:20',
                    operator: '李四'
                },{
                    id: 1,
                    chukudanhao: 'CK202108250001',
                    parts_name: '电脑',
                    parts_number: 'D202108250001',
                    quantity: '1',
                    receiver: '张三',
                    shift: 'A',
                    date: '2023-08-25 15:52:20',
                    operator: '李四'
                }
            ],
            currentPage: 1,
            pageSize: 10,
            total: 10,
        }
    },
    mounted() {
        setTimeout(() => {
            this.loading = false;
        }, 1000);
    },
    methods: {
        handleCurrentChange(val) {
            this.currentPage = val;
        },
        handleSizeChange(val) {
            this.pageSize = val;
        },
        // 搜索
        onSubmit() {
            console.log('submit!');
        },
        // 重置
        onReset() {
            console.log('reset!');
        },
    }
}
</script>
<style lang="scss" scoped>
.el-card__body {
    padding: 20px 20px 0 20px;
}

.forms {
    display: flex;
    flex-wrap: wrap;
}
.el-pagination {
   padding: 20px 0;
}
</style>