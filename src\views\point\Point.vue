<!--
 * @Author: flyknit <EMAIL>
 * @Date: 2024-05-07 12:47:39
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-07-13 13:19:22
 * @FilePath: \electronic-filed:\gitee\backend-demo\src\views\point\Point.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by ${<EMAIL>}, All Rights Reserved. 
-->
<template>
  <div>
    <el-card>
      <el-row :gutter="10" style="display: flex; align-items: center">
        <el-col :span="24">
          <el-form inline :form="form" size="medium">
            <el-form-item label="Model名称">
              <el-input
                v-model="queryInfo.keyword"
                placeholder="请输入model名称"
                clearable
                @clear="clearHandler"
              ></el-input>
            </el-form-item>
            <!-- 选择尺码下拉框 -->
            <el-form-item label="尺码">
              <el-select
                v-model="form.size"
                placeholder="请选择尺码"
                clearable
                @clear="clearHandler"
              >
                <el-option
                  v-for="item in sizeList"
                  :key="item.id"
                  :label="item.size"
                  :value="item.size"
                ></el-option>
              </el-select>
            </el-form-item>
            <!-- 下拉框类型选项 -->
            <el-form-item label="品名">
              <el-select
                v-model="form.pm"
                placeholder="请选择品名"
                clearable
                @clear="clearHandler"
              >
                <el-option
                  v-for="item in pmList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name"
                ></el-option>
              </el-select>
            </el-form-item>
            <!-- 搜索按钮 -->
            <el-form-item>
              <el-button
                type="success"
                icon="el-icon-search"
                size="medium"
                @click="searchHandler"
                >搜索</el-button
              >
            </el-form-item>
            <el-form-item v-if="User.type == 2">
              <el-button
                type="primary"
                icon="el-icon-plus"
                size="medium"
                @click="switchHandler"
                >添加点位</el-button
              >
            </el-form-item>
            <!-- 导入点位 -->
            <el-form-item v-if="User.type == 2">
              <el-button
                type="primary"
                icon="el-icon-upload"
                size="medium"
                @click="importHandler"
                >导入点位</el-button
              >
            </el-form-item>
          </el-form>
          <el-table
            :data="styleList"
            resizable
            stripe
            v-loading="loading"
            element-loading-text="Flyknit"
            style="font-size: 0.88rem"
          >
            <el-table-column label="#" type="index">
              <el-checkbox v-model="checked"></el-checkbox>
            </el-table-column>
            <el-table-column
              label="Model"
              header-align="center"
              sortable
              prop="model_name"
              align="center"
            ></el-table-column>
            <el-table-column
              label="尺码"
              sortable
              prop="size"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              label="品名"
              sortable
              prop="pm"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              label="点位"
              sortable
              prop="name"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              label="Min"
              sortable
              prop="min"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              label="Target"
              sortable
              prop="target"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              label="Max"
              sortable
              prop="max"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              label="标准类型"
              sortable
              prop="type"
              header-align="center"
              align="center"
            >
              <template slot-scope="scope">
                <el-tag v-if="scope.row.type === 0" type="success" size="mini"
                  >毛坯标准</el-tag
                >
                <el-tag
                  v-else-if="scope.row.type === 1"
                  type="warning"
                  size="mini"
                  >光坯标准</el-tag
                >
              </template>
            </el-table-column>
            <!-- 创建日期 -->
            <!-- <el-table-column
              label="创建日期"
              sortable
              prop="create_time"
              header-align="center"
              align="center"
            ></el-table-column> -->
            <!-- 操作 -->
            <el-table-column
              label="操作"
              header-align="center"
              align="center"
              v-if="User.type == 2"
            >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="mini"
                  @click="editHandler(scope.row)"
                  >编辑</el-button
                >
                <el-button
                  type="text"
                  style="color: #eb4c42"
                  size="mini"
                  @click="deleteHandler(scope.row.id)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <div class="page" v-show="total > 0">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="currentchange"
              :current-page="queryInfo.currentnum"
              :page-sizes="pageSizes"
              :page-size="queryInfo.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            >
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <!-- 添加点位弹窗 -->
    <el-drawer :title="title" :visible.sync="dialogFormVisible" size="30%">
      <el-form
        :model="form"
        :rules="rules"
        ref="form"
        label-width="80px"
        style="margin: 0 1rem"
      >
        <el-form-item label="Model" prop="model_name">
          <el-autocomplete
            class="inline-input"
            v-model="form.model_name"
            :fetch-suggestions="querySearch"
            placeholder="请输入Model名称"
            @select="handleSelect"
            clearable
            style="width: 215px"
          ></el-autocomplete>
        </el-form-item>
        <el-form-item label="尺码" prop="size">
          <el-select v-model="form.size" filterable placeholder="请选择尺码">
            <el-option
              v-for="item in sizeList"
              :key="item.id"
              :label="item.size"
              :value="item.size"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="品名" prop="pm">
          <!-- 下拉选择 -->
          <el-select v-model="form.pm" filterable placeholder="请选择品名">
            <el-option
              v-for="item in pmList"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="点位" prop="name">
          <el-select
            v-model="form.name"
            filterable
            placeholder="请选择测量点位"
          >
            <el-option
              v-for="item in measureList"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="Min" prop="min">
          <el-input v-model="form.min" placeholder="请输入最小值"></el-input>
        </el-form-item>
        <el-form-item label="Target" prop="target">
          <el-input v-model="form.target" placeholder="请输入中间值"></el-input>
        </el-form-item>
        <el-form-item label="Max" prop="max">
          <el-input v-model="form.max" placeholder="请输入最大值"></el-input>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio label="0">毛坯标准</el-radio>
            <el-radio label="1">光坯标准</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item style="position: absolute; bottom: 0">
          <el-button
            style="width: 10rem"
            type="primary"
            @click="submitForm('form')"
            >{{ btnText }}</el-button
          >
          <el-button style="width: 10rem" @click="dialogFormVisible = false"
            >取消</el-button
          >
        </el-form-item>
      </el-form>
    </el-drawer>
    <!-- 上传点位excel弹窗 -->
    <el-dialog title="上传点位标准" :visible.sync="dialogVisible" width="50%">
      <div style="display: flex; justify-content: center; align-items: center">
        <el-upload
          class="upload-demo"
          drag
          :action="action"
          accept=".xls,.xlsx"
          :headers="headers"
          name="file"
          :limit="limit"
          :before-upload="beforeUploadFile"
          :auto-upload="autoUpload"
          :on-change="fileChange"
          :on-exceed="exceedFile"
          :on-success="handleSuccess"
          :on-error="handleError"
          :file-list="fileList"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">
            只能上传.xls/.xlsx文件，且不超过2M
          </div>
        </el-upload>
      </div>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="uploadFile" size="medium"
          >立即上传</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from "vuex";
import config from "@/config/config.js";
import axios from "axios";
export default {
  name: "Point",
  data() {
    return {
      fileList: [], // 上传文件列表
      action: config.uploadsURL, // 上传图片的地址
      headers: {
        Authorization: `Bearer ${localStorage.getItem("accessToken")}`,
      },
      limit: 1, // 上传文件数量限制
      autoUpload: false, // 是否自动上传
      dialogVisible: false, // 上传点位excel弹窗
      sizeList: [], // 尺码列表
      styleList: [], // 点位列表
      loading: true,
      checked: false,
      queryInfo: {
        currentnum: 1,
        pageNum: 1,
        pageSize: 12,
        keyword: "",
      },
      pmList: [], // 品名列表
      total: 0,
      pageSizes: [12, 16, 18, 20],
      dialogFormVisible: false,
      form: {
        model_name: "",
        size: "",
        pm: "",
        name: "",
        min: "",
        target: "",
        max: "",
        type: "0",
      },
      rules: {
        model_name: [
          { required: true, message: "请输入Model", trigger: "blur" },
        ],
        size: [{ required: true, message: "请输入尺码", trigger: "blur" }],
        pm: [{ required: true, message: "请输入品名", trigger: "blur" }],
        name: [{ required: true, message: "请输入点位", trigger: "blur" }],
        min: [{ required: true, message: "请输入点位最小值", trigger: "blur" }],
        target: [
          { required: true, message: "请输入点位中间值", trigger: "blur" },
        ],
        max: [{ required: true, message: "请输入点位最大值", trigger: "blur" }],
        type: [{ required: true, message: "请选择类型", trigger: "blur" }],
      },
      btnText: "添加",
      title: "添加点位",
      measureList: [], // 测量点位列表
      modelList: [], // model列表
    };
  },
  computed: {
    ...mapState({
      User: (state) => state.users,
    }),
  },
  created() {
    this.getStyleList();
    this.getSizeList();
    this.getMeasureList();
    this.getModelList();
    this.getModelName();
  },
  methods: {
    // 获取品名列表
    async getModelName() {
      try {
        const res = await this.$http.getPmLists();
        if (res.code !== 200) {
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          })
        }
        const { list } = res.data
        this.pmList = list
      } catch (error) {
        return this.$notify({
          title: "系统提示",
          message: "获取品名列表失败",
          type: "error",
        })
      }
    },
    // 文件超出个数限制时的钩子
    exceedFile(files, fileList) {
      this.$message.warning(
        `只能选择 ${this.limitNum} 个文件，当前共选择了 ${
          files.length + fileList.length
        } 个`
      );
    },
    // 文件状态改变时的钩子
    fileChange(file, fileList) {
      // console.log(file.raw);
      this.fileList = fileList; // 直接赋值新的文件列表
      // console.log(this.fileList);
    },
    UploadUrl: function () {
      // 因为action参数是必填项，我们使用二次确认进行文件上传时，直接填上传文件的url会因为没有参数导致api报404，所以这里将action设置为一个返回为空的方法就行，避免抛错
      return "";
    },
    // 上传文件之前的钩子, 参数为上传的文件,若返回 false 或者返回 Promise 且被 reject，则停止上传
    beforeUploadFile(file) {
      const extension = file.name.substring(file.name.lastIndexOf(".") + 1);
      const size = file.size / 1024 / 1024;
      console.log("extension", extension);

      if (extension !== "xlsx" && extension !== "xls") {
        this.$notify.error({
          title: "错误",
          message: "上传文件只能是xlsx或xls格式",
        });
        return false; // 阻止文件上传
      }

      if (size > 2) {
        this.$notify.error({
          title: "错误",
          message: "上传文件大小不能超过2M",
        });
        return false; // 阻止文件上传
      }
      return true; // 允许文件上传
    },
    // 文件上传成功时的钩子
    handleSuccess(res, file, fileList) {
      this.$notify.success("文件上传成功");
      // 上传成功后清空文件列表
      this.fileList = [];
      this.dialogVisible = false;
      this.getStyleList();
    },
    // 文件上传失败时的钩子
    handleError(err, file, fileList) {
      this.$notify.error("文件上传失败");
      // 上传失败后清空文件列表
      this.fileList = [];
    },
    // 导入excel点位
    importHandler() {
      this.dialogVisible = true;
    },
    // 立即上传
    uploadFile() {
      if (this.fileList.length === 0) {
        this.$notify.error({
          title: "错误",
          message: "请选择要上传的文件",
        });
        return;
      }

      const file = this.fileList[0];
      const formData = new FormData();
      formData.append("file", file.raw);

      // 这里可以使用你的文件上传方法
      axios
        .post(this.action, formData, {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${localStorage.getItem("accessToken")}`,
          },
        })
        .then((response) => {
          this.$notify.success("文件上传成功");
          this.fileList = [];
          this.dialogVisible = false;
          this.getStyleList();
        })
        .catch((err) => {
          this.$notify.error("文件上传失败");
          this.fileList = [];
        });
    },

    // 获取全部model
    async getModelList() {
      try {
        const res = await this.$http.getAllModelList();
        if (res.code !== 200) {
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        const arrayList = res.data.list;
        this.modelList = arrayList.map((item) => {
          return {
            value: item.model_name,
            name: item.serial,
          };
        });
      } catch (error) {
        return this.$notify({
          title: "系统提示",
          message: "获取model列表失败",
          type: "error",
        });
      }
    },
    // 搜索建议
    querySearch(queryString, cb) {
      const modelArrays = this.modelList;
      const results = queryString
        ? modelArrays.filter(this.createStateFilter(queryString))
        : modelArrays;
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        cb(results);
      }, 1500 * Math.random());
    },
    // 搜索建议
    createStateFilter(queryString) {
      return (state) => {
        return (
          state.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
        );
      };
    },
    handleSelect(item) {
      this.form.model_name = item.value;
    },
    // 获取全部测量点位
    async getMeasureList() {
      try {
        const res = await this.$http.getAllLocation();
        if (res.code !== 200) {
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        this.measureList = res.data.list;
      } catch (error) {
        return this.$notify({
          title: "系统提示",
          message: "获取测量点位列表失败",
          type: "error",
        });
      }
    },
    // 清空搜索
    clearHandler() {
      this.queryInfo.currentnum = 1;
      this.queryInfo.pageNum = 1;
      this.queryInfo.size = "";
      this.queryInfo.pm = "";
      this.queryInfo.keyword = "";
      this.getStyleList();
    },
    // 搜索
    searchHandler() {
      this.queryInfo.currentnum = 1;
      this.queryInfo.pageNum = 1;
      this.queryInfo.size = this.form.size;
      this.queryInfo.pm = this.form.pm;
      this.getStyleList();
    },
    // 获取全部尺码
    async getSizeList() {
      try {
        const res = await this.$http.getAllSizeList();
        if (res.code !== 200) {
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        this.sizeList = res.data.list;
      } catch (error) {
        return this.$notify({
          title: "系统提示",
          message: "获取尺码列表失败",
          type: "error",
        });
      }
    },
    // 删除点位
    async deleteHandler(id) {
      this.$confirm("此操作将永久删除该点位, 是否继续?", "系统提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.deleteStyleApi(id);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 全局删除点位接口
    async deleteStyleApi(id) {
      try {
        const res = await this.$http.deleteMeasurePoint({ id });
        if (res.code !== 200) {
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        this.$notify({
          title: "成功",
          message: res.message,
          type: "success",
        });
        this.getStyleList();
      } catch (error) {
        return this.$notify({
          title: "系统提示",
          message: "删除点位失败",
          type: "error",
        });
      }
    },
    // 编辑点位
    editHandler(row) {
      this.title = "编辑点位";
      this.btnText = "编辑";
      this.form.model_name = row.model_name;
      this.form.size = row.size;
      this.form.pm = row.pm;
      this.form.name = row.name;
      this.form.min = row.min;
      this.form.target = row.target;
      this.form.max = row.max;
      this.form.type = String(row.type);
      this.form.id = row.id;
      this.dialogFormVisible = true;
    },
    // 添加点位弹窗开关
    switchHandler() {
      this.dialogFormVisible = true;
      this.form = {
        model_name: "",
        size: "",
        pm: "",
        name: "",
        min: "",
        target: "",
        max: "",
        type: "0",
      };
      this.btnText = "添加";
      this.title = "添加点位";
    },
    // 添加点位
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.submitFormApi();
        } else {
          return false;
        }
      });
    },
    // 封装提交表单接口
    async submitFormApi() {
      console.log("form", this.form);
      try {
        const res = await this.$http.addMeasurePoint(this.form);
        if (res.code !== 200) {
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        this.$notify({
          title: "成功",
          message: res.message,
          type: "success",
        });
        this.dialogFormVisible = false;
        this.form = {
          model_name: "",
          size: "",
          pm: "",
          name: "",
          min: "",
          target: "",
          max: "",
          type: "0",
        };
        this.getStyleList();
      } catch (error) {
        return this.$notify({
          title: "系统提示",
          message: "添加点位失败",
          type: "error",
        });
      }
    },
    // 获取点位列表
    async getStyleList() {
      try {
        const res = await this.$http.getMeasurePointList(this.queryInfo);
        if (res.code !== 200) {
          this.loading = false;
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        const { result, total } = res.data.list;
        this.styleList = result;
        this.total = total;
        setTimeout(() => {
          this.loading = false;
        }, 500);
      } catch (error) {
        this.loading = false;
        return this.$notify({
          title: "系统提示",
          message: "获取点位列表失败",
          type: "error",
        });
      }
    },
    handleSizeChange(val) {
      this.loading = true;
      this.queryInfo.pageSize = val;
      this.getStyleList();
    },
    currentchange(val) {
      this.loading = true;
      this.queryInfo.pageNum = val;
      this.getStyleList();
    },
  },
};
</script>
<style scoped>
.page {
  display: flex;
  justify-content: flex-start;
  margin-top: 20px;
}
</style>