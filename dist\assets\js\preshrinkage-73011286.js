import{i as p,r,o,g as i,b as u,w as a,e as _,a as m,d,C as f}from"./index-444b28c3.js";import{E as v}from"./el-input-6b488ec7.js";import{E as w}from"./el-button-9bbdfcf9.js";import"./event-fe80fd0c.js";import"./index-4d7f16ce.js";const x=f({name:"copy"}),C=Object.assign(x,{setup(y){const e=p("我是复制内容");return(E,t)=>{const l=w,s=v,n=r("copy");return o(),i("div",null,[u(s,{modelValue:e.value,"onUpdate:modelValue":t[0]||(t[0]=c=>e.value=c),placeholder:"Please input",class:"input-with-select",style:{width:"400px"}},{append:a(()=>[_((o(),m(l,null,{default:a(()=>[d("复制")]),_:1})),[[n,e.value]])]),_:1},8,["modelValue"])])}}});export{C as default};
