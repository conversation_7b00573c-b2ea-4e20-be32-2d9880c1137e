import{u as y,i,V as v,k,bl as w,E as C,e as x,o as p,g as j,b as t,w as n,n as m,a as d,f as g,m as E,C as b}from"./index-444b28c3.js";/* empty css                   */import{E as D,a as B}from"./el-col-bd5e5418.js";import L from"./HomeUser-d97b857f.js";import l from"./HomeCount-8abe58ee.js";import H from"./HomeLine-851e9ae3.js";import N from"./HomeTable-f02e71fc.js";import{v as V}from"./directive-ce1b251f.js";import"./el-card-6f02be36.js";import"./useEcharts-57db09d5.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./el-tag-29cbefd8.js";import"./CountTo-f11294d2.js";import"./el-table-column-fa1764a8.js";import"./el-scrollbar-af6196f4.js";import"./event-fe80fd0c.js";import"./focus-trap-6de7266c.js";import"./index-4d7f16ce.js";import"./index-e305bb62.js";import"./el-checkbox-f3df62fa.js";import"./el-tooltip-4ed993c7.js";import"./el-divider-d33f4e37.js";const M=""+new URL("../png/work-9e006c5e.png",import.meta.url).href,R={class:"app-container","element-loading-text":"Flyknit..."},U=E("img",{src:M,style:{width:"100%",height:"100%"},alt:"worker"},null,-1),$=b({name:"home"}),me=Object.assign($,{setup(F){const{t:a}=y(),r=i([]),u=i([]);let o=i([{key:"jitaixiaji",name:a("home.jitaixiaji"),date:a("home.ri"),type:"info",count:0,icon:"@/assets/images/see.png"},{key:"maopijianyan",name:a("home.maopijianyan"),date:a("home.ri"),type:"success",count:0,icon:"@/assets/images/upload.png"},{key:"guangpijianyan",name:a("home.guangpijianyan"),date:a("home.ri"),type:"warning",count:0,icon:"@/assets/images/money.png"},{key:"yusuodingxing",name:a("home.yusuodingxing"),date:a("home.ri"),type:"danger",count:0,icon:"@/assets/images/pie.png"}]);v.on("changeLanguage",()=>{o.value.forEach(e=>{e.name=a(`home.${e.key}`),e.date=a("home.ri")})});const c=i(!0);k(()=>{_()});const _=async()=>{try{const e=await w();e.code!==200&&C({title:"",message:e.msg,type:"error"}),o.value[0].count=e.data.offMachineCount,o.value[1].count=e.data.measureGpsCount,o.value[2].count=e.data.measureMpsCount,o.value[3].count=e.data.steamCount,r.value=e.data.modelCount,u.value=e.data.weekCount,setTimeout(()=>{c.value=!1},500)}catch(e){return c.value=!1,!1}};return(e,G)=>{const s=D,f=B,h=V;return x((p(),j("div",R,[t(f,{gutter:10},{default:n(()=>[t(s,{span:24,style:{width:"100%",margin:"0 0.5rem 0.5rem 0rem"}},{default:n(()=>[U]),_:1}),t(s,{xs:24,sm:24,md:12,lg:12},{default:n(()=>[t(L)]),_:1}),t(s,{xs:24,sm:24,md:6,lg:6},{default:n(()=>[t(l,{list:m(o)[0]},null,8,["list"]),t(l,{list:m(o)[1]},null,8,["list"])]),_:1}),t(s,{xs:24,sm:24,md:6,lg:6},{default:n(()=>[t(l,{list:m(o)[2]},null,8,["list"]),t(l,{list:m(o)[3]},null,8,["list"])]),_:1}),t(s,{xs:24,sm:24,md:8,lg:8},{default:n(()=>[r.value.length>0?(p(),d(N,{key:0,tableData:r.value},null,8,["tableData"])):g("",!0)]),_:1}),t(s,{xs:24,sm:24,md:24,lg:24},{default:n(()=>[u.value.length>0?(p(),d(H,{key:0,weekCount:u.value},null,8,["weekCount"])):g("",!0)]),_:1})]),_:1})])),[[h,c.value]])}}});export{me as default};
