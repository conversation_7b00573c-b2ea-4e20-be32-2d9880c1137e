<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-07-24 09:14:24
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-26 09:07:52
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div id="app" class="app">
    <router-view></router-view>
    <message-box v-if="showMessageBox" :message="message" :type="messageType" @close="hideMessageBox" />
  </div>
</template>
<style>
@import 'element-ui/lib/theme-chalk/index.css';
@import '../src/assets/css/iconfont.css';
.message-box {
  z-index: 999;
  /* 确保消息框在其他内容的上方 */
}

.el-submenu__title:hover,
.el-menu-item:focus,
.el-menu-item:hover {
  background-color: #ecf5ff !important;
}
</style>
<script>
import MessageBox from './views/components/MessageBox.vue';
import { mapGetters, mapMutations } from 'vuex';
export default {
  components: {
    MessageBox
  },
  data() {
    return {}
  },
  computed: {
    ...mapGetters(['showMessageBox', 'message', 'messageType'])
  },
  created() {
    // 初始化用户信息
    this.$store.commit('initUsers')
    // window.addEventListener('resize', this.reloadPage);
  },
  mounted() {
    //console.log('渲染组件完成')
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.reloadPage);
    window.removeEventListener('fullscreenchange', this.fullScreenChange);
  },
  methods: {
    ...mapMutations(['hideMessageBox']),
    reloadPage() {
      // 判断是否处于全屏模式，如果是则不刷新页面
      if (!document.fullscreenElement && !document.mozFullScreenElement && !document.webkitFullscreenElement && !document.msFullscreenElement) {
        location.reload();
      }
    },
    fullScreenChange() {
      // 在全屏切换时刷新页面
      location.reload();
    }
  }
}
</script>
