<!--
 * @Author: flyknit <EMAIL>
 * @Date: 2024-04-09 09:02:42
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-05-31 09:21:50
 * @FilePath: \electronic-filed:\gitee\backend-demo\src\App.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by ${<EMAIL>}, All Rights Reserved. 
-->
<template>
  <div id="app" class="app">
    <keep-alive>
      <router-view></router-view>
    </keep-alive>
  </div>
</template>
<style>
.el-submenu__title:hover,
.el-menu-item:focus,
.el-menu-item:hover {
  background-color: #ecf5ff !important;
}
</style>
<script>
export default {
  data() {
    return {};
  },

  created() {
    //console.log('创建组件');
    this.$store.commit("initUsers");
    // 如果没有用户信息，证明没有登录
    if (!this.$store.state.users) {
      this.$router.push("/login");
    }
  },
  mounted() {
    this.setFontSize(); // Listen for window resize event
    window.addEventListener("resize", this.setFontSize);
    // console.log('渲染组件完成', this.socketStatus)
    this.forbidRightClick();
  },
  beforeDestroy() {
    // Remove the resize event listener when the component is destroyed
    window.removeEventListener("resize", this.setFontSize);
  },
  methods: {
    setFontSize() {
      // Get window width
      const whei =
        window.innerWidth ||
        document.documentElement.clientWidth ||
        document.body.clientWidth; // Set font size of html element

      document.querySelector("html").style.fontSize = `${whei / 110}px`;
    },
    // 禁止右键
    forbidRightClick() {
      document.oncontextmenu = function () {
        return false;
      };
    },
  },
};
</script>
