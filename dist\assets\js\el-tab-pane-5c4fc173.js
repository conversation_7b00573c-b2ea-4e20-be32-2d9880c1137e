import{Q as U,aw as G,ax as ne,_ as le,C as I,ab as Q,X as J,R as K,i as w,a8 as k,G as Z,bM as oe,o as re,g as ie,H as ce,n as x,ad as ge,cC as ye,cD as _e,c as M,k as ue,bN as Ne,b as d,a5 as D,aE as Te,aF as Pe,af as Ce,aN as V,j as de,a9 as Ee,cE as we,S as be,bf as te,aS as Se,a7 as $e,b0 as xe,cF as ae,Y as Be,e as ke,a3 as Oe,f as Re,U as ze,ag as Ae}from"./index-444b28c3.js";import{t as ee,U as ve}from"./event-fe80fd0c.js";import{c as A}from"./validator-e4131fc3.js";import{u as Fe}from"./el-button-9bbdfcf9.js";const j=Symbol("tabsRootContextKey"),Me=U({tabs:{type:G(Array),default:()=>ne([])}}),Le={name:"ElTabBar"},De=I({...Le,props:Me,setup(e,{expose:l}){const _=e,O="ElTabBar",g=Q(),a=J(j);a||ee(O,"<el-tabs><el-tab-bar /></el-tabs>");const m=K("tabs"),N=w(),b=w(),o=()=>{let r=0,u=0;const P=["top","bottom"].includes(a.props.tabPosition)?"width":"height",c=P==="width"?"x":"y";return _.tabs.every(t=>{var p,v,$,R;const B=(v=(p=g.parent)==null?void 0:p.refs)==null?void 0:v[`tab-${t.uid}`];if(!B)return!1;if(!t.active)return!0;u=B[`client${A(P)}`];const L=c==="x"?"left":"top";r=B.getBoundingClientRect()[L]-((R=($=B.parentElement)==null?void 0:$.getBoundingClientRect()[L])!=null?R:0);const F=window.getComputedStyle(B);return P==="width"&&(_.tabs.length>1&&(u-=Number.parseFloat(F.paddingLeft)+Number.parseFloat(F.paddingRight)),r+=Number.parseFloat(F.paddingLeft)),!1}),{[P]:`${u}px`,transform:`translate${A(c)}(${r}px)`}},T=()=>b.value=o();return k(()=>_.tabs,async()=>{await Z(),T()},{immediate:!0}),oe(N,()=>T()),l({ref:N,update:T}),(r,u)=>(re(),ie("div",{ref_key:"barRef",ref:N,class:ce([x(m).e("active-bar"),x(m).is(x(a).props.tabPosition)]),style:ge(b.value)},null,6))}});var Ve=le(De,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/tabs/src/tab-bar.vue"]]);const Ue=U({panes:{type:G(Array),default:()=>ne([])},currentName:{type:[String,Number],default:""},editable:Boolean,type:{type:String,values:["card","border-card",""],default:""},stretch:Boolean}),Ie={tabClick:(e,l,_)=>_ instanceof Event,tabRemove:(e,l)=>l instanceof Event},se="ElTabNav",Ke=I({name:se,props:Ue,emits:Ie,setup(e,{expose:l,emit:_}){const O=Q(),g=J(j);g||ee(se,"<el-tabs><tab-nav /></el-tabs>");const a=K("tabs"),m=ye(),N=_e(),b=w(),o=w(),T=w(),r=w(!1),u=w(0),P=w(!1),c=w(!0),t=M(()=>["top","bottom"].includes(g.props.tabPosition)?"width":"height"),p=M(()=>({transform:`translate${t.value==="width"?"X":"Y"}(-${u.value}px)`})),v=()=>{if(!b.value)return;const s=b.value[`offset${A(t.value)}`],i=u.value;if(!i)return;const n=i>s?i-s:0;u.value=n},$=()=>{if(!b.value||!o.value)return;const s=o.value[`offset${A(t.value)}`],i=b.value[`offset${A(t.value)}`],n=u.value;if(s-n<=i)return;const C=s-n>i*2?n+i:s-i;u.value=C},R=async()=>{const s=o.value;if(!r.value||!T.value||!b.value||!s)return;await Z();const i=T.value.querySelector(".is-active");if(!i)return;const n=b.value,C=["top","bottom"].includes(g.props.tabPosition),y=i.getBoundingClientRect(),h=n.getBoundingClientRect(),S=C?s.offsetWidth-h.width:s.offsetHeight-h.height,E=u.value;let f=E;C?(y.left<h.left&&(f=E-(h.left-y.left)),y.right>h.right&&(f=E+y.right-h.right)):(y.top<h.top&&(f=E-(h.top-y.top)),y.bottom>h.bottom&&(f=E+(y.bottom-h.bottom))),f=Math.max(f,0),u.value=Math.min(f,S)},B=()=>{if(!o.value||!b.value)return;const s=o.value[`offset${A(t.value)}`],i=b.value[`offset${A(t.value)}`],n=u.value;if(i<s){const C=u.value;r.value=r.value||{},r.value.prev=C,r.value.next=C+i<s,s-C<i&&(u.value=s-i)}else r.value=!1,n>0&&(u.value=0)},L=s=>{const i=s.code,{up:n,down:C,left:y,right:h}=V;if(![n,C,y,h].includes(i))return;const S=Array.from(s.currentTarget.querySelectorAll("[role=tab]:not(.is-disabled)")),E=S.indexOf(s.target);let f;i===y||i===n?E===0?f=S.length-1:f=E-1:E<S.length-1?f=E+1:f=0,S[f].focus(),S[f].click(),F()},F=()=>{c.value&&(P.value=!0)},H=()=>P.value=!1;return k(m,s=>{s==="hidden"?c.value=!1:s==="visible"&&setTimeout(()=>c.value=!0,50)}),k(N,s=>{s?setTimeout(()=>c.value=!0,50):c.value=!1}),oe(T,B),ue(()=>setTimeout(()=>R(),0)),Ne(()=>B()),l({scrollToActiveTab:R,removeFocus:H}),k(()=>e.panes,()=>O.update(),{flush:"post"}),()=>{const s=r.value?[d("span",{class:[a.e("nav-prev"),a.is("disabled",!r.value.prev)],onClick:v},[d(D,null,{default:()=>[d(Te,null,null)]})]),d("span",{class:[a.e("nav-next"),a.is("disabled",!r.value.next)],onClick:$},[d(D,null,{default:()=>[d(Pe,null,null)]})])]:null,i=e.panes.map((n,C)=>{var y,h,S,E;const f=n.uid,q=n.props.disabled,W=(h=(y=n.props.name)!=null?y:n.index)!=null?h:`${C}`,X=!q&&(n.isClosable||e.editable);n.index=`${C}`;const me=X?d(D,{class:"is-icon-close",onClick:z=>_("tabRemove",n,z)},{default:()=>[d(Ce,null,null)]}):null,pe=((E=(S=n.slots).label)==null?void 0:E.call(S))||n.props.label,he=!q&&n.active?0:-1;return d("div",{ref:`tab-${f}`,class:[a.e("item"),a.is(g.props.tabPosition),a.is("active",n.active),a.is("disabled",q),a.is("closable",X),a.is("focus",P.value)],id:`tab-${W}`,key:`tab-${f}`,"aria-controls":`pane-${W}`,role:"tab","aria-selected":n.active,tabindex:he,onFocus:()=>F(),onBlur:()=>H(),onClick:z=>{H(),_("tabClick",n,W,z)},onKeydown:z=>{X&&(z.code===V.delete||z.code===V.backspace)&&_("tabRemove",n,z)}},[pe,me])});return d("div",{ref:T,class:[a.e("nav-wrap"),a.is("scrollable",!!r.value),a.is(g.props.tabPosition)]},[s,d("div",{class:a.e("nav-scroll"),ref:b},[d("div",{class:[a.e("nav"),a.is(g.props.tabPosition),a.is("stretch",e.stretch&&["top","bottom"].includes(g.props.tabPosition))],ref:o,style:p.value,role:"tablist",onKeydown:L},[e.type?null:d(Ve,{tabs:[...e.panes]},null),i])])])}}}),je=U({type:{type:String,values:["card","border-card",""],default:""},activeName:{type:[String,Number]},closable:Boolean,addable:Boolean,modelValue:{type:[String,Number]},editable:Boolean,tabPosition:{type:String,values:["top","right","bottom","left"],default:"top"},beforeLeave:{type:G(Function),default:()=>!0},stretch:Boolean}),Y=e=>Se(e)||$e(e),He={[ve]:e=>Y(e),tabClick:(e,l)=>l instanceof Event,tabChange:e=>Y(e),edit:(e,l)=>["remove","add"].includes(l),tabRemove:e=>Y(e),tabAdd:()=>!0};var qe=I({name:"ElTabs",props:je,emits:He,setup(e,{emit:l,slots:_,expose:O}){var g,a;const m=K("tabs"),N=w(),b=de({}),o=w((a=(g=e.modelValue)!=null?g:e.activeName)!=null?a:"0"),T=t=>{o.value=t,l(ve,t),l("tabChange",t)},r=async t=>{var p,v,$;if(!(o.value===t||te(t)))try{await((p=e.beforeLeave)==null?void 0:p.call(e,t,o.value))!==!1&&(T(t),($=(v=N.value)==null?void 0:v.removeFocus)==null||$.call(v))}catch{}},u=(t,p,v)=>{t.props.disabled||(r(p),l("tabClick",t,v))},P=(t,p)=>{t.props.disabled||te(t.props.name)||(p.stopPropagation(),l("edit",t.props.name,"remove"),l("tabRemove",t.props.name))},c=()=>{l("edit",void 0,"add"),l("tabAdd")};return Fe({from:'"activeName"',replacement:'"model-value" or "v-model"',scope:"ElTabs",version:"2.3.0",ref:"https://element-plus.org/en-US/component/tabs.html#attributes",type:"Attribute"},M(()=>!!e.activeName)),k(()=>e.activeName,t=>r(t)),k(()=>e.modelValue,t=>r(t)),k(o,async()=>{var t;await Z(),(t=N.value)==null||t.scrollToActiveTab()}),Ee(j,{props:e,currentName:o,registerPane:v=>b[v.uid]=v,unregisterPane:v=>delete b[v]}),O({currentName:o}),()=>{const t=e.editable||e.addable?d("span",{class:m.e("new-tab"),tabindex:"0",onClick:c,onKeydown:$=>{$.code===V.enter&&c()}},[d(D,{class:m.is("icon-plus")},{default:()=>[d(we,null,null)]})]):null,p=d("div",{class:[m.e("header"),m.is(e.tabPosition)]},[t,d(Ke,{ref:N,currentName:o.value,editable:e.editable,type:e.type,panes:Object.values(b),stretch:e.stretch,onTabClick:u,onTabRemove:P},null)]),v=d("div",{class:m.e("content")},[be(_,"default")]);return d("div",{class:[m.b(),m.m(e.tabPosition),{[m.m("card")]:e.type==="card",[m.m("border-card")]:e.type==="border-card"}]},[...e.tabPosition!=="bottom"?[p,v]:[v,p]])}}});const We=U({label:{type:String,default:""},name:{type:[String,Number]},closable:Boolean,disabled:Boolean,lazy:Boolean}),Xe=["id","aria-hidden","aria-labelledby"],Ye={name:"ElTabPane"},Ge=I({...Ye,props:We,setup(e){const l=e,_="ElTabPane",O=Q(),g=xe(),a=J(j);a||ee(_,"usage: <el-tabs><el-tab-pane /></el-tabs/>");const m=K("tab-pane"),N=w(),b=M(()=>l.closable||a.props.closable),o=ae(()=>{var c;return a.currentName.value===((c=l.name)!=null?c:N.value)}),T=w(o.value),r=M(()=>{var c;return(c=l.name)!=null?c:N.value}),u=ae(()=>!l.lazy||T.value||o.value);k(o,c=>{c&&(T.value=!0)});const P=de({uid:O.uid,slots:g,props:l,paneName:r,active:o,index:N,isClosable:b});return ue(()=>{a.registerPane(P)}),Be(()=>{a.unregisterPane(P.uid)}),(c,t)=>x(u)?ke((re(),ie("div",{key:0,id:`pane-${x(r)}`,class:ce(x(m).b()),role:"tabpanel","aria-hidden":!x(o),"aria-labelledby":`tab-${x(r)}`},[be(c.$slots,"default")],10,Xe)),[[Oe,x(o)]]):Re("v-if",!0)}});var fe=le(Ge,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/tabs/src/tab-pane.vue"]]);const tt=ze(qe,{TabPane:fe}),at=Ae(fe);export{at as E,tt as a};
