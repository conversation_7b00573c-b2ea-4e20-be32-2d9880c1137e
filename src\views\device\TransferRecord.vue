<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-08-12 14:04:17
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-26 10:09:21
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\device\TransferRecord.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-card>
            <!-- 表单form，设备名称，调拨人，搜索按钮，重置按钮，刷新按钮 -->
            <el-form :inline="true" :model="form" class="demo-form-inline" size="medium">
                <el-form-item label="搜索条件">
                    <el-input 
                        v-model="form.keywords" 
                        placeholder="请输入设备名称/编号"
                        clearable
                        @clear="handleClear('keywords')">
                    </el-input>
                </el-form-item>
                <el-form-item label="调拨人">
                    <el-input 
                        v-model="form.username" 
                        placeholder="请输入调拨人姓名"
                        clearable
                        @clear="handleClear('username')">
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSubmit" icon="el-icon-search">查询</el-button>
                    <el-button @click="onReset" icon="el-icon-refresh">重置</el-button>
                    <el-button type="primary" plain @click="onRefresh" icon="el-icon-refresh-right">刷新</el-button>
                </el-form-item>
            </el-form>
            <!-- 表格部门，表格，分页，表格字段，设备编号，设备名称，旧部门，新部门，调拨人，调拨时间 -->
           <el-row :gutter="10">
            <el-col>
                <h3 style="font-weight: normal;color: #606266;font-size: 1rem;border-bottom: 1px solid #DCDFE6;padding-bottom: 10px;">调拨总数：{{ total }}</h3>
            </el-col>
               <el-col :xl="12" :lg="24" :md="24" :sm="24" :xs="24">
                 <el-table :data="tableData" style="width: 100%" stripe v-loading="loading" element-loading-text="Flyknit">
                <!-- 索引 -->
                <el-table-column type="selection" width="55" fixed> </el-table-column>
                <el-table-column prop="device_number" label="设备编号"></el-table-column>
                <el-table-column prop="device_name" label="设备名称"></el-table-column>
                <el-table-column prop="old_depart" label="原部门"></el-table-column>
                <el-table-column prop="new_depart" label="新部门"></el-table-column>
                <el-table-column prop="old_location" label="旧存放地点"></el-table-column>
                <el-table-column prop="new_location" label="新存放地点"></el-table-column>
                <el-table-column prop="transfer_user" label="调拨人"></el-table-column>
                <el-table-column prop="create_time" label="调拨时间"></el-table-column>
                <!-- 操作 -->
                <el-table-column label="操作" width="200">
                    <template slot-scope="scope">
                        <el-button type="danger" plain size="mini"
                            @click="handleDelete(scope.row.id)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
               </el-col>
              
           </el-row>
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
                :page-sizes="[10, 20, 30, 40]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="total">
            </el-pagination>
        </el-card>
    </div>
</template>
<script>
export default {
    data() {
        return {
            loading: true,
            form: {
                keywords: '',
                username: '',
            },
            tableData: [],
            currentPage: 1,
            pageSize: 10,
            total: 12
        }
    },
    mounted() {
        this.getTransferList();
    },
    methods: {
        // 处理输入框清除
        handleClear(field) {
            this.form[field] = '';
            this.currentPage = 1;
            this.getTransferList();
        },
        // 获取调拨记录列表
        async getTransferList() {
            try {
                this.form.page = this.currentPage;
                this.form.pageSize = this.pageSize;
                const res = await this.$http.getTransferRecord(this.form)
                if (res.status !== 200) {
                    return this.$message.error(res.message);
                }
                const { list, total } = res.data;
                this.tableData = list;
                this.total = total;
                setTimeout(() => {
                    this.loading = false;
                }, 500);
            } catch (error) {
                this.loading = false;
                console.error('获取调拨记录失败:', error);
                return this.$message.error('服务器错误，请稍后重试！');
            }
        },
        onSubmit() {
            if (!this.form.keywords && !this.form.username) {
                this.$message.warning('请至少输入一个搜索条件');
                return;
            }
            this.loading = true;
            this.currentPage = 1; // 重置到第一页
            this.getTransferList();
        },
        onReset() {
            // 重置表单数据
            this.form.keywords = '';
            this.form.username = '';
            
            // 重置分页
            this.currentPage = 1;
            this.pageSize = 10;
            
            // 显示加载状态
            this.loading = true;
            
            // 重新获取数据
            this.getTransferList();
            
            // 提示用户
            this.$message.success('重置成功');
        },
        onRefresh() {
            this.loading = true;
            this.getTransferList();
        },
        handleSizeChange(val) {
            this.loading = true;
            this.pageSize = val;
            this.getTransferList();
        },
        handleCurrentChange(val) {
            this.loading = true;
            this.currentPage = val;
            this.getTransferList();
        },
        async handleDelete(id) {
           try {
                this.$confirm('此操作将永久删除该条记录, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    const res = await this.$http.deleteTransferRecord({ id });
                    if (res.status !== 200) {
                        return this.$message.error(res.message);
                    }
                    this.getTransferList();
                    return this.$message.success(res.message);
                }).catch(() => {
                   return false;
                });
           } catch (error) {
                return this.$message.error('服务器错误，请稍后重试！');
           }
        },
    }
}
</script>
<style scoped>
.el-card__body {
    padding: 10px 20px 0 20px;
}
.el-table {
    width: 100%;
    margin-top: 10px;
}
.el-pagination {
    margin-top: 20px;
}
</style>