<!--
 * @Author: flyknit <EMAIL>
 * @Date: 2024-05-07 12:47:39
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-07-13 13:18:27
 * @FilePath: \electronic-filed:\gitee\backend-demo\src\views\style\Style.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by ${<EMAIL>}, All Rights Reserved. 
-->
<template>
  <div>
    <el-card>
      <el-row :gutter="10" style="display: flex; align-items: center">
        <el-col :span="24">
          <el-form inline :form="form">
            <!-- 搜索 -->
            <!-- <el-form-item>
              <el-input
                v-model="queryInfo.keyword"
                placeholder="请输入关键字"
                clearable
                size="medium"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="info" icon="el-icon-search" size="medium"
                >搜索</el-button
              >
            </el-form-item> -->
            <!-- <el-form-item>
              <el-button type="success" icon="el-icon-document-add" size="mini"
                >导入</el-button
              >
            </el-form-item> -->
            <el-form-item>
              <el-button type="primary" icon="el-icon-plus" size="medium" @click="switchHandler">添加品名</el-button>
            </el-form-item>
          </el-form>
          <el-table :data="styleList" resizable stripe v-loading="loading" element-loading-text="Flyknit"
            style="font-size: 0.88rem">
            <el-table-column label="#" type="index">
              <el-checkbox v-model="checked"></el-checkbox>
            </el-table-column>
            <el-table-column label="Model" header-align="center" sortable prop="model_name"
              align="center"></el-table-column>
            <el-table-column label="品名" sortable prop="pm" header-align="center" align="center"></el-table-column>
            <!-- <el-table-column
              label="尺码"
              sortable
              prop="size"
              header-align="center"
              align="center"
            ></el-table-column> -->
            <el-table-column label="类型" sortable prop="type" header-align="center" align="center">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.type === 0" type="success" size="mini">毛坯标准</el-tag>
                <el-tag v-else-if="scope.row.type === 1" type="warning" size="mini">光坯标准</el-tag>
              </template>
            </el-table-column>
            <!-- 创建日期 -->
            <el-table-column label="创建日期" sortable prop="create_time" header-align="center"
              align="center"></el-table-column>
            <!-- 操作 -->
            <el-table-column label="操作" header-align="center" align="center">
              <template slot-scope="scope">
                <el-button type="text" size="mini" @click="editHandler(scope.row)">编辑</el-button>
                <el-button type="text" style="color: #eb4c42" size="mini"
                  @click="deleteHandler(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <div class="page" v-show="total > 0">
            <el-pagination @size-change="handleSizeChange" @current-change="currentchange"
              :current-page="queryInfo.currentnum" :page-sizes="pageSizes" :page-size="queryInfo.pageSize"
              layout="total, sizes, prev, pager, next, jumper" :total="total">
            </el-pagination>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <!-- 添加品名弹窗 -->
    <el-dialog :title="title" :visible.sync="dialogFormVisible">
      <el-form :model="form" :rules="rules" ref="form" label-width="80px">
        <el-form-item label="Model" prop="model_name">
          <!-- <el-input v-model="form.model_name"></el-input> -->
          <el-autocomplete class="inline-input" v-model="form.model_name" :fetch-suggestions="querySearch"
            placeholder="请输入Model名称" @select="handleSelect" clearable style="width: 215px"></el-autocomplete>
        </el-form-item>
        <!-- <el-form-item label="品名" prop="style">
          <el-input v-model="form.style"></el-input>
        </el-form-item> -->
        <el-form-item label="品名" prop="style">
          <el-select v-model="form.style" filterable placeholder="请选择品名" style="flex: 1;">
            <el-option v-for="item in pmList" :key="item.id" :label="item.name" :value="item.name"></el-option>
          </el-select>
          <div class="add-model">
            <div class="pm-box" @click="showHandler">
              <i class="el-icon-circle-plus-outline" style="font-size: 1.2rem" v-if="!isShow"></i>
              <i class="el-icon-remove-outline" v-else style="font-size: 1.2rem"></i>
            </div>
            <blockquote v-if="isShow">
              <el-input v-model="form.pmname" style="width:220px;" placeholder="请输入品名"></el-input>
              <el-button type="primary" style="margin-left: 1rem;" plain @click="addHandler">添加</el-button>
            </blockquote>
          </div>
        </el-form-item>

        <!-- <el-form-item label="尺码" prop="size">
          <el-input v-model="form.size"></el-input>
        </el-form-item> -->
        <el-form-item label="尺码" prop="size">
          <!-- 下拉选择 -->
          <el-select v-model="form.size" filterable placeholder="请选择尺码">
            <el-option v-for="item in sizeList" :key="item.id" :label="item.size" :value="item.size"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio label="0">毛坯标准</el-radio>
            <el-radio label="1">光坯标准</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm('form')">{{
            btnText
            }}</el-button>
          <el-button @click="dialogFormVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: "Size",
  data() {
    return {
      isShow: false, // 是否显示添加品名输入框
      pmList: [], // 品名列表
      styleList: [], // 品名列表
      sizeList: [], // 尺码列表
      loading: true,
      checked: false,
      queryInfo: {
        currentnum: 1,
        pageNum: 1,
        pageSize: 10,
        keyword: "",
      },
      total: 10,
      pageSizes: [10, 20, 30, 40],
      dialogFormVisible: false,
      form: {
        pmname: "",
        model_name: "",
        style: "",
        size: "",
        type: "0",
      },
      rules: {
        model_name: [
          { required: true, message: "请输入Model", trigger: "blur" },
        ],
        style: [{ required: true, message: "请选择品名", trigger: "blur" }],
        size: [{ required: true, message: "请输入尺码", trigger: "blur" }],
        type: [{ required: true, message: "请选择类型", trigger: "blur" }],
      },
      btnText: "立即添加",
      title: "添加品名",
      modelList: [], // model列表
    };
  },
  created() {
    this.getStyleList();
    this.getSizeList();
    this.getModelList();
    this.getModelName();
  },
  methods: {
    // 获取品名列表
    async getModelName() {
      try {
        const res = await this.$http.getPmLists();
        if (res.code !== 200) {
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          })
        }
        const { list } = res.data
        this.pmList = list
      } catch (error) {
        return this.$notify({
          title: "系统提示",
          message: "获取品名列表失败",
          type: "error",
        })
      }
    },
    // 添加品名接口
    async addHandler() {
      if (!this.form.pmname) {
        return this.$notify({
          title: "系统提示",
          message: "请输入品名",
          type: "error",
        });
      }
      try {
        const res = await this.$http.addProductByName({
          name: this.form.pmname,
        });
        if (res.code !== 200) {
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        this.$notify({
          title: "成功",
          message: res.message,
          type: "success",
        });
        this.form = {
          model_name: ""
        };
        this.isShow = false;
        this.getModelName()
      } catch (error) {
        return this.$notify({
          title: "系统提示",
          message: "添加品名失败",
          type: "error",
        });
      }
    },
    // 展示添加品名输入框
    showHandler() {
      this.isShow = !this.isShow;
    },
    // 获取全部model
    async getModelList() {
      try {
        const res = await this.$http.getAllModelList();
        if (res.code !== 200) {
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        const arrayList = res.data.list;
        this.modelList = arrayList.map(item => {
          return {
            value: item.model_name,
            name: item.serial,
          };
        });
      } catch (error) {
        return this.$notify({
          title: "系统提示",
          message: "获取model列表失败",
          type: "error",
        });
      }
    },
    // 搜索建议
    querySearch(queryString, cb) {
      const modelArrays = this.modelList;
      const results = queryString
        ? modelArrays.filter(this.createStateFilter(queryString))
        : modelArrays;
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        cb(results);
      }, 1500 * Math.random());
    },
    // 搜索建议
    createStateFilter(queryString) {
      return (state) => {
        return (
          state.value
            .toLowerCase()
            .indexOf(queryString.toLowerCase()) === 0
        );
      };
    },
    handleSelect(item) {
      this.form.model_name = item.value;
    },
    // 获取全部尺码
    async getSizeList() {
      try {
        const res = await this.$http.getAllSizeList();
        if (res.code !== 200) {
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        this.sizeList = res.data.list;
      } catch (error) {
        return this.$notify({
          title: "系统提示",
          message: "获取尺码列表失败",
          type: "error",
        });
      }
    },
    // 删除品名
    async deleteHandler(id) {
      this.$confirm("此操作将永久删除该品名, 是否继续?", "系统提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.deleteStyleApi(id);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 全局删除品名接口
    async deleteStyleApi(id) {
      try {
        const res = await this.$http.deleteProductName({ id });
        if (res.code !== 200) {
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        this.$notify({
          title: "成功",
          message: res.message,
          type: "success",
        });
        this.getStyleList();
      } catch (error) {
        return this.$notify({
          title: "系统提示",
          message: "删除品名失败",
          type: "error",
        });
      }
    },
    // 编辑品名
    editHandler(row) {
      this.title = "编辑品名";
      this.btnText = "编辑";
      this.form.model_name = row.model_name;
      this.form.style = row.pm;
      this.form.size = row.size;
      this.form.type = String(row.type);
      this.form.id = row.id;
      this.dialogFormVisible = true;
    },
    // 添加品名弹窗开关
    switchHandler() {
      this.dialogFormVisible = true;
      this.form = {
        model_name: "",
        style: "",
        size: "",
        type: "0",
      };
      this.btnText = "立即添加";
      this.title = "添加品名";
    },
    // 添加品名
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.submitFormApi();
        } else {
          return false;
        }
      });
    },
    // 封装提交表单接口
    async submitFormApi() {
      try {
        const res = await this.$http.addProductName(this.form);
        if (res.code !== 200) {
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        this.$notify({
          title: "成功",
          message: res.message,
          type: "success",
        });
        this.dialogFormVisible = false;
        this.form = {
          model_name: "",
          style: "",
          size: "",
          type: "0",
        };
        this.getStyleList();
      } catch (error) {
        return this.$notify({
          title: "系统提示",
          message: "添加品名失败",
          type: "error",
        });
      }
    },
    // 获取品名列表
    async getStyleList() {
      try {
        this.loading = true;
        const res = await this.$http.getProductNameList(this.queryInfo);
        if (res.code !== 200) {
          this.loading = false;
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "error",
          });
        }
        const { result, total } = res.data.list;
        this.styleList = result;
        this.total = total;
        setTimeout(() => {
          this.loading = false;
        }, 500);
      } catch (error) {
        this.loading = false;
        return this.$notify({
          title: "系统提示",
          message: "获取品名列表失败",
          type: "error",
        });
      }
    },
    handleSizeChange(val) {
      this.queryInfo.pageSize = val;
      this.getStyleList();
    },
    currentchange(val) {
      this.queryInfo.pageNum = val;
      this.getStyleList();
    },
  },
};
</script>
<style scoped>
.page {
  display: flex;
  justify-content: flex-start;
  margin-top: 20px;
}
.add-model {
  position: absolute;
  right: 0;
  top: 0px;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  right: 40%;
  width: 280px;
}

.pm-box {
  display: flex;
  font-size: 0.85rem;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  color: #409EFF;
  margin: 0 1rem;
}
.pm-box .i {
  font-size: 1rem;
}
</style>