 <template>
  <div>
    <!-- 卡片式图 -->
    <el-card class="card-list">
      <!-- 表单，搜索框，搜索按钮，添加按钮 -->
      <el-form :form="form" ref="form" inline size="medium">
        <el-form-item label="姓名">
          <el-input
            v-model="form.nickname"
            placeholder="请输入姓名"
            clearable
            @clear="clearHandler"
          ></el-input>
        </el-form-item>
        <el-form-item label="用户类型">
          <el-select
            v-model="form.type"
            placeholder="请选择用户类型"
            clearable
            @clear="clearHandler"
          >
            <el-option label="保全工" value="0"></el-option>
            <el-option label="巡检员" value="1"></el-option>
            <el-option label="超级管理员" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="用户状态">
          <el-select
            v-model="info.is_ban"
            placeholder="请选择用户状态"
            clearable
            @clear="clearHandler"
          >
            <el-option label="正常" value="0"></el-option>
            <el-option label="禁用" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="success"
            icon="el-icon-search"
            @click="searchHandler"
          >
            搜索
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-plus" @click="addUser">
            添加用户
          </el-button>
        </el-form-item>
      </el-form>
      <!-- 订单列表区域 -->
      <el-table
        :data="userList"
        stripe
        style="width: 100%"
        v-loading="loading"
        element-loading-text="加载中..."
      >
        <el-table-column label="#" type="index">
          <el-checkbox v-model="checked"></el-checkbox>
        </el-table-column>
        <el-table-column label="头像" prop="avatar_url">
          <template slot-scope="scope">
            <div
              style="
                width: 40px;
                height: 40px;
                overflow: hidden;
                border-radius: 7px;
              "
            >
              <el-image :src="scope.row.avatar_url" lazy :fit="fit">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="姓名" prop="nickname"></el-table-column>
        <el-table-column label="班次" prop="shift"></el-table-column>
        <el-table-column label="工号" prop="idcard"></el-table-column>
        <el-table-column label="用户类型" prop="type">
          <template slot-scope="scope">
            <el-tag :type="scope.row.type > 0 ? 'success' : 'info'">
              {{
                scope.row.type === 0
                  ? "保全工"
                  : scope.row.type === 1
                  ? "巡检员"
                  : "系统管理员"
              }}</el-tag
            >
          </template>
        </el-table-column>
        <!-- 是否禁用 -->
        <el-table-column label="用户状态" prop="is_ban">
          <template slot-scope="scope">
            <el-tag
              :type="scope.row.is_ban === 0 ? 'success' : 'danger'"
              v-if="scope.row.is_ban === 0"
              >正常</el-tag
            >
            <el-tag :type="scope.row.is_ban === 1 ? 'danger' : 'success'" v-else
              >禁用</el-tag
            >
          </template>
        </el-table-column>
        <!-- 是否在线 -->
        <el-table-column label="在线状态" prop="status">
          <template slot-scope="scope">
            <el-tag
              :type="scope.row.status === 0 ? 'info' : 'success'"
              v-if="scope.row.status === 0"
              >离线</el-tag
            >
            <el-tag :type="scope.row.status === 1 ? 'success' : 'info'" v-else
              >在线</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column label="创建日期" prop="create_time"></el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <template>
              <el-button
                size="mini"
                type="info"
                @click.native="modifyUser(scope.row)"
                :disabled="scope.row.type === 2&&scope.row.nickname == 'admin'"
              >
                <i class="el-icon-edit"></i>
                修改
              </el-button>
              <el-button
                size="mini"
                type="danger"
                plain
                @click.native="deleteUser(scope.row.id)"
                :disabled="scope.row.type === 2&&scope.row.nickname == 'admin'"
              >
                <i class="el-icon-delete"></i>
                删除
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="page" v-show="total > 0">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="currentchange"
          :current-page="queryInfo.currentnum"
          :page-sizes="pageSizes"
          :page-size="queryInfo.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
      <!-- 添加用户弹窗 -->
      <el-drawer :title="title" :visible.sync="dialogFormVisible" size="30%">
        <el-form
          :model="userform"
          :rules="rules"
          ref="userform"
          label-width="80px"
          class="demo-ruleForm"
          style="margin: 0rem 1rem 0 1rem;"
        >
          <el-form-item label="头像" prop="avatar">
            <el-upload
              :action="action"
              :headers="headers"
              name="file"
              :file-list="logo"
              :multiple="false"
              list-type="picture-card"
              :on-success="uploadDocumentHandler"
              :on-remove="logoRemove"
              :on-error="onErr"
              :on-progress="project"
            >
              <i class="el-icon-plus"></i>
            </el-upload>
            <el-dialog :visible.sync="dialogVisible">
              <img width="100%" :src="dialogImageUrl" alt="加载失败" />
            </el-dialog>
          </el-form-item>
          <el-form-item label="姓名" prop="nickname">
            <el-input
              v-model="userform.nickname"
              placeholder="请输入姓名"
            ></el-input>
          </el-form-item>
          <el-form-item label="班次" prop="shift">
            <el-select v-model="userform.shift" placeholder="请选择班次">
              <el-option label="A" value="A"></el-option>
              <el-option label="B" value="B"></el-option>
              <el-option label="C" value="C"></el-option>
              <el-option label="E" value="E"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="工号" :prop="isEdit ? 'idcard' : ''">
            <el-input
              v-model="userform.idcard"
              :readonly="isEdit ? true : false"
              placeholder="请输入工号"
            ></el-input>
          </el-form-item>
          <el-form-item label="用户类型" prop="type">
            <el-select v-model="userform.type" placeholder="请选择用户类型">
              <el-option label="保全工" value="0"></el-option>
              <el-option label="巡检员" value="1"></el-option>
              <el-option label="超级管理员" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户状态" prop="is_ban">
            <el-select v-model="userform.is_ban" placeholder="请选择用户状态">
              <el-option label="正常" value="0"></el-option>
              <el-option label="禁用" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input
              v-model="userform.password"
              placeholder="请输入密码"
              type="password"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="确认密码" prop="repassword">
            <el-input
              type="password"
              v-model="userform.repassword"
              placeholder="请再次输入密码"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item style="position: absolute;bottom: 0;">
            <el-button type="primary" style="width: 10rem;" @click="submitForm('userform')">{{
              btnText
            }}</el-button>
            <el-button style="width: 10rem;" @click="resetForm('userform')">重置</el-button>
          </el-form-item>
        </el-form>
      </el-drawer>
    </el-card>
  </div>
</template>
<script>
import { mapState } from "vuex";
import config from "@/config/config.js";
export default {
  data() {
    return {
      fileList: [], // 上传图片的列表
      dialogFormVisible: false, // 弹窗开关
      dialogImageUrl: "", // 图片预览路径
      dialogVisible: false, // 图片预览开关
      // 上传logoing
      loadmen: false,
      action: config.uploadURL, // 上传图片的地址
      logo: [], // model图片
      headers: {
        Authorization: `Bearer ${localStorage.getItem("accessToken")}`,
      },
      baseURL: config.imgPrefix, // 图片地址
      form: {
        nickname: "",
        type: "",
        is_ban: "",
      },
      loading: true, // 表格加载
      checked: false,
      // 管理员列表
      userList: [],
      queryInfo: {
        pageNum: 1,
        pageSize: 12,
        currentnum: 1,
      },
      pageSizes: [10, 12, 14],
      total: 1,
      info: [],
      fit: "fill", // 图片裁剪方式
      url: "https://img0.baidu.com/it/u=1821253856,3774998416&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500", // 默认头
      userform: {
        avatar: "",
        nickname: "",
        shift: "",
        idcard: "",
        type: "",
        is_ban: "",
        password: "",
        repassword: "",
      },
      rules: {
        nickname: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        shift: [{ required: true, message: "请输入班次", trigger: "blur" }],
        idcard: [{ required: true, message: "请输入工号", trigger: "blur" }],
        type: [{ required: true, message: "请选择用户类型", trigger: "blur" }],
        is_ban: [
          { required: true, message: "请选择用户状态", trigger: "blur" },
        ],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
        repassword: [
          { required: true, message: "请再次输入密码", trigger: "blur" },
        ],
      },
      btnText: "立即添加",
      title: "添加用户",
      isEdit: false, // 是否编辑
    };
  },
  computed: {
    ...mapState({
      User: (state) => state.users,
    }),
  },
  created() {
    this.getUserList();
  },
  methods: {
    // 修改用户接口
    modifyUser(row) {
      this.isEdit = true;
      this.logo = [];
      this.title = "修改用户";
      this.btnText = "立即修改";
      this.userform.avatar = row.avatar_url;
      this.userform.nickname = row.nickname;
      this.userform.shift = row.shift;
      this.userform.idcard = row.idcard;
      this.userform.type = String(row.type);
      this.userform.is_ban = String(row.is_ban);
      this.userform.password = row.password;
      this.userform.repassword = row.password;
      this.userform.id = row.id;
      const obj = {
        id: row.id,
        url: row.avatar_url,
      };
      this.logo.push(obj);
      this.dialogFormVisible = true;
    },
    // 上传失败
    onErr(e) {
      this.loadmen = false;
      this.$message.error("上传失败,尝试重新上传");
    },
    // 上传时
    project(file) {
      this.loadmen = true;
    },
    // logo移除文件时的钩子
    logoRemove(file, fileList) {
      this.logo = [];
      this.userform.avatar = "";
    },
    // 展开大图
    handlepreview(file) {
      // console.log('file', file);
      this.modelImage = file.url;
      this.showModleImage = true;
    },
    // 上传model图片成功回掉
    uploadDocumentHandler(res, file, fileList) {
      const { list } = res.data;
      this.logo.push({ url: list, uid: file.uid }); //element展示图片时需要数组类型的才能展示
      this.loadmen = false;
      this.userform.avatar = list;
    },
    // 提交表单事件
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.addUserHandler();
        } else {
          return false;
        }
      });
    },
    // 清除事件
    clearHandler() {
      this.form.nickname = "";
      this.form.type = "";
      this.form.is_ban = "";
      this.getUserList();
    },
    // 刷新表单
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    // 添加用户
    addUser() {
      this.isEdit = false;
      this.title = "添加用户";
      this.btnText = "立即添加";
      this.logo = [];
      this.userform = {
        avatar: "",
        nickname: "",
        shift: "",
        idcard: "",
        type: "",
        is_ban: "",
        password: "",
        repassword: "",
      };
      this.dialogFormVisible = true;
    },
    // 添加用户接口
    async addUserHandler() {
      try {
        const res = await this.$http.createAdmin(this.userform);
        if (res.code === 200) {
          this.$notify({
            title: "成功",
            message: res.message,
            type: "success",
          });
          this.dialogFormVisible = false;
          this.resetForm("userform");
          this.logo = [];
          this.getUserList();
        } else {
          this.$notify({
            title: "警告",
            message: res.message,
            type: "warning",
          });
        }
      } catch (error) {
        this.$notify({
          title: "系统提示",
          message: error.message,
          type: "warning",
        });
      }
    },
    // 搜索
    searchHandler() {
      this.loading = true;
      this.queryInfo.pageNum = 1;
      this.getUserList();
    },
    // 获取用户列表
    async getUserList() {
      try {
        this.queryInfo.nickname = this.form.nickname;
        this.queryInfo.type = this.form.type;
        this.queryInfo.is_ban = this.form.is_ban;
        const res = await this.$http.getAdminList(this.queryInfo);
        if (res.code !== 200) {
          this.userList = [];
          this.total = 0;
          this.$notify({
            title: "警告",
            message: res.message,
            type: "warning",
          });
        }
        const { result, total } = res.data.list;
        this.userList = result;
        this.total = total;
        setTimeout(() => {
          this.loading = false;
        }, 500);
      } catch (error) {
        this.$notify({
          title: "系统提示",
          message: error.message,
          type: "warning",
        });
      } finally {
        setTimeout(() => {
          this.loading = false;
        }, 500);
      }
    },
    // 删除管理员接口
    async deleteUser(id) {
      this.$confirm("此操作将永久删除该管理员, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          try {
            const res = await this.$http.deleteAdmin({
              id,
            });
            if (res.code === 200) {
              this.$notify({
                title: "成功",
                message: res.message,
                type: "success",
              });
              this.getUserList();
            } else {
              this.$notify({
                title: "警告",
                message: res.message,
                type: "warning",
              });
            }
          } catch (error) {
            this.$notify({
              title: "系统提示",
              message: error.message,
              type: "warning",
            });
          }
        })
        .catch(() => {
          this.$notify({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 分页
    handleSizeChange(val) {
      this.loading = true;
      this.queryInfo.pageSize = val;
      this.getUserList();
    },
    // 分页
    currentchange(val) {
      this.loading = true;
      this.queryInfo.pageNum = val;
      this.getUserList();
    },
  },
};
</script>
<style lang="scss" scoped>
::vue-deep.hide .el-upload--picture-card {
  display: none;
}
.submit-btn {
  background-color: #000;
  color: #fff;
  border: none;
}
.search-btn {
  background-color: #000;
  color: #fff;
  border: none;
}
.reset-btn {
  background-color: #fff;
  color: #000;
}
.refresh-btn {
  background-color: #fff;
  color: #000;
}

.btn-box {
  display: flex;
  padding: 50px 0 0 0;
  justify-content: flex-start;
  align-items: center;
  .el-button {
    flex: 1;
  }
}

.input-box {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  width: 400px;
}

.image-view-title {
  margin: 10px 20px 10px 10px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.image-view-title img {
  object-fit: cover;
}

.image-list {
  width: 100px;
}

.image-button {
  text-align: center;
  padding: 120px 0;
}

.status-text {
  color: #30c82b;
  font-weight: bold;
  border: 1px solid #30c82b;
  display: block;
  width: 60px;
  height: 20px;
  margin: 0 auto;
  line-height: 20px;
  text-align: center;
}

.wait-complete {
  color: #ccc;
  font-weight: bold;
  border: 1px solid #ccc;
  display: block;
  width: 60px;
  height: 20px;
  margin: 0 auto;
  line-height: 20px;
  text-align: center;
}

.page {
  padding: 20px 0 0 0;
}

.el-image__inner {
  border-radius: 50%;
}

.el-table .cell {
  text-align: center;
}
</style>