<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-07-24 11:18:21
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-15 11:21:55
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\department\MaintainceList.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-card class="operate-area">
            <el-row :gutter="20">
                <el-col :span="3">
                    <el-button type="primary" icon="el-icon-plus" @click="addMaintaince">添加保养项</el-button>
                </el-col>
                <el-col :span="21" style="display:flex;justify-content:flex-end;">
                    <el-button type="primary" plain icon="el-icon-refresh-right" @click="refresh"></el-button>
                </el-col>
            </el-row>
        </el-card>
        <!-- 内容展示区域 -->
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane :label="item.lable" :name="item.name" v-for="(item, index) in departList"
                :key="index"></el-tab-pane>
        </el-tabs>
        <!-- 保养项列表 -->
        <el-row :gutter="20" class="project-list">
            <el-col :span="24">
                <el-card>
                    <el-table :data="tableData" stripe v-loading="loading" element-loading-text="Flyknit">
                        <el-table-column type="index" label="序号" width="60"></el-table-column>
                        <el-table-column prop="type" label="保养项名称"></el-table-column>
                        <el-table-column prop="maintaince_name" label="保养项描述" width="400"></el-table-column>
                        <el-table-column prop="classify" label="保养分类">
                            <template slot-scope="scope">
                                <span>{{ scope.row.classify_id === 1 ? '日保养' : scope.row.classify_id === 2 ? '月保养'
                                    : scope.row.classify_id === 3 ? '季度保养' : scope.row.classify_id === 4 ? '半年保养' :
                                        '年保养' }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="equipment_type" label="针对设备类型"></el-table-column>
                        <el-table-column prop="cycle" label="保养周期">
                            <template slot-scope="scope">
                                <span>{{ scope.row.cycle }}天</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="points" label="标准扣分分数">
                            <template slot-scope="scope">
                                <span>{{ scope.row.points }}分</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作">
                            <template slot-scope="scope">
                                <el-button type="primary" size="mini" @click="editHandler(scope.row)">编辑</el-button>
                                <el-button type="danger" size="mini" @click="delHandler(scope.row.id)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <!-- 分页 -->
                    <el-row :gutter="20" class="bottom-page" v-show="total > 0">
                        <el-col :span="24">
                            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                                :current-page="currentnum" :page-sizes="pageSizes" :page-size="pageSize"
                                layout="total, sizes, prev, pager, next, jumper" :total="total">
                            </el-pagination>
                        </el-col>
                    </el-row>
                </el-card>
            </el-col>
        </el-row>
        <!-- 添加保养项弹窗 -->
        <el-dialog title="添加保养项" :visible.sync="dialogVisible" width="40%">
            <el-form :model="form" ref="form" :rules="rules" label-width="100px">
                <el-form-item label="保养项名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入保养项目名称"></el-input>
                </el-form-item>
                <el-form-item label="保养内容" prop="describe">
                    <el-input v-model="form.describe" placeholder="请输入保养项目内容"></el-input>
                </el-form-item>
                <el-form-item label="保养分类" prop="classify">
                    <el-select v-model="form.classify" placeholder="请选分类">
                        <el-option label="日保养" value="1"></el-option>
                        <el-option label="周保养" value="6"></el-option>
                        <el-option label="月保养" value="2"></el-option>
                        <el-option label="季度保养" value="3"></el-option>
                        <el-option label="半年保养" value="4"></el-option>
                        <el-option label="年保养" value="5"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="保养周期" prop="cycle">
                    <el-input v-model="form.cycle" placeholder="请输入项目的保养周期，例如周保养7天就是7"></el-input>
                </el-form-item>
                <el-form-item label="设备类型" prop="equipment">
                    <el-select v-model="form.equipment" placeholder="请选择针对的设备类型">
                        <el-option label="电脑编织横机" value="电脑编织横机"></el-option>
                        <el-option label="预缩机" value="预缩机"></el-option>
                        <el-option label="流水线" value="流水线"></el-option>
                        <el-option label="包装机" value="包装机"></el-option>
                        <el-option label="验针机" value="验针机"></el-option>
                        <el-option label="粉碎机" value="粉碎机"></el-option>
                        <el-option label="精雕机" value="精雕机"></el-option>
                        <el-option label="稳压柜" value="稳压柜"></el-option>
                        <el-option label="配电间" value="配电间"></el-option>
                        <el-option label="机顶照明与安全电器机顶照明开关拉线、通道照明灯吊架、检验台、接地线、应急灯、安全指示牌是否完好安全"
                            value="机顶照明与安全电器机顶照明开关拉线、通道照明灯吊架、检验台、接地线、应急灯、安全指示牌是否完好安全"></el-option>
                        <el-option label="照明终端箱" value="照明终端箱5"></el-option>
                        <el-option label="配电房" value="配电房"></el-option>
                        <el-option label="送风风机" value="送风风机"></el-option>
                        <el-option label="制冷机组" value="制冷机组"></el-option>
                        <el-option label="空压机" value="空压机"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="扣分分数" prop="points">
                    <!-- <el-input v-model="form.points" placeholder="请输入该项的扣分分数标准"></el-input> -->
                    <el-input-number v-model="form.points" @change="handleChanges" :min="form.min" :step="form.min"
                        :max="form.max" label="描述文字"></el-input-number>
                </el-form-item>
                <el-form-item label="所属部门" prop="department">
                    <el-select v-model="form.department" placeholder="请选部门">
                        <el-option label="织造车间" value="织造车间"></el-option>
                        <el-option label="整理车间" value="整理车间"></el-option>
                        <el-option label="检验车间" value="检验车间"></el-option>
                        <el-option label="打样车间" value="打样车间"></el-option>
                        <el-option label="综合维修" value="综合维修"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="addHandler('form')" icon="el-icon-postcard">{{ btnText }}</el-button>
                <el-button type="button" icon="el-icon-refresh" @click="resetTable('form')">重 置 表 单</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
export default {
    data() {
        return {
            activeName: 'first',
            activeNames: ['1'],
            departList: [{
                id: 1,
                lable: '织造车间',
                name: 'first'
            }, {
                id: 1,
                lable: '整理车间',
                name: 'second'
            }, {
                id: 1,
                lable: '打样车间',
                name: 'third'
            }, {
                id: 1,
                lable: '综合维修',
                name: 'fourth'
            }],
            tableData: [], // 表格数据
            page: 1,
            pageSize: 10,
            currentnum: 1,
            total: 0,
            pageSizes: [10, 20, 30, 40], // 每页显示的条目个数
            dialogVisible: false,
            form: {
                name: '',
                describe: '',
                department: '织造车间',
                classify: '',
                cycle: '',
                points: '',
                min: 0.5,
                max: 5,
                equipment: '', // 针对设备类型
                types: '', // 操作类型
                mid: 0, // 编辑保养项id
            },
            loading: true, // 加载
            title: '添加保养项', // 标题
            btnText: '立 即 添 加', // 按钮文字
            rules: {
                name: [{
                    required: true,
                    message: '请输入保养项名称',
                    trigger: 'blur'
                }],
                describe: [{
                    required: true,
                    message: '请输入保养项描述',
                    trigger: 'blur'
                }],
                department: [{
                    required: true,
                    message: '请选择所属部门',
                    trigger: 'change'
                }],
                classify: [{
                    required: true,
                    message: '请选择保养项分类',
                    trigger: 'change'
                }],
                cycle: [{
                    required: true,
                    message: '请输入保养项周期',
                    trigger: 'blur'
                }],
                points: [{
                    required: true,
                    message: '请输入保养项扣分分数',
                    trigger: 'blur'
                }],
                equipment: [{
                    required: true,
                    message: '请选择设备类型',
                    trigger: 'change'
                }]
            }
        }
    },
    created() {
        this.getMaintainceList();
    },
    methods: {
        // 编辑保养项目
        editHandler(row) {
            this.form.types = 'edit';
            this.title = '编辑保养项';
            this.btnText = '立 即 修 改';
            this.dialogVisible = true;
            this.form.name = row.type;
            this.form.describe = row.maintaince_name;
            this.form.department = row.department;
            this.form.classify = row.classify_id == 1 ? '日保养' : row.classify_id == 2 ? '月保养' : row.classify_id == 3 ? '季度保养' : row.classify_id == 4 ? '半年保养' : '年保养';
            this.form.cycle = row.cycle;
            this.form.points = row.points;
            this.form.equipment = row.equipment_type;
            this.form.mid = row.id;
        },
        async delHandler(id) {
            try {
                const res = await this.$http.delMaintaince({ mid: id });
                if (res.status !== 200) {
                    return this.$message.error(res.message);
                }
                this.$message.success(res.message);
                this.getMaintainceList();
            } catch (error) {
                return this.$message.error('服务器异常，请稍后重试');
            }
        },
        // 刷新数据
        refresh() {
            this.loading = true
            this.page = 1;
            this.getMaintainceList();
        },
        // 获取保养项目列表
        async getMaintainceList() {
            try {
                const obj = {
                    page: this.page,
                    pageSize: this.pageSize,
                    keyword: this.form.department,
                }
                const res = await this.$http.getMaintainceList(obj);
                if (res.status !== 200) {
                    return this.$message.error(res.message);
                }
                const { list } = res.data;
                this.total = res.data.total;
                this.tableData = list
                setTimeout(() => {
                    this.loading = false
                }, 500)
                return false
            } catch (error) {
                this.loading = false
                return this.$message.error('服务器异常，请稍后重试');
            }
        },
        // tab切换
        handleClick(tab, event) {
            this.loading = true
            this.form.department = tab.label;
            this.getMaintainceList();
        },
        // 每页显示的条目个数改变时触发
        handleSizeChange(val) {
            this.loading = true
            this.pageSize = val;
            this.getMaintainceList();
        },
        // 分页
        handleCurrentChange(val) {
            this.loading = true
            this.page = val;
            this.getMaintainceList();
        },
        // 添加保养项目弹窗
        addMaintaince() {
            this.form.types = 'add';
            this.dialogVisible = true;
        },
        // 进步输入框事件
        handleChanges(val) {
            console.log(val);
        },
        // 添加保养项目
        async addHandler(formName) {
            try {
                this.$refs[formName].validate(async (valid) => {
                    if (valid) {
                        // 表单验证通过，可以执行提交操作
                        const res = await this.$http.addMaintaince(this.form)
                        if (res.status !== 200) {
                            return this.$message.error(res.message);
                        } else {
                            this.$message.success(res.message);
                            this.dialogVisible = false;
                            this.resetTable();
                            this.getMaintainceList();
                        }
                    } else {
                        // 表单验证不通过，可以进行相应处理
                        return false;
                    }
                });
            } catch (error) {
                return this.$message.error(error);
            }
        },
        // 重置表单
        resetTable() {
            this.$refs['form'].resetFields();
        }
    }
}
</script>
<style scoped>
.operate-area {
    padding: 0px 20px 15px 0px;
}

.el-tabs {
    padding: 5px 0 0 0;
}

.bottom-page {
    margin-top: 20px;
}

.dialog-footer {
    text-align: left;
    padding: 0 30px;
}
</style>