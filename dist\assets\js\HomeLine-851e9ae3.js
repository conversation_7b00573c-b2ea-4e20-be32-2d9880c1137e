import{e as n,u as h}from"./useEcharts-57db09d5.js";import{u as d,i,V as y,k as m,G as _,ac as w,bm as x,o as g,g as b,C as k}from"./index-444b28c3.js";import{_ as C}from"./_plugin-vue_export-helper-c27b6911.js";const S=k({name:"columnChart"}),v=Object.assign(S,{props:{weekCount:{type:Array,default:()=>[]}},setup(c){const{t:f}=d(),t=i(),a=i([]);let e=null;const u=c;y.on("changeLanguage",()=>{e&&(e.dispose(),e=null),r()}),m(async()=>{await _(),e&&(e.dispose(),e=null),r()}),w(()=>{e&&(e.dispose(),e=null)});const p=()=>{try{if(!t.value){console.error("Chart container not found");return}e=n.init(t.value);let o={title:{text:f("home.xiajiqushi"),textStyle:{color:"#909399"}},tooltip:{show:!0,trigger:"axis",axisPointer:{type:"shadow",z:100,shadowStyle:{shadowColor:"rgba(167, 167, 167, 1)",shadowBlur:10,color:"rgba(255, 255, 255, 1)"}}},grid:{top:"40px",bottom:"20px",left:"40px",right:"20px"},xAxis:[{type:"category",axisTick:{show:!1,alignWithLabel:!0},axisLine:{lineStyle:{color:"#889fcc"}},data:a.value}],yAxis:[{type:"value",splitLine:{show:!0,lineStyle:{color:"rgba(136, 159, 204, .2)"}},axisLine:{show:!1,lineStyle:{color:"#889fcc"}},axisTick:{show:!1}}],series:[{z:200,type:"line",smooth:!0,symbolSize:15,showSymbol:!1,show:!1,color:"#3282FF",lineStyle:{color:"#3282FF"},label:{show:!0,position:"top",color:"#999999",fontSize:10},emphasis:{show:!0,color:"#3282FF",borderColor:"#ffffff",label:{show:!0,position:"top",color:"##fff",fontSize:14}},areaStyle:{color:new n.graphic.LinearGradient(0,0,0,1,[{offset:0,color:"rgba(50,130,255,0.3)"},{offset:1,color:"rgba(50,130,255,0)"}],!1),shadowColor:"rgba(59, 34, 201,1)",shadowBlur:10},data:u.weekCount}]};h(e,o)}catch(o){}},r=()=>{const o=[],l=x().startOf("week");for(let s=0;s<7;s++)o.push(l.add(s,"days").format("YYYY-MM-DD"));a.value=o,p()};return(o,l)=>(g(),b("div",{ref_key:"echartsRef",ref:t,class:"card content-box"},null,512))}}),z=C(v,[["__scopeId","data-v-b5019ce6"]]);export{z as default};
