<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-07-24 11:21:41
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-15 12:45:03
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\department\MaintainceArea.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-card>
            <el-row :gutter="20">
                <el-col :span="18">
                    <el-form :inline="true" :model="form" ref="form">
                        <el-form-item label="车间部门">
                            <el-select v-model="form.depart" placeholder="请选择部门">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="onSubmit" icon="el-icon-search">搜索一下</el-button>
                        </el-form-item>
                        <el-form-item v-show="User.level > 0">
                            <el-button type="success" @click="addLocation" icon="el-icon-plus">添加区域</el-button>
                        </el-form-item>
                        <el-form-item v-show="User.level > 0 && delList.length > 0">
                            <el-button type="danger" icon="el-icon-delete" @click="deleteAll">批量删除</el-button>
                        </el-form-item>
                    </el-form>
                </el-col>
                <el-col :span="6" style="display:flex;justify-content:flex-end;padding-right:20px;">
                    <el-button type="primary" icon="el-icon-refresh-right" plain @click="refresh"></el-button>
                </el-col>
            </el-row>
        </el-card>
        <!-- 区域列表 -->
       <template v-if="areaList.length > 0">
        <div class="area-list" v-loading="loading" element-loading-text="Flyknit">
            <div v-for="(item, index) in areaList" :key="index" class="list-box">
                <div class="top-area" v-show="User.level > 0">
                    <el-checkbox-group v-model="checkList" @change="changeData(item.id, index)">
                        <el-checkbox :label="'ID:' + item.id" :value="item.id"></el-checkbox>
                    </el-checkbox-group>
                    <el-row :gutter="20" style="display: flex;" v-show="delList.length <= 1">
                        <el-col :span="11">
                            <el-tooltip content="编辑" placement="bottom" effect="dark"
                                v-show="delList.length > 0 && currentIndex === index">
                                <span class="el-icon-edit" style="color:#517DF7;cursor:pointer;" @click="editArea(item)"
                                    v-show="delList.length > 0 && currentIndex === index"></span>
                            </el-tooltip>
                        </el-col>
                        <el-col :span="2">
                            <span style="color:#e5e5e5;" v-show="delList.length > 0 && currentIndex === index">|</span>
                        </el-col>
                        <el-col :span="11">
                            <span class="el-icon-delete" style="color:#e04c07;cursor:pointer;" @click="delAreal(item.id)"
                                v-show="delList.length > 0 && currentIndex === index"></span>
                        </el-col>
                    </el-row>
                </div>
                <div class="area-box">
                    <el-row :gutter="10" style="width:100%;padding-left:20px;">
                        <el-col :span="8"
                            style="display:flex;flex-direction:column;justify-content:center;align-items:center;">
                            <el-image style="width: 40px; height: 40px;border-radius:50%;" lazy :src="item.avatar"
                                fit="fill">
                                <div slot="error" class="image-slot">
                                    <i class="el-icon-picture-outline"></i>
                                </div>
                            </el-image>
                            <h5 style="font-size: 12px;color:#999;">【{{ item.username }}】</h5>
                        </el-col>
                        <el-col :span="10">
                            <h5 style="color: #999;font-size:16px;letter-spacing:3px;">{{ item.area }}</h5>
                        </el-col>
                        <el-col :span="6"></el-col>
                    </el-row>
                    <div class="list-item">
                        <el-col :span="4" v-for="(item1, index1) in item.machine_range" :key="index1">{{ item1 }}</el-col>
                    </div>
                </div>
            </div>
        </div>
       </template>
        <div v-else class="no-data-list">
            <el-row :gutter="20">
                <el-col :span="24" style="display: flex;flex-direction:column;align-items:center;">
                    <el-image :src="src" style="width:300px;height:300px;"></el-image>
                    <span style="color: #999;letter-spacing:3px;">暂无数据~</span>
                </el-col>
            </el-row>
        </div>
        <!-- 分页 -->
        <div class="page" v-show="areaList.length > 0">
            <el-pagination @size-change="handleSizeChange" @current-change="currentchange"
                :current-page="queryInfo.currentnum" :page-sizes="pageSizes" :page-size="queryInfo.pageSize"
                layout="total, sizes, prev, pager, next, jumper" :total="total">
            </el-pagination>
        </div>
        <!-- 添加区域弹窗 -->
        <el-dialog :title="title" :visible.sync="dialogFormVisible">
            <div class="area-content">
                <el-row>
                    <el-col :span="10" style="border: 1px solid #eee;padding:20px;">
                        <el-row :gutter="20">
                            <el-col :span="24">
                                <el-input placeholder="请输入设备区域名称" v-model="areaName" style="width:200px;"></el-input>
                            </el-col>
                            <el-col :span="16">
                                <el-input placeholder="请输入设备编号" v-model="machineNum" style="width:200px;"></el-input>
                            </el-col>
                            <el-col :span="8">
                                <el-button type="primary" plain icon="el-icon-plus" v-on:keyup.13.native="pushData"
                                    @click="pushData">添加</el-button>
                            </el-col>
                            <el-col :span="24">
                                <el-input placeholder="请输入保全工号" maxlength="5" v-model="uuid"
                                    style="width:200px;"></el-input>
                            </el-col>
                            <el-col :span="12">
                                <el-select v-model="depart" placeholder="请选择部门" style="width:200px;">
                                    <el-option label="织造车间" value="织造车间"></el-option>
                                    <el-option label="整理车间" value="整理车间"></el-option>
                                    <el-option label="检验车间" value="检验车间"></el-option>
                                    <el-option label="打样车间" value="打样车间"></el-option>
                                    <el-option label="综合维修" value="综合维修"></el-option>
                                </el-select>
                            </el-col>
                            <el-col :span="24">
                                <el-button type="primary" style="background-color:#517DF7;" @click="saveInfo">{{ btnText
                                }}</el-button>
                                <el-button v-show="machineList.length > 0" type="warning" @click="clearData"
                                    plain>清空右侧列表</el-button>
                            </el-col>
                        </el-row>
                    </el-col>
                    <el-col :span="4"
                        style="display: flex;flex-direction:column;height:230px;justify-content:center;align-items:center;">
                        <h5 style="font-size:14px;">右侧设备列表</h5>
                        <span class="el-icon-sort"
                            style="font-size:40px; transform: rotate(90deg);margin-top:10px;color:#517DF7"></span>
                    </el-col>
                    <el-col :span="10">
                        <div class="area-list-box">
                            <div class="list-title">
                                <h5>{{ areaName }}</h5>
                            </div>
                            <div class="area-list-item">
                                <el-row :gutter="20" v-if="machineList.length > 0">
                                    <el-col :span="4" v-for="(item, index) in machineList " :key="index">{{ item }}</el-col>
                                </el-row>
                                <el-row v-else>
                                    <el-col :span="24" style="display: flex;flex-direction:column;">
                                        <el-image :src="src" style="width:200px;height:200px;"></el-image>
                                        <span style="color: #999;letter-spacing:3px;">暂无设备信息~</span>
                                    </el-col>
                                </el-row>
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { mapState } from 'vuex'
export default {
    data() {
        return {
            src: '../src/assets/image/data.png',
            checkList: [], // 选中状态
            loading: true, // 加载
            form: {
                region: '',
                depart: '',
                name: ''
            },
            areaName: '', // 区域名称
            machineNum: '', // 机台编号
            uuid: '', // 保全工号
            depart: '', // 部门
            total: 0,
            pageSizes: [8, 10, 12, 14],
            queryInfo: {
                pageNum: 1,
                currentnum: 1,
                pageSize: 8,
            },
            options: [{
                value: '织造车间',
                label: '织造车间'
            }, {
                value: '整理车间',
                label: '整理车间'
            }, {
                value: '打样车间',
                label: '打样车间'
            }, {
                value: '综合维修',
                label: '综合维修'
            }],
            dialogFormVisible: false, // 弹窗开关
            machineList: [], // 添加的横机列表
            areaList: [], // 区域列表
            delList: [], // 删除列表
            currentIndex: 0, // 当前索引
            type: '', // 提交类型
            btnText: '立即保存', // 按钮文字
            title: '添加区域', // 弹窗标题
            editId: 0, // 编辑id
        }
    },
    computed: {
        ...mapState({
            User: state => state.users
        })
    },
    mounted() {
        // 监听键盘的点击事件
        window.addEventListener("keydown", this.keyDown);
    },
    destroyed() {
        window.removeEventListener("keydown", this.keyDown, false);
    },
    created() {
        this.getAreaList()
    },
    methods: {
        //绑定监听事件
        keyDown(e) {
            //如果是按回车则执行登录方法
            if (e.keyCode == 13) {
                this.pushData();
            }
        },
        // 刷新数据
        refresh() {
            this.form.depart = ''
            this.loading = true
            this.getAreaList()
        },
        // 编辑区域
        editArea(data) {
            // 判断用户的权限
            if (this.User.level !== 1) {
                return this.$message.warning('您没有权限操作')
            }
            this.title = '编辑区域'
            this.btnText = '立即修改'
            this.dialogFormVisible = true
            this.areaName = data.area
            this.machineList = data.machine_range
            this.uuid = data.uuid
            this.depart = data.department
            this.editId = data.id
        },
        // 批量删除
        async deleteAll() {
            // 判断用户的权限
            if (this.User.level !== 1) {
                return this.$message.warning('您没有权限操作')
            }
            if (this.delList.length === 0) {
                return this.$message.error('请选择要删除的区域')
            }
            try {
                const res = await this.$http.delAreaLists({
                    areaList: this.delList
                })
                if (res.status === 200) {
                    this.$message.success(res.message)
                    this.getAreaList()
                } else {
                    this.$message.error(res.message)
                }
            } catch (error) {
                return this.$message.error('服务器错误，请稍后重试')
            }
        },
        // 删除单个区域
        async delAreal(id) {
            // 判断用户的权限
            if (this.User.level !== 1) {
                return this.$message.warning('您没有权限操作')
            }
            try {
                const res = await this.$http.delOneArea({
                    aid: id
                })
                if (res.status === 200) {
                    this.$message.success(res.message)
                    this.getAreaList()
                } else {
                    this.$message.error(res.message)
                }
            } catch (error) {
                return this.$message.error('服务器错误，请稍后重试')
            }
        },
        //多选框change事件
        changeData(e, index) {
            const id = e;
            this.currentIndex = index;
            const res = this.delList.findIndex(item => item === id);
            if (res == -1) {
                this.delList.push(id);
            } else {
                this.delList.forEach(item => {
                    if (item === id) {
                        this.delList.splice(index, true)
                    }
                })
            }
        },
        // 获取列表区域
        async getAreaList() {
            try {
                this.queryInfo.keyword = this.form.depart
                const res = await this.$http.getAreaLists(this.queryInfo)
                if (res.status !== 200) {
                    return this.$message.error(res.message)
                }
                const { list } = res.data
                if (list.length > 0) {
                    this.areaList = list
                    this.total = list[0].total
                    // 排序
                    this.areaList.sort((a, b) => {
                        return a.area.localeCompare(b.area, 'zh-CN')
                    })
                    setTimeout(() => {
                        this.loading = false
                    }, 1000)
                } else {
                    this.areaList = []
                    this.total = 0
                    setTimeout(() => {
                        this.loading = false
                    }, 1000)
                }

            } catch (error) {
                this.loading = false
                return this.$message.error(error.message)
            }
        },
        // 保存区域信息到数据库
        async saveInfo() {
            // 判断用户的权限
            if (this.User.level !== 1) {
                return this.$message.warning('您没有权限操作')
            }
            try {
                if (!this.areaName) {
                    return this.$message.warning('请填写区域名称')
                }
                if (!this.uuid) {
                    return this.$message.warning('请填写机修工号')
                }
                if (!this.depart) {
                    return this.$message.warning('请选择部门')
                }
                if (this.machineList.length < 1) {
                    return this.$message.warning('请先添加设备编号')
                }
                const obj = {
                    uuid: this.uuid,
                    area: this.areaName,
                    depart: this.depart,
                    machine_range: this.machineList.join(','), // 数组转字符串
                    eid: this.editId,
                }
                const res = await this.$http.submitInfo(obj)
                // 区域已存在
                if (res.status === 10006) {
                    return this.$message.warning(res.message)
                }
                // 区域不存在
                if (res.status === 10003) {
                    return this.$message.warning(res.message)
                }
                if (res.status === 200) {
                    this.areaName = ''
                    this.uuid = ''
                    this.depart = ''
                    this.machineList = []
                    this.dialogFormVisible = false
                    this.getAreaList()
                    return this.$message.success(res.message)
                } else {
                    return this.$message.warning(res.message);
                }
            } catch (error) {
                return this.$message.error('服务器内部错误')
            }
        },
        // 查询
        onSubmit() {
            if (this.form.depart) {
                this.loading = true
                this.getAreaList()
            } else {
                return false
            }
        },
        handleSizeChange(val) {
            this.loading = true
            this.queryInfo.pageSize = val;
            this.getAreaList()
        },
        currentchange(val) {
            this.loading = true
            this.queryInfo.pageNum = val;
            this.getAreaList()
        },
        // 添加区域
        addLocation() {
            // 判断用户的权限
            if (this.User.level !== 1) {
                return this.$message.warning('您没有权限操作')
            }
            this.dialogFormVisible = true
        },
        // 添加横机到右侧数组
        pushData() {
            if (this.machineNum) {
                this.machineList.push(this.machineNum)
            }
            setTimeout(() => {
                this.machineNum = ''
            }, 1000)
        },
        // 清空列表
        clearData() {
            this.machineList = []
        }
    }
}
</script>
<style>
.no-data-list {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 100px 0px;
    margin: 20px 0;
}
.top-area {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding-right: 20px;
    background-color: #feffff;
    margin-right: 5px;
    padding-top: 5px;
    padding-left: 10px;
}

.el-card__body {
    padding: 15px 0px 0px 20px;
    box-sizing: border-box;
    overflow: hidden;
}

.area-content .el-col {
    margin-bottom: 20px;
}

.area-list {
    padding: 10px 0px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
}

.area-list-box {
    border: 1px solid #eee;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px 10px;
}

.area-list-item {
    margin: 10px 0;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    align-items: center;
    padding: 10px;
}

.area-list-item .el-col {
    padding: 10px;
    background-color: #ECF5FF;
    margin: 3px;
    text-align: center;
    color: #666;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
}

.list-box {
    display: flex;
    flex-wrap: wrap;
    width: 25%;
    margin: 5px 0;
    transition: box-shadow 0.3s ease;
    box-sizing: border-box;
    padding: 3px;
}

.area-box {
    background-color: #feffff;
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-right: 5px;
}

.list-box:hover {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
}

h5 {
    font-size: 16px;
    color: #666;
}

.list-item {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    margin-top: 10px;
    box-sizing: border-box;
    overflow: hidden;
    padding-left: 10px;
}

.list-item .el-col {
    padding: 5px 0;
    font-size: 14px;
    background-color: #ECF5FF;
    margin: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #999;
    cursor: pointer;
    border-radius: 4px;
}
</style>