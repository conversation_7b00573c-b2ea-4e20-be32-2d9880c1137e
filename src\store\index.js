/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-07-24 09:14:24
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-17 08:33:26
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\store\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// index.js
import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex) // 使用Vuex
export default new Vuex.Store({
	state: {
		count: 0,
		users: {},
		token: false,
		// 侧边宽度
		asideWidth: "250px",
		showMessageBox: false,
		message: '',
		messageType: '',
		hasLoginStatus: false
	},
	mutations: {
		initUsers(state, data) {
			//console.log('初始化用户');
			let users = localStorage.getItem("userInfo")
			let token = localStorage.getItem("accessToken")
			if (users) {
				state.users = JSON.parse(users)
				state.hasLoginStatus = true
				localStorage.setItem("isLoggedIn", true)
			}
			if (token) {
				state.token = token
			}
		},
		setUserInfo(state, data) {
			//console.log('设置用户信息', data);
			state.users = data
			state.token = data.token
			// 持久化存储
			localStorage.setItem("userInfo", JSON.stringify(data))
			localStorage.setItem("accessToken", data.token)
		},
		// 展开/缩起侧边
		handleAsideWidth(state) {
			state.asideWidth = state.asideWidth == "250px" ? "64px" : "250px"
		},
		showMessageBox(state, { message, type }) {
			state.showMessageBox = true;
			state.message = message;
			state.messageType = type;
		},
		hideMessageBox(state) {
			state.showMessageBox = false;
			state.message = '';
			state.messageType = '';
		}
	},
	actions: {
		showMessage({ commit }, { message, type }) {
			commit('showMessageBox', { message, type });
			setTimeout(() => {
				commit('hideMessageBox');
			}, 600000000); // 10分钟后自动关闭消息框
		}
	},
	getters: {
		showMessageBox(state) {
			return state.showMessageBox;
		},
		message(state) {
			return state.message;
		},
		messageType(state) {
			return state.messageType;
		}
	},
	modules: {}
})
