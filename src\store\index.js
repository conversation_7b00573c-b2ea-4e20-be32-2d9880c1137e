/*
 * @Author: flyknit <EMAIL>
 * @Date: 2023-12-02 13:54:12
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-05-21 09:32:37
 * @FilePath: \electronic-filed:\gitee\backend-demo\src\store\index.js
 * @Description: 
 * 
 * Copyright (c) 2023 by ${<EMAIL>}, All Rights Reserved. 
 */
// index.js
import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex) // 使用Vuex
export default new Vuex.Store({
	state: {
		count: 0,
		users: {},
		token: false,
		// 侧边宽度
		asideWidth: "230px",
		// socket链接状态
		socketStatus: false,
	},
	mutations: {
		initUsers(state, data) {
			//console.log('初始化用户');
			let users = localStorage.getItem("userInfo")
			let token = localStorage.getItem("accessToken")
			// console.log('token', token);
			console.log("users", JSON.parse(users));
			if(users) {
				state.users = JSON.parse(users)
			}
			if(token) {
				state.token = token
			}
		},
		setUserInfo (state, data) {
			console.log('传进来的', data);
			state.users = data
			state.token = data.token
			// 持久化存储
			localStorage.setItem("userInfo", JSON.stringify(data))
			localStorage.setItem("accessToken", data.token)
		},
		// 展开/缩起侧边
		handleAsideWidth (state) {
			state.asideWidth = state.asideWidth == "230px" ? "64px" : "230px"
		},
		// 修改socket链接状态
		changeSocketStatus(state, data) {
			console.log('修改socket链接状态', data);
			state.socketStatus = data
		},
		// 修改配置信息
		changeSetting(state, data) {
			console.log('修改配置信息', data);
			state.users.config[0].is_show = data.show_link
			state.users.config[0].version = data.version
			state.users.config[0].content = data.content
			state.users.config[0].link_url = data.download_url

			// 持久化存储
			localStorage.setItem("userInfo", JSON.stringify(state.users))

		}
	},
	actions: {},
	modules: {}
})
