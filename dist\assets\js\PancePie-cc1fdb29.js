import{e as s,u as l}from"./useEcharts-57db09d5.js";import{i as r,k as i,o as n,g as c}from"./index-444b28c3.js";const d={__name:"<PERSON><PERSON><PERSON><PERSON>",setup(f){const e=r(),o=r([{name:"今天",value:100},{name:"昨天",value:100},{name:"前天",value:200}]);return i(()=>{let t=s.init(e.value),a={grid:{top:0,bottom:0,left:0,right:0},legend:{orient:"vertical",left:"left"},tooltip:{trigger:"item"},color:["#F56C6C","#409EFF","#67C23A"],series:[{name:"Access From",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},labelLine:{show:!1},data:o.value}]};l(t,a)}),(t,a)=>(n(),c("div",{ref_key:"echartsRef",ref:e,class:"echarts-content"},null,512))}};export{d as default};
