import{e as i,u as f}from"./useEcharts-57db09d5.js";import{i as c,k as d,o as h,g as p}from"./index-444b28c3.js";const m={__name:"PancelLine",setup(b){const l=c();let s={borderColor:"#409eff",color:"#fff",borderWidth:2},n={show:!0,position:"top",lineHeight:20,borderRadius:5,backgroundColor:"rgba(255,255,255,.9)",borderColor:"#ccc",borderWidth:"1",padding:[5,15,4],color:"#000000",fontSize:14},t=[55,65,68,43,88,95],a=[];return t=t.sort(function(e,o){return e-o}),t.forEach((e,o)=>{let r={value:e,itemStyle:s,label:n};o===t.length-1&&(r.itemStyle={borderColor:"#409eff",color:"#409eff",borderWidth:3},r.label={show:!0,position:"top",lineHeight:20,backgroundColor:"#409eff",borderRadius:5,borderColor:"#409eff",borderWidth:"1",padding:[5,15,4],color:"#fff",fontSize:14,fontWeight:"normal"}),a.push(r)}),d(()=>{let e=i.init(l.value),o={grid:{top:"25%",left:"0",right:"0",bottom:"20px"},xAxis:[{type:"category",axisTick:{show:!1},splitLine:{show:!0,lineStyle:{type:"dashed"}},data:["1/1","1/2","1/3","1/4","1/5","1/6"]}],yAxis:[{type:"value",min:0,max:100,splitNumber:3,axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!0,lineStyle:{type:"dashed"}}}],series:[{type:"line",showAllSymbol:!0,symbol:"circle",symbolSize:10,lineStyle:{color:"#409eff",width:5},tooltip:{show:!1},areaStyle:{color:new i.graphic.LinearGradient(0,0,0,1,[{offset:0,color:"rgba(33, 150, 243, 0.6)"},{offset:1,color:"rgba(33, 150, 243, 0.6)"}],!1)},data:a,markLine:{symbol:["none","none"],itemStyle:{lineStyle:{type:"dashed",color:"#ccc",width:2}},data:[{name:"Y 轴值为 80 的水平线",yAxis:80,label:{formatter:"(标准) 80"}}]}}]};f(e,o)}),(e,o)=>(h(),p("div",{ref_key:"echartsRef",ref:l,class:"echarts-content"},null,512))}};export{m as default};
