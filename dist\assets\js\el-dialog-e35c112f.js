import{bk as Q,_ as M,C as N,X as L,c as k,o as m,g as A,m as E,S as i,H as t,n as e,t as X,b as R,w as r,a as I,L as Y,a5 as G,f as S,ad as z,y as W,bA as Z,b0 as _,R as x,i as D,a9 as ee,e as oe,aQ as se,bB as ae,a3 as le,aK as te,aL as ne,U as re}from"./index-444b28c3.js";import{E as ie,u as de}from"./index-eba6e623.js";import{d as ce,a as ue,b as fe,c as me,u as pe}from"./el-overlay-9f4b42b1.js";import{u as ge}from"./index-e305bb62.js";import{F as be,E as ve}from"./focus-trap-6de7266c.js";import{u as Ce}from"./index-11a84590.js";import{u as B}from"./el-button-9bbdfcf9.js";const ye=(...p)=>d=>{p.forEach(l=>{Q(l)?l(d):l.value=d})},K=Symbol("dialogInjectionKey"),he=["aria-label"],ke=["id"],Ee={name:"ElDialogContent"},Re=N({...Ee,props:ce,emits:ue,setup(p){const d=p,{t:l}=ge(),{Close:$}=Z,{dialogRef:c,headerRef:g,bodyId:w,ns:s,style:b}=L(K),{focusTrapRef:C}=L(be),y=ye(C,c),h=k(()=>d.draggable);return Ce(c,g,h),(a,n)=>(m(),A("div",{ref:e(y),class:t([e(s).b(),e(s).is("fullscreen",a.fullscreen),e(s).is("draggable",e(h)),e(s).is("align-center",a.alignCenter),{[e(s).m("center")]:a.center},a.customClass]),style:z(e(b)),tabindex:"-1",onClick:n[1]||(n[1]=W(()=>{},["stop"]))},[E("header",{ref_key:"headerRef",ref:g,class:t(e(s).e("header"))},[i(a.$slots,"header",{},()=>[E("span",{role:"heading",class:t(e(s).e("title"))},X(a.title),3)]),a.showClose?(m(),A("button",{key:0,"aria-label":e(l)("el.dialog.close"),class:t(e(s).e("headerbtn")),type:"button",onClick:n[0]||(n[0]=F=>a.$emit("close"))},[R(e(G),{class:t(e(s).e("close"))},{default:r(()=>[(m(),I(Y(a.closeIcon||e($))))]),_:1},8,["class"])],10,he)):S("v-if",!0)],2),E("div",{id:e(w),class:t(e(s).e("body"))},[i(a.$slots,"default")],10,ke),a.$slots.footer?(m(),A("footer",{key:0,class:t(e(s).e("footer"))},[i(a.$slots,"footer")],2)):S("v-if",!0)],6))}});var $e=M(Re,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/dialog/src/dialog-content.vue"]]);const we=["aria-label","aria-labelledby","aria-describedby"],Ae={name:"ElDialog",inheritAttrs:!1},De=N({...Ae,props:fe,emits:me,setup(p,{expose:d}){const l=p,$=_();B({scope:"el-dialog",from:"the title slot",replacement:"the header slot",version:"3.0.0",ref:"https://element-plus.org/en-US/component/dialog.html#slots"},k(()=>!!$.title)),B({scope:"el-dialog",from:"custom-class",replacement:"class",version:"2.3.0",ref:"https://element-plus.org/en-US/component/dialog.html#attributes",type:"Attribute"},k(()=>!!l.customClass));const c=x("dialog"),g=D(),w=D(),s=D(),{visible:b,titleId:C,bodyId:y,style:h,overlayDialogStyle:a,rendered:n,zIndex:F,afterEnter:O,afterLeave:P,beforeLeave:U,handleClose:T,onModalClick:j,onOpenAutoFocus:q,onCloseAutoFocus:V,onCloseRequested:H}=pe(l,g);ee(K,{dialogRef:g,headerRef:w,bodyId:y,ns:c,rendered:n,style:h});const u=de(j),J=k(()=>l.draggable&&!l.fullscreen);return d({visible:b,dialogContentRef:s}),(o,f)=>(m(),I(ne,{to:"body",disabled:!o.appendToBody},[R(te,{name:"dialog-fade",onAfterEnter:e(O),onAfterLeave:e(P),onBeforeLeave:e(U),persisted:""},{default:r(()=>[oe(R(e(ie),{"custom-mask-event":"",mask:o.modal,"overlay-class":o.modalClass,"z-index":e(F)},{default:r(()=>[E("div",{role:"dialog","aria-modal":"true","aria-label":o.title||void 0,"aria-labelledby":o.title?void 0:e(C),"aria-describedby":e(y),class:t(`${e(c).namespace.value}-overlay-dialog`),style:z(e(a)),onClick:f[0]||(f[0]=(...v)=>e(u).onClick&&e(u).onClick(...v)),onMousedown:f[1]||(f[1]=(...v)=>e(u).onMousedown&&e(u).onMousedown(...v)),onMouseup:f[2]||(f[2]=(...v)=>e(u).onMouseup&&e(u).onMouseup(...v))},[R(e(ve),{loop:"",trapped:e(b),"focus-start-el":"container",onFocusAfterTrapped:e(q),onFocusAfterReleased:e(V),onReleaseRequested:e(H)},{default:r(()=>[e(n)?(m(),I($e,se({key:0,ref_key:"dialogContentRef",ref:s},o.$attrs,{"custom-class":o.customClass,center:o.center,"align-center":o.alignCenter,"close-icon":o.closeIcon,draggable:e(J),fullscreen:o.fullscreen,"show-close":o.showClose,title:o.title,onClose:e(T)}),ae({header:r(()=>[o.$slots.title?i(o.$slots,"title",{key:1}):i(o.$slots,"header",{key:0,close:e(T),titleId:e(C),titleClass:e(c).e("title")})]),default:r(()=>[i(o.$slots,"default")]),_:2},[o.$slots.footer?{name:"footer",fn:r(()=>[i(o.$slots,"footer")])}:void 0]),1040,["custom-class","center","align-center","close-icon","draggable","fullscreen","show-close","title","onClose"])):S("v-if",!0)]),_:3},8,["trapped","onFocusAfterTrapped","onFocusAfterReleased","onReleaseRequested"])],46,we)]),_:3},8,["mask","overlay-class","z-index"]),[[le,e(b)]])]),_:3},8,["onAfterEnter","onAfterLeave","onBeforeLeave"])],8,["disabled"]))}});var Ie=M(De,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/dialog/src/dialog.vue"]]);const ze=re(Ie);export{ze as E,ye as c};
