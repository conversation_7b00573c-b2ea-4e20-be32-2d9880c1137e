<template>
  <div>
    <el-card>
      <!-- 表单，出库单号搜索，备件名称搜索，领用人姓名搜索，部门筛选，日期选择 -->
      <el-form :model="form" inline ref="form" class="forms">
        <el-form-item label="入库单号">
          <el-input
            v-model="form.stock_number"
            placeholder="请输入入库单号"
          ></el-input>
        </el-form-item>
        <el-form-item label="备件名称/编号">
          <el-input
            v-model="form.keywords"
            placeholder="请输入备件名称或备件编号"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="部门">
          <el-select
            placeholder="请选择部门"
            v-model="form.department"
            clearable
            filterable
          >
            <el-option
              v-for="item in departmentList"
              :key="item.id"
              :label="item.depart_name"
              :value="item.depart_name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="日期">
          <el-date-picker
            v-model="form.date"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right"
            value-format="yyyy-MM-dd"
            clearable
          >
          </el-date-picker>
        </el-form-item>
        <!-- 搜索按钮 -->
        <el-form-item>
          <el-button type="primary" @click="searchHandler" icon="el-icon-search"
            >搜索</el-button
          >
        </el-form-item>
        <!-- 重置按钮 -->
        <el-form-item>
          <el-button icon="el-icon-refresh" @click="resetHandler"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <!-- 主体部分，表格，入库单号，备件名称，备件编号，货架编号，库位，入库日期，入库人，入库数量，入库类型，备件分类， -->
      <el-table
        :data="tableData"
        stripe
        style="width: 100%"
        v-loading="loading"
        element-loading-text="Flyknit..."
      >
        <!-- 索引 -->
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column prop="stock_number" label="入库单号" width="200">
        </el-table-column>
        <el-table-column prop="part_name" label="备件名称" width="120">
        </el-table-column>
        <el-table-column prop="unit" label="单位" width="100">
        </el-table-column>
        <el-table-column prop="part_number" label="备件编号" width="120">
        </el-table-column>
        <el-table-column prop="shelf_name" label="货架编号" width="120">
        </el-table-column>
        <el-table-column prop="storage_number" label="库位" width="100">
        </el-table-column>
        <el-table-column prop="quantity" label="入库数量" width="100">
        </el-table-column>
        <el-table-column prop="amount" label="金额" width="150">
        </el-table-column>
        <el-table-column prop="currency" label="货币类型" width="100">
        </el-table-column>
        <el-table-column prop="stock_specific" label="入库类型" width="100">
        </el-table-column>
        <el-table-column prop="category" label="备件分类" width="100">
        </el-table-column>
        <el-table-column prop="department" label="所属部门" width="100">
        </el-table-column>
        <el-table-column prop="create_time" label="入库日期" width="200">
        </el-table-column>
        <el-table-column prop="operator" label="操作员" width="120">
        </el-table-column>
        <!-- 操作 -->
        <el-table-column label="操作" width="150">
          <template slot-scope="scope">
            <!-- <el-button
              type="primary"
              size="mini"
              icon="el-icon-edit"
              @click="handleEdit(scope.$index, scope.row)"
              >编辑</el-button
            > -->
            <el-button
              type="text"
              size="mini"
              icon="el-icon-delete"
              style="color: #f56c6c"
              @click="handleDelete(scope.$index, scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </el-card>
  </div>
</template>
<script>
export default {
  data() {
    return {
      loading: true,
      form: {
        stock_number: "",
        keywords: "",
        department: "",
        date: "",
      },
      tableData: [], // 表格数据
      currentPage: 1,
      page: 1,
      pageSize: 10,
      total: 0,
      departmentList: [], // 部门列表
    };
  },
  mounted() {
    this.getDepartList();
    this.getStockRecords();
  },
  methods: {
    // 搜索
    searchHandler() {
      if (
        this.form.stock_number ||
        this.form.keywords ||
        this.form.department ||
        this.form.date
      ) {
        this.loading = true;
        this.getStockRecords();
      } else {
        return false;
      }
    },
    // 重置
    resetHandler() {
      this.form = {
        stock_number: "",
        parts_name: "",
        department: "",
        date: "",
      };
      this.loading = true;
      this.getStockRecords();
    },
    // 获取入库记录
    async getStockRecords() {
      try {
        const res = await this.$http.getPartsInList({
          page: this.page,
          pageSize: this.pageSize,
          stock_number: this.form.stock_number,
          keywords: this.form.keywords,
          department: this.form.department,
          start_date: this.form.date[0],
          end_date: this.form.date[1],
        });
        if (res.status !== 200) {
          return this.$message.error(res.message);
        } else {
          this.tableData = res.data.list;
          this.total = res.data.total;
          setTimeout(() => {
            this.loading = false;
          }, 500);
        }
      } catch (error) {
        this.loading = false;
        return this.$message.error(error);
      }
    },
    // 获取各部门列表
    async getDepartList() {
      try {
        const res = await this.$http.getDepartmentList();
        if (res.status !== 200) {
          return this.$message.error(res.message);
        } else {
          this.departmentList = res.data;
        }
      } catch (error) {
        return this.$message.error(error);
      }
    },
    // 分页
    handleCurrentChange(val) {
      this.page = val;
      this.loading = true;
      this.getStockRecords();
    },
    // 分页
    handleSizeChange(val) {
      this.pageSize = val;
      this.loading = true;
      this.getStockRecords();
    },
    // 编辑
    // handleEdit(index, row) {
    //   console.log(index, row);
    // },
    // 删除
    handleDelete(index, row) {
      this.$confirm("此操作将永久删除该入库单, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.deleteStockIn(row.stock_number, row.part_number, row.quantity);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 删除入库单
    async deleteStockIn(stock_number, part_number, quantity) {
      try {
        const res = await this.$http.deleteStockIn({
          stock_number,
          part_number,
          quantity,
        });
        if (res.status !== 200) {
          return this.$message.error(res.message);
        } else {
          this.$message.success("删除成功");
          this.getStockRecords();
        }
      } catch (error) {
        return this.$message.error(error);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.el-card__body {
  padding: 20px 20px 0 20px;
}

.forms {
  display: flex;
  flex-wrap: wrap;
}
.el-pagination {
  padding: 20px 0;
}
</style>
