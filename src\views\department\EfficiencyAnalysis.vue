<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-07-26 08:27:05
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-04 10:47:22
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\department\EfficiencyAnalysis.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-card>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form :inline="true" class="demo-form-inline">
                        <el-form-item label="车间部门">
                            <el-select v-model="form.department">
                                <el-option label="织造车间" value="织造车间"></el-option>
                                <el-option label="整理车间" value="整理车间"></el-option>
                                <el-option label="打样车间" value="打样车间"></el-option>
                                <el-option label="综合维修" value="综合维修"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="el-icon-search" @click="searchHandler">查询</el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="button" icon="el-icon-refresh" @click="resetHandler">重置</el-button>
                        </el-form-item>
                        <el-form-item class="float-btn">
                            <el-button type="primary" plain icon="el-icon-refresh-right" @click="refresh"></el-button>
                        </el-form-item>
                    </el-form>
                </el-col>
            </el-row>
        </el-card>
        <!-- 主体部分 -->
        <div class="user-efficient-box">
            <el-row :gutter="20" class="user-efficient-content" v-loading="loading" element-loading-text="Flyknit" v-if="efficientData.length > 0">
                <el-col class="user_box-list" v-for="(item, index) in efficientData" :key="index" :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
                    <el-row :gutter="10">
                        <el-col :span="8" style="display: flex;flex-direction:column;">
                            <div class="image-back">
                                <!-- <el-image style="width: 60px; height: 60px;border-radius:50%;" lazy :src="item.avatar" fit="fill">
                                    <div slot="error" class="image-slot">
                                        <i class="el-icon-picture-outline"></i>
                                    </div>
                                </el-image> -->
                                <img :src="item.avatar" :alt="item.username" style="width: 60px; height: 60px;border-radius:50%;">
                            </div>
                            <div class="name-text">【{{ item.username }}】</div>
                        </el-col>
                        <el-col :span="16" style="display: flex;justify-content:space-around;">
                            <div>
                                <div>
                                    <el-progress type="circle" :width="90" :stroke-width="8"
                                        :percentage="item.monthEfficiency"></el-progress>
                                </div>
                                <div style="font-size: 14px;color:#999;">月度工作效率</div>
                            </div>
                            <div>
                                <h1>{{ item.monthWork }}</h1>
                                <div style="font-size: 14px;color:#999;margin-top:20px;">月工单数</div>
                            </div>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :span="8">
                            <div class="left-text">昨日单数：</div>
                        </el-col>
                        <el-col :span="16">
                            <div class="right-text">{{ item.yesterdayWork }}</div>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :span="8">
                            <div class="left-text">今日单数：</div>
                        </el-col>
                        <el-col :span="16">
                            <div class="right-text">{{ item.todayWork }}</div>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :span="8">
                            <div class="left-text">昨日工效：</div>
                        </el-col>
                        <el-col :span="16">
                            <div style="width:100%;padding:0 10px 0 0;">
                                <el-progress :stroke-width="8"
                                    :percentage="Number((item.yesterdayEfficiency * 100).toFixed(0))"></el-progress>
                            </div>
                        </el-col>
                    </el-row>
                    <el-row :gutter="10">
                        <el-col :span="8">
                            <div class="left-text">今日工效：</div>
                        </el-col>
                        <el-col :span="16">
                            <div style="width:100%;padding:0 10px 0 0;">
                                <el-progress :stroke-width="8"
                                    :percentage="Number((item.todayEfficiency * 100).toFixed(0))"></el-progress>
                            </div>
                        </el-col>
                    </el-row>
                </el-col>
            </el-row>
            <el-row :gutter="20" class="nodata-content" v-loading="loading" v-else>
                <el-col :span="24" style="text-align: center;">
                    <el-image :src="url" style="width: 300px;height:300px;"></el-image>
                    <h5 style="color: #DAE2EF;letter-spacing:3px;">暂无任何数据~</h5>
                </el-col>
            </el-row>
        </div>
        <!-- 分页 -->
        <div class="pagination-box" v-show="total > 0">
            <div class="pagination">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="currentPage" :page-sizes="pageSizes" :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper" :total="total">
                </el-pagination>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            loading: true,
            form: {
                department: '织造车间'
            },
            currentPage: 1,
            pageSizes: [8, 12, 14, 16],
            page: 1,
            pageSize: 8,
            total: 0,
            url: '../src/assets/image/data.png',
            efficientData: [], // 工作效率数据
        }
    },
    mounted() {
        this.getEfficientData()
    },
    methods: {
        // 获取工作效率数据
        async getEfficientData() {
           try {
                this.form.page = this.currentPage
                this.form.pageSize = this.pageSize
                const res = await this.$http.efficientData(this.form)
                if(res.status !== 200) {
                    return this.$message.error(res.message)
                }
                const {list} = res.data
                this.total = res.data.total
                this.efficientData = list
                setTimeout(() => {
                    this.loading = false
                }, 1000);
                return false
           } catch (error) {
                this.loading = false
                return this.$message.error('服务器错误，请稍后重试')
           }
        },
        searchHandler() {
            this.loading = true
            this.getEfficientData()
        },
        resetHandler() {
            this.form.department = ''
        },
        refresh() {
            this.loading = true
            this.getEfficientData()
        },
        handleSizeChange(val) {
            this.loading = true
            this.pageSize = val
            this.getEfficientData()
        },
        handleCurrentChange(val) {
            this.loading = true
            this.currentPage = val
            this.getEfficientData()
        }
    }
}
</script>
<style lang="scss">
.nodata-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-top: 100px;
}
.el-image {
    border-radius: 50%;
}
.image-back {
    background-color:#ECF5FF;border-radius:50%;padding:10px;
}

.el-card__body {
    padding: 20px 0 0 20px;
}
.user-efficient-content {
    display: flex;flex-wrap:wrap;justify-content:flex-start;
}
.user-efficient-box {
    background-color: #F1F5F9;
    padding: 5px 10px 0 10px;
}

.user_box-list {
    padding: 0 1px 10px 0;
    border-radius: 10px;
    cursor: pointer;
    background-color: #feffff;
    margin: 5px;
    flex: auto;
    flex-shrink: 0;

    .justify {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .el-row {
        margin-top: 10px;

        .el-col {
            display: flex;
            padding: 10px;
            justify-content: center;
            align-items: center;

            .name-text {
                font-size: 14px;
                font-weight: bold;
                padding: 10px 0;
                color: #666;
                letter-spacing: 2px;
            }

            .left-text {
                color: #999;
                font-size: 14px;
            }

            .right-text {
                color: #517DF7;
                font-weight: bold;
            }
        }
    }

    .pagination-box {
        padding-top: 20px;
    }

}

.user_box-list:hover {
    box-shadow: 0 0 10px #ccc;
}

.demo-form-inline {
    .el-form-item {
        margin-left: 10px;
    }
}

.float-btn {
    float: right;
    padding-right: 20px;
}</style>