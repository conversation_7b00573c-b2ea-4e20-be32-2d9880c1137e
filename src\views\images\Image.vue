<template>
  <div class="ordering contents" v-loading="loading" element-loading-text="Flyknit...">
    <el-form inline size="medium" style="margin: 1rem 0 0 0">
      <el-form-item label="model名称">
        <el-input
          v-model="form.model_name"
          placeholder="请输入model名"
          clearable
          @clear="clearHandler"
        ></el-input>
      </el-form-item>
      <!-- 搜索按钮 -->
      <el-form-item>
        <el-button type="success" size="medium" @click="searchModel">
          <i class="el-icon-search"></i>搜索
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="openModel" size="medium">
          <i class="el-icon-upload"></i> 上传新Model
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button
          type="danger"
          v-if="delList.length > 0"
          @click="delImage"
          size="medium"
        >
          <i class="el-icon-delete"></i>立即删除
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 照片列表 -->
    <div class="image-list" id="demo" v-if="imgList.length > 0">
      <div class="image-box" v-for="(item, index) in imgList" :key="index">
        <div
          class="imgs"
          style="display: flex; flex-direction: column; padding: 1rem"
        >
          <div class="top-box">
            <el-checkbox-group
              v-model="checkList"
              @change="changeData"
            >
              <el-checkbox :label="item.id" :value="item.id"></el-checkbox>
            </el-checkbox-group>
            <span class="demonstration">{{ item.model_name }}</span>
          </div>
          <el-image
            class="model-image"
            :src="item.img_url"
            fit="fill"
            @click="handlepreview(item)"
            lazy
          >
          </el-image>
          <div class="model-info__box">
            <span class="user-box">品名:{{ item.pm }}</span>
            <span class="time-text">时间:{{ item.create_time }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 没图片 -->
    <div class="no-data" v-else>
      <el-empty description="暂无任何model数据"></el-empty>
    </div>

    <!-- 大图展开 --> 
    <el-dialog :visible.sync="dialogVisible" width="35%" :title="title" :before-close="closeHandler">
      <div style="box-sizing: border-box; overflow: hiddend;display: flex;justify-content: center;align-items: center;">
        <el-image :src="dialogImageUrl" :fit="fit" id="rotateImage"></el-image>
      </div>
      <div style="display: flex;justify-content: center;align-items: center;padding: 1rem 0;">
        <el-button
          class="el-icon-refresh"
          @click="chongzhi('rotateImage')"
        ></el-button>
        <el-button
          class="el-icon-refresh-right"
          @click="xuanzhuan('rotateImage')"
        ></el-button>
        <el-button
          class="el-icon-right"
          @click="zuoyou('rotateImage')"
        ></el-button>
        <el-button
          class="el-icon-top"
          @click="shangxia('rotateImage')"
        ></el-button>
      </div>
    </el-dialog>
    <!-- 分页 -->
    <div class="page" v-show="total > 0">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="currentchange"
        :current-page="queryInfo.currentnum"
        :page-sizes="pageSizes"
        :page-size="queryInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 添加新model -->
    <el-dialog title="添加新Model图片" :visible.sync="showContent" width="40%">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="Model名称" prop="model_name">
          <el-input
            v-model="form.model_name"
            placeholder="请输入model名"
          ></el-input>
        </el-form-item>
        <el-form-item label="Model品名" prop="pm">
          <el-select v-model="form.pm" placeholder="请选择品名">
            <el-option
              v-for="(item, index) in modelList"
              :key="index"
              :label="item.name"
              :value="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- model照片 -->
        <el-form-item label="Model图片">
          <el-upload
            :action="action"
            list-type="picture-card"
            name="file"
            accept=".jpg,.png,.webp,.jfif"
            :on-remove="logoRemove"
            :on-success="logoSuccess"
            :multiple="false"
            :on-error="onErr"
            :before-upload="project"
            :file-list="logo"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>
        <!-- 大图展开 -->
        <el-dialog :modal="false" :visible.sync="dialogVisible">
          <img width="100%" :src="dialogImageUrl" alt="" />
        </el-dialog>
        <el-form-item>
          <el-button type="primary" size="medium" @click="submitForm('form')"
            >立即添加</el-button
          >
          <el-button size="medium" @click="closeBox">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import config from "@/config/config";;
import { mapState } from "vuex";
export default {
  data() {
    return {
      fit: "cover",
      modelList: [
        {
          id: 1,
          name: "UPPER-L",
        },
        {
          id: 2,
          name: "UPPER-R",
        },
        {
          id: 3,
          name: "HEEL",
        },
        {
          id: 4,
          name: "HEEL-L",
        },
        {
          id: 5,
          name: "HEEL-R",
        },
        {
          id: 6,
          name: "TONGUE",
        },
        {
          id: 7,
          name: "PAIR",
        },
        {
          id: 8,
          name: "UPPER",
        },
        {
          id: 9,
          name: "MED-L",
        },
        {
          id: 10,
          name: "MED-R",
        },
        {
          id: 11,
          name: "RAND-L",
        },
        {
          id: 12,
          name: "RAND-R",
        },
        {
          id: 13,
          name: "STRAP-R",
        },
        {
          id: 14,
          name: "STRAP-L",
        },
        {
          id: 15,
          name: "CUFF-L",
        },
        {
          id: 16,
          name: "CUFF-R",
        },
        {
          id: 17,
          name: "VAMP-R",
        },
        {
          id: 18,
          name: "VAMP-R",
        },
        {
          id: 19,
          name: "LAT-R",
        },
        {
          id: 20,
          name: "LAT-L",
        },
      ], // 品名列表
      checkList: [],
      delList: [],
      action: config.uploadURL, // 上传图片的地址
      showContent: false,
      loading: true,
      dialogVisible: false,
      dialogImageUrl: "",
      // 上传logoing
      loadmen: false,
      headers: {
        Authorization: `Bearer ${localStorage.getItem("accessToken")}`,
      },
      imgList: [],
      logo: [],
      form: {
        model_name: "",
        pm: "",
      },
      cover: "cover",
      styleName: "",
      show: false,
      webUrl: config.webUrl,
      title: "",
      xuanzhuanNumCar: 0,
      rules: {
        model_name: [
          { required: true, message: "请输入model名", trigger: "blur" },
        ],
        pm: [{ required: true, message: "请选择品名", trigger: "blur" }],
      },
      queryInfo: {
        pageNum: 1,
        pageSize: 10,
        currentnum: 1,
      },
      pageSizes: [10, 12, 14],
      total: 0,
    };
  },
  computed: {
    ...mapState({
      User: state => state.users,
    }),
  },
  created() {
    // //获取图片列表
    this.getImageList();
  },
  methods: {
    //  清空搜索框
    clearHandler() {
      this.form.model_name = "";
      this.getImageList();
      this.loading = true;
    },
    // 搜索model
    searchModel() {
      this.getImageList();
    },
    // 分页
    handleSizeChange(val) {
      this.loading = true;
      this.queryInfo.pageSize = val;
      this.getImageList();
    },
    // 分页
    currentchange(val) {
      this.loading = true;
      this.queryInfo.pageNum = val;
      this.getImageList();
    },
    // 获取图片列表
    async getImageList() {
      try {
        this.queryInfo.model_name = this.form.model_name;
        const res = await this.$http.getModelImage(this.queryInfo);
        if (res.code !== 200) {
          return this.$notify.error({
            title: "错误",
            message: res.message,
            type: "error",
          });
        }
        const { result, total } = res.data.list;
        // console.log("result", result, "total", total);
        this.imgList = result;
        this.total = total;
        setTimeout(() => {
          this.loading = false;
        }, 500);
      } catch (error) {
        this.loading = false;
        return this.$notify.error({
          title: "错误",
          message: "服务器发生错误,请重试",
          type: "error",
        });
      }
    },
    //添加pm展开隐藏
    showHandle() {
      this.show = !this.show;
    },
    // 展开大图
    handlepreview(file) {
      this.dialogImageUrl = file.img_url;
      this.title = file.model_name;
      this.dialogVisible = true;
    },
    //翻转图片
    chongzhi(elename) {
      this.xuanzhuanNumCar = this.xuanzhuanNumCar + 1;
      var box = document.getElementById(elename);
      box.style.transform = "rotateZ(" + 0 + "deg)";
      box.style.transform = "rotateX(" + 0 + "deg)";
      box.style.transform = "rotateY(" + 0 + "deg)";
    },
    xuanzhuan(elename) {
      this.xuanzhuanNumCar = this.xuanzhuanNumCar + 1;
      var box = document.getElementById(elename);
      box.style.transform = "rotateZ(" + 90 * this.xuanzhuanNumCar + "deg)";
    },
    zuoyou(elename) {
      this.xuanzhuanNumCar = this.xuanzhuanNumCar + 1;
      var box = document.getElementById(elename);
      box.style.transform = "rotateY(" + 180 * this.xuanzhuanNumCar + "deg)";
    },
    shangxia(elename) {
      this.xuanzhuanNumCar = this.xuanzhuanNumCar + 1;
      var box = document.getElementById(elename);
      box.style.transform = "rotateX(" + 180 * this.xuanzhuanNumCar + "deg)";
    },
    handleview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    //打开添加页面
    openModel() {
      this.showContent = true;
    },
    // 上传失败
    onErr(e) {
      this.loadmen = false;
      this.$message.error("上传失败,尝试重新上传");
    },
    // 上传时
    project(file) {
      this.loadmen = true;
    },
    // logo移除文件时的钩子
    logoRemove(file, fileList) {
      this.logo = [];
    },
    // 上传成功：logo
    logoSuccess(res, file, fileList) {
      //   console.log("res", res);
      const { list } = res.data;
      this.logo.push({ url: list, uid: file.uid }); //element展示图片时需要数组类型的才能展示
      this.loadmen = false;
    },
    //关闭弹窗
    closeBox() {
      this.showContent = false;
    },
    //多选框change事件
    changeData(e) {
      // console.log('e', e);
      // const id = e;
      // const res = this.delList.findIndex((item) => item === id);
      // if (res == -1) {
      //   this.delList.push(id);
      // } else {
      //   this.delList.forEach((item) => {
      //     if (item === id) {
      //       this.delList.splice(index, true);
      //     }
      //   });
      // }
      // console.log('e', this.delList);
      // 选中和取消选中
      this.delList = e;
    },
    //删除图片
    delImage() {
      this.$confirm("确定删除所选图片吗?此操作不可恢复...", "系统提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          //调用删除
          this.delSelectImage();
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "取消删除",
          });
        });
    },
    //封装删除逻辑
      async delSelectImage() {
        try {
            const res = await this.$http.deleteModelImage({
            ids: this.delList,
            });
            if (res.code !== 200) {
            return this.$notify.error({
                title: "错误",
                message: res.message,
                type: "error",
            });
            }
            this.$notify.success({
            title: "成功",
            message: res.message,
            type: "success",
            });
            this.delList = [];
            this.getImageList();
        } catch (error) {
            return this.$notify.error({
            title: "错误",
            message: "服务器发生错误,请重试",
            type: "error",
            });
        }
    },
    // 提交表单事件
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.addModel();
        } else {
          return false;
        }
      });
    },
    //添加model
    addModel() {
      let imgs = "";
      this.logo.forEach((item) => {
        imgs = item.url;
      });
      this.form.image_url = imgs;
      //调用封装添加图片接口
      this.addModels();
    },
    //添加图片信息
    async addModels() {
      try {
        const res = await this.$http.addModelImage(this.form);
        if (res.code !== 200) {
          return this.$notify.error({
            title: "错误",
            message: res.message,
            type: "error",
          });
        }
        this.showContent = false;
        this.form = {
          model_name: "",
          pm: "",
        };
        this.$notify.success({
          title: "成功",
          message: res.message,
          type: "success",
        });
        this.getImageList();
        this.logo = [];
      } catch (error) {
        return this.$notify.error({
          title: "错误",
          message: "服务器发生错误,请重试",
          type: "error",
        });
      }
    },
    // 关闭弹窗
    closeHandler(done) {
      this.dialogVisible = false;
      this.form = {
        model_name: "",
        pm: "",
      };
      done();
    },
  },
};
</script>
<style scoped>
.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 30px;
  background-color: #feffff;
  margin: 20px 0;
}
.add-model {
  position: absolute;
  right: 0;
  top: 0px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  right: 12%;
  width: 280px;
}
.pm-box {
  display: flex;
  font-size: 12px;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  color: #409eff;
}
.contents {
  /* position: absolute;
  right: 30px;
  box-sizing: border-box; */
}
.top-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.box-contents {
  padding: 20px;
  width: 700px;
  height: 420px;
  background-color: #feffff;
  box-sizing: border-box;
  overflow: hidden;
}
.image-view-title {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}
.image-view-title img {
  object-fit: cover;
}
.upload-box {
  flex: 1;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}
.user-box {
  font-size: 12px;
  color: #666;
}
.image-list {
  width: 100%;
  padding: 1rem;
  margin: 10px 0;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  box-sizing: border-box;
  background-color: #feffff;
}
.image-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: calc(100% / 4);
  border: 1px solid #eeeeee;
  box-sizing: border-box;
  border-radius: 10px;
  height: 100%;
}

.el-image {
  cursor: pointer;
}
.model-image {
  width: 100%;
  height: 100%;
  flex-shrink: 0;
  border-radius: 10px;
}
.demonstration {
  padding: 10px;
  flex: 1;
  font-size: 14px;
  color: #000;
  font-weight: bold;
}
.time-text {
  font-size: 12px;
  color: #666;
}
.model-info__box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
}
.image-box:hover {
  box-shadow: 0 16px 32px 0 rgba(48, 55, 66, 0.15); /* 鼠标悬浮时盒子出现的阴影 */
  transform: translate(0, -10px); /* 鼠标悬浮时盒子上移10px */
  border-radius: 10px;
  background-color: #feffff;
}
</style>
