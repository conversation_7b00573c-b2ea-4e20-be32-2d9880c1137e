import{bw as N,bx as A,n as B,i as V,a8 as w,c as Q,C as F,by as D,k as P,o as G,g as j,t as z,ad as W}from"./index-444b28c3.js";import{_ as X}from"./_plugin-vue_export-helper-c27b6911.js";function q(e){return N()?(A(e),!0):!1}function f(e){return typeof e=="function"?e():B(e)}typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;function M(e,n=!1,a="Timeout"){return new Promise((u,s)=>{setTimeout(n?()=>s(a):u,e)})}function $(e){return e}const R={easeInSine:[.12,0,.39,0],easeOutSine:[.61,1,.88,1],easeInOutSine:[.37,0,.63,1],easeInQuad:[.11,0,.5,0],easeOutQuad:[.5,1,.89,1],easeInOutQuad:[.45,0,.55,1],easeInCubic:[.32,0,.67,0],easeOutCubic:[.33,1,.68,1],easeInOutCubic:[.65,0,.35,1],easeInQuart:[.5,0,.75,0],easeOutQuart:[.25,1,.5,1],easeInOutQuart:[.76,0,.24,1],easeInQuint:[.64,0,.78,0],easeOutQuint:[.22,1,.36,1],easeInOutQuint:[.83,0,.17,1],easeInExpo:[.7,0,.84,0],easeOutExpo:[.16,1,.3,1],easeInOutExpo:[.87,0,.13,1],easeInCirc:[.55,0,1,.45],easeOutCirc:[0,.55,.45,1],easeInOutCirc:[.85,0,.15,1],easeInBack:[.36,0,.66,-.56],easeOutBack:[.34,1.56,.64,1],easeInOutBack:[.68,-.6,.32,1.6]},H=Object.assign({},{linear:$},R);function J([e,n,a,u]){const s=(t,r)=>1-3*r+3*t,i=(t,r)=>3*r-6*t,l=t=>3*t,d=(t,r,c)=>((s(r,c)*t+i(r,c))*t+l(r))*t,y=(t,r,c)=>3*s(r,c)*t*t+2*i(r,c)*t+l(r),m=t=>{let r=t;for(let c=0;c<4;++c){const o=y(r,e,a);if(o===0)return r;const p=d(r,e,a)-t;r-=p/o}return r};return t=>e===n&&a===u?t:d(m(t),n,u)}function C(e,n,a){return e+a*(n-e)}function _(e){return(typeof e=="number"?[e]:e)||[]}function K(e,n,a,u={}){var s,i;const l=f(n),d=f(a),y=_(l),m=_(d),t=(s=f(u.duration))!=null?s:1e3,r=Date.now(),c=Date.now()+t,o=typeof u.transition=="function"?u.transition:(i=f(u.transition))!=null?i:$,p=typeof o=="function"?o:J(o);return new Promise(O=>{e.value=l;const S=()=>{var b;if((b=u.abort)!=null&&b.call(u)){O();return}const I=Date.now(),v=p((I-r)/t),x=_(e.value).map((E,g)=>C(y[g],m[g],v));Array.isArray(e.value)?e.value=x.map((E,g)=>{var h,T;return C((h=y[g])!=null?h:0,(T=m[g])!=null?T:0,v)}):typeof e.value=="number"&&(e.value=x[0]),I<c?requestAnimationFrame(S):(e.value=d,O())};S()})}function k(e,n={}){let a=0;const u=()=>{const i=f(e);return typeof i=="number"?i:i.map(f)},s=V(u());return w(u,async i=>{var l,d;if(f(n.disabled))return;const y=++a;if(n.delay&&await M(f(n.delay)),y!==a)return;const m=Array.isArray(i)?i.map(f):f(i);(l=n.onStarted)==null||l.call(n),await K(s,s.value,m,{...n,abort:()=>{var t;return y!==a||((t=n.abort)==null?void 0:t.call(n))}}),(d=n.onFinished)==null||d.call(n)},{deep:!0}),w(()=>f(n.disabled),i=>{i&&(a++,s.value=u())}),q(()=>{a++}),Q(()=>f(n.disabled)?u():s.value)}function L(e,n){return toString.call(e)===`[object ${n}]`}function U(e){return L(e,"Number")}const Y={startVal:{type:Number,default:0},endVal:{type:Number,default:99},duration:{type:Number,default:1e3},autoplay:{type:Boolean,default:!0},cutting:{type:Boolean,default:!1},decimals:{type:Number,default:0,validator(e){return e>=0}},prefix:{type:String,default:""},suffix:{type:String,default:""},separator:{type:String,default:","},decimal:{type:String,default:"."},color:{type:String},useEasing:{type:Boolean,default:!0},transition:{type:String,default:"linear"}},Z=F({props:Y,emits:["onStarted","onFinished"],setup(e,{emit:n}){const a=V(e.startVal),u=V(!1);let s=k(a);const i=Q(()=>m(B(s)));D(()=>{a.value=e.startVal}),w([()=>e.startVal,()=>e.endVal],()=>{e.autoplay&&l()}),P(()=>{e.autoplay&&l()});function l(){y(),a.value=e.endVal}function d(){a.value=e.startVal,y()}function y(){s=k(a,{disabled:u,duration:e.duration,onFinished:()=>n("onFinished"),onStarted:()=>n("onStarted"),...e.useEasing?{transition:H[e.transition]}:{}})}function m(t){if(!t&&t!==0)return"";const{decimals:r,decimal:c,separator:o,suffix:p,prefix:O}=e;t=Number(t).toFixed(r),t+="";const S=t.split(".");let b=S[0];const I=S.length>1?c+S[1]:"",v=/(\d+)(\d{3})/;if(e.cutting){if(o&&!U(o))for(;v.test(b);)b=b.replace(v,"$1"+o+"$2")}else if(o&&!o)for(;v.test(b);)b=b.replace(v,"$1"+o+"$2");return O+b+I+p}return{value:i,start:l,reset:d}}});function ee(e,n,a,u,s,i){return G(),j("span",{style:W({color:e.color})},z(e.value),5)}const ae=X(Z,[["render",ee]]);export{ae as C};
