import{cB as r,cI as o,cJ as c,cK as h}from"./index-444b28c3.js";const l=""+new URL("../svg/nike-logo-61c44aaa.svg",import.meta.url).href,p=""+new URL("../svg/nike-logo-white-052f6464.svg",import.meta.url).href,b=r({id:"TabsState",state:()=>({tabsMenuList:[]}),getters:{},actions:{async addTabs(s){o.includes(s.path)||this.tabsMenuList.every(t=>t.path!==s.path)&&this.tabsMenuList.push(s)},async removeTabs(s,t=!0){const a=this.tabsMenuList;t&&a.forEach((e,i)=>{if(e.path!==s)return;const n=a[i+1]||a[i-1];n&&c.push(n.path)}),this.tabsMenuList=a.filter(e=>e.path!==s)},async closeMultipleTab(s){this.tabsMenuList=this.tabsMenuList.filter(t=>t.path===s||!t.close)},async setTabs(s){this.tabsMenuList=s},async setTabsTitle(s){const t=location.hash.substring(1);this.tabsMenuList.forEach(a=>{a.path==t&&(a.title=s)})}},persist:h("TabsState")});export{b as T,l as _,p as a};
