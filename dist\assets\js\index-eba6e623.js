import{I as v,R as f,cs as b,av as h,bg as i,a8 as C,cb as w,c9 as g,bx as k,ca as M,bO as u,Q as x,aw as m,C as S,b as E,S as y,D as O}from"./index-444b28c3.js";import{t as P}from"./event-fe80fd0c.js";import{a as T}from"./scroll-a66dde9b.js";import{P as d}from"./vnode-b9ec7db4.js";const W=e=>{v(e)||P("[useLockscreen]","You need to pass a ref param to this function");const s=f("popup"),t=b(()=>s.bm("parent","hidden"));if(!h||i(document.body,t.value))return;let n=0,a=!1,l="0";const o=()=>{setTimeout(()=>{M(document.body,t.value),a&&(document.body.style.width=l)},200)};C(e,c=>{if(!c){o();return}a=!i(document.body,t.value),a&&(l=document.body.style.width),n=T(s.namespace.value);const r=document.documentElement.clientHeight<document.body.scrollHeight,p=w(document.body,"overflowY");n>0&&(r||p==="scroll")&&a&&(document.body.style.width=`calc(100% - ${n}px)`),g(document.body,t.value)}),k(()=>o())},I=e=>{if(!e)return{onClick:u,onMousedown:u,onMouseup:u};let s=!1,t=!1;return{onClick:o=>{s&&t&&e(o),s=t=!1},onMousedown:o=>{s=o.target===o.currentTarget},onMouseup:o=>{t=o.target===o.currentTarget}}},z=x({mask:{type:Boolean,default:!0},customMaskEvent:{type:Boolean,default:!1},overlayClass:{type:m([String,Array,Object])},zIndex:{type:m([String,Number])}}),B={click:e=>e instanceof MouseEvent};var H=S({name:"ElOverlay",props:z,emits:B,setup(e,{slots:s,emit:t}){const n=f("overlay"),a=r=>{t("click",r)},{onClick:l,onMousedown:o,onMouseup:c}=I(e.customMaskEvent?void 0:a);return()=>e.mask?E("div",{class:[n.b(),e.overlayClass],style:{zIndex:e.zIndex},onClick:l,onMousedown:o,onMouseup:c},[y(s,"default")],d.STYLE|d.CLASS|d.PROPS,["onClick","onMouseup","onMousedown"]):O("div",{class:e.overlayClass,style:{zIndex:e.zIndex,position:"fixed",top:"0px",right:"0px",bottom:"0px",left:"0px"}},[y(s,"default")])}});const A=H;export{A as E,W as a,I as u};
