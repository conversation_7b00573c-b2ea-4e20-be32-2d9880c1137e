<template>
  <div>
    <!-- 卡片式图 -->
    <el-card class="card-list">
      <!-- <el-form
        :inline="true"
        :model="form"
        class="demo-form-inline"
        size="medium"
      >
        <el-form-item>
          <el-button type="primary" icon="el-icon-plus" @click="addUser"
            >制定新路线</el-button
          >
        </el-form-item>
      </el-form> -->
      <!-- 订单列表区域 -->
      <el-table
        :data="wayList"
        stripe
        style="width: 100%"
        v-loading="loading"
        element-loading-text="加载中..."
      >
        <el-table-column label="#" type="index">
          <el-checkbox v-model="checked"></el-checkbox>
        </el-table-column>
        <el-table-column label="请假人" prop="nickname"></el-table-column>
        <el-table-column label="请假原因" width="200">
          <template slot-scope="scope">
            <div class="text-ellipsis">{{ scope.row.reason }}</div>
          </template>
        </el-table-column>
        <el-table-column label="开始时间" prop="start_time"></el-table-column>
        <el-table-column label="结束时间" prop="end_time"></el-table-column>
        <el-table-column label="申请日期" prop="create_time"></el-table-column>
          <el-table-column label="是否审批" prop="ischeck">
            <template slot-scope="scope">
              <el-tag
                v-if="scope.row.ischeck == 0"
                type="warning"
                size="mini"
                >待审批</el-tag
              >
              <el-tag
                v-else-if="scope.row.ischeck == 1"
                type="success"
                size="mini"
                >已通过</el-tag
              >
              <el-tag
                v-else-if="scope.row.ischeck == 2"
                type="danger"
                size="mini"
                >已拒绝</el-tag
              >
            </template>
          </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <!-- 超级管理员 -->
            <template>
              <el-button
                size="mini"
                type="primary"
                :disabled="scope.row.ischeck != 0"
                @click.native="passHandler(scope.row.id)"
              >
                <i class="el-icon-edit"></i>
                {{ scope.row.ischeck == 0 ? "拒绝" : "已通过" }}
              </el-button>
              <!-- <el-button
                size="mini"
                type="danger"
                plain
                @click.native="denyPass(scope.row.id)"
              >
                <i class="el-icon-error"></i>
                拒绝
              </el-button> -->
            </template>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="page" v-show="total > 0">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="currentchange"
          :current-page="queryInfo.currentnum"
          :page-sizes="pageSizes"
          :page-size="queryInfo.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </el-card>
  </div>
</template>
<script>
import { mapState } from "vuex";
export default {
  data() {
    return {
      loading: true, // 表格加载
      checked: false,
      // 景区列表
      wayList: [],
      queryInfo: {
        pageNum: 1,
        pageSize: 12,
        currentnum: 1,
      },
      pageSizes: [10, 15, 20],
      total: 5,
      info: [],
    };
  },
  computed: {
    ...mapState({
      User: (state) => state.users,
    }),
  },
  created() {
    this.getWayList();
  },
  mounted() {
    this.loading = false;
  },
  methods: {
    // 获取路线列表
    async getWayList() {
      try {
        const res = await this.$http.getWayList(this.queryInfo);
        if (res.code !== 200) {
          this.userList = [];
          this.total = 0;
          this.$notify({
            title: "警告",
            message: res.message,
            type: "warning",
          });
        } else {
          const { result, total } = res.data.list;
          this.wayList = result;
          this.total = total;
        }
      } catch (error) {
        this.$notify({
          title: "系统提示",
          message: error.message,
          type: "warning",
        });
      } finally {
        setTimeout(() => {
          this.loading = false;
        }, 500);
      }
    },
    // 通过审核
    async passHandler(id) {
      try {
        await this.$http.passLeave({
          id: id,
        }).then((res) => {
          if (res.code !== 200) {
            return this.$notify({
              title: "系统提示",
              message: res.message,
              type: "error",
            });
          }
          // 关闭弹窗
          this.getWayList()
          this.$notify({
            title: "系统提示",
            message: res.message,
            type: "success",
          });
        });
      } catch (error) {
        return this.$notify({
          title: "系统提示",
          message: error.message,
          type: "error",
        });
      }
    },
    // 修改路线接口
    async denyPass() {
      try {
        this.$http.updateRouters(this.form).then((res) => {
          if (res.code !== 200) {
            return this.$notify({
              title: "系统提示",
              message: res.message,
              type: "warning",
            });
          }
          // 重置表单
          this.form = {}
          // 关闭弹窗
          this.getWayList()
          this.dialogFormVisible = false;
          this.isAdd = 1;
          this.$notify({
            title: "系统提示",
            message: res.message,
            type: "success",
          });
        });
      } catch (error) {
        return false;
      }
    },
    // 分页
    handleSizeChange(val) {
      this.loading = true;
      this.queryInfo.pageSize = val;
      this.getWayList();
    },
    // 分页
    currentchange(val) {
      this.loading = true;
      this.queryInfo.pageNum = val;
      this.getWayList();
    },
  },
};
</script>
<style lang="scss" scoped>
::vue-deep.hide .el-upload--picture-card {
  display: none;
}
.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.submit-btn {
  background-color: #000;
  color: #fff;
  border: none;
}
.search-btn {
  background-color: #000;
  color: #fff;
  border: none;
}
.reset-btn {
  background-color: #fff;
  color: #000;
}
.refresh-btn {
  background-color: #fff;
  color: #000;
}

.btn-box {
  display: flex;
  padding: 50px 0 0 0;
  justify-content: flex-start;
  align-items: center;
  .el-button {
    width: 150px;
  }
}

.input-box {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  width: 400px;
}

.image-view-title {
  margin: 10px 20px 10px 10px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.image-view-title img {
  object-fit: cover;
}

.image-list {
  width: 100px;
}

.image-button {
  text-align: center;
  padding: 120px 0;
}

.status-text {
  color: #30c82b;
  font-weight: bold;
  border: 1px solid #30c82b;
  display: block;
  width: 60px;
  height: 20px;
  margin: 0 auto;
  line-height: 20px;
  text-align: center;
}

.wait-complete {
  color: #ccc;
  font-weight: bold;
  border: 1px solid #ccc;
  display: block;
  width: 60px;
  height: 20px;
  margin: 0 auto;
  line-height: 20px;
  text-align: center;
}

.page {
  padding: 20px 0 0 0;
}

.el-image__inner {
  border-radius: 50%;
}

.el-table .cell {
  text-align: center;
}
</style>