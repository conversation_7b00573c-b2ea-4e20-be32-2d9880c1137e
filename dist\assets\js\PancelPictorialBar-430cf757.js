import{i as s,k as l,o as i,g as c,b as n,w as f,m as p}from"./index-444b28c3.js";import{E as b}from"./el-card-6f02be36.js";import{e as d,u as g}from"./useEcharts-57db09d5.js";import{_ as x}from"./_plugin-vue_export-helper-c27b6911.js";const _={__name:"PancelPictorialBar",setup(h){const e=s();return l(()=>{let t=d.init(e.value),o=[[{offset:0,color:"rgba(204,251,255, 0.8)"},{offset:1,color:"rgba(239,150,197, 0.8)"}],[{offset:0,color:"rgba(249,149,127, 0.8)"},{offset:1,color:"rgba(242,245,208, 0.8)"}],[{offset:0,color:"rgba(234,229,201, 0.8)"},{offset:1,color:"rgba(108,198,203, 0.8)"}],[{offset:0,color:"rgba(159,165,213, 0.8)"},{offset:1,color:"rgba(232,245,200, 0.8)"}],[{offset:0,color:"rgba(200,115,255, 0.8)"},{offset:1,color:"rgba(174,186,248, 0.8)"}],[{offset:0,color:"rgba(12,123,179, 0.8)"},{offset:1,color:"rgba(242,186,232, 0.8)"}]];g(t,{grid:{left:"20px",top:"20px",bottom:"20px",right:"20px"},tooltip:{trigger:"axis",axisPointer:{type:"none"},extraCssText:"width:100px;height:60px;"},xAxis:{data:["销售部","电商部","技术部","市场部","硬件部","人事部"],axisTick:{show:!1},axisLine:{lineStyle:{color:"rgba(45, 140, 240, 0.65)",width:1}},axisLabel:{show:!0,interval:0,rotate:0,margin:8,color:"#1890FF",fontSize:14,fontWeight:400}},yAxis:[{splitNumber:2,axisTick:{show:!1},axisLine:{lineStyle:{color:"rgba(24,144,255, 0.15)",width:1}},axisLabel:{inside:!0,color:"#1890FF",fontWeight:400,fontSize:14},splitArea:{show:!1,areaStyle:{color:"rgba(255,255,255,.5)"}},splitLine:{show:!0,lineStyle:{color:"rgba(24,144,255, 0.15)",width:1,type:"solid"}}}],series:[{name:"人员",type:"pictorialBar",barCategoryGap:"-50%",symbol:"path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z",label:{show:!1,position:"top",distance:15,color:"#1890FF",fontWeight:"bolder",fontSize:14},itemStyle:{color:function(r){return{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[o[r.dataIndex][0],o[r.dataIndex][1]],global:!1}}},data:[13,16,25,18,12,9],z:10}]})}),(t,o)=>{const a=b;return i(),c("div",null,[n(a,{shadow:"always"},{default:f(()=>[p("div",{ref_key:"echartsRef",ref:e,class:"echarts-content"},null,512)]),_:1})])}}},B=x(_,[["__scopeId","data-v-c333d29b"]]);export{B as default};
