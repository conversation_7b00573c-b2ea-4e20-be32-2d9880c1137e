<template>
  <div>
    <el-card shadow="hover" v-loading="loading" element-loading-text="Flyknit...">
      <el-row :gutter="10">
        <el-col :span="24" v-show="User.type === 2">
          <el-button type="primary" size="medium" icon="el-icon-setting" @click="settingHandler">{{
            User.config.length > 0 ? "修改配置" : "系统配置"
          }}</el-button>
        </el-col>
        <el-col :span="24" style="padding: 2rem 0.5rem">
          <el-descriptions title="配置信息" :column="1">
            <el-descriptions-item label="下载地址">{{
              User.config[0].link_url
            }}</el-descriptions-item>
            <el-descriptions-item label="当前版本"
              >V{{ User.config[0].version }}</el-descriptions-item
            >
            <el-descriptions-item label="更新内容">
              <p>
                <span v-html="User.config[0].content"></span>
              </p>
            </el-descriptions-item>
            <el-descriptions-item label="是否显示下载二维码">
              <el-switch
                v-model="show"
                active-value="1"
                disabled
                inactive-value="0"
              ></el-switch>
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>
    </el-card>
    <!-- 添加配置弹窗 -->
    <el-dialog :title="title" :visible.sync="dialogVisible" width="50%" center>
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
      >
        <el-form-item label="下载地址" prop="download_url">
          <el-input
            v-model="ruleForm.download_url"
            placeholder="请输入下载链接地址"
          ></el-input>
        </el-form-item>
        <el-form-item label="是否显示" prop="show_link">
          <el-switch
            v-model="ruleForm.show_link"
            @change="changeShow"
            active-value="1"
            inactive-value="0"
          ></el-switch>
        </el-form-item>
        <el-form-item label="App版本" prop="version">
          <el-input
            v-model="ruleForm.version"
            placeholder="请输入版本号"
          ></el-input>
        </el-form-item>
        <el-form-item label="更新内容">
          <quill-editor
            ref="text"
            v-model="ruleForm.content"
            class="editor"
            :options="editorOption"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm('ruleForm')">{{
            btnText
          }}</el-button>
          <el-button @click="resetForm('ruleForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from "vuex";
export default {
  name: "Setting",
  data() {
    return {
      ruleForm: {
        download_url: "",
        show_link: "",
        content: "",
        version: "",
      },
      show: "0", // 是否显示下载二维码
      editorOption: {}, // 富文本编辑器配置
      dialogVisible: false, // 展示开关
      rules: {
        download_url: [
          { required: true, message: "请输入App下载地址", trigger: "blur" },
        ],
        show_link: [
          { required: true, message: "请选择是否显示", trigger: "change" },
        ],
      },
      btnText: "添加配置",
      title: "添加配置",
      loading: true, // 加载
    };
  },
  watch: {
    User: {
      handler(val) {
        this.show = val.config[0].is_show;
      },
      deep: true,
    },
  },
  computed: {
    ...mapState({
      User: (state) => state.users,
    }),
  },
  mounted() {
    this.show = this.User.config[0].is_show
    setTimeout(() => {
      this.loading = false
    }, 500)
    // console.log("this.show", this.show);
  },
  methods: {
    // 显示开关事件
    changeShow(val) {
      this.ruleForm.show_link = val;
    },
    // 添加配置
    settingHandler() {
      this.dialogVisible = true;
      if (this.User.config.length > 0) {
        // console.log("User", this.User);
        this.ruleForm.download_url = this.User.config[0].link_url;
        this.ruleForm.show_link = String(this.User.config[0].is_show);
        this.ruleForm.content = this.User.config[0].content;
        this.ruleForm.version = this.User.config[0].version;
        this.ruleForm.id = this.User.config[0].id;
        this.btnText = "修改配置";
        this.title = "修改配置";
      } else {
        this.btnText = "添加配置";
        this.title = "添加配置";
      }
    },
    // 提交表单
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.addConfig();
          this.loading = true
          // 修改store信息
          this.$store.commit("changeSetting", this.ruleForm);
        } else {
          return false;
        }
      });
    },
    // 重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    // 全局添加配置接口
    async addConfig() {
      try {
        const res = await this.$http.addConfig(this.ruleForm);
        if (res.code !== 200) {
          return this.$notify({
            title: "错误",
            message: res.message,
            type: "error",
          });
        }
        this.dialogVisible = false;
        this.$refs["ruleForm"].resetFields();
        this.$notify({
          title: "成功",
          message: res.message,
          type: "success",
        });
        this.loading = false
      } catch (error) {
        this.loading = false
        return false;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-descriptions-item {
  padding: 1rem 0;
}
</style>