<!--
 * @Author: flyknit <EMAIL>
 * @Date: 2024-04-09 09:02:42
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-06-17 09:01:22
 * @FilePath: \electronic-filed:\gitee\backend-demo\src\views\index\Index.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by ${<EMAIL>}, All Rights Reserved. 
-->
<template>
  <el-container>
    <!-- 头部高度设为50px（默认60px） -->
    <el-header>
      <!-- logo -->
      <div class="header-logos">
        <el-image
          style="width: 3rem; height: 3rem"
          :src="url"
          :fit="fit"
        ></el-image>
        <a class="logo" href="/">巡检管理系统</a>
      </div>
      <!-- 折叠菜单按钮 -->
      <div class="toggle" @click="changeHide">
        <i class="el-icon-s-unfold" v-if="isCollapse"></i>
        <i class="el-icon-s-fold" v-if="!isCollapse"></i>
      </div>
      <!-- 通知消息 -->
      <el-col :span="24" class="title-bar">
        <!-- 扫码下载app下拉展示二维码 -->
        <el-col
          :span="8"
          style="padding: 1rem 0"
          v-if="User.config[0].is_show == '1'"
        >
          <el-dropdown trigger="hover" placement="bottom" class="download-link">
            <el-button type="text" style="color: #fff; font-size: 1rem">
              <i class="el-icon-download"></i>
              扫码下载调机助手
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <!-- 生成二维码 -->
                <canvas id="QRCode_header"></canvas>
                <div style="margin: 0 auto; text-align: center">
                  <h5>V{{ User.config[0].version }}</h5>
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-col>
        <!-- 退出菜单 -->
        <el-col :span="5">
          <div class="logout-box">
            <span class="el-dropdown-link">
              <img
                :src="User.avatar_url ? User.avatar_url : circleUrl"
                :fit="fit"
              />
              <span class="user-text">
                {{ time }}, {{ User.nickname ? User.nickname : User.idcard }}
              </span>
              <!-- 退出下拉展示 -->

              <el-dropdown trigger="hover" placement="bottom">
                <el-button type="text" style="color: #fff">
                  <i class="el-icon-arrow-down"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item>
                    <span class="user-texts" @click="logout">退出登录</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </span>
          </div>
        </el-col>
      </el-col>
    </el-header>
    <el-container>
      <!-- 菜单栏宽度设为自动 -->
      <el-aside :width="isCollapse ? '64px' : '230px'">
        <el-menu
          :default-active="indexs"
          class="el-menu-vertical"
          background-color="#feffff"
          text-color="#000"
          active-text-color="#000"
          unique-opened
          :collapse="isCollapse"
          :default-openeds="openeds"
        >
          <div v-for="(item, index) in menuList" :key="index">
            <el-submenu :index="item.id">
              <template slot="title">
                <i :class="item.icon"></i>
                <span @click="initOpend">{{ item.title }}</span>
              </template>
              <template v-if="item.Subclass.length > 0">
                <el-menu-item-group
                  v-for="(item1, index1) in item.Subclass"
                  :key="index1"
                >
                  <router-link
                    :to="{ path: item1.router, query: { id: item1.id } }"
                    class="texts"
                  >
                    <el-menu-item
                      :index="item1.id"
                      @click="
                        getLink(
                          item1.id,
                          item1.title,
                          item1.router,
                          item.id,
                          item
                        )
                      "
                      ><i :class="item1.icon"></i
                      >{{ item1.title }}</el-menu-item
                    >
                  </router-link>
                </el-menu-item-group>
              </template>
            </el-submenu>
          </div>
        </el-menu>
      </el-aside>
      <!-- 可以结合vue-router路由嵌套实现页面的跳转与显示 -->
      <el-main :style="{ 'margin-top': tabList.length > 12 ? '90px' : '60px' }">
        <FTagList
          :tabList="tabList"
          :activeItem="activeItem"
          @changeTabs="changeTabs"
          @handleTabsEdit="handleTabsEdit"
          @clearAll="initOpend"
        ></FTagList>
        <Main></Main>
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
import QRCode from "qrcode"; //引入生成二维码插件
import Main from "@/views/components/Main.vue";
import FTagList from "@/views/components/FTagList.vue";
import { mapState } from "vuex";
export default {
  components: {
    Main,
    FTagList,
  },
  data() {
    return {
      activeItem: 0, // 传递给tagList组件的当前激活项
      indexs: "0",
      isCollapse: false,
      url: "/src/assets/image/logo1.png",
      fit: "fill",
      openeds: ["0"],
      uniqueOpened: true,
      circleUrl:
        "https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fb-ssl.duitang.com%2Fuploads%2Fitem%2F201602%2F22%2F20160222210947_stQjR.jpeg&refer=http%3A%2F%2Fb-ssl.duitang.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1671786282&t=2b55938ce1451e0b9f857819b3001e29",
      menuList: [
        {
          id: "1",
          title: "控制台",
          router: "console",
          icon: "el-icon-s-home",
          Subclass: [
            {
              id: "1",
              title: "控制台",
              router: "console",
              icon: "el-icon-help",
            },
          ],
        },
        {
          id: "2",
          title: "标准管理",
          router: "",
          icon: "el-icon-s-order",
          Subclass: [
            {
              id: "1",
              title: "Model初始化",
              router: "sites",
              icon: "el-icon-edit-outline",
            },
            {
              id: "2",
              title: "巡检标准初始化",
              router: "standard",
              icon: "el-icon-date",
            },
            {
              id: "3",
              title: "尺码初始化",
              router: "size",
              icon: "el-icon-attract",
            },
            {
              id: "4",
              title: "品名初始化",
              router: "style",
              icon: "el-icon-document-add",
            },
            {
              id: "5",
              title: "测量点初始化",
              router: "point",
              icon: "el-icon-school",
            },
            {
              id: "6",
              title: "规格标准类型",
              router: "type",
              icon: "el-icon-setting",
            },
            {
              id: "7",
              title: "巡检员操作",
              router: "inspection",
              icon: "el-icon-mobile",
            },
          ],
        },
        {
          id: "3",
          title: "巡检管理",
          router: "swithing",
          icon: "el-icon-s-cooperation",
          Subclass: [
            {
              id: "1",
              title: "巡检订单",
              router: "tasty",
              icon: "el-icon-notebook-2",
            },
            {
              id: "2",
              title: "图片管理",
              router: "image",
              icon: "el-icon-document-copy",
            },
          ],
        },
        {
          id: "4",
          title: "系统管理",
          router: "user",
          icon: "el-icon-s-custom",
          Subclass: [
            {
              id: "1",
              title: "用户列表",
              router: "users",
              icon: "el-icon-user",
            },
            {
              // 系统配置
              id: "2",
              title: "系统配置",
              router: "setting",
              icon: "el-icon-setting",
            },
          ],
        },
      ], // 左侧菜单列表
      tabList: [], // 点击的菜单
    };
  },
  watch: {
    User: {
      handler(val) {
        if (val.config.length > 0 && val.config[0].is_show == "1") {
          this.$nextTick(() => {
            this.getQRCode();
          });
        }
      },
      deep: true,
    },
  },
  computed: {
    ...mapState({
      User: (state) => state.users,
    }),
    //判断当前是几点返回早上好，中午好，下午好，晚上好
    time() {
      let now = new Date();
      let hour = now.getHours();
      if (hour < 6) {
        return "凌晨好呀";
      } else if (hour < 9) {
        return "早上好呀";
      } else if (hour < 12) {
        return "上午好呀";
      } else if (hour < 14) {
        return "中午好呀";
      } else if (hour < 17) {
        return "下午好呀";
      } else if (hour < 19) {
        return "傍晚好呀";
      } else if (hour < 22) {
        return "晚上好呀";
      } else {
        return "夜里好呀";
      }
    },
  },
  mounted() {
    // 生成的二维码
    this.getQRCode();
    let tabList = localStorage.getItem("tab");
    const fid = localStorage.getItem("fid");
    const open = localStorage.getItem("opend");

    if (tabList) {
      tabList = JSON.parse(tabList);
      const seenPaths = new Set();
      const uniqueTabList = tabList.filter((item) => {
        if (seenPaths.has(item.path)) {
          return false;
        } else {
          seenPaths.add(item.path);
          return true;
        }
      });
      this.tabList = uniqueTabList;

      if (fid && open) {
        uniqueTabList.forEach((item, index) => {
          if (item.class_id === open && item.id === fid) {
            this.activeItem = index;
            this.indexs = item.id + "";
            this.openeds.push(item.class_id + "");
          }
        });
      } else {
        this.indexs = "1";
        this.openeds = ["1"];
      }
    } else {
      this.indexs = "1";
      this.openeds = ["1"];
      this.tabList = [];
    }

    localStorage.setItem("tab", JSON.stringify(this.tabList));
  },
  methods: {
    // 生成二维码
    getQRCode() {
      let opts = {
        errorCorrectionLevel: "H", //容错级别
        type: "image/png", //生成的二维码类型
        quality: 0.3, //二维码质量
        margin: 5, //二维码留白边距
        width: 150, //宽
        height: 150, //高
        text: "调机助手", //二维码内容
        color: {
          dark: "#333333", //前景色
          light: "#fff", //背景色
        },
      };
      this.QRCodeMsg = this.User.config[0].link_url; //生成的二维码为URL地址js
      let msg = document.getElementById("QRCode_header");
      // 将获取到的数据（val）画到msg（canvas）上
      QRCode.toCanvas(msg, this.QRCodeMsg, opts, function (error) {
        console.log(error);
      });
    },
    // 关闭重连机制
    closeReconnect() {
      // console.log('关闭重连机制', this.$reconnectCount, this.$maxReconnectCount);
      this.$reconnectCount = this.$maxReconnectCount;
      setTimeout(() => {
        this.$reconnectCount = 0;
      }, 1000);
    },
    // 初始化点击的menu
    initOpend(obj) {
      this.indexs = "1";
      if (obj.length > 0) {
        //  console.log('当前tablist', this.tabList);
        // this.tabList = []
        // console.log('清空后', this.tabList);
        this.tabList = obj;
      }
    },
    // 隐藏侧边栏
    changeHide() {
      this.isCollapse = !this.isCollapse;
      this.$store.commit("handleAsideWidth");
    },
    // 退出登录
    async logout() {
      this.$confirm("您即将退出系统, 是否继续?", "系统提示", {
        confirmButtonText: "退出",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const res = await this.$http.logOutBackend();
          if (res.code !== 200)
            return this.$notify({
              title: "错误",
              message: res.message,
              type: "error",
            });
          localStorage.removeItem("accessToken");
          localStorage.removeItem("userInfo");
          localStorage.removeItem("tab");
          localStorage.removeItem("opend");
					localStorage.removeItem("fid");
					localStorage.removeItem("sepc-info");
          this.$notify({
            title: "退出成功",
            message: "退出成功",
            type: "success",
          });
          setTimeout(() => {
            this.$router.push("/login");
          }, 1000);
        })
        .catch(() => {
          return false;
        });
    },
    // 收集tab标签
    getLink(id, name, link, classify_id) {
      // console.log('小分类', id);

      // console.log('大分类', classify_id);
      // 存储当前激活选项
      localStorage.setItem("fid", id + "");
      // 存储当前大分类
      localStorage.setItem("opend", classify_id + "");

      const obj = {
        id: id,
        title: name,
        path: link,
        class_id: classify_id,
      };
      // 去重
      this.tabList.map((item, index) => {
        // console.log("item", item);
        if (item.path === link) {
          this.tabList.splice(index, 1);
        }
      });
      this.tabList.push(obj);
      // 存储打开的tablist
      localStorage.setItem("tab", JSON.stringify(this.tabList));
    },

    // 删除tab
    handleTabsEdit(e) {
      // this.tabList.forEach((tab, index) => {
      //   if (tab.path === e.router) {
      //     this.tabList.splice(index, 1);
      //   }
      // });
      const tabs = JSON.parse(localStorage.getItem("tab"));
      tabs.forEach((tab, index) => {
        if (tab.path === e.router) {
          tabs.splice(index, 1);
        }
      });
      // console.log('上一个tab', this.tabList.length - 1);
      // console.log('上一个tab的path', this.tabList[this.tabList.length - 1].path);
      // console.log('上一个tab的分类id', this.tabList[this.tabList.length - 1].class_id);
      // console.log('上一个tab的小分类id', this.tabList[this.tabList.length - 1].id);
      if (tabs[tabs.length - 1].id == 20) {
        this.indexs = "0";
        setTimeout(() => {
          this.indexs = "1";
        }, 200);
      }
      // 跳转小分类id选中
      this.indexs = tabs[tabs.length - 1].id + "";
      // 默认打开的大分类
      this.openeds = [];
      this.openeds.push(tabs[tabs.length - 1].class_id + "");
      this.$router.push(tabs[tabs.length - 1].path);
      this.tabList = tabs;
      // 重新存储删除后的tabList
      localStorage.setItem("tab", JSON.stringify(this.tabList));
    },
    // 切换tab
    changeTabs(e) {
      // console.log('大分类',e.cid);
      // console.log('index', e.idx);
      // console.log('子类id', e.fid);
      if (e.fid == 20) {
        e.fid = "1";
      }
      this.indexs = e.fid;
      this.openeds = [];
      this.openeds.push(e.cid + "");
      // console.log('当前激活', this.indexs);
      //  console.log('当前大分类', this.openeds);
      // 存储当前激活选项
      localStorage.setItem("fid", this.indexs);
      // 存储当前大分类
      localStorage.setItem("opend", this.openeds);
    },
  },
};
</script>

<style scoped lang="scss">
.download-link {
}
.el-submenu__title:hover {
  background-color: rgb(3, 19, 33) !important;
}
/*隐藏文字*/
.el-menu--collapse .el-submenu__title span {
  display: none;
  transition: all 0.2s ease 0ms;
}
/*隐藏 > */
.el-menu--collapse .el-submenu__title .el-submenu__icon-arrow {
  display: none;
  transition: all 0.2s ease 0ms;
}

.title-bar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 5px;
  box-sizing: border-box;
  overflow: hidden;
  .header-message {
    display: flex;
    flex: 2;
    justify-content: flex-end;
    align-items: flex-end;
    margin-top: 5px;
  }
  .logout-box {
    display: flex;
    flex: 1;
    justify-content: flex-end;
    align-items: center;
    padding-right: 30px;
    color: #fff;
    img {
      width: 40px;
      height: 40px;
      border-radius: 100%;
    }
  }
}
.el-aside {
  /* transition: all 0.2s; */
  transition: all 0.2s ease 0ms;
}
.fade-enter-from {
  opacity: 0;
}
.fade-enter-to {
  opacity: 1;
}
.fade-leave-from {
  opacity: 1;
}
.fade-leave-to {
  opacity: 0;
}
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s;
}
.fade-enter-active {
  transition-delay: 0.3s;
}
.message-box {
  display: flex;
  width: 100px;
  position: absolute;
  left: 25%;
  top: 2.6%;
}
.message-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.message-item .message-detail {
  font-size: 0.8rem;
  width: 200px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}
.message-item .time-text {
  font-size: 0.8rem;
  margin-left: 10px;
}
.el-button {
  background-color: #409eff !important;
  padding: 0;
  margin: 0;
  border: 0 !important;
  display: flex;
  justify-content: center;
  align-items: center;
}
.info {
  color: #000;
  font-size: 1rem;
  position: relative;
  top: -3px;
  left: 5px;
}

.texts {
  text-decoration: none !important;
}
/* 导航二级菜单栏点击之后的一像素边的问题 */
/* 导航栏点击后,左边的颜色设置 */
::v-deep .el-submenu span {
  font-size: 15px !important;
  font-weight: bolder !important;
  letter-spacing: 1.5px;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}
::v-deep .el-menu-item {
  font-size: 14px !important;
}
::v-deep.el-submenu:hover {
  background-color: #ecf5ff !important;
}
.el-menu-item:hover {
  background-color: #ecf5ff !important;
}
.el-menu-item.is-active {
  background-color: #409eff !important;
  color: #fff !important;
  //border-left: solid 4px rgb(0, 0, 0) !important;
}

/*占满全屏*/
.el-container {
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
  bottom: 0;
  background-color: #f3f7f9;
  box-sizing: border-box;
  overflow: hidden;
}
/*上外边距50px，防止挡住Header*/
.el-aside,
.el-main {
  margin-top: 60px;
  // height: calc(100vh - 61px);
  box-sizing: border-box;
}
/*设置背景色，方便观察效果*/
.el-header {
  background-color: #409eff;
  /* 上层显示，避免被Main和Aside遮挡 */
  z-index: 999;
  box-sizing: border-box;
  overflow-x: hidden;
}
.el-menu-item {
  font-size: 15px;
}
.header-logos {
  width: 230px;
  display: flex;
  position: absolute;
  left: 0;
  top: 1px;
  justify-content: center;
  align-items: center;
}
.el-image {
  margin: 2px 10px;
  border-radius: 50px !important;
  width: 100%;
  height: 100%;
}
.el-aside {
  background-color: #feffff;
}
.el-main {
  background-color: #f3f4f5;
}
.user-text {
  font-size: 1rem;
  padding: 0 1rem;
  color: #fff;
  cursor: pointer;
}
.user-texts {
  font-size: 1rem;
  padding: 0 0.3rem;
  cursor: pointer;
}
/* 去除菜单右侧边框 */
.el-menu {
  border-right: none;
}
/* 设置展开时菜单宽度 */
.el-menu-vertical:not(.el-menu--collapse) {
  width: 230px;
}
/* logo */
.logo {
  color: #ffffff;
  text-align: center;
  font-size: 1.28rem;
  line-height: 55px;
  padding: 0 5px;
  font-weight: 400;
  text-decoration: none;
  font-weight: bold;
  letter-spacing: 1px;
}
/* 折叠按钮 */
.toggle {
  color: #ffffff;
  text-align: center;
  font-size: 26px;
  line-height: 60px;
  display: inline-block;
  padding: 0 15px;
  //border-left: solid 1px #ccc;
  position: absolute;
  left: 230px;
  cursor: pointer;
}
.toggle:hover {
  /* background-color: #ffd04b; */
}
/* 下拉菜单 */
.el-dropdown {
  color: #fff;
  text-align: center;
  font-size: 26px;
  line-height: 50px;
  float: right;
}
.el-submenu span {
  font-size: 16px;
  font-weight: normal;
}
.el-dropdown-link {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 5px;
}
</style>
