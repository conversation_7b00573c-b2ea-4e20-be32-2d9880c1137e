<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-08-18 09:59:29
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-19 16:32:23
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\system\DepartmentManage.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-card>
            <!-- 表单、搜索框，新增按钮 -->
            <div class="filter-container">
                <el-form :inline="true" :model="filterForm" ref="filterForm" class="demo-form-inline">
                    <el-form-item label="部门名称" prop="name">
                        <el-input v-model="filterForm.name" placeholder="请输入部门名称"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="filterSubmit" icon="el-icon-search">查询</el-button>
                        <el-button type="button" @click="resetForm('filterForm')" icon="el-icon-refresh">重置</el-button>
                        <el-button type="success" icon="el-icon-plus" @click="addDepart">创建部门</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <!-- 主体部分，表格，部门树 -->
            <div class="table-container">
                <el-table :data="tableData" border stripe v-loading="loading" element-loading-text="Flyknit">
                    <el-table-column type="index" label="序号" width="80"></el-table-column>
                    <el-table-column prop="id" label="ID" width="80"></el-table-column>
                    <el-table-column prop="depart_name" label="部门名称"></el-table-column>
                    <el-table-column prop="depart_manager" label="部门负责人"></el-table-column>
                    <el-table-column prop="email" label="部门负责人邮箱"></el-table-column>
                    <el-table-column prop="depart_desc" label="部门描述"></el-table-column>
                    <el-table-column prop="create_time" label="创建时间"></el-table-column>
                    <el-table-column prop="update_time" label="更新时间"></el-table-column>
                    <el-table-column label="操作" width="200">
                        <template slot-scope="scope">
                            <el-button type="primary" size="mini" icon="el-icon-edit" @click="editDepart(scope.row)">编辑</el-button>
                            <el-button type="danger" size="mini" icon="el-icon-delete" @click="delDepart(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <!-- 分页 -->
            <div class="pagination-container">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 30, 40]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
        </el-card>
        <!-- 添加部门弹窗，部门名称，部门负责人，部门负责人邮箱，部门描述 -->
        <el-dialog :visible.sync="addDialogVisible" :title="title" width="30%">
            <el-form :model="addForm" ref="addForm" :rules="addFormRules" label-width="100px" class="add-form">
                <el-form-item label="部门名称" prop="name">
                    <el-input v-model="addForm.name" placeholder="请输入部门名称"></el-input>
                </el-form-item>
                <el-form-item label="负责人" prop="depart_manager">
                    <el-input v-model="addForm.depart_manager" placeholder="请输入部门负责人"></el-input>
                </el-form-item>
                <el-form-item label="负责人邮箱" prop="email">
                    <el-input v-model="addForm.email" placeholder="请输入部门负责人邮箱"></el-input>
                </el-form-item>
                <el-form-item label="部门描述" prop="depart_desc">
                    <el-input v-model="addForm.depart_desc" placeholder="请输入部门描述"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="addDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="addSubmit">{{ btnText }}</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { set } from 'nprogress'

export default {
    data() {
        return {
            loading: true,
            addDialogVisible: false,
            filterForm: {
                name: ''
            },
            tableData: [],
            currentPage: 1,
            pageSize: 10,
            total: 0,
            addForm: {
                name: '',
                depart_manager: '',
                email: '',
                depart_desc: ''
            },
            addFormRules: {
                name: [
                    { required: true, message: '请输入部门名称', trigger: 'blur' }
                ],
                depart_manager: [
                    { required: true, message: '请输入部门负责人', trigger: 'blur' }
                ],
                email: [
                    { required: true, message: '请输入部门负责人邮箱', trigger: 'blur' }
                ],
                depart_desc: [
                    { required: true, message: '请输入部门描述', trigger: 'blur' }
                ]
            },
            btnText: '创 建',
            title: '添加部门'
        }
    },
    created() {
        this.getDepartList()
    },
    methods: {
        // 获取部门列表
        async getDepartList() {
            try {
                this.filterForm.page = this.currentPage
                this.filterForm.pageSize = this.pageSize
                const res = await this.$http.getDepartments(this.filterForm)
                if(res.status !== 200) {
                    return this.$message.error(res.message)
                }
                const {list, total} = res.data
                this.tableData = list
                this.total = total
                setTimeout(() => {
                    this.loading = false
                }, 500);
            } catch (error) {
                this.loading = false
                return this.$message.error('服务器异常，请稍后重试')
            }
        },
        // 查询
        filterSubmit() {
            if(this.filterForm.name.trim()) {
                this.loading = true
                this.getDepartList()
            }
        },
        // 重置
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.loading = true
            this.getDepartList()
        },
        // 新增部门
        addDepart() {
            this.addDialogVisible = true
            // 重置表单
            this.$nextTick(() => {
                this.$refs['addForm'].resetFields()
            })
        },
        handleCurrentChange(val) {
            this.loading = true
            this.currentPage = val
            this.getDepartList()
        },
        handleSizeChange(val) {
            this.loading = true
            this.pageSize = val
            this.getDepartList()
        },
        // 检测表单
        addSubmit() {
            this.$refs['addForm'].validate((valid) => {
                if (valid) {
                    this.addDepartApi()
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        // 添加部门接口 
        async addDepartApi() {
            try {
                const res = await this.$http.addDepartments(this.addForm)
                if(res.status !== 200) {
                    return this.$message.error(res.message)
                }
                this.$message.success(res.message)
                this.addDialogVisible = false
                this.getDepartList()
                // 重置表单
                this.$refs['addForm'].resetFields()
            } catch (error) {
                return this.$message.error('服务器异常，请稍后重试！')
            }
        },
        // 编辑部门
        editDepart(row) {
            this.addForm.id = row.id
            this.addForm.name = row.depart_name
            this.addForm.depart_manager = row.depart_manager
            this.addForm.email = row.email
            this.addForm.depart_desc = row.depart_desc
            this.addDialogVisible = true
            this.btnText = '编 辑'
            this.title = '编辑部门'
        },
        // 删除部门
        delDepart(row) {
            this.$confirm('此操作将永久删除该部门, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
                }).then(async () => {
                    try {
                        const res = await this.$http.delDepartments({id: row.id})
                        if(res.status !== 200) {
                            return this.$message.error(res.message)
                        }
                        this.$message.success(res.message)
                        this.getDepartList()
                    } catch (error) {
                        return this.$message.error('服务器异常，请稍后重试！')
                    }
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });          
                });
        }

    }
}
</script>
<style lang="scss">
    .el-card__body {
        padding: 20px 20px 0px 20px;
    }
    .table-container {
        margin-top: 20px;
    }
    .pagination-container {
        padding: 20px 0 ;
    }
</style>