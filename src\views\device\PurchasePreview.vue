<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-08-16 13:21:51
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-17 16:26:11
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\device\PurchaseDetail.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-card>
            <!-- 头部，返回按钮，标题为设备采购单预览，打印按钮 -->
            <div slot="header">
                <el-row>
                    <el-col :span="10">
                        <i class="el-icon-back"></i> <el-divider direction="vertical"></el-divider> <span
                            class="card-back-text" @click="reTurn">返回上一页</span>
                    </el-col>
                    <el-col :span="12">
                        <span class="card-header-text">设备采购申购单</span>
                    </el-col>
                    <el-col :span="2">
                        <el-button type="primary" size="mini" icon="el-icon-printer" v-print="'#printMe'">打印</el-button>
                    </el-col>
                </el-row>
            </div>
            <!-- 申购单主体，表格 -->
            <div ref="print">
                <el-row :gutter="40">
                    <el-col :xs="24" :sm="24" :md="24" :lg="12">
                        <el-form :model="forms" ref="forms" :rules="rules" label-width="80px">
                            <el-form-item label="设备名称:" label-width="80px" prop="device_name">
                                <el-input v-model="forms.device_name" placeholder="请输入设备名称"></el-input>
                            </el-form-item>
                            <el-form-item label="品牌型号:" label-width="80px" prop="brand">
                                <el-input v-model="forms.brand" placeholder="请输入设备品牌/型号"></el-input>
                            </el-form-item>
                            <el-form-item label="供应商:" label-width="80px" prop="supplier">
                                <el-input v-model="forms.supplier" placeholder="请输入供应商"></el-input>
                            </el-form-item>
                            <el-form-item label="申购数量:" prop="quantity">
                                <el-input type="number" v-model="forms.quantity" placeholder="请输入申购数量"></el-input>
                            </el-form-item>
                            <el-form-item label="设备单价预估:" label-width="110px" prop="price">
                                <el-input v-model="forms.price" placeholder="请输入设备单价预估金额"></el-input>
                            </el-form-item>
                            <el-form-item label="设备总金额预估:" label-width="120px" prop="total_price">
                                <el-input v-model="forms.total_price" placeholder="请输入设备总金额预估"></el-input>
                            </el-form-item>
                            <el-form-item label="询价人:" prop="inquirer">
                                <el-input v-model="forms.inquirer" placeholder="请输入询价人"></el-input>
                            </el-form-item>
                            <el-form-item label="采购经办人:" prop="operator" label-width="100px">
                                <el-input v-model="forms.operator" placeholder="请输入采购经办人"></el-input>
                            </el-form-item>
                            <el-form-item label="申购原因及其要求:" label-width="140px" prop="reason">
                                <el-input type="textarea" rows="4" v-model="forms.reason"
                                    placeholder="请输入申购原因及其要求，越详细越好"></el-input>
                            </el-form-item>
                            <el-form-item label="申购人:" prop="applicant">
                                <el-input v-model="forms.applicant" placeholder="请输入申购人姓名"></el-input>
                            </el-form-item>
                            <el-form-item label="联系方式:" prop="contact">
                                <el-input v-model="forms.contact" placeholder="请输入申购人联系方式"></el-input>
                            </el-form-item>
                            <el-form-item label="设备管理负责人:" label-width="120px" prop="manager">
                                <el-input v-model="forms.manager" placeholder="待签字" disabled></el-input>
                            </el-form-item>
                            <el-form-item label="部门负责人:" prop="departmentManager" label-width="100px">
                                <el-input v-model="forms.depart_manager" placeholder="待签字" disabled></el-input>
                            </el-form-item>
                            <el-form-item label="审批人:" prop="approver">
                                <el-input v-model="forms.approver" placeholder="待审批" disabled></el-input>
                            </el-form-item>
                            <!-- 提交申购单按钮和表单重置按钮 -->
                            <el-form-item>
                                <el-button type="primary" @click="submitForm('forms')"
                                    icon="el-icon-position">{{btnText}}</el-button>
                                <el-button @click="resetForm('forms')" type="button" icon="el-icon-refresh">重置</el-button>
                            </el-form-item>
                        </el-form>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="12" class="right-content">
                        <div id="printMe" class="print-content keep-original">
                            <div class="purchase-title">
                                设备采购申购单
                            </div>
                            <div class="biaoqian">
                                <h5>QSD/SZ-XX08-{{ year }}</h5>
                            </div>
                            <div class="table-title">
                                <div class="left-title">
                                    <span>申购部门:</span><span>特种面料厂</span>
                                </div>
                                <div class="right-title">
                                    <span>申购日期:</span><span>{{ year }}</span><span>年</span><span>{{ month
                                    }}</span><span>月</span><span>{{ day }}</span><span>日</span>
                                </div>
                            </div>
                            <table width="100%" border="0" cellpadding="0" cellspacing="0" align="center"
                                style="border: 1px solid #666;">
                                <tr align="left">
                                    <td width="25%">设备名称</td>
                                    <td width="25%">{{ forms.device_name }}</td>
                                    <td width="25%">品牌型号</td>
                                    <td width="25%">{{ forms.brand }}</td>
                                </tr>
                                <tr align="left">
                                    <td width="25%">供应商</td>
                                    <td width="25%">{{ forms.supplier }}</td>
                                    <td width="25%">申购数量</td>
                                    <td width="25%">{{ forms.quantity }}</td>
                                </tr>
                                <tr align="left">
                                    <td width="25%">设备单价预估</td>
                                    <td width="25%">{{ forms.price }}</td>
                                    <td width="25%">设备总金额预估</td>
                                    <td width="25%">{{ forms.total_price }}</td>
                                </tr>
                                <tr align="left">
                                    <td width="25%">询价人</td>
                                    <td width="25%">{{ forms.inquirer }}</td>
                                    <td width="25%">采购经办人</td>
                                    <td width="25%">{{ forms.operator }}</td>
                                </tr>
                                <tr>
                                    <td style="width:10px;height:300px;">
                                        <div class="vertical-text">申购原因及其设备要求</div>
                                    </td>
                                    <td colspan="3">
                                        <div class="textarea">
                                            {{ forms.reason }}
                                        </div>
                                        <div class="right-textarea">
                                            <div class="way">
                                                <h5>申购人：</h5><span style="width: 200px;">{{ forms.applicant }}</span>
                                            </div>
                                            <div class="way">
                                                <h5>联系方式：</h5><span style="width: 183px;">{{ forms.contact }}</span>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="4" style="height:110px;">
                                        <div class="top-text">
                                            (是否对显示资产可替代发表意见)
                                        </div>
                                        <div class="bottom-text">
                                            设备管理负责人：<span style="width: 120px;">{{ forms.manager }}</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="4" style="height: 120px;">
                                        <div class="bottom-nametext">
                                            部门负责人：<span style="width: 120px;">{{ forms.depart_manager }}</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="4" style="height: 120px;">
                                        <div class="bottom-nametexts">
                                            审批人：<span style="width: 120px;">{{ forms.approver }}</span>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                            <div class="notice-text">
                                <h5>（本单一式两联，一联交由财务部门；一联交由采购经办人，并在验收时交由验收人核对。）</h5>
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </el-card>
    </div>
</template>
<script>
export default {
    data() {
        return {
            btnText: '创建申购单',
            forms: {
                device_name: '',
                brand: '',
                supplier: '',
                quantity: '',
                price: '',
                total_price: '',
                inquirer: '',
                operator: '',
                reason: '',
                applicant: '',
                contact: '',
                manager: '',
                depart_manager: '',
                approver: '',
                id: '',
            },
            edit_info: {},
            rules: {
                device_name: [
                    { required: true, message: '请输入设备名称', trigger: 'blur' },
                    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
                ],
                brand: [
                    { required: true, message: '请输入设备品牌', trigger: 'blur' }
                ],
                // supplier: [
                //     { required: true, message: '请输入供应商', trigger: 'blur' }
                // ],
                quantity: [
                    { required: true, message: '请输入申购数量', trigger: 'blur' }
                ],
                // price: [
                //     { required: true, message: '请输入设备预估单价', trigger: 'blur' }
                // ],
                // total_price: [
                //     { required: true, message: '请输入设备总金额预估', trigger: 'blur' }
                // ],
                // inquirer: [
                //     { required: true, message: '请输入询价人', trigger: 'blur' }
                // ],
                // operator: [
                //     { required: true, message: '请输入经办人', trigger: 'blur' }
                // ],
                reason: [
                    { required: true, message: '请输入申购原因', trigger: 'blur' }
                ],
                applicant: [
                    { required: true, message: '请输入申购人姓名', trigger: 'blur' }
                ],
                contact: [
                    { required: true, message: '请输入申购人联系方式', trigger: 'blur' }
                ],
                // manager: [
                //     { required: true, message: '请输入设备管理负责人姓名', trigger: 'blur' }
                // ],
                // departmentManager: [
                //     { required: true, message: '请输入部门经理姓名', trigger: 'blur' }
                // ],
                // approver: [
                //     { required: true, message: '请输入审批人姓名', trigger: 'blur' }
                // ],
            },
            year: '',
            month: '',
            day: '',
        }
    },
    created() {
        this.edit_info = this.$route.query.info;
        if (this.edit_info) {
            this.btnText = '修改申购单'
            this.forms.id = this.edit_info.id
            this.forms.device_name = this.edit_info.device_name
            this.forms.brand = this.edit_info.brand
            this.forms.supplier = this.edit_info.supplier
            this.forms.quantity = this.edit_info.quantity
            this.forms.price = this.edit_info.price
            this.forms.total_price = this.edit_info.total_price
            this.forms.inquirer = this.edit_info.inquirer
            this.forms.operator = this.edit_info.operator
            this.forms.reason = this.edit_info.reason
            this.forms.applicant = this.edit_info.applicant
            this.forms.contact = this.edit_info.contact
            this.forms.manager = this.edit_info.manager
            this.forms.depart_manager = this.edit_info.depart_manager
            this.forms.approver = this.edit_info.approver
        }
        this.initTime()
    },
    methods: {
        // 初始化时间函数，获取年月日
        initTime() {
            let date = new Date()
            let year = date.getFullYear()
            let month = date.getMonth() + 1
            let day = date.getDate()
            this.year = year
            this.month = month
            this.day = day
        },
        // 返回按钮
        reTurn() {
            this.$router.push('/purchase')
        },
        // 打印按钮
        print() {
            // 打印
            this.$print(this.$refs.print)
        },
        // 新创建申购单
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    // 创建申购单方法
                    this.savePurchaseInfo()
                } else {
                    return false;
                }
            });
        },
        // 重置表单
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
        async savePurchaseInfo() {
            try {
                const res = await this.$http.submitPurchase(this.forms)
                if (res.status !== 200) {
                    return this.$message.error(res.message)
                }
                this.$message.success(res.message)
                this.$router.push('/purchase')
            } catch (error) {
                return this.$message.error('服务器异常，请稍后重试')
            }
        }
    },
   
}
</script>
<style lang="scss" scoped>
.print-content {
    width: 100%;
    height: 100%;
    padding: 0 2rem;
    box-sizing: border-box;
    overflow: hidden;
}

.purchase-title {
    text-align: center;
    font-size: 18px;
}

.card-header-text {
    font-size: 18px;
    letter-spacing: 2px;
    color: #666;
}

.notice-text {
    padding: 5px 8px;
    font-size: 18px;
    letter-spacing: 2px;
}

.textarea {
    height: 100px;
    margin: 5px;
    position: relative;
    top: -30px;
    padding: 20px 0;
}

.top-text {
    display: flex;
    justify-content: flex-start;
    padding: 10px;
    position: relative;
    top: -20px;
}

.bottom-text {
    display: flex;
    justify-content: flex-end;
    padding: 10px;
    position: relative;
    top: 20px;
    right: 20px;
}

.bottom-nametext {
    display: flex;
    justify-content: flex-end;
    padding: 10px;
    position: relative;
    top: 40px;
    right: 55px;
}

.bottom-nametexts {
    display: flex;
    justify-content: flex-end;
    padding: 10px;
    position: relative;
    top: 40px;
    right: 85px;
}

.vertical-text {
    writing-mode: vertical-rl;
    /* 竖排方向从右向左 */
    text-orientation: upright;
    /* 竖排文字的方向为正常 */
    letter-spacing: 8px;
    padding: 10px 0;
    margin-left: 10px;
}

.right-textarea {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: flex-end;
    padding-right: 20px;
    line-height: 1.8;
    margin-top: 80px;
}

.way {
    display: flex;

    h5 {
        font-size: 16px;
        font-weight: normal;
    }
}

.card-back-text {
    cursor: pointer;
}

@media print {

    .no-print,
    .no-print * {
        display: none !important;
    }

    .print {
        display: block !important;
    }

    .keep-original {
        position: fixed;
        /* 使元素固定在页面上 */
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 9999;
        /* 设置较高的层级，确保元素在打印时不被其他元素遮挡 */
    }
}

.right-content {
    border: 1px solid #eee;
    overflow-x: auto;
}

td {
    height: 40px;
    padding-left: 10px;
    border: 1px solid #666;
    word-wrap: break-word;
    word-break: break-all;
}

.table-title {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;

    .left-title {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex: 1;
        flex-shrink: 0;

        span {
            padding: 0 2px;
            width: 80px;
        }
    }

    .right-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex: 1;
        flex-shrink: 0;

        span {
            padding: 0 5px;
        }
    }

    .right-title span:nth-child(1) {
        width: 70px !important;
    }
}

.biaoqian {
    display: flex;
    width: 100%;
    justify-content: flex-end;
    padding: 10px 0;

    h5 {
        font-weight: normal;
        margin-right: 80px;
    }
}</style>