import{i,u as c,V as n,k as u,ac as m,o as d,g as h,b as f,w as p,m as _}from"./index-444b28c3.js";import{E as g}from"./el-card-6f02be36.js";import{e as y,u as x}from"./useEcharts-57db09d5.js";import{_ as v}from"./_plugin-vue_export-helper-c27b6911.js";const C={__name:"HomeUser",setup(w){const a=i(null),{t:o}=c();let e=null;n.on("resizeChart",()=>{e&&(e.dispose(),e=null),s()}),n.on("changeLanguage",()=>{e&&e.dispose(),s()}),u(()=>{s()}),m(()=>{e&&(e.dispose(),e=null)});const s=()=>{try{if(e&&e.dispose(),a.value){e=y.init(a.value);const t=getComputedStyle(document.documentElement).getPropertyValue("--el-menu-text-color").trim(),l={title:{text:"FCDC WorkStation Statistics",left:"center",textStyle:{color:t,fontSize:"24px",fontWeight:"bolder"}},tooltip:{trigger:"item"},legend:{left:"left",align:"auto",orient:"vertical",textStyle:{color:t},itemGap:30},series:[{name:o("home.yewushujuzonglang"),type:"pie",roseType:"area",center:["50%","50%"],selectedMode:"single",data:[{value:120,name:o("home.jitaishuliang"),selected:!0},{value:56,name:o("home.zaikaijitaishuliang")},{value:35,name:o("home.ceshishuliang")}],color:["#DA7F6A","#E6A23C","#67C23A"],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"},label:{show:!0,fontSize:"20px",fontWeight:"bolder"},labelLine:{show:!1}}}]};x(e,l)}}catch(t){}};return(t,l)=>{const r=g;return d(),h("div",null,[f(r,{shadow:"always",class:"user"},{default:p(()=>[_("div",{ref_key:"echartsRef",ref:a,class:"echarts-content"},null,512)]),_:1})])}}},E=v(C,[["__scopeId","data-v-09d23389"]]);export{E as default};
