<template>
  <el-container>
    <el-row :gutter="10" style="width: 100%">
      <!-- 左侧布局 -->

      <!-- 中间布局 -->
      <el-col :span="24">
        <!-- 翻改疵品统计分析 -->
        <el-card shadow="hover" style="height: 800px; margin-top: 10px">
          <div class="text item">
            <el-col style="display: flex; align-items: center">
              <i
                class="el-icon-s-marketing"
                style="color: #303133; font-size: 20px; margin-right: 10px"
              ></i>
              <h4>工艺周巡检数据走势</h4>
            </el-col>

            <el-row :gutter="10">
              <el-col :span="24" style="margin-top: 20px">
                <div style="height: 750px" id="mains"></div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </el-container>
</template>

<script>
import * as echarts from "echarts";
import { set } from "lodash";
export default {
  name: "Console",
  data() {
    return {
      weekNum: [], // 巡检数据
      hege: [], // 合格率
      tiaoji: [], // 调机率
    };
  },
  created() {
    this.getHomedata();
  },
  methods: {
    // 初始化图表
    async initChart() {
      // 基于准备好的dom，初始化echarts实例
      let myChart = echarts.init(document.getElementById("mains")); //也可以通过$refs.newCharts的方式去获取到dom实例。
      myChart.showLoading();
      // 获取当前日期
      const today = new Date();
      // 获取本周的第一天（星期一）
      const firstDayOfWeek = new Date(
        today.setDate(today.getDate() - today.getDay() + 1)
      );
      // 存储每一天的日期
      const daysOfWeek = [];
      // 循环获取本周每一天的日期
      for (let i = 0; i < 7; i++) {
        const date = new Date(firstDayOfWeek);
        date.setDate(date.getDate() + i);
        const formattedDate = date.toISOString().split("T")[0];
        daysOfWeek.push(formattedDate);
      }
      // 绘制图表
      myChart.setOption({
        title: { text: "" }, //图标的标题
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        toolbox: {
          show: true,
          feature: {
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ["line", "bar"] },
            restore: { show: true },
            saveAsImage: { show: true },
          },
        },
        legend: {
          data: ["毛坯巡检", "合格率", "调机率"],
        },
        calculable: true,
        // X轴
        xAxis: {
          type: "category", //坐标轴类型,类目轴，适用于离散的类目数据。为该类型时类目数据可自动从 series.data 或 dataset.source 中取，或者可通过 xAxis.data 设置类目数据
          //坐标轴刻度相关设置
          axisTick: {
            show: false, //是否显示坐标轴刻度
            alignWithLabel: true, //为true时，可以让刻度跟底部的标签内容对齐
          },
          data: daysOfWeek, //X轴的刻度数据
          name: "", //坐标轴名称
          nameLocation: "start", //坐标轴名称显示位置
          //坐标轴名称的文字样式
          nameTextStyle: {
            align: "center", //文字水平对齐方式，默认自动
            verticalAlign: "top", //文字垂直对齐方式，默认自动
            lineHeight: 38, //行高
            fontSize: 14, //字体大小
            color: "rgba(0, 0, 0, 1)", //字体颜色
          },
          // 隐藏X轴刻度线
          axisLine: {
            show: false,
          },
          // 隐藏网格线
          splitLine: {
            show: false,
          },
          //坐标轴刻度标签的相关设置
          axisLabel: {
            interval: "auto", //坐标轴刻度标签的显示间隔，在类目轴中有效。可以设置成 0 强制显示所有标签,如果设置为 1，表示『隔一个标签显示一个标签』，如果值为 2，表示『隔两个标签显示一个标签』，以此类推。
          },
        },
        // Y轴
        yAxis: [
          {
            type: "value", //坐标轴类型,'value' 数值轴，适用于连续数据
            //坐标轴刻度标签的相关设置
            axisLabel: {
              formatter: this.formatter, //刻度标签的内容格式器，支持字符串模板和回调函数两种形式。简单讲就是可以自己格式化标签的内容。
            },
            name: "巡检机台数量", //坐标轴名称
            // 隐藏网格线
            splitLine: {
              show: false,
            },
          },
          {
            type: "value",
            name: "",
            min: 0,
            max: 100,
            interval: 5,
            axisLabel: {
              formatter: "{value} %",
            },
          },
          {
            type: "value",
            name: "",
            min: 0,
            max: 100,
            interval: 5,
            axisLabel: {
              formatter: "{value} %",
            },
          },
        ],
        //直角坐标系内绘图网格,简单理解指的就是这个折线图。
        grid: {
          left: 80, //grid 组件离容器左侧的距离
        },
        // 数据
        series: [
          {
            name: "毛坯巡检",
            data: this.weekNum, //折线图要展示的数据
            type: "bar", //数据以什么样的类型展示。line为折线图
            label: {
              // 柱图头部显示值
              show: true,
              position: "inside",
              color: "#fff",
              fontSize: "16px",
              formatter: (params) => {
                return params.value + "台";
              },
            },
            color: ["#409EFF"],
            itemStyle: {
              borderRadius: [10], // 设置圆角弧度，依次为左上角、右上角、右下角、左下角
            },
          },
          {
            name: "合格率",
            type: "line",
            lineStyle: {
              color: "#50C878",
              width: 8,
            },
            color: "#50C878",
            yAxisIndex: 1,
            tooltip: {
              valueFormatter: function (value) {
                return value + " %";
              },
            },
            label: {
              // 柱图头部显示值
              show: true,
              position: "top",
              color: "#50C878",
              fontSize: "16px",
              formatter: "{c}%", // 设置标签显示为百分比
            },
            data: this.hege, //折线图要展示的数据
          },
          {
            name: "调机率",
            type: "line",
            lineStyle: {
              color: "#F48439",
              width: 8,
            },
            color: "#F48439",
            yAxisIndex: 1,
            tooltip: {
              valueFormatter: function (value) {
                return value + " %";
              },
            },
            label: {
              // 柱图头部显示值
              show: true,
              position: "top",
              color: "#F48439",
              fontSize: "16px",
              formatter: "{c}%", // 设置标签显示为百分比
            },
            data: this.tiaoji, //折线图要展示的数据
          },
        ],
      });
      setTimeout(() => {
        myChart.hideLoading();
      }, 500);
      window.onresize = function () {
        myChart.resize();
      };
    },
    // 获取首页数据
    async getHomedata() {
      try {
        const res = await this.$http.getHomeDatas();
        if (res.code !== 200) {
          return this.$notify({
            title: "系统提示",
            message: res.message,
            type: "warning",
          });
        }
        const { orderData, qualifiedRate, inspectionRate } = res.data.list;
        this.weekNum = orderData;
        this.hege = qualifiedRate;
        this.tiaoji = inspectionRate;
        this.initChart();
      } catch (error) {
        return this.$notify({
          title: "系统提示",
          message: error.message,
          type: "warning",
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.analyse-content {
  padding: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  h5 {
    font-size: 14px;
    color: #666;
    padding: 30px 0;
    letter-spacing: 2px;
  }
}
::v-deep .el-progress__text {
  font-size: 16px;
  color: #000 !important;
}
.el-card {
  padding: 0;
  background-color: #feffff;
  color: #000;
  border-radius: 10px;
  h4 {
    letter-spacing: 4px;
    color: #606266;
    font-size: 16px;
  }
}
.text.item {
  color: #000;
  border-radius: 10px;
}
.image-content {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 100%; /* 设置圆角大小 */
  overflow: hidden; /* 隐藏超出圆角范围的内容 */
}
.el-container {
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  .logo {
    font-size: 36px;
    color: #000;
    font-weight: 600;
  }
}
</style>
