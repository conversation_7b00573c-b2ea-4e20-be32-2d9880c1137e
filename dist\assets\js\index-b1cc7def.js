import{o as f,a as u,w as s,b as t,m as o,n as l,d as h,t as g,C as b,z as v,A as x}from"./index-444b28c3.js";import{E as w,a as E}from"./el-col-bd5e5418.js";import{E as y,a as k}from"./el-table-column-fa1764a8.js";import"./el-checkbox-f3df62fa.js";import{E as C}from"./el-tag-29cbefd8.js";import"./el-tooltip-4ed993c7.js";import"./el-scrollbar-af6196f4.js";import{C as r}from"./CountTo-f11294d2.js";import V from"./PancelLine-c832fa75.js";import P from"./progressBar-853bed40.js";import T from"./PancePie-cc1fdb29.js";import B from"./PancelPictorialBar-430cf757.js";import{_ as I}from"./_plugin-vue_export-helper-c27b6911.js";import"./event-fe80fd0c.js";import"./index-e305bb62.js";import"./index-4d7f16ce.js";import"./focus-trap-6de7266c.js";import"./useEcharts-57db09d5.js";import"./el-card-6f02be36.js";const n=a=>(v("data-v-74c57b0f"),a=a(),x(),a),S={class:"card h-300"},$=n(()=>o("div",{class:"title"},"总下机片数",-1)),G={class:"count"},J={class:"graphical"},N={class:"graphical-left"},z={class:"graphical-right"},D={class:"card h-300"},j=n(()=>o("div",{class:"ranking-title"},"排行版",-1)),A=n(()=>o("div",{class:"ranking"},[o("img",{src:"https://img2.baidu.com/it/u=2779580288,2898080432&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500",class:"ranking-user user-left"}),o("img",{src:"https://img2.baidu.com/it/u=2779580288,2898080432&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500",class:"ranking-user"}),o("img",{src:"https://img2.baidu.com/it/u=2779580288,2898080432&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500",class:"ranking-user user-right"})],-1)),O={class:"ranking-table"},R={class:"card",style:{"margin-top":"10px",height:"400px"}},q=b({name:"panel"}),F=Object.assign(q,{setup(a){const _=[{name:"王二蛋",section:"销售部",money:1e4},{name:"张小明",section:"销售部",money:1e4},{name:"坤哥",section:"销售部",money:199999}];return(H,K)=>{const e=w,i=y,m=C,p=k,d=E;return f(),u(d,{gutter:10},{default:s(()=>[t(e,{xs:24,sm:24,md:16,lg:16},{default:s(()=>[o("div",S,[$,o("div",G,[t(l(r),{cutting:!0,endVal:10009988e3})]),t(P),o("div",J,[o("div",N,[t(T)]),o("div",z,[t(V)])])])]),_:1}),t(e,{xs:24,sm:24,md:8,lg:8},{default:s(()=>[o("div",D,[j,A,o("div",O,[t(p,{data:_,style:{width:"100%"}},{default:s(()=>[t(i,{prop:"name",label:"姓名",width:"120"}),t(i,{prop:"section",label:"部门",width:"120"},{default:s(c=>[t(m,{size:"small"},{default:s(()=>[h(g(c.row.section),1)]),_:2},1024)]),_:1}),t(i,{prop:"money",label:"成交金额"},{default:s(c=>[t(l(r),{prefix:"$",cutting:!0,endVal:c.row.money},null,8,["endVal"])]),_:1})]),_:1})])])]),_:1}),t(e,{xs:24,sm:24,md:24,lg:24},{default:s(()=>[o("div",R,[t(B)])]),_:1})]),_:1})}}}),mt=I(F,[["__scopeId","data-v-74c57b0f"]]);export{mt as default};
