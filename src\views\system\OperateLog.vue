<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-07-24 11:35:33
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-16 17:09:30
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\system\OperateLog.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-card>
            <!-- form表单，IP地址搜，用户名，日期 -->
            <el-form :inline="true" :model="form" class="demo-form-inline">
                <el-form-item label="IP地址">
                    <el-input v-model="form.ip" placeholder="IP地址"></el-input>
                </el-form-item>
                <el-form-item label="用户名">
                    <el-input v-model="form.username" placeholder="用户名"></el-input>
                </el-form-item>
                <el-form-item label="日期">
                    <el-date-picker v-model="form.date" type="date" placeholder="选择日期"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search" icon="el-icon-search">查询</el-button>
                </el-form-item>
            </el-form>
            <!-- 主体部分 -->
            <el-table :data="tableData" style="width: 100%" stripe v-loading="loading" element-loading-text="Flyknit"> 
                <el-table-column type="index" label="编号" width="80">
                </el-table-column>
                <el-table-column prop="username" label="登录用户" width="180">
                </el-table-column>
                <el-table-column prop="ip_addr" label="IP地址" width="180">
                </el-table-column>
                <el-table-column prop="browser_info" label="登录设备信息">
                </el-table-column>
                <el-table-column prop="login_time" label="登录时间">
                </el-table-column>
            </el-table>
            <!-- 分页组件 -->
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="form.currentPage" :page-sizes="[10, 20, 30, 40]" :page-size="form.pageSize"
                layout="total, sizes, prev, pager, next, jumper" :total="form.total">
            </el-pagination>
        </el-card>
    </div>
</template>
<script>
export default {
    data() {
        return {
            form: {
                ip: '',
                username: '',
                date: '',
                currentPage: 1,
                pageSize: 10,
                total: 100
            },
            tableData: [],
            loading: true
        }
    },
    mounted() {
        this.getLoginLog()
    },
    methods: {
        // 获取登录日志
        async getLoginLog() {
            try {
                const res = await this.$http.getLoginLogs(this.form)
                if (res.status !== 200) return this.$message.error(res.message)
                const { list, total } = res.data
                this.tableData = list
                this.form.total = total
                setTimeout(() => {
                    this.loading = false
                }, 1000);
            } catch (error) {
                this.loading = false
                return this.$message.error('服务器错误，请稍后重试')
            }
        },
        search() {
            if (this.form.date || this.form.username || this.form.ip) {
                this.loading = true;
                this.getLoginLog();
            } else {
                return false;
            }
        },
        handleSizeChange(val) {
            this.loading = true
            this.pageSize = val
            this.getLoginLog()
        },
        handleCurrentChange(val) {
            this.loading = true
            this.currentPage = val
            this.getLoginLog()
        }
    }
}
</script>
<style lang="scss">
.el-card__body {
    padding: 20px 20px 0px 20px;
}

.el-pagination {
    padding: 20px 0;
}
</style>