<template>
    <div>
        <el-card>
            <!-- 表单、搜索框，状态筛选下拉，搜索按钮，重置按钮，新增按钮 -->
            <div class="filter-container">
                <el-form :inline="true" :model="forms" ref="forms" class="demo-form-inline">
                    <el-form-item label="用户名/邮箱" prop="keywords">
                        <el-input v-model="forms.keywords" placeholder="请输入用户名/邮箱"></el-input>
                    </el-form-item>
                    <el-form-item label="部门" prop="department">
                        <el-select placeholder="请选择部门" v-model="forms.department" clearable>
                            <el-option label="织造车间" value="织造车间"></el-option>
                            <el-option label="整理车间" value="整理车间"></el-option>
                            <el-option label="检验车间" value="检验车间"></el-option>
                            <el-option label="打样车间" value="打样车间"></el-option>
                            <el-option label="综合维修" value="综合维修"></el-option>
                            <el-option label="计划部门" value="计划部门"></el-option>
                            <el-option label="工艺" value="工艺"></el-option>
                            <el-option label="行政办公室" value="行政办公室"></el-option>
                            <el-option label="IT" value="IT"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="type" label="用户类型">
                        <el-select placeholder="请选择类型" v-model="forms.type" clearable>
                            <el-option v-for="(item, index) in roles" :key="index" :label="item.label"
                                :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="status" label="用户状态">
                        <el-select placeholder="请选择状态" v-model="forms.status" clearable>
                            <el-option label="正常" value="正常"></el-option>
                            <el-option label="已禁用" value="已禁用"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="filterSubmit" icon="el-icon-search">搜索</el-button>
                        <el-button @click="resetForm('forms')" icon="el-icon-refresh">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>
        </el-card>
        <el-card style="margin-top: 10px;">
            <el-row>
                <el-col :span="12">
                    <el-button type="success" @click="addUser" icon="el-icon-user" size="small">添加用户</el-button>
                    <el-button type="danger" icon="el-icon-delete" size="small" @click="delAll">批量删除</el-button>
                </el-col>
            </el-row>
            <!-- 中间主体部分，表格，用户名，邮箱，用户类型，用户状态，操作 -->
            <div class="table-container">
                <el-table :data="tableData" stripe border v-loading="loading" element-loading-text="Flyknit"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55"></el-table-column>
                    <el-table-column prop="avatar" label="头像">
                        <template slot-scope="scope">
                            <img :src="scope.row.avatar" style="width: 50px; height: 50px;border-radius:50%;" alt="加载失败">
                        </template>
                    </el-table-column>
                    <el-table-column prop="username" label="用户名"></el-table-column>
                    <el-table-column prop="department" label="部门"></el-table-column>
                    <el-table-column prop="email" label="邮箱"></el-table-column>
                    <el-table-column prop="roles" label="用户角色">
                        <template slot-scope="scope">
                            <el-tag :type="scope.row.roles ? 'success' : 'warning'">{{ scope.row.roles ? scope.row.roles :
                                '暂未分配角色' }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="用户状态">
                        <!-- 如果status===0,正常，如果status===1,已禁用,用switch 组件 -->
                        <template slot-scope="scope">
                            <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0"
                                @change="changeStatus(scope.row)">
                            </el-switch>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="300">
                        <template slot-scope="scope">
                            <el-button type="primary" plain size="mini" icon="el-icon-user"
                                @click="dispathRoles(scope.row)">角色分配</el-button>
                            <el-button type="warning" plain size="mini" icon="el-icon-edit"
                                @click="edit(scope.row)">编辑</el-button>
                            <el-button type="danger" plain size="mini" icon="el-icon-delete"
                                @click="del(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <!-- 分页 -->
            <div class="pagination-container">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="currentPage" :page-sizes="[10, 20, 30, 40]" :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper" :total="total">
                </el-pagination>
            </div>
        </el-card>
        <!-- 抽屉组件 -->
        <el-drawer :title="title" :visible.sync="drawer" :direction="direction" @close="closeHandler">
            <!-- 添加用户表单，用户名，部门，邮箱，用户角色，用户状态 -->
            <el-form :model="form" :rules="rules" ref="form" label-width="80px" class="forms">
                <el-form-item label="用户名" prop="username">
                    <el-input v-model="form.username" placeholder="请输入用户名"></el-input>
                </el-form-item>
                <el-form-item label="邮箱" prop="email">
                    <el-input v-model="form.email" placeholder="请输入用户邮箱"></el-input>
                </el-form-item>
                <el-form-item label="密码" prop="password">
                    <el-input type="password" v-model="form.password" placeholder="请输入密码"></el-input>
                </el-form-item>
                <el-form-item label="部门" prop="department">
                    <el-select placeholder="请选择部门" v-model="form.department" clearable>
                        <el-option label="织造车间" value="织造车间"></el-option>
                        <el-option label="整理车间" value="整理车间"></el-option>
                        <el-option label="检验车间" value="检验车间"></el-option>
                        <el-option label="打样车间" value="打样车间"></el-option>
                        <el-option label="综合维修" value="综合维修"></el-option>
                        <el-option label="计划部门" value="计划部门"></el-option>
                        <el-option label="工艺" value="工艺"></el-option>
                        <el-option label="行政办公室" value="行政办公室"></el-option>
                        <el-option label="IT" value="IT"></el-option>
                    </el-select>
                </el-form-item>
                <!-- 头像上传 -->
                <el-form-item label="头像" prop="avatar">
                    <div class="image-view-title">
                        <el-upload :action="action" :headers="headers" list-type="picture-card" name="files"
                            accept=".jpg,.png,.webp,.jfif" :limit="1" :on-remove="logoRemove" :on-success="logoSuccess"
                            :on-preview="handlepreview" :multiple="false" :on-error="onErr" :before-upload="project"
                            :file-list="logo">
                            <i class="el-icon-plus"></i>
                        </el-upload>
                        <!-- 大图展开 -->
                        <el-dialog :visible.sync="dialogVisible" :modal="false">
                            <img width="100%" :src="dialogImageUrl" alt="上传失败" />
                        </el-dialog>
                    </div>
                </el-form-item>
                <el-form-item style="margin-top: 50px;">
                    <el-button @click="drawer = false">取 消</el-button>
                    <el-button type="primary" @click="saveHandler('form')">{{ btnText }}</el-button>
                </el-form-item>
            </el-form>
        </el-drawer>
        <!-- 分配用户角色弹窗 -->
        <el-dialog title="分配用户角色" :visible.sync="roleDialog" width="30%">
            <!-- 表单，用户名，单选框 -->
            <el-form :model="ruleForm" :rules="roleRules" ref="ruleForm" label-width="80px" class="forms">
                <el-form-item label="用户名" prop="username">
                    <el-input v-model="ruleForm.username" placeholder="请输入用户名" disabled></el-input>
                </el-form-item>
                <el-form-item label="角色列表" prop="role">
                    <el-radio-group v-model="ruleForm.role" style="display: flex;flex-wrap:wrap;">
                        <el-radio v-for="(item, index) in roles" :key="index" :value="item.value" :label="item.label"
                            style="margin: 10px;"></el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="roleDialog = false">取 消</el-button>
                <el-button type="primary" @click="dispatchEvent('ruleForm')">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import config from '../../common/config.js'
export default {
    data() {
        return {
            roleDialog: false,
            title: '添加用户',
            loading: true,
            // 抽屉组件
            btnText: '添 加',
            forms: {
                keywords: '',
                department: '',
                type: '',
                status: ''
            },
            tableData: [],
            currentPage: 1,
            pageSize: 10,
            total: 0,
            drawer: false,
            direction: 'rtl',
            rules: {
                username: [{
                    required: true,
                    message: '请输入用户名',
                    trigger: 'blur',
                    min: 2,
                    max: 16
                }],
                email: [{
                    required: true,
                    message: '请输入用户邮箱',
                    trigger: 'blur'
                }],
                password: [{
                    required: true,
                    message: '请输入用户密码',
                    trigger: 'blur',
                }],
                department: [{
                    required: true,
                    message: '请选择所属部门',
                    trigger: 'blur'
                }],
                avatar: [{
                    required: true,
                    message: '请上传用户头像',
                    trigger: 'blur'
                }],
            },
            form: {
                username: '',
                department: '',
                email: '',
                password: '',
                department: '',
                avatar: '',
            },
            action: config.uploadURL, // 上传地址
            dialogImageUrl: '', // 大图展示链接
            // 上传logoing
            loadmen: false,
            logo: [],// 上传的图片
            headers: {
                Authorization: `Bearer ${localStorage.getItem("accessToken")}`
            },
            baseURL: config.imgPrefix, // 图片地址
            dialogVisible: false, // 大图展示
            roles: [], // 角色列表
            multipleSelection: [], // 多选
            deletList: [], // 删除列表
            ruleForm: {
                username: '',
                role: ''
            },
            roleRules: {
                role: [{
                    required: true,
                    message: '请选择用户角色',
                    trigger: 'blur'
                }]
            }
        }
    },
    created() {
        this.getUsers();
        this.getRoles();
    },
    methods: {
        // 关闭抽屉前的回调
        closeHandler() {
            this.form = {
                username: '',
                department: '',
                email: '',
                password: '',
                department: '',
                avatar: '',
            }
            this.logo = [];
            this.dialogImageUrl = '';
            this.dialogVisible = false;
            this.title = '添加用户';
            this.btnText = '添 加';
        },
        // 分配用户角色
        dispatchEvent(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.dispatchRole();
                } else {
                    return false;
                }
            });
        },
        // 分配用户角色接口
        async dispatchRole() {
            try {
                const res = await this.$http.dispatchRole(this.ruleForm);
                if (res.status !== 200) {
                    return this.$message.error(res.message);
                }
                this.$message.success(res.message);
                this.roleDialog = false;
                this.getUsers();
            } catch (error) {
                return this.$message.error('服务器错误，请稍后重试');
            }
        },
        // 获取用户角色列表
        async getRoles() {
            try {
                const res = await this.$http.getRoles();
                if (res.status !== 200) {
                    return this.$message.error(res.message);
                }
                const { list } = res.data;
                list.forEach(item => {
                    this.roles.push({ label: item.role_name, value: item.role_name });
                });
            } catch (error) {
                return this.$message.error('服务器错误，请稍后重试');
            }
        },
        // 获取管理员列表
        async getUsers() {
            try {
                this.forms.page = this.currentPage;
                this.forms.pageSize = this.pageSize;
                const res = await this.$http.getUsers(this.forms);
                if (res.status !== 200) {
                    return this.$message.error(res.message);
                }
                const { list, total } = res.data;
                this.tableData = list;
                this.total = total;
                setTimeout(() => {
                    this.loading = false;
                }, 500);
            } catch (error) {
                this.loading = false;
                return this.$message.error('服务器错误，请稍后重试');
            }
        },
        // 封装统一提交用户信息方法
        async submitUser() {
            try {
                const res = await this.$http.addUsers(this.form);
                if (res.status !== 200) {
                    return this.$message.error(res.message);
                }
                this.drawer = false;
                this.resetForm('form');
                this.getUsers();
                return this.$message.success(res.message);
            } catch (error) {
                return this.$message.error('服务器错误，请稍后重试');
            }
        },
        // 提交用户信息
        saveHandler(formName) {
            // 验证表单
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.submitUser();
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        // 更改用户状态
        changeStatus(row) {
            const { id, status } = row;
            if (status === 1) {
                this.$confirm('此操作将锁定该用户, 是否继续?', '温馨提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    try {
                        const res = await this.$http.changeStatus({ id, status });
                        if (res.status !== 200) {
                            return this.$message.error(res.message);
                        }
                        this.getUsers();
                        return this.$message.success('锁定用户成功');
                    } catch (error) {
                        return this.$message.error('服务器错误，请稍后重试');
                    }
                }).catch(() => {
                    return false
                });
            } else {
                this.$confirm('此操作将会解锁该用户, 是否继续?', '温馨提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    try {
                        const res = await this.$http.changeStatus({ id, status });
                        if (res.status !== 200) {
                            return this.$message.error(res.message);
                        }
                        this.getUsers();
                        return this.$message.success('解锁用户成功');
                    } catch (error) {
                        return this.$message.error('服务器错误，请稍后重试');
                    }
                }).catch(() => {
                    return false
                });
            }

        },
        filterSubmit() {
            if (this.forms.keywords || this.forms.department || this.forms.type || this.forms.status) {
                this.loading = true;
                this.getUsers();
            } else {
                return false
            }
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.loading = true;
            this.getUsers();
        },
        addUser() {
            this.drawer = true;
            // 重置表单
            this.form = {
                username: '',
                email: '',
                department: '',
                password: '',
                avatar: '',
                id: '',
            };
            this.logo = [];
        },
        // 编辑用户
        edit(row) {
            this.title = '编辑用户';
            this.btnText = '保 存 编 辑';
            this.drawer = true;
            this.form.username = row.username;
            this.form.email = row.email;
            this.form.department = row.department;
            this.form.password = row.password;
            this.form.id = row.id;
            this.logo = []
            this.logo.push({
                url: row.avatar,
                uid: row.id,
            });
            this.form.avatar = this.logo[0].url
        },
        // 删除用户
        del(row) {
            const { id } = row;
            this.deletList.push(id);
            this.delAll();
        },
        // 分配角色
        dispathRoles(row) {
            this.roleDialog = true;
            this.ruleForm.username = row.username;
            this.ruleForm.id = row.id;
        },
        handleSizeChange(val) {
            this.loading = true;
            this.pageSize = val;
            this.getUsers();
        },
        handleCurrentChange(val) {
            this.loading = true;
            this.currentPage = val;
            this.getUsers();
        },
        // 展开大图
        handlepreview(file) {
            this.dialogImageUrl = file.url;
            this.dialogVisible = true;
        },
        // 上传失败
        onErr(e) {
            this.loadmen = false;
            this.$message.error("上传失败,尝试重新上传");
        },
        // 上传时
        project(file) {
            this.loadmen = true;
        },
        // logo移除文件时的钩子
        logoRemove(file, fileList) {
            this.logo = [];
        },
        // 上传成功：logo
        logoSuccess(res, file, fileList) {
            const { url } = res.data;
            this.logo.push({ url: this.baseURL + url, uid: file.uid })//element展示图片时需要数组类型的才能展示
            this.form.avatar = this.logo[0].url;
            this.loadmen = false;
        },
        // 勾选设备事件
        handleSelectionChange(val) {
            // 每次勾选前清空
            this.deletList = [];
            // 如果有值
            if (val.length > 0) {
                this.multipleSelection = val;
                this.multipleSelection.map((item, index) => {
                    this.deletList.push(item.id);
                });
            } else {
                this.multipleSelection = [];
            }
            const s2 = new Set(this.deletList);
            const res1 = [...s2];
            this.deletList = res1;
        },
        // 批量删除
        async delAll() {
            if (this.deletList.length === 0) {
                return this.$message.warning('请选择要删除的用户');
            }
            try {
                this.$confirm('此操作将永久删除, 是否继续?', '温馨提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    const res = await this.$http.delAllUsers({
                        ids: this.deletList
                    });
                    if (res.status !== 200) {
                        return this.$message.error(res.message);
                    }
                    this.getUsers();
                    return this.$message.success(res.message);
                }).catch(() => {
                    this.$message.info('已取消删除');
                });
            } catch (error) {
                return this.$message.error('服务器错误，请稍后重试');
            }
        },
    }
}
</script>
<style lang="scss">
.demo-form-inline {
    display: flex;
    flex-wrap: wrap;
}

.forms {
    padding: 0 20px;
}

.filter-container {
    padding: 0px;
    background-color: #fff;

    .el-form-item {
        margin-right: 20px;
    }
}

.el-card__body {
    padding: 20px 20px 0 20px;
}

.table-container {
    padding: 10px 0;
    background-color: #fff;
}

.pagination-container {
    padding: 0 0 10px 0px;
    background-color: #fff;
}

.footer {}
</style>