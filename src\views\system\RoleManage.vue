<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-08-18 09:48:06
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-22 16:06:06
 * @FilePath: \electronic-filee:\编程项目\maintainceMe-backend\src\views\system\RoleManage.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-card>
            <!-- 表单、搜索框，查询、重置 -->
            <el-form :inline="true" :model="filterForm" ref="filterForm" class="demo-form-inline filter-item">
                <el-form-item label="角色名称" prop="name">
                    <el-input v-model="filterForm.name" placeholder="请输入角色名称" clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="filterSubmit" icon="el-icon-search">查询</el-button>
                    <!-- 添加角色按钮 -->
                    <el-button type="success" icon="el-icon-plus" @click="addRole">添加角色</el-button>
                </el-form-item>
            </el-form>
            <!-- 主体部分 -->
            <el-table :data="tableData" border stripe v-loading="loading" element-loading-text="Flyknit">
                <!-- 编号 -->
                <el-table-column type="index" width="55" label="编号"></el-table-column>
                <el-table-column type="selection" width="55"></el-table-column>
                <el-table-column prop="id" width="55" label="ID"></el-table-column>
                <el-table-column prop="role_name" label="角色名称"></el-table-column>
                <el-table-column prop="role_desc" label="角色描述"></el-table-column>
                <el-table-column prop="create_time" label="创建时间"></el-table-column>
                <el-table-column prop="update_time" label="更新时间"></el-table-column>
                <el-table-column label="操作" width="300">
                    <template slot-scope="scope">
                        <el-button type="primary" size="mini" icon="el-icon-edit-outline"
                            @click="dispatchRights(scope.row)">权限分配</el-button>
                        <el-button type="success" size="mini" icon="el-icon-edit" @click="edit(scope.row)">编辑</el-button>
                        <el-button type="danger" size="mini" icon="el-icon-delete" @click="del(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
                :page-sizes="[10, 20, 30, 40]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="total">
            </el-pagination>
        </el-card>
        <!-- 添加角色弹窗 -->
        <el-dialog :title="title" :visible.sync="addDialogVisible" width="30%">
            <el-form :model="addForm" ref="addForm" :rules="rules" label-width="80px" class="demo-ruleForm">
                <el-form-item label="角色名称" prop="role_name">
                    <el-input v-model="addForm.role_name" placeholder="请输入角色名称" clearable></el-input>
                </el-form-item>
                <el-form-item label="角色描述" prop="role_desc">
                    <el-input v-model="addForm.role_desc" placeholder="请输入角色描述" clearable></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="addDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="SubmitEvent('addForm')">{{ btnText }}</el-button>
            </div>
        </el-dialog>
        <!-- 分配权限右侧<el-drawer></el-drawer> -->
        <el-drawer :title="titles" :visible.sync="drawer" default-expand-all :with-header="true" :direction="direction"
            size="40%" @open="openDrawer" @close="closeHandler">
            <el-tree :data="treeData" show-checkbox default-expand-all node-key="id" ref="tree" icon-class='el-icon-home'
                :check-on-click-node='false' :expand-on-click-node="false"  empty-text="没有数据" :props="defaultProps"
                @check-change="handleCheckChange">
            </el-tree>
            <div class="btn-box">
                <el-button @click="drawer = false">取 消</el-button>
                <el-button type="primary" @click="dispatchLevel">确 定</el-button>
            </div>
        </el-drawer>
    </div>
</template>
<script>
export default {
    data() {
        return {
            title: '添加角色',
            loading: true,
            rules: {
                role_name: [
                    { required: true, message: '请输入角色名称', trigger: 'blur' },
                    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
                ],
                role_desc: [
                    { required: true, message: '请输入角色描述', trigger: 'blur' },
                    { min: 2, max: 50, message: '长度在 2 到 20 个字符', trigger: 'blur' }
                ]
            },
            addDialogVisible: false,
            filterForm: {
                name: ''
            },
            addForm: {
                role_name: '',
                role_desc: ''
            },
            tableData: [],
            currentPage: 1,
            pageSize: 10,
            total: 0,
            btnText: '添 加',
            drawer: false,
            direction: 'rtl',
            defaultProps: {
                children: 'children',
                label: 'title',
                icon: 'icon'
            },
            treeData: [], // 权限列表
            checkedNodes: [], // 选中的节点信息
            checkedKeys: [], // 选中的节点的key
            titles: '分配权限',
        }
    },
    created() {
        this.getRoleList()
        this.getRightsList()
    },
    methods: {
        // 打开抽屉之前的回调
        openDrawer() {
            console.log('打开抽屉');
        },
        // 关闭抽屉之前的回调
        closeHandler() {
            // 清除选中的节点
            this.$refs.tree.setCheckedKeys([]);
        },
        // 分配权限
        dispatchLevel() {
            console.log('分配权限');
        },
        // 获取权限列表
        async getRightsList() {
            try {
                const res = await this.$http.getPermissionList()
                if (res.status !== 200) return this.$message.error(res.message)
                const { list } = res.data
                this.treeData = list
            } catch (error) {
                return this.$message.error('服务器错误，请稍后重试')
            }
        },
        // 获取选中的节点的父节点id以及子节点id
        handleCheckChange() {
            // 获取选中的节点
            const checkedNodes = this.$refs.tree.getCheckedNodes()
            // 获取选中的节点的key
            const checkedKeys = this.$refs.tree.getCheckedKeys()

            this.checkedNodes = checkedNodes
            this.checkedKeys = checkedKeys

            console.log(checkedNodes)
            console.log(checkedKeys)
        },
        // 获取角色列表
        async getRoleList() {
            try {
                this.filterForm.page = this.currentPage
                this.filterForm.pageSize = this.pageSize
                const res = await this.$http.getRoles(this.filterForm)
                if (res.status !== 200) return this.$message.error(res.message)
                const { list, total } = res.data
                this.tableData = list
                this.total = total
                setTimeout(() => {
                    this.loading = false
                }, 500)
            } catch (error) {
                this.loading = false
                return this.$message.error('服务器错误，请稍后重试')
            }
        },
        // 搜索
        filterSubmit() {
            if (this.filterForm.name.trim().length > 0) {
                this.loading = true
                this.getRoleList()
            }
        },
        // 打开添加角色弹窗
        addRole() {
            this.addForm.role_name = ''
            this.addForm.role_desc = ''
            this.title = '添加角色'
            this.addDialogVisible = true
        },
        // 提交表单事件
        SubmitEvent(formName) {
            // 验证表单
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.saveRole()
                } else {
                    return false;
                }
            });
        },
        // 提交保存角色
        async saveRole() {
            try {
                const res = await this.$http.addRoles(this.addForm)
                if (res.status !== 200) return this.$message.error(res.message)
                this.$message.success(res.message)
                this.addDialogVisible = false
                this.getRoleList()
            } catch (error) {
                return this.$message.error('服务器错误，请稍后重试！')
            }
        },
        handleCurrentChange(val) {
            this.loading = true
            this.currentPage = val
            this.getRoleList()
        },
        handleSizeChange(val) {
            this.loading = true
            this.pageSize = val
            this.getRoleList()
        },
        // 编辑角色
        edit(row) {
            this.addForm.role_name = row.role_name
            this.addForm.role_desc = row.role_desc
            this.addForm.id = row.id
            this.addDialogVisible = true
            if (this.addForm.id) {
                this.btnText = '修 改'
                this.title = '编辑角色'
            } else {
                this.btnText = '添 加'
            }
        },
        // 删除角色
        async del(row) {
            try {
                this.$confirm('此操作将永久删除该角色, 是否继续?', '温馨提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    const res = await this.$http.delRoles({ id: row.id })
                    if (res.status !== 200) return this.$message.error(res.message)
                    this.$message.success(res.message)
                    this.getRoleList()
                }).catch(() => {
                    return false
                })
            } catch (error) {
                return this.$message.error('服务器错误，请稍后重试！')
            }
        },
        // 权限分配
        dispatchRights(row) {
            this.titles = row.role_name + '->权限分配'
            this.drawer = true
        },
        checkChange(data, checked, indeterminate) {
            this.defaultCheckedKeys = data
        }
    }
}
</script>
<style lang="scss">
.el-tree-node__content {
    width: 140px !important;
    float: left !important;
    margin-left: 5px !important;
}

.el-tree>div {
    margin: 30px 0 !important;
    background: #fff !important;
}

.el-tree-node.is-current>.el-tree-node__content:hover {
    background: #fff !important;
}

.el-tree-node__content:hover {
    /* background: #fff !important; */
}

.el-tree-node__expand-icon.is-leaf {
    display: none;
}

.el-tree-node__expand-icon.expanded {
    display: none;
}


.el-tree>div {
    margin: 10px 0 !important;
    background: #fff !important;
}

.el-tree-node.is-current>.el-tree-node__content:hover {
    background: #fff !important;
}

.el-tree-node__content:hover {
    /* background: #fff !important; */
}

.el-tree-node__expand-icon.is-leaf {
    display: none;
}

.el-tree-node__expand-icon.expanded {
    display: none;
}

.el-tree-node__content {
    width: 140px !important;
    float: left !important;
    margin-left: 5px !important;
}

.el-tree>div {
    padding: 5px 0 !important;
    height: auto;
    overflow: hidden;
    background: #fff !important;
    border-bottom: #F0F0F0 dotted 1px;
}

/* .el-tree-node.is-current>.el-tree-node__content:hover {
    background: #fff !important;
  } */

.el-tree-node__content:hover {
    /* background: #fff !important; */
}

.el-tree-node__expand-icon.is-leaf {
    display: none;
}

.el-tree-node__expand-icon.expanded {
    display: none;
}

.el-tree-node__children {
    border-left: #ccc dotted 1px;
}

.el-tree-node:hover {
    background: #F5F7FA !important;
}

.el-pagination {
    padding: 20px 0 20px 0;
}

.el-tree {
    padding: 0 15px;
}

.btn-box {
    position: absolute;
    right: 5%;
    bottom: 20px;
}
</style>