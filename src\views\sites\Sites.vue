<template>
  <div>
    <!-- 卡片式图 -->
    <el-row :gutter="1">
      <el-col :span="24" style="margin-bottom: 0rem">
        <el-card shadow="never">
          <el-form inline class="demo-form-inline" style="">
            <el-form-item label="Model名称">
              <el-input
                placeholder="请输入Model名称"
                v-model="keyword"
                clearable
                size="medium"
                @clear="clearHandler"
                style="width: 14rem"
              ></el-input>
            </el-form-item>
            <el-form-item label="Model区域">
              <el-select
                v-model="location"
                @change="changeHandler"
                placeholder="请选择数据源"
                size="medium"
                style="width: 12rem"
              >
                <el-option label="越南" value="0"></el-option>
                <el-option label="华耀" value="1"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="medium"
                @click="searchHandler"
                >查询</el-button
              >
            </el-form-item>
            <!-- 刷新按钮 -->
            <el-form-item>
              <el-button
                type="info"
                icon="el-icon-refresh"
                size="medium"
                @click="getModelLists"
                >刷新</el-button
              >
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      <el-col :span="10">
        <el-card class="card-list" shadow="never">
          <!-- model列表 -->
          <el-row :gutter="10">
            <el-col :span="24">
              <el-col :span="24" class="standard-title">
                <span class="el-icon-s-grid"></span>
                <h5>{{ tableTitle }}</h5>
                <!-- <el-button
                  v-if="multipleSelection.length > 0"
                  type="primary"
                  icon="el-icon-refresh"
                  @click="cancleSelection"
                  style="margin-left: 2rem"
                  size="mini"
                  >取消选中</el-button
                > -->
              </el-col>
            </el-col>
            <el-col :span="24">
              <el-col :span="24">
                <el-table
                  @row-click="clickRow"
                  :data="standardList"
                  resizable
                  stripe
                  border
                  v-loading="loading"
                  element-loading-text="Flyknit"
                  ref="multipleTable"
                  tooltip-effect="dark"
                  style="width: 100%; font-size: 0.85rem"
                  @selection-change="handleSelectionChange"
                >
                  <el-table-column type="selection" width="55">
                  </el-table-column>
                  <el-table-column
                    label="编号"
                    sortable
                    prop="serial"
                    width="100"
                    header-align="center"
                    align="center"
                  >
                  </el-table-column>
                  <el-table-column
                    label="MODEL"
                    sortable
                    prop="model"
                    header-align="center"
                    align="center"
                  ></el-table-column>
                </el-table>
                <!-- 分页 -->
                <div class="page" v-show="total > 0">
                  <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="currentchange"
                    :current-page.sync="queryInfo.currentnum"
                    :page-size="queryInfo.pageSize"
                    layout="total, prev, pager, next"
                    :total="total"
                  >
                  </el-pagination>
                </div>
              </el-col>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
      <el-col :span="14">
        <el-card shadow="never">
          <el-col :span="24" class="standard-titles">
            <el-col :span="4" style="display: flex">
              <span class="el-icon-s-grid"></span>
              <h5>打样Model列表</h5>
            </el-col>
            <el-col
              :span="4"
              v-if="User.type == 2"
              style="
                display: flex;
                justify-content: space-around;
                align-items: center;
                margin-left: 4rem;
              "
            >
              <el-tooltip
                class="item"
                effect="dark"
                content="添加Model"
                placement="bottom"
              >
                <el-button
                  type="text"
                  icon="el-icon-plus"
                  @click="addModel"
                ></el-button>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                content="编辑Model"
                placement="bottom"
              >
                <el-button
                  type="text"
                  icon="el-icon-edit"
                  @click="editHanlder"
                ></el-button>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                content="删除Model"
                placement="bottom"
              >
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click="deletehandler"
                ></el-button>
              </el-tooltip>
            </el-col>
          </el-col>
          <el-table
            :data="modelList"
            border
            stripe
            style="width: 100%; font-size: 0.85rem"
            v-loading="loading"
            element-loading-text="Flyknit"
            @row-click="chooseRow"
            ref="modelTable"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column
              label="编号"
              sortable
              prop="serial"
              width="100"
              header-align="center"
              align="center"
            >
              <!-- 如果没有serial显示NULL -->
              <template slot-scope="scoped">
                <div>
                  <span>{{
                    scoped.row.serial ? scoped.row.serial : "NULL"
                  }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="对标地点"
              sortable
              prop="origin"
              width="110"
              header-align="center"
              align="center"
            >
              <template slot-scope="scoped">
                <div>
                  <span>{{
                    scoped.row.origin == 0
                      ? "越南大货"
                      : scoped.row.origin == 1
                      ? "华耀大货"
                      : "越南打样"
                  }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="MODEL"
              sortable
              prop="model_name"
              header-align="center"
              align="center"
            ></el-table-column>
            <!-- <el-table-column
              label="创建人"
              sortable
              prop="create_user"
              header-align="center"
              align="center"
            ></el-table-column> -->
            <el-table-column
              label="创建时间"
              sortable
              prop="create_time"
              header-align="center"
              align="center"
            ></el-table-column>
          </el-table>
          <!-- 分页 -->
          <div class="page" v-show="totals > 0 && modelList.length > 0">
            <el-pagination
              @size-change="handleSizeChanges"
              @current-change="currentchanges"
              :current-page="current"
              :page-sizes="pageSizess"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="totals"
            >
            </el-pagination>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <!-- 修改打样model弹窗 -->
    <el-dialog
      :title="titles"
      :visible.sync="dialogVisible"
      width="40%"
      :before-close="closeHandler"
    >
      <el-form
        :model="form"
        label-width="100px"
        :rules="rules"
        ref="form"
        size="medium"
      >
        <el-form-item
          label="大货编号"
          prop="serial"
          v-show="multipleSelection.length > 0"
        >
          <el-input
            readonly
            v-model="form.serial"
            placeholder="请输入大货编号"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="大货Model"
          prop="model"
          v-show="multipleSelection.length > 0"
        >
          <el-input
            readonly
            v-model="form.model"
            placeholder="请输入大货Model"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="打样编号"
          prop="serial"
          v-show="multipleSelection.length > 0"
        >
          <el-input
            v-model="form.serial"
            readonly
            placeholder="请输入编号"
          ></el-input>
        </el-form-item>
        <el-form-item label="打样MODEL" prop="model_name">
          <el-input
            v-model="form.model_name"
            placeholder="请输入Model名称"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm">{{
            btnText
          }}</el-button>
          <el-button @click="resetForm" icon="el-icon-refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from "vuex";
export default {
  data() {
    return {
      titles: "修改打样Model", // 修改打样model弹窗标题
      dialogVisible: false, // 修改打样model弹窗
      loading: true, // 表格加载
      checked: false, // 大货列表选中
      checkeds: false, // 打样model列表选中
      // 大货列表
      standardList: [],
      // model列表
      modelList: [],
      location: "0", // 数据源
      tableTitle: "越南大货Model列表",
      queryInfo: {
        pageNum: 1,
        pageSize: 12,
        currentnum: 1,
      },
      form: {
        serial: "",
        model: "",
        model_name: "",
      },
      rules: {
        model_name: [
          {
            required: true,
            message: "请输入要添加的打样MODEL名称",
            trigger: "blur",
          },
        ],
      },
      pageSizes: [12, 16, 20],
      total: 0, // model总数
      totals: 0, // 打样model总数
      page: 1,
      pageSize: 12,
      pageSizess: [12, 16, 20],
      current: 1,
      info: [],
      fit: "cover", // 图片裁剪方式
      url: "https://img0.baidu.com/it/u=1821253856,3774998416&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500", // 默认头像
      keyword: "", // 搜索关键字
      multipleSelection: [], // 选中的model
      isClick: false, // 是否点击
      modelSelection: [], // 选中的打样model
      modelIds: [], // 选中的打样model id
      editModel: {}, // 编辑model
      isEdit: false, // 是否编辑
      btnText: "立即添加", // 按钮文字
    };
  },
  computed: {
    ...mapState({
      User: (state) => state.users,
    }),
  },
  created() {
    this.getModelLists();
    this.getModelsNike();
  },
  methods: {
    // 清除搜索
    clearHandler() {
      this.keyword = "";
      this.queryInfo.keyword = this.keyword;
      this.queryInfo.pageNum = 1;
      this.queryInfo.pageSize = 13;
      this.getModelLists();
    },
    // 搜索model
    searchHandler() {
      this.queryInfo.keyword = this.keyword;
      this.getModelLists();
    },
    // 编辑Model
    editHanlder() {
      console.log('this.editModel', this.editModel);
      if (this.modelSelection.length == 0) {
        this.$notify({
          title: "系统提示",
          message: "请选择要编辑的model",
          type: "warning",
        });
        return;
      }
      this.isEdit = true;
      this.btnText = "提交编辑";
      // console.log("editModel", this.editModel);
      this.form.serial = this.editModel.serial;
      this.form.model = this.editModel.model;
      this.form.model_name = this.editModel.model_name;
      this.form.id = this.editModel.id;
      this.dialogVisible = true;
    },
    // 删除选中的model
    deletehandler() {
      if (this.modelIds.length == 0) {
        this.$notify({
          title: "系统提示",
          message: "请选择要删除的model",
          type: "warning",
        });
        return;
      }
      this.$confirm("是否删除选中的model?", "系统提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.deleteModel();
        })
        .catch(() => {
          this.$notify({
            title: "系统提示",
            message: "已取消删除",
            type: "info",
          });
          // 清除所有选中
          this.modelSelection = [];
          this.toggleSelections();
        });
    },
    // 选中打样model
    chooseRow(row) {
      if (this.modelSelection.includes(row)) {
        // 如果是当前选中，就取消选中
        this.$refs.modelTable.clearSelection();
        this.modelSelection = [];
        this.editModel = {};
        this.modelIds = [];
      } else {
        // 如果不是当前选中，就清空其他选中并选中当前行
        this.$refs.modelTable.clearSelection();
        this.$refs.modelTable.toggleRowSelection(row, true);
        this.modelSelection = [row];
        this.editModel = row;
        this.modelIds.push(row.id);
      }
    },
    // 删除model接口
    async deleteModel() {
      try {
        const res = await this.$http.deleteModel({ ids: this.modelIds });
        if (res.code == 200) {
          this.$notify({
            title: "系统提示",
            message: res.message,
            type: "success",
          });
          // 清除选中
          this.modelSelection = [];
          this.toggleSelections();
          // 获取FCDC model列表
          this.getModelsNike();
          // 清除选中的ids
          this.modelIds = [];
        } else {
          this.$notify({
            title: "系统提示",
            message: res.message,
            type: "warning",
          });
        }
      } catch (error) {
        this.$notify({
          title: "系统提示",
          message: error.message,
          type: "warning",
        });
      }
    },
    // 关闭弹窗
    closeHandler(done) {
      // 清除所有选中
      this.multipleSelection = [];
      this.toggleSelection();
      this.isClick = false;
      this.dialogVisible = false;
      this.form = {
        serial: "",
        model: "",
        model_name: "",
      };
      // 清除选中modelTable
      this.modelSelection = [];
      this.toggleSelections();
      done();
    },
    // 添加model弹窗开关
    addModel() {
      this.isClick = true;
      // 如果没有选择大货model，就是普通添加
      if (this.multipleSelection.length == 0) {
        this.titles = "添加打样Model";
        this.dialogVisible = true;
      } else {
        // 如果选择了大货model，就是关联添加
        this.titles = "当前大货Model：" + this.multipleSelection[0].model;
        this.form.serial = this.multipleSelection[0].serial;
        this.form.model = this.multipleSelection[0].model;
        this.form.location = this.location;
        this.dialogVisible = true;
      }
    },
    // 提交保存model
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.submitFormHandler();
        } else {
          return false;
        }
      });
    },
    // 封装全局提交保存model方法
    async submitFormHandler() {
      // console.log('this.form', this.form);
      try {
        const res = await this.$http.addModel(this.form);
        if (res.code == 200) {
          this.dialogVisible = false;
          this.$notify({
            title: "系统提示",
            message: res.message,
            type: "success",
          });
          // 清除选中
          this.multipleSelection = [];
          this.toggleSelection();
          // 获取FCDC model列表
          this.getModelsNike();
          // 清除表单
          this.resetForm();
          this.form = {
            serial: "",
            model: "",
            model_name: "",
          };
          // 隐藏弹窗
          this.dialogVisible = false;
          this.isClick = false;
          if (this.isEdit) {
            this.isEdit = false;
            this.btnText = "立即添加";
          }
        } else {
          this.$notify({
            title: "系统提示",
            message: res.message,
            type: "warning",
          });
        }
      } catch (error) {
        this.$notify({
          title: "系统提示",
          message: error.message,
          type: "warning",
        });
      }
    },
    // 重置表单
    resetForm() {
      this.$refs.form.resetFields();
    },
    // 点击行
    clickRow(row) {
      if (this.multipleSelection.includes(row)) {
        // 如果是当前选中，就取消选中
        this.$refs.multipleTable.clearSelection();
        this.multipleSelection = [];
        // 获取FCDC model列表
        this.getModelsNike();
      } else {
        // 如果不是当前选中，就清空其他选中并选中当前行
        this.$refs.multipleTable.clearSelection();
        this.$refs.multipleTable.toggleRowSelection(row, true);
        this.multipleSelection = [row];
        // 获取FCDC model列表
        this.getModelsNike();
      }
    },
    // 取消选中
    cancleSelection() {
      // 清除所有选中
      this.multipleSelection = [];
      this.toggleSelection();
      // 获取FCDC model列表
      this.getModelsNike();
    },
    // 大货model选中
    toggleSelection(rows) {
      if (rows) {
        rows.forEach((row) => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.multipleTable.clearSelection();
      }
    },
    // 样品toggle
    toggleSelections(rows) {
      if (rows) {
        rows.forEach((row) => {
          this.$refs.modelTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.modelTable.clearSelection();
      }
    },
    // 选中当前行
    handleSelectionChange(val) {
      // console.log('选中', val);
      // this.multipleSelection = val;
      // if (val.length > 1) {
      //   this.$refs.multipleTable.clearSelection();
      //   this.$refs.multipleTable.toggleRowSelection(val[val.length - 1]);
      // }
    },
    // 切换数据源
    changeHandler(e) {
      this.queryInfo.pageNum = 1;
      this.location = e;
      if (e == 0) {
        this.tableTitle = "越南大货Model列表";
      } else {
        this.tableTitle = "华耀大货Model列表";
      }
      this.loading = true;
      this.queryInfo.pageNum = 1;
      this.queryInfo.pageSize = 12;
      this.queryInfo.currentnum = 1;
      this.page = 1;
      this.pageSize = 12;
      this.getModelLists();
      this.getModelsNike();
    },
    // 获取FCDC model列表
    async getModelsNike() {
      this.loading = true;
      try {
        const res = await this.$http.getFcdcModelLists({
          page: this.page,
          pageSize: this.pageSize,
          serial:
            this.multipleSelection && this.multipleSelection.length > 0
              ? this.multipleSelection[0].serial
              : "",
          model:
            this.multipleSelection && this.multipleSelection.length > 0
              ? this.multipleSelection[0].model
              : "",
          location: this.location,
        });
        if (res.code !== 200) {
          this.modelList = [];
          this.total = 0;
          this.$notify({
            title: "警告",
            message: res.message,
            type: "warning",
          });
        } else {
          const { result, total } = res.data.list;
          this.modelList = result;
          this.totals = total;
        }
      } catch (error) {
        // console.log("error", error.message);
        this.$notify({
          title: "系统提示",
          message: error.message,
          type: "warning",
        });
      } finally {
        setTimeout(() => {
          this.loading = false;
        }, 500);
      }
    },
    // 获取越南/华耀model列表
    async getModelLists() {
      try {
        this.queryInfo.location = this.location;
        const res = await this.$http.getModelLists(this.queryInfo);
        if (res.code !== 200) {
          this.modelList = [];
          this.total = 0;
          this.$notify({
            title: "警告",
            message: res.message,
            type: "warning",
          });
        } else {
          const { result, total } = res.data.list;
          this.standardList = result;
          this.total = total;
        }
      } catch (error) {
        this.$notify({
          title: "系统提示",
          message: error.message,
          type: "warning",
        });
      } finally {
        setTimeout(() => {
          this.loading = false;
        }, 500);
      }
    },
    // 左侧model分页
    handleSizeChange(val) {
      this.loading = true;
      this.queryInfo.pageSize = val;
      this.getModelLists();
    },
    // 左侧model分页
    currentchange(val) {
      this.loading = true;
      this.queryInfo.pageNum = val;
      this.getModelLists();
    },
    // 打样model分页
    handleSizeChanges(val) {
      console.log("val", val);
      this.loading = true;
      this.pageSize = val;
      this.getModelsNike();
    },
    // 打样model分页
    currentchanges(val) {
      console.log("val", val);
      this.loading = true;
      this.page = val;
      this.getModelsNike();
    },
  },
};
</script>
<style lang="scss" scoped>
.el-card ::v-deep .el-card__body {
  padding-bottom: 0px;
  box-shadow: none !important;
}

.standard-title {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin: 10px 0;
  h5 {
    font-size: 1rem;
    font-weight: bold;
    letter-spacing: 1px;
    color: #909399;
  }
  .el-icon-s-grid {
    font-size: 20px;
    color: #909399;
    margin-right: 10px;
  }
}
.standard-titles {
  display: flex;
  justify-content: flex-start;
  align-items: center;

  h5 {
    font-size: 1rem;
    font-weight: bold;
    letter-spacing: 1px;
    color: #909399;
  }
  .el-icon-s-grid {
    font-size: 20px;
    color: #909399;
    margin-right: 10px;
  }
}
.el-list-enter-active,
.el-list-leave-active {
  -webkit-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
}
.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

::vue-deep.el-list-enter-active,
::vue-deep.el-list-leave-active {
  transition: none;
}

::vue-deep.el-list-enter,
::vue-deep.el-list-leave-active {
  opacity: 0;
}

::vue-deep.hide .el-upload--picture-card {
  display: none;
}
.submit-btn {
  background-color: #409eff;
  color: #fff;
  border: none;
}
.search-btn {
  background-color: #409eff;
  color: #fff;
  border: none;
}
.reset-btn {
  background-color: #409eff;
  color: #000;
}
.refresh-btn {
  background-color: #409eff;
  color: #000;
}

.btn-box {
  display: flex;
  padding: 50px 0 0 0;
  justify-content: flex-start;
  align-items: center;
  .el-button {
    width: 100px;
  }
}

.input-box {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  width: 400px;
}

.image-view-title {
  margin: 10px 20px 10px 10px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.image-view-title img {
  object-fit: cover;
}

.image-list {
  width: 100px;
}

.image-button {
  text-align: center;
  padding: 120px 0;
}

.status-text {
  color: #30c82b;
  font-weight: bold;
  border: 1px solid #30c82b;
  display: block;
  width: 60px;
  height: 20px;
  margin: 0 auto;
  line-height: 20px;
  text-align: center;
}

.wait-complete {
  color: #ccc;
  font-weight: bold;
  border: 1px solid #ccc;
  display: block;
  width: 60px;
  height: 20px;
  margin: 0 auto;
  line-height: 20px;
  text-align: center;
}

.page {
  padding: 20px 0 20px 0;
}

.el-image__inner {
  border-radius: 50%;
}

.el-table .cell {
  text-align: center;
}
</style>