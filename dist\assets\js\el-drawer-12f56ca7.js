import{Q as E,_ as S,C as R,a5 as g,af as z,c as m,i as y,R as $,aZ as B,B as n,o,a as D,b as l,w as i,e as I,m as w,H as s,ad as L,y as T,g as t,S as d,t as A,f as r,a3 as H,aK as N,aL as P,U as q}from"./index-444b28c3.js";import{E as U}from"./index-eba6e623.js";import{b as V,c as F,u as M}from"./el-overlay-9f4b42b1.js";import{E as O}from"./focus-trap-6de7266c.js";import{u as K}from"./el-button-9bbdfcf9.js";import{u as Q}from"./index-e305bb62.js";const Z=E({...V,direction:{type:String,default:"rtl",values:["ltr","rtl","ttb","btt"]},size:{type:[String,Number],default:"30%"},withHeader:{type:Boolean,default:!0},modalFade:{type:Boolean,default:!0}}),j=F,G=R({name:"ElDrawer",components:{ElOverlay:U,ElFocusTrap:O,ElIcon:g,Close:z},props:Z,emits:j,setup(e,{slots:a}){K({scope:"el-drawer",from:"the title slot",replacement:"the header slot",version:"3.0.0",ref:"https://element-plus.org/en-US/component/drawer.html#slots"},m(()=>!!a.title));const f=y(),c=y(),b=$("drawer"),{t:v}=Q(),u=m(()=>e.direction==="rtl"||e.direction==="ltr"),p=m(()=>B(e.size));return{...M(e,f),drawerRef:f,focusStartRef:c,isHorizontal:u,drawerSize:p,ns:b,t:v}}}),J=["aria-label","aria-labelledby","aria-describedby"],W=["id"],X=["aria-label"],Y=["id"];function _(e,a,f,c,b,v){const u=n("close"),p=n("el-icon"),h=n("el-focus-trap"),C=n("el-overlay");return o(),D(P,{to:"body",disabled:!e.appendToBody},[l(N,{name:e.ns.b("fade"),onAfterEnter:e.afterEnter,onAfterLeave:e.afterLeave,onBeforeLeave:e.beforeLeave,persisted:""},{default:i(()=>[I(l(C,{mask:e.modal,"overlay-class":e.modalClass,"z-index":e.zIndex,onClick:e.onModalClick},{default:i(()=>[l(h,{loop:"",trapped:e.visible,"focus-trap-el":e.drawerRef,"focus-start-el":e.focusStartRef,onReleaseRequested:e.onCloseRequested},{default:i(()=>[w("div",{ref:"drawerRef","aria-modal":"true","aria-label":e.title||void 0,"aria-labelledby":e.title?void 0:e.titleId,"aria-describedby":e.bodyId,class:s([e.ns.b(),e.direction,e.visible&&"open",e.customClass]),style:L(e.isHorizontal?"width: "+e.drawerSize:"height: "+e.drawerSize),role:"dialog",onClick:a[1]||(a[1]=T(()=>{},["stop"]))},[w("span",{ref:"focusStartRef",class:s(e.ns.e("sr-focus")),tabindex:"-1"},null,2),e.withHeader?(o(),t("header",{key:0,class:s(e.ns.e("header"))},[e.$slots.title?d(e.$slots,"title",{key:1},()=>[r(" DEPRECATED SLOT ")]):d(e.$slots,"header",{key:0,close:e.handleClose,titleId:e.titleId,titleClass:e.ns.e("title")},()=>[e.$slots.title?r("v-if",!0):(o(),t("span",{key:0,id:e.titleId,role:"heading",class:s(e.ns.e("title"))},A(e.title),11,W))]),e.showClose?(o(),t("button",{key:2,"aria-label":e.t("el.drawer.close"),class:s(e.ns.e("close-btn")),type:"button",onClick:a[0]||(a[0]=(...k)=>e.handleClose&&e.handleClose(...k))},[l(p,{class:s(e.ns.e("close"))},{default:i(()=>[l(u)]),_:1},8,["class"])],10,X)):r("v-if",!0)],2)):r("v-if",!0),e.rendered?(o(),t("div",{key:1,id:e.bodyId,class:s(e.ns.e("body"))},[d(e.$slots,"default")],10,Y)):r("v-if",!0),e.$slots.footer?(o(),t("div",{key:2,class:s(e.ns.e("footer"))},[d(e.$slots,"footer")],2)):r("v-if",!0)],14,J)]),_:3},8,["trapped","focus-trap-el","focus-start-el","onReleaseRequested"])]),_:3},8,["mask","overlay-class","z-index","onClick"]),[[H,e.visible]])]),_:3},8,["name","onAfterEnter","onAfterLeave","onBeforeLeave"])],8,["disabled"])}var x=S(G,[["render",_],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/drawer/src/drawer.vue"]]);const te=q(x);export{te as E};
