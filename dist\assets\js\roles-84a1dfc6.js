import{c8 as ke,j as ie,bz as Ue,_ as Ee,C as we,R as se,X as re,D as Te,a9 as ae,i as w,ca as me,c9 as We,a5 as Ye,cd as Ge,ab as Be,a8 as _,G as ve,cP as Qe,B as W,e as he,a3 as Ne,o as I,g as Z,m as Y,a as P,w as E,L as Xe,H as M,y as V,f as oe,b as k,ad as Je,F as Le,h as ze,bk as Ze,aS as et,aD as Ae,k as $e,bN as tt,aM as nt,aN as U,aa as ot,c as rt,c5 as at,t as st,cQ as dt,E as B,cR as lt,n as J,s as it,d as $,p as ct,cM as ut,bq as ht,cN as ft,ar as pt,cS as gt,cT as yt,cU as mt,cV as kt,cW as vt}from"./index-444b28c3.js";/* empty css                   */import{E as Nt}from"./el-drawer-12f56ca7.js";import"./el-overlay-9f4b42b1.js";import{E as Ct}from"./el-checkbox-f3df62fa.js";import{E as bt}from"./el-dialog-e35c112f.js";import{E as Et,a as wt}from"./el-form-10dec954.js";import"./el-form-item-4ed993c7.js";import{E as Dt}from"./el-card-6f02be36.js";import{E as Kt}from"./el-pagination-6fc73be7.js";import{E as xt}from"./el-input-6b488ec7.js";import"./el-tag-29cbefd8.js";import"./el-select-980e5896.js";import"./el-scrollbar-af6196f4.js";import{E as St,a as Tt}from"./el-table-column-fa1764a8.js";import"./el-tooltip-4ed993c7.js";import{E as At,a as It}from"./el-col-bd5e5418.js";import{E as Bt}from"./el-button-9bbdfcf9.js";import{E as Lt}from"./index-be3c1320.js";import{E as ne}from"./index-df5d5edc.js";import{_ as zt}from"./index-73693531.js";import{u as $t}from"./index-e305bb62.js";import{v as _t}from"./directive-ce1b251f.js";import"./index-eba6e623.js";import"./event-fe80fd0c.js";import"./scroll-a66dde9b.js";import"./vnode-b9ec7db4.js";import"./focus-trap-6de7266c.js";import"./index-4d7f16ce.js";import"./index-11a84590.js";import"./_Uint8Array-55276dff.js";import"./validator-e4131fc3.js";const ee="$treeNodeId",Ie=function(t,e){!e||e[ee]||Object.defineProperty(e,ee,{value:t.id,enumerable:!1,configurable:!1,writable:!1})},De=function(t,e){return t?e[t]:e[ee]},Ce=(t,e,o)=>{const r=t.value.currentNode;o();const n=t.value.currentNode;r!==n&&e("current-change",n||null,n)},be=t=>{let e=!0,o=!0,r=!0;for(let n=0,a=t.length;n<a;n++){const d=t[n];(d.checked!==!0||d.indeterminate)&&(e=!1,d.disabled||(r=!1)),(d.checked!==!1||d.indeterminate)&&(o=!1)}return{all:e,none:o,allWithoutDisable:r,half:!e&&!o}},ce=function(t){if(t.childNodes.length===0||t.loading)return;const{all:e,none:o,half:r}=be(t.childNodes);e?(t.checked=!0,t.indeterminate=!1):r?(t.checked=!1,t.indeterminate=!0):o&&(t.checked=!1,t.indeterminate=!1);const n=t.parent;!n||n.level===0||t.store.checkStrictly||ce(n)},le=function(t,e){const o=t.store.props,r=t.data||{},n=o[e];if(typeof n=="function")return n(r,t);if(typeof n=="string")return r[n];if(typeof n>"u"){const a=r[e];return a===void 0?"":a}};let Ft=0;class G{constructor(e){this.id=Ft++,this.text=null,this.checked=!1,this.indeterminate=!1,this.data=null,this.expanded=!1,this.parent=null,this.visible=!0,this.isCurrent=!1,this.canFocus=!1;for(const o in e)ke(e,o)&&(this[o]=e[o]);this.level=0,this.loaded=!1,this.childNodes=[],this.loading=!1,this.parent&&(this.level=this.parent.level+1)}initialize(){const e=this.store;if(!e)throw new Error("[Node]store is required!");e.registerNode(this);const o=e.props;if(o&&typeof o.isLeaf<"u"){const a=le(this,"isLeaf");typeof a=="boolean"&&(this.isLeafByUser=a)}if(e.lazy!==!0&&this.data?(this.setData(this.data),e.defaultExpandAll&&(this.expanded=!0,this.canFocus=!0)):this.level>0&&e.lazy&&e.defaultExpandAll&&this.expand(),Array.isArray(this.data)||Ie(this,this.data),!this.data)return;const r=e.defaultExpandedKeys,n=e.key;n&&r&&r.includes(this.key)&&this.expand(null,e.autoExpandParent),n&&e.currentNodeKey!==void 0&&this.key===e.currentNodeKey&&(e.currentNode=this,e.currentNode.isCurrent=!0),e.lazy&&e._initDefaultCheckedNode(this),this.updateLeafState(),this.parent&&(this.level===1||this.parent.expanded===!0)&&(this.canFocus=!0)}setData(e){Array.isArray(e)||Ie(this,e),this.data=e,this.childNodes=[];let o;this.level===0&&Array.isArray(this.data)?o=this.data:o=le(this,"children")||[];for(let r=0,n=o.length;r<n;r++)this.insertChild({data:o[r]})}get label(){return le(this,"label")}get key(){const e=this.store.key;return this.data?this.data[e]:null}get disabled(){return le(this,"disabled")}get nextSibling(){const e=this.parent;if(e){const o=e.childNodes.indexOf(this);if(o>-1)return e.childNodes[o+1]}return null}get previousSibling(){const e=this.parent;if(e){const o=e.childNodes.indexOf(this);if(o>-1)return o>0?e.childNodes[o-1]:null}return null}contains(e,o=!0){return(this.childNodes||[]).some(r=>r===e||o&&r.contains(e))}remove(){const e=this.parent;e&&e.removeChild(this)}insertChild(e,o,r){if(!e)throw new Error("InsertChild error: child is required.");if(!(e instanceof G)){if(!r){const n=this.getChildren(!0);n.includes(e.data)||(typeof o>"u"||o<0?n.push(e.data):n.splice(o,0,e.data))}Object.assign(e,{parent:this,store:this.store}),e=ie(new G(e)),e instanceof G&&e.initialize()}e.level=this.level+1,typeof o>"u"||o<0?this.childNodes.push(e):this.childNodes.splice(o,0,e),this.updateLeafState()}insertBefore(e,o){let r;o&&(r=this.childNodes.indexOf(o)),this.insertChild(e,r)}insertAfter(e,o){let r;o&&(r=this.childNodes.indexOf(o),r!==-1&&(r+=1)),this.insertChild(e,r)}removeChild(e){const o=this.getChildren()||[],r=o.indexOf(e.data);r>-1&&o.splice(r,1);const n=this.childNodes.indexOf(e);n>-1&&(this.store&&this.store.deregisterNode(e),e.parent=null,this.childNodes.splice(n,1)),this.updateLeafState()}removeChildByData(e){let o=null;for(let r=0;r<this.childNodes.length;r++)if(this.childNodes[r].data===e){o=this.childNodes[r];break}o&&this.removeChild(o)}expand(e,o){const r=()=>{if(o){let n=this.parent;for(;n.level>0;)n.expanded=!0,n=n.parent}this.expanded=!0,e&&e(),this.childNodes.forEach(n=>{n.canFocus=!0})};this.shouldLoadData()?this.loadData(n=>{Array.isArray(n)&&(this.checked?this.setChecked(!0,!0):this.store.checkStrictly||ce(this),r())}):r()}doCreateChildren(e,o={}){e.forEach(r=>{this.insertChild(Object.assign({data:r},o),void 0,!0)})}collapse(){this.expanded=!1,this.childNodes.forEach(e=>{e.canFocus=!1})}shouldLoadData(){return this.store.lazy===!0&&this.store.load&&!this.loaded}updateLeafState(){if(this.store.lazy===!0&&this.loaded!==!0&&typeof this.isLeafByUser<"u"){this.isLeaf=this.isLeafByUser;return}const e=this.childNodes;if(!this.store.lazy||this.store.lazy===!0&&this.loaded===!0){this.isLeaf=!e||e.length===0;return}this.isLeaf=!1}setChecked(e,o,r,n){if(this.indeterminate=e==="half",this.checked=e===!0,this.store.checkStrictly)return;if(!(this.shouldLoadData()&&!this.store.checkDescendants)){const{all:d,allWithoutDisable:l}=be(this.childNodes);!this.isLeaf&&!d&&l&&(this.checked=!1,e=!1);const v=()=>{if(o){const g=this.childNodes;for(let s=0,y=g.length;s<y;s++){const N=g[s];n=n||e!==!1;const x=N.disabled?N.checked:n;N.setChecked(x,o,!0,n)}const{half:p,all:c}=be(g);c||(this.checked=c,this.indeterminate=p)}};if(this.shouldLoadData()){this.loadData(()=>{v(),ce(this)},{checked:e!==!1});return}else v()}const a=this.parent;!a||a.level===0||r||ce(a)}getChildren(e=!1){if(this.level===0)return this.data;const o=this.data;if(!o)return null;const r=this.store.props;let n="children";return r&&(n=r.children||"children"),o[n]===void 0&&(o[n]=null),e&&!o[n]&&(o[n]=[]),o[n]}updateChildren(){const e=this.getChildren()||[],o=this.childNodes.map(a=>a.data),r={},n=[];e.forEach((a,d)=>{const l=a[ee];!!l&&o.findIndex(g=>g[ee]===l)>=0?r[l]={index:d,data:a}:n.push({index:d,data:a})}),this.store.lazy||o.forEach(a=>{r[a[ee]]||this.removeChildByData(a)}),n.forEach(({index:a,data:d})=>{this.insertChild({data:d},a)}),this.updateLeafState()}loadData(e,o={}){if(this.store.lazy===!0&&this.store.load&&!this.loaded&&(!this.loading||Object.keys(o).length)){this.loading=!0;const r=n=>{this.childNodes=[],this.doCreateChildren(n,o),this.loaded=!0,this.loading=!1,this.updateLeafState(),e&&e.call(this,n)};this.store.load(this,r)}else e&&e.call(this)}}class Mt{constructor(e){this.currentNode=null,this.currentNodeKey=null;for(const o in e)ke(e,o)&&(this[o]=e[o]);this.nodesMap={}}initialize(){if(this.root=new G({data:this.data,store:this}),this.root.initialize(),this.lazy&&this.load){const e=this.load;e(this.root,o=>{this.root.doCreateChildren(o),this._initDefaultCheckedNodes()})}else this._initDefaultCheckedNodes()}filter(e){const o=this.filterNodeMethod,r=this.lazy,n=function(a){const d=a.root?a.root.childNodes:a.childNodes;if(d.forEach(l=>{l.visible=o.call(l,e,l.data,l),n(l)}),!a.visible&&d.length){let l=!0;l=!d.some(v=>v.visible),a.root?a.root.visible=l===!1:a.visible=l===!1}e&&a.visible&&!a.isLeaf&&!r&&a.expand()};n(this)}setData(e){e!==this.root.data?(this.root.setData(e),this._initDefaultCheckedNodes()):this.root.updateChildren()}getNode(e){if(e instanceof G)return e;const o=Ue(e)?De(this.key,e):e;return this.nodesMap[o]||null}insertBefore(e,o){const r=this.getNode(o);r.parent.insertBefore({data:e},r)}insertAfter(e,o){const r=this.getNode(o);r.parent.insertAfter({data:e},r)}remove(e){const o=this.getNode(e);o&&o.parent&&(o===this.currentNode&&(this.currentNode=null),o.parent.removeChild(o))}append(e,o){const r=o?this.getNode(o):this.root;r&&r.insertChild({data:e})}_initDefaultCheckedNodes(){const e=this.defaultCheckedKeys||[],o=this.nodesMap;e.forEach(r=>{const n=o[r];n&&n.setChecked(!0,!this.checkStrictly)})}_initDefaultCheckedNode(e){(this.defaultCheckedKeys||[]).includes(e.key)&&e.setChecked(!0,!this.checkStrictly)}setDefaultCheckedKey(e){e!==this.defaultCheckedKeys&&(this.defaultCheckedKeys=e,this._initDefaultCheckedNodes())}registerNode(e){const o=this.key;!e||!e.data||(o?e.key!==void 0&&(this.nodesMap[e.key]=e):this.nodesMap[e.id]=e)}deregisterNode(e){!this.key||!e||!e.data||(e.childNodes.forEach(r=>{this.deregisterNode(r)}),delete this.nodesMap[e.key])}getCheckedNodes(e=!1,o=!1){const r=[],n=function(a){(a.root?a.root.childNodes:a.childNodes).forEach(l=>{(l.checked||o&&l.indeterminate)&&(!e||e&&l.isLeaf)&&r.push(l.data),n(l)})};return n(this),r}getCheckedKeys(e=!1){return this.getCheckedNodes(e).map(o=>(o||{})[this.key])}getHalfCheckedNodes(){const e=[],o=function(r){(r.root?r.root.childNodes:r.childNodes).forEach(a=>{a.indeterminate&&e.push(a.data),o(a)})};return o(this),e}getHalfCheckedKeys(){return this.getHalfCheckedNodes().map(e=>(e||{})[this.key])}_getAllNodes(){const e=[],o=this.nodesMap;for(const r in o)ke(o,r)&&e.push(o[r]);return e}updateChildren(e,o){const r=this.nodesMap[e];if(!r)return;const n=r.childNodes;for(let a=n.length-1;a>=0;a--){const d=n[a];this.remove(d.data)}for(let a=0,d=o.length;a<d;a++){const l=o[a];this.append(l,r.data)}}_setCheckedKeys(e,o=!1,r){const n=this._getAllNodes().sort((l,v)=>v.level-l.level),a=Object.create(null),d=Object.keys(r);n.forEach(l=>l.setChecked(!1,!1));for(let l=0,v=n.length;l<v;l++){const g=n[l],p=g.data[e].toString();if(!d.includes(p)){g.checked&&!a[p]&&g.setChecked(!1,!1);continue}let s=g.parent;for(;s&&s.level>0;)a[s.data[e]]=!0,s=s.parent;if(g.isLeaf||this.checkStrictly){g.setChecked(!0,!1);continue}if(g.setChecked(!0,!0),o){g.setChecked(!1,!1);const y=function(N){N.childNodes.forEach(D=>{D.isLeaf||D.setChecked(!1,!1),y(D)})};y(g)}}}setCheckedNodes(e,o=!1){const r=this.key,n={};e.forEach(a=>{n[(a||{})[r]]=!0}),this._setCheckedKeys(r,o,n)}setCheckedKeys(e,o=!1){this.defaultCheckedKeys=e;const r=this.key,n={};e.forEach(a=>{n[a]=!0}),this._setCheckedKeys(r,o,n)}setDefaultExpandedKeys(e){e=e||[],this.defaultExpandedKeys=e,e.forEach(o=>{const r=this.getNode(o);r&&r.expand(null,this.autoExpandParent)})}setChecked(e,o,r){const n=this.getNode(e);n&&n.setChecked(!!o,r)}getCurrentNode(){return this.currentNode}setCurrentNode(e){const o=this.currentNode;o&&(o.isCurrent=!1),this.currentNode=e,this.currentNode.isCurrent=!0}setUserCurrentNode(e,o=!0){const r=e[this.key],n=this.nodesMap[r];this.setCurrentNode(n),o&&this.currentNode.level>1&&this.currentNode.parent.expand(null,!0)}setCurrentNodeKey(e,o=!0){if(e==null){this.currentNode&&(this.currentNode.isCurrent=!1),this.currentNode=null;return}const r=this.getNode(e);r&&(this.setCurrentNode(r),o&&this.currentNode.level>1&&this.currentNode.parent.expand(null,!0))}}const Ot=we({name:"ElTreeNodeContent",props:{node:{type:Object,required:!0},renderContent:Function},setup(t){const e=se("tree"),o=re("NodeInstance"),r=re("RootTree");return()=>{const n=t.node,{data:a,store:d}=n;return t.renderContent?t.renderContent(Te,{_self:o,node:n,data:a,store:d}):Te("span",{class:e.be("node","label")},[r.ctx.slots.default?r.ctx.slots.default({node:n,data:a}):n.label])}}});var Rt=Ee(Ot,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/tree/src/tree-node-content.vue"]]);function _e(t){const e=re("TreeNodeMap",null),o={treeNodeExpand:r=>{t.node!==r&&t.node.collapse()},children:[]};return e&&e.children.push(o),ae("TreeNodeMap",o),{broadcastExpanded:r=>{if(t.accordion)for(const n of o.children)n.treeNodeExpand(r)}}}const Fe=Symbol("dragEvents");function qt({props:t,ctx:e,el$:o,dropIndicator$:r,store:n}){const a=se("tree"),d=w({showDropIndicator:!1,draggingNode:null,dropNode:null,allowDrop:!0,dropType:null});return ae(Fe,{treeNodeDragStart:({event:p,treeNode:c})=>{if(typeof t.allowDrag=="function"&&!t.allowDrag(c.node))return p.preventDefault(),!1;p.dataTransfer.effectAllowed="move";try{p.dataTransfer.setData("text/plain","")}catch{}d.value.draggingNode=c,e.emit("node-drag-start",c.node,p)},treeNodeDragOver:({event:p,treeNode:c})=>{const s=c,y=d.value.dropNode;y&&y!==s&&me(y.$el,a.is("drop-inner"));const N=d.value.draggingNode;if(!N||!s)return;let x=!0,D=!0,m=!0,F=!0;typeof t.allowDrop=="function"&&(x=t.allowDrop(N.node,s.node,"prev"),F=D=t.allowDrop(N.node,s.node,"inner"),m=t.allowDrop(N.node,s.node,"next")),p.dataTransfer.dropEffect=D||x||m?"move":"none",(x||D||m)&&y!==s&&(y&&e.emit("node-drag-leave",N.node,y.node,p),e.emit("node-drag-enter",N.node,s.node,p)),(x||D||m)&&(d.value.dropNode=s),s.node.nextSibling===N.node&&(m=!1),s.node.previousSibling===N.node&&(x=!1),s.node.contains(N.node,!1)&&(D=!1),(N.node===s.node||N.node.contains(s.node))&&(x=!1,D=!1,m=!1);const L=s.$el.getBoundingClientRect(),O=o.value.getBoundingClientRect();let A;const Q=x?D?.25:m?.45:1:-1,X=m?D?.75:x?.55:0:1;let R=-9999;const u=p.clientY-L.top;u<L.height*Q?A="before":u>L.height*X?A="after":D?A="inner":A="none";const b=s.$el.querySelector(`.${a.be("node","expand-icon")}`).getBoundingClientRect(),S=r.value;A==="before"?R=b.top-O.top:A==="after"&&(R=b.bottom-O.top),S.style.top=`${R}px`,S.style.left=`${b.right-O.left}px`,A==="inner"?We(s.$el,a.is("drop-inner")):me(s.$el,a.is("drop-inner")),d.value.showDropIndicator=A==="before"||A==="after",d.value.allowDrop=d.value.showDropIndicator||F,d.value.dropType=A,e.emit("node-drag-over",N.node,s.node,p)},treeNodeDragEnd:p=>{const{draggingNode:c,dropType:s,dropNode:y}=d.value;if(p.preventDefault(),p.dataTransfer.dropEffect="move",c&&y){const N={data:c.node.data};s!=="none"&&c.node.remove(),s==="before"?y.node.parent.insertBefore(N,y.node):s==="after"?y.node.parent.insertAfter(N,y.node):s==="inner"&&y.node.insertChild(N),s!=="none"&&n.value.registerNode(N),me(y.$el,a.is("drop-inner")),e.emit("node-drag-end",c.node,y.node,s,p),s!=="none"&&e.emit("node-drop",c.node,y.node,s,p)}c&&!y&&e.emit("node-drag-end",c.node,null,s,p),d.value.showDropIndicator=!1,d.value.draggingNode=null,d.value.dropNode=null,d.value.allowDrop=!0}}),{dragState:d}}const Pt=we({name:"ElTreeNode",components:{ElCollapseTransition:zt,ElCheckbox:Ct,NodeContent:Rt,ElIcon:Ye,Loading:Ge},props:{node:{type:G,default:()=>({})},props:{type:Object,default:()=>({})},accordion:Boolean,renderContent:Function,renderAfterExpand:Boolean,showCheckbox:{type:Boolean,default:!1}},emits:["node-expand"],setup(t,e){const o=se("tree"),{broadcastExpanded:r}=_e(t),n=re("RootTree"),a=w(!1),d=w(!1),l=w(null),v=w(null),g=w(null),p=re(Fe),c=Be();ae("NodeInstance",c),t.node.expanded&&(a.value=!0,d.value=!0);const s=n.props.children||"children";_(()=>{const u=t.node.data[s];return u&&[...u]},()=>{t.node.updateChildren()}),_(()=>t.node.indeterminate,u=>{x(t.node.checked,u)}),_(()=>t.node.checked,u=>{x(u,t.node.indeterminate)}),_(()=>t.node.expanded,u=>{ve(()=>a.value=u),u&&(d.value=!0)});const y=u=>De(n.props.nodeKey,u.data),N=u=>{const b=t.props.class;if(!b)return{};let S;if(Ze(b)){const{data:te}=u;S=b(te,u)}else S=b;return et(S)?{[S]:!0}:S},x=(u,b)=>{(l.value!==u||v.value!==b)&&n.ctx.emit("check-change",t.node.data,u,b),l.value=u,v.value=b},D=u=>{Ce(n.store,n.ctx.emit,()=>n.store.value.setCurrentNode(t.node)),n.currentNode.value=t.node,n.props.expandOnClickNode&&F(),n.props.checkOnClickNode&&!t.node.disabled&&L(null,{target:{checked:!t.node.checked}}),n.ctx.emit("node-click",t.node.data,t.node,c,u)},m=u=>{n.instance.vnode.props.onNodeContextmenu&&(u.stopPropagation(),u.preventDefault()),n.ctx.emit("node-contextmenu",u,t.node.data,t.node,c)},F=()=>{t.node.isLeaf||(a.value?(n.ctx.emit("node-collapse",t.node.data,t.node,c),t.node.collapse()):(t.node.expand(),e.emit("node-expand",t.node.data,t.node,c)))},L=(u,b)=>{t.node.setChecked(b.target.checked,!n.props.checkStrictly),ve(()=>{const S=n.store.value;n.ctx.emit("check",t.node.data,{checkedNodes:S.getCheckedNodes(),checkedKeys:S.getCheckedKeys(),halfCheckedNodes:S.getHalfCheckedNodes(),halfCheckedKeys:S.getHalfCheckedKeys()})})};return{ns:o,node$:g,tree:n,expanded:a,childNodeRendered:d,oldChecked:l,oldIndeterminate:v,getNodeKey:y,getNodeClass:N,handleSelectChange:x,handleClick:D,handleContextMenu:m,handleExpandIconClick:F,handleCheckChange:L,handleChildNodeExpand:(u,b,S)=>{r(b),n.ctx.emit("node-expand",u,b,S)},handleDragStart:u=>{n.props.draggable&&p.treeNodeDragStart({event:u,treeNode:t})},handleDragOver:u=>{u.preventDefault(),n.props.draggable&&p.treeNodeDragOver({event:u,treeNode:{$el:g.value,node:t.node}})},handleDrop:u=>{u.preventDefault()},handleDragEnd:u=>{n.props.draggable&&p.treeNodeDragEnd(u)},CaretRight:Qe}}}),jt=["aria-expanded","aria-disabled","aria-checked","draggable","data-key"],Ht=["aria-expanded"];function Vt(t,e,o,r,n,a){const d=W("el-icon"),l=W("el-checkbox"),v=W("loading"),g=W("node-content"),p=W("el-tree-node"),c=W("el-collapse-transition");return he((I(),Z("div",{ref:"node$",class:M([t.ns.b("node"),t.ns.is("expanded",t.expanded),t.ns.is("current",t.node.isCurrent),t.ns.is("hidden",!t.node.visible),t.ns.is("focusable",!t.node.disabled),t.ns.is("checked",!t.node.disabled&&t.node.checked),t.getNodeClass(t.node)]),role:"treeitem",tabindex:"-1","aria-expanded":t.expanded,"aria-disabled":t.node.disabled,"aria-checked":t.node.checked,draggable:t.tree.props.draggable,"data-key":t.getNodeKey(t.node),onClick:e[1]||(e[1]=V((...s)=>t.handleClick&&t.handleClick(...s),["stop"])),onContextmenu:e[2]||(e[2]=(...s)=>t.handleContextMenu&&t.handleContextMenu(...s)),onDragstart:e[3]||(e[3]=V((...s)=>t.handleDragStart&&t.handleDragStart(...s),["stop"])),onDragover:e[4]||(e[4]=V((...s)=>t.handleDragOver&&t.handleDragOver(...s),["stop"])),onDragend:e[5]||(e[5]=V((...s)=>t.handleDragEnd&&t.handleDragEnd(...s),["stop"])),onDrop:e[6]||(e[6]=V((...s)=>t.handleDrop&&t.handleDrop(...s),["stop"]))},[Y("div",{class:M(t.ns.be("node","content")),style:Je({paddingLeft:(t.node.level-1)*t.tree.props.indent+"px"})},[t.tree.props.icon||t.CaretRight?(I(),P(d,{key:0,class:M([t.ns.be("node","expand-icon"),t.ns.is("leaf",t.node.isLeaf),{expanded:!t.node.isLeaf&&t.expanded}]),onClick:V(t.handleExpandIconClick,["stop"])},{default:E(()=>[(I(),P(Xe(t.tree.props.icon||t.CaretRight)))]),_:1},8,["class","onClick"])):oe("v-if",!0),t.showCheckbox?(I(),P(l,{key:1,"model-value":t.node.checked,indeterminate:t.node.indeterminate,disabled:!!t.node.disabled,onClick:e[0]||(e[0]=V(()=>{},["stop"])),onChange:t.handleCheckChange},null,8,["model-value","indeterminate","disabled","onChange"])):oe("v-if",!0),t.node.loading?(I(),P(d,{key:2,class:M([t.ns.be("node","loading-icon"),t.ns.is("loading")])},{default:E(()=>[k(v)]),_:1},8,["class"])):oe("v-if",!0),k(g,{node:t.node,"render-content":t.renderContent},null,8,["node","render-content"])],6),k(c,null,{default:E(()=>[!t.renderAfterExpand||t.childNodeRendered?he((I(),Z("div",{key:0,class:M(t.ns.be("node","children")),role:"group","aria-expanded":t.expanded},[(I(!0),Z(Le,null,ze(t.node.childNodes,s=>(I(),P(p,{key:t.getNodeKey(s),"render-content":t.renderContent,"render-after-expand":t.renderAfterExpand,"show-checkbox":t.showCheckbox,node:s,accordion:t.accordion,props:t.props,onNodeExpand:t.handleChildNodeExpand},null,8,["render-content","render-after-expand","show-checkbox","node","accordion","props","onNodeExpand"]))),128))],10,Ht)),[[Ne,t.expanded]]):oe("v-if",!0)]),_:1})],42,jt)),[[Ne,t.node.visible]])}var Ut=Ee(Pt,[["render",Vt],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/tree/src/tree-node.vue"]]);function Wt({el$:t},e){const o=se("tree"),r=Ae([]),n=Ae([]);$e(()=>{d()}),tt(()=>{r.value=Array.from(t.value.querySelectorAll("[role=treeitem]")),n.value=Array.from(t.value.querySelectorAll("input[type=checkbox]"))}),_(n,l=>{l.forEach(v=>{v.setAttribute("tabindex","-1")})}),nt(t,"keydown",l=>{const v=l.target;if(!v.className.includes(o.b("node")))return;const g=l.code;r.value=Array.from(t.value.querySelectorAll(`.${o.is("focusable")}[role=treeitem]`));const p=r.value.indexOf(v);let c;if([U.up,U.down].includes(g)){if(l.preventDefault(),g===U.up){c=p===-1?0:p!==0?p-1:r.value.length-1;const y=c;for(;!e.value.getNode(r.value[c].dataset.key).canFocus;){if(c--,c===y){c=-1;break}c<0&&(c=r.value.length-1)}}else{c=p===-1?0:p<r.value.length-1?p+1:0;const y=c;for(;!e.value.getNode(r.value[c].dataset.key).canFocus;){if(c++,c===y){c=-1;break}c>=r.value.length&&(c=0)}}c!==-1&&r.value[c].focus()}[U.left,U.right].includes(g)&&(l.preventDefault(),v.click());const s=v.querySelector('[type="checkbox"]');[U.enter,U.space].includes(g)&&s&&(l.preventDefault(),s.click())});const d=()=>{var l;r.value=Array.from(t.value.querySelectorAll(`.${o.is("focusable")}[role=treeitem]`)),n.value=Array.from(t.value.querySelectorAll("input[type=checkbox]"));const v=t.value.querySelectorAll(`.${o.is("checked")}[role=treeitem]`);if(v.length){v[0].setAttribute("tabindex","0");return}(l=r.value[0])==null||l.setAttribute("tabindex","0")}}const Yt=we({name:"ElTree",components:{ElTreeNode:Ut},props:{data:{type:Array,default:()=>[]},emptyText:{type:String},renderAfterExpand:{type:Boolean,default:!0},nodeKey:String,checkStrictly:Boolean,defaultExpandAll:Boolean,expandOnClickNode:{type:Boolean,default:!0},checkOnClickNode:Boolean,checkDescendants:{type:Boolean,default:!1},autoExpandParent:{type:Boolean,default:!0},defaultCheckedKeys:Array,defaultExpandedKeys:Array,currentNodeKey:[String,Number],renderContent:Function,showCheckbox:{type:Boolean,default:!1},draggable:{type:Boolean,default:!1},allowDrag:Function,allowDrop:Function,props:{type:Object,default:()=>({children:"children",label:"label",disabled:"disabled"})},lazy:{type:Boolean,default:!1},highlightCurrent:Boolean,load:Function,filterNodeMethod:Function,accordion:Boolean,indent:{type:Number,default:18},icon:{type:ot}},emits:["check-change","current-change","node-click","node-contextmenu","node-collapse","node-expand","check","node-drag-start","node-drag-end","node-drop","node-drag-leave","node-drag-enter","node-drag-over"],setup(t,e){const{t:o}=$t(),r=se("tree"),n=w(new Mt({key:t.nodeKey,data:t.data,lazy:t.lazy,props:t.props,load:t.load,currentNodeKey:t.currentNodeKey,checkStrictly:t.checkStrictly,checkDescendants:t.checkDescendants,defaultCheckedKeys:t.defaultCheckedKeys,defaultExpandedKeys:t.defaultExpandedKeys,autoExpandParent:t.autoExpandParent,defaultExpandAll:t.defaultExpandAll,filterNodeMethod:t.filterNodeMethod}));n.value.initialize();const a=w(n.value.root),d=w(null),l=w(null),v=w(null),{broadcastExpanded:g}=_e(t),{dragState:p}=qt({props:t,ctx:e,el$:l,dropIndicator$:v,store:n});Wt({el$:l},n);const c=rt(()=>{const{childNodes:i}=a.value;return!i||i.length===0||i.every(({visible:C})=>!C)});_(()=>t.currentNodeKey,i=>{n.value.setCurrentNodeKey(i)}),_(()=>t.defaultCheckedKeys,i=>{n.value.setDefaultCheckedKey(i)}),_(()=>t.defaultExpandedKeys,i=>{n.value.setDefaultExpandedKeys(i)}),_(()=>t.data,i=>{n.value.setData(i)},{deep:!0}),_(()=>t.checkStrictly,i=>{n.value.checkStrictly=i});const s=i=>{if(!t.filterNodeMethod)throw new Error("[Tree] filterNodeMethod is required when filter");n.value.filter(i)},y=i=>De(t.nodeKey,i.data),N=i=>{if(!t.nodeKey)throw new Error("[Tree] nodeKey is required in getNodePath");const C=n.value.getNode(i);if(!C)return[];const q=[C.data];let j=C.parent;for(;j&&j!==a.value;)q.push(j.data),j=j.parent;return q.reverse()},x=(i,C)=>n.value.getCheckedNodes(i,C),D=i=>n.value.getCheckedKeys(i),m=()=>{const i=n.value.getCurrentNode();return i?i.data:null},F=()=>{if(!t.nodeKey)throw new Error("[Tree] nodeKey is required in getCurrentKey");const i=m();return i?i[t.nodeKey]:null},L=(i,C)=>{if(!t.nodeKey)throw new Error("[Tree] nodeKey is required in setCheckedNodes");n.value.setCheckedNodes(i,C)},O=(i,C)=>{if(!t.nodeKey)throw new Error("[Tree] nodeKey is required in setCheckedKeys");n.value.setCheckedKeys(i,C)},A=(i,C,q)=>{n.value.setChecked(i,C,q)},Q=()=>n.value.getHalfCheckedNodes(),X=()=>n.value.getHalfCheckedKeys(),R=(i,C=!0)=>{if(!t.nodeKey)throw new Error("[Tree] nodeKey is required in setCurrentNode");Ce(n,e.emit,()=>n.value.setUserCurrentNode(i,C))},u=(i,C=!0)=>{if(!t.nodeKey)throw new Error("[Tree] nodeKey is required in setCurrentKey");Ce(n,e.emit,()=>n.value.setCurrentNodeKey(i,C))},b=i=>n.value.getNode(i),S=i=>{n.value.remove(i)},te=(i,C)=>{n.value.append(i,C)},fe=(i,C)=>{n.value.insertBefore(i,C)},pe=(i,C)=>{n.value.insertAfter(i,C)},ge=(i,C,q)=>{g(C),e.emit("node-expand",i,C,q)},de=(i,C)=>{if(!t.nodeKey)throw new Error("[Tree] nodeKey is required in updateKeyChild");n.value.updateChildren(i,C)};return ae("RootTree",{ctx:e,props:t,store:n,root:a,currentNode:d,instance:Be()}),ae(at,void 0),{ns:r,store:n,root:a,currentNode:d,dragState:p,el$:l,dropIndicator$:v,isEmpty:c,filter:s,getNodeKey:y,getNodePath:N,getCheckedNodes:x,getCheckedKeys:D,getCurrentNode:m,getCurrentKey:F,setCheckedNodes:L,setCheckedKeys:O,setChecked:A,getHalfCheckedNodes:Q,getHalfCheckedKeys:X,setCurrentNode:R,setCurrentKey:u,t:o,getNode:b,remove:S,append:te,insertBefore:fe,insertAfter:pe,handleNodeExpand:ge,updateKeyChildren:de}}});function Gt(t,e,o,r,n,a){var d;const l=W("el-tree-node");return I(),Z("div",{ref:"el$",class:M([t.ns.b(),t.ns.is("dragging",!!t.dragState.draggingNode),t.ns.is("drop-not-allow",!t.dragState.allowDrop),t.ns.is("drop-inner",t.dragState.dropType==="inner"),{[t.ns.m("highlight-current")]:t.highlightCurrent}]),role:"tree"},[(I(!0),Z(Le,null,ze(t.root.childNodes,v=>(I(),P(l,{key:t.getNodeKey(v),node:v,props:t.props,accordion:t.accordion,"render-after-expand":t.renderAfterExpand,"show-checkbox":t.showCheckbox,"render-content":t.renderContent,onNodeExpand:t.handleNodeExpand},null,8,["node","props","accordion","render-after-expand","show-checkbox","render-content","onNodeExpand"]))),128)),t.isEmpty?(I(),Z("div",{key:0,class:M(t.ns.e("empty-block"))},[Y("span",{class:M(t.ns.e("empty-text"))},st((d=t.emptyText)!=null?d:t.t("el.tree.emptyText")),3)],2)):oe("v-if",!0),he(Y("div",{ref:"dropIndicator$",class:M(t.ns.e("drop-indicator"))},null,2),[[Ne,t.dragState.showDropIndicator]])],2)}var ue=Ee(Yt,[["render",Gt],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/tree/src/tree.vue"]]);ue.install=t=>{t.component(ue.name,ue)};const Qt=ue,Xt=Qt;const Jt={class:"demo-pagination-block"},Zt={class:"dialog-footer"},en={style:{flex:"auto"}},zn={__name:"roles",setup(t){const e=w(!0),o=ie([]),r=w(""),n=w(1),a=w(14),d=w(0),l=w("small"),v=w(!1),g=w(!1),p=w(),c=w(!1),s=w([]),y=w([]),N=w(!1),x={children:"children",label:"title"},D=w(null),m=ie({name:"",description:""}),F=ie({name:[{required:!0,message:"请输入角色名称",trigger:"blur"}],description:[{required:!0,message:"请输入角色描述",trigger:"blur"}]});$e(()=>{b(),L()});const L=async()=>{try{const h=await dt();if(h.code!==200)return B({title:"",message:h.msg,type:"error",duration:2e3});s.value=h.data}catch{return!1}},O=()=>{r.value&&b()},A=()=>{r.value="",e.value=!0,b()},Q=h=>{a.value=h,e.value=!0,b()},X=h=>{n.value=h,e.value=!0,b()},R=()=>{N.value=!1,y.value=[],D.value.setCheckedKeys([])},u=()=>{p.value.resetFields(),m.name="",m.description="",m.menuIds=[],c.value=!1,g.value=!1,delete m.id},b=async()=>{try{const h=await lt({keywords:r.value,page:n.value,pageSize:a.value});if(h.code!==200)return e.value=!1,B({title:"Error",message:h.msg,type:"error",duration:2e3});setTimeout(()=>{e.value=!1},500),o.splice(0,o.length,...h.data.list),d.value=h.data.total}catch{return e.value=!1,!1}},S=()=>{g.value=!0},te=async h=>{h&&await h.validate(async f=>{if(f)c.value?i():ge();else return!1})},fe=async h=>{c.value=!0,g.value=!0,m.name=h.name,m.description=h.description,m.id=h._id},pe=async h=>{Lt.confirm("此操作将永久删除该角色, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const f=ne.service({lock:!0,text:"Flyknit...",background:"rgba(0, 0, 0, 0.7)"}),T=await gt({id:h._id});if(T.code!==200)return f.close(),B({title:"Error",message:T.msg,type:"error",duration:2e3});setTimeout(()=>{f.close(),B({title:"",message:T.msg,type:"success",duration:2e3}),b()},500)}).catch(()=>!1)},ge=async()=>{const h=ne.service({lock:!0,text:"Flyknit...",background:"rgba(0, 0, 0, 0.7)"});try{const f=await yt(m);if(f.code!==200)return h.close(),B({title:"Error",message:f.msg,type:"error",duration:2e3});B({title:"",message:f.msg,type:"success",duration:2e3}),g.value=!1,b(),setTimeout(()=>{de(),h.close()},500)}catch{return h.close(),!1}},de=()=>{m.name="",m.description="",c.value=!1,delete m.id},i=async()=>{const h=ne.service({lock:!0,text:"Flyknit...",background:"rgba(0, 0, 0, 0.7)"});try{const f=await mt(m);if(f.code!==200)return h.close(),B({title:"Error",message:f.msg,type:"error",duration:2e3});g.value=!1,setTimeout(()=>{h.close(),B({title:"",message:f.msg,type:"success",duration:2e3}),de(),b()},500)}catch{return h.close(),!1}},C=async h=>{m.id=h._id,j();const f=ne.service({lock:!0,text:"Flyknit...",background:"rgba(0, 0, 0, 0.7)"});setTimeout(()=>{f.close(),N.value=!0},500)},q=async()=>{const h=D.value.getCheckedNodes(!1,!0).map(T=>T.id);if(h.length===0)return B({title:"",message:"请选择权限",type:"error",duration:2e3});m.menuIds=h;const f=ne.service({lock:!0,text:"Flyknit...",background:"rgba(0, 0, 0, 0.7)"});try{const T=await kt(m);if(T.code!==200)return B({title:"",message:T.msg,type:"error",duration:2e3});setTimeout(()=>{f.close(),B({title:"",message:T.msg,type:"success",duration:2e3}),N.value=!1,D.value.setCheckedKeys([]),y.value=[]},500)}catch{return!1}},j=async()=>{try{const h=await vt(m);if(h.code!==200)return B({title:"Error",message:h.msg,type:"error",duration:2e3});await ve(),y.value=h.data.filter(f=>s.value.some(T=>T.id===f))}catch{return!1}};return(h,f)=>{const T=xt,ye=At,z=Bt,Ke=It,H=St,Me=Tt,Oe=Kt,Re=Dt,xe=Et,qe=wt,Pe=bt,je=Xt,He=Nt,Ve=_t;return I(),P(Ke,{gutter:20},{default:E(()=>[k(ye,{xs:24,sm:24,md:24,lg:24},{default:E(()=>[k(Re,{shadow:"hover"},{default:E(()=>[k(Ke,{gutter:20},{default:E(()=>[k(ye,{span:6},{default:E(()=>[k(T,{modelValue:r.value,"onUpdate:modelValue":f[0]||(f[0]=K=>r.value=K),placeholder:"请输入角色名称",clearable:""},null,8,["modelValue"])]),_:1}),k(ye,{span:14},{default:E(()=>[k(z,{type:"primary",icon:J(it),onClick:O},{default:E(()=>[$("搜 索")]),_:1},8,["icon"]),k(z,{type:"info",icon:J(ct),onClick:A},{default:E(()=>[$("重 置")]),_:1},8,["icon"]),k(z,{type:"success",icon:J(ut),onClick:S},{default:E(()=>[$("创建角色")]),_:1},8,["icon"])]),_:1})]),_:1}),he((I(),P(Me,{data:o,stripe:"",style:{"margin-top":"2rem"},"element-loading-text":"Flyknit..."},{default:E(()=>[k(H,{type:"selection",width:"55"}),k(H,{prop:"_id",label:"ID",width:"230"}),k(H,{prop:"name",label:"角色名称"}),k(H,{prop:"description",label:"角色描述"}),k(H,{prop:"create_time",label:"创建时间",width:"170"}),k(H,{prop:"update_time",label:"更新时间",width:"170"}),k(H,{label:"操作",width:"280"},{default:E(K=>[k(z,{type:"primary",size:"small",icon:J(ht),onClick:Se=>C(K.row)},{default:E(()=>[$("权限分配")]),_:2},1032,["icon","onClick"]),k(z,{type:"warning",size:"small",icon:J(ft),onClick:Se=>fe(K.row)},{default:E(()=>[$("编 辑")]),_:2},1032,["icon","onClick"]),k(z,{type:"danger",size:"small",icon:J(pt),onClick:Se=>pe(K.row)},{default:E(()=>[$("删 除")]),_:2},1032,["icon","onClick"])]),_:1})]),_:1},8,["data"])),[[Ve,e.value]]),Y("div",Jt,[k(Oe,{style:{"margin-top":"1rem"},"current-page":n.value,"onUpdate:currentPage":f[1]||(f[1]=K=>n.value=K),"page-size":a.value,"onUpdate:pageSize":f[2]||(f[2]=K=>a.value=K),"page-sizes":[14,16,18,20],size:l.value,background:v.value,layout:"total, sizes, prev, pager, next, jumper",total:d.value,onSizeChange:Q,onCurrentChange:X},null,8,["current-page","page-size","size","background","total"])])]),_:1})]),_:1}),k(Pe,{title:"创建角色",modelValue:g.value,"onUpdate:modelValue":f[7]||(f[7]=K=>g.value=K),width:"40%","close-on-click-modal":!1,"before-close":u},{footer:E(()=>[Y("span",Zt,[k(z,{onClick:f[5]||(f[5]=K=>g.value=!1)},{default:E(()=>[$("取 消")]),_:1}),k(z,{type:"primary",onClick:f[6]||(f[6]=K=>te(p.value))},{default:E(()=>[$("确 定")]),_:1})])]),default:E(()=>[k(qe,{ref_key:"ruleFormRef",ref:p,model:m,rules:F,"label-width":"120px"},{default:E(()=>[k(xe,{label:"角色名称",prop:"name"},{default:E(()=>[k(T,{modelValue:m.name,"onUpdate:modelValue":f[3]||(f[3]=K=>m.name=K),maxlength:"20",placeholder:"请输入角色名称",clearable:""},null,8,["modelValue"])]),_:1}),k(xe,{label:"角色描述",prop:"description"},{default:E(()=>[k(T,{modelValue:m.description,"onUpdate:modelValue":f[4]||(f[4]=K=>m.description=K),maxlength:"50",placeholder:"请输入角色描述",clearable:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),k(He,{modelValue:N.value,"onUpdate:modelValue":f[9]||(f[9]=K=>N.value=K),direction:"rtl",title:"菜单权限分配",onClose:R},{default:E(()=>[Y("div",null,[k(je,{ref_key:"tree",ref:D,props:x,"current-node-key":"id",data:s.value,"show-checkbox":"","node-key":"id","default-expand-all":"","highlight-current":"","default-checked-keys":y.value},null,8,["data","default-checked-keys"])])]),footer:E(()=>[Y("div",en,[k(z,{onClick:f[8]||(f[8]=K=>N.value=!1)},{default:E(()=>[$("取 消")]),_:1}),k(z,{type:"primary",onClick:q},{default:E(()=>[$("确 认")]),_:1})])]),_:1},8,["modelValue"])]),_:1})}}};export{zn as default};
