import{Q as y,aw as r,ax as c,_ as b,C as g,X as S,c as p,R as w,a7 as d,bz as O,o as _,a as h,w as $,S as v,H as j,n as o,ad as N,L as C,U as R,a9 as k}from"./index-444b28c3.js";const x=Symbol("rowContextKey"),L=y({tag:{type:String,default:"div"},span:{type:Number,default:24},offset:{type:Number,default:0},pull:{type:Number,default:0},push:{type:Number,default:0},xs:{type:r([Number,Object]),default:()=>c({})},sm:{type:r([Number,Object]),default:()=>c({})},md:{type:r([Number,Object]),default:()=>c({})},lg:{type:r([Number,Object]),default:()=>c({})},xl:{type:r([Number,Object]),default:()=>c({})}}),P={name:"ElCol"},B=g({...P,props:L,setup(f){const t=f,{gutter:n}=S(x,{gutter:p(()=>0)}),l=w("col"),i=p(()=>{const e={};return n.value&&(e.paddingLeft=e.paddingRight=`${n.value/2}px`),e}),a=p(()=>{const e=[];return["span","offset","pull","push"].forEach(s=>{const u=t[s];d(u)&&(s==="span"?e.push(l.b(`${t[s]}`)):u>0&&e.push(l.b(`${s}-${t[s]}`)))}),["xs","sm","md","lg","xl"].forEach(s=>{d(t[s])?e.push(l.b(`${s}-${t[s]}`)):O(t[s])&&Object.entries(t[s]).forEach(([u,m])=>{e.push(u!=="span"?l.b(`${s}-${u}-${m}`):l.b(`${s}-${m}`))})}),n.value&&e.push(l.is("guttered")),e});return(e,E)=>(_(),h(C(e.tag),{class:j([o(l).b(),o(a)]),style:N(o(i))},{default:$(()=>[v(e.$slots,"default")]),_:3},8,["class","style"]))}});var K=b(B,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/col/src/col.vue"]]);const X=R(K),A=["start","center","end","space-around","space-between","space-evenly"],D=["top","middle","bottom"],H=y({tag:{type:String,default:"div"},gutter:{type:Number,default:0},justify:{type:String,values:A,default:"start"},align:{type:String,values:D,default:"top"}}),I={name:"ElRow"},J=g({...I,props:H,setup(f){const t=f,n=w("row"),l=p(()=>t.gutter);k(x,{gutter:l});const i=p(()=>{const a={};return t.gutter&&(a.marginRight=a.marginLeft=`-${t.gutter/2}px`),a});return(a,e)=>(_(),h(C(a.tag),{class:j([o(n).b(),o(n).is(`justify-${t.justify}`,a.justify!=="start"),o(n).is(`align-${t.align}`,a.align!=="top")]),style:N(o(i))},{default:$(()=>[v(a.$slots,"default")]),_:3},8,["class","style"]))}});var Q=b(J,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/row/src/row.vue"]]);const q=R(Q);export{X as E,q as a};
